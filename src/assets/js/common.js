
//获取cookie
import getNoce from '../../plugins/getNoce';
let axios = require('axios');
function getCookie(name) {
    var strcookie = document.cookie;//获取cookie字符串
    var arrcookie = strcookie.split("; ");//分割
    //遍历匹配
    for (var i = 0; i < arrcookie.length; i++) {
        var arr = arrcookie[i].split("=");
        if (arr[0] == name) {
            return arr[1];
        }
    }
    return "";
}
function formatDate(val) {
    var date = new Date(val);//时间戳为10位需*1000，时间戳为13位的话不需乘1000
    var Y = date.getFullYear() + '-';
    var M = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
    var D = (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) + ' ';
    var h = (date.getHours() < 10 ? "0" + date.getHours() : date.getHours()) + ':';
    var m = (date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes()) + ':';
    var s = (date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds());
    return Y + M + D + h + m + s;
}
// 获取主域名 
function getDomain() {
    var hostname = window.location.hostname;
    var ip = hostname.match(/\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}/g);
    if (ip) {
        return ip;
    }
    var domain = hostname.match(/([a-z0-9][a-z0-9\-]*?\.(?:com|cn|net|org|gov|info|la|cc|co|jp)(?:\.(?:cn|jp))?)$/);
    if (domain) {
        return domain[0];
    }
    return hostname;
}
async function fetchData() {
    const nonce = await getNoce.useNonce();
    axios.defaults.headers.common['Authorization'] = 'Bearer ' + getCookie('ZTGlS_TOKEN');
    axios.defaults.headers.common['Once'] = nonce;
    axios.defaults.baseURL = process.env.VUE_APP_URL
    return axios.get('/gateway/client-cpus/userLoginAdmin/loginPhoneInfo', {}).then(res => {
        return res.data
    }).catch(error => {
        console.error('请求失败:', error);
        throw error; // 抛出错误
    });
}
export default {
    getCookie: function (name) {
        return getCookie(name)
    },
    formatDate: function (val) {
        return formatDate(val)
    },
    getDomain: function () {
        return getDomain()
    },
    fetchData: function () {
        return fetchData()
    }
}