// 创建模板页面通用样式
// 用于 CreateTemplate1.vue 等模板创建相关页面

// 引入通用样式
@import './send-details-common.less';

// 模板创建页面特有样式
.modern-template-page {
  min-height: 100vh;
  background: #f5f7fa;
  
  // 页面头部样式
  .page-header {
    background: #fff;
    padding: 24px 32px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      
      .header-left {
        display: flex;
        align-items: center;
        
        .back-btn {
          color: #1890ff;
          font-size: 14px;
          padding: 8px 0;
          margin-right: 16px;
          
          &:hover {
            color: #40a9ff;
          }
          
          i {
            margin-right: 4px;
          }
        }
        
        .page-title {
          font-size: 24px;
          font-weight: 600;
          color: #262626;
          margin: 0;
        }
      }
      
      .header-right {
        /deep/ .el-tag {
          // padding: 8px 16px;
          font-size: 12px;
          border-radius: 16px;
        }
      }
    }
  }
  
  // 主要内容区域
  .page-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
    
    .content-container {
      .modern-form {
        .form-card {
          margin-bottom: 24px;
          border-radius: 12px;
          border: 1px solid #e8eaec;
          transition: all 0.3s ease;
          
          &:hover {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
          }
          
          .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .card-title {
              display: flex;
              align-items: center;
              font-weight: 600;
              color: #262626;
              font-size: 16px;
              
              i {
                margin-right: 8px;
                color: #1890ff;
                font-size: 18px;
              }
            }
            
            .help-icon {
              color: #8c8c8c;
              cursor: help;
              
              &:hover {
                color: #1890ff;
              }
            }
            
            .header-actions {
              .example-btn, .clear-btn {
                color: #1890ff;
                font-size: 12px;
                
                &:hover {
                  color: #40a9ff;
                }
                
                i {
                  margin-right: 4px;
                }
              }
            }
          }
        }
      }
    }
  }
}

// 模板类型选择器样式
.template-type-card {
  .template-type-selector {
    &.horizontal-layout {
      width: 100%;
      overflow: visible; // 确保内容不被裁剪
    }

    .type-radio-group {
      width: 100%;

      // 横向布局样式
      &.horizontal {
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        gap: 20px;
        align-items: stretch; // 确保所有卡片高度一致

        // 大屏幕优化
        @media (min-width: 1600px) {
          gap: 24px;
        }

        @media (max-width: 1400px) {
          gap: 18px;
        }

        @media (max-width: 1200px) {
          grid-template-columns: repeat(2, 1fr);
          gap: 16px;
        }

        // 中等屏幕优化 - 提前切换到单列
        @media (max-width: 1000px) {
          grid-template-columns: 1fr;
          gap: 16px;
        }

        @media (max-width: 768px) {
          grid-template-columns: 1fr;
          gap: 12px;
        }
      }

      .type-option-card {
        border: 1px solid #e8eaec;
        border-radius: 12px;
        padding: 20px;
        transition: all 0.3s ease;
        cursor: pointer;
        position: relative;
        background: #fff;
        overflow: visible; // 确保内容不被裁剪
        box-sizing: border-box; // 确保padding计算正确

        &:hover {
          border-color: #40a9ff;
          box-shadow: 0 4px 16px rgba(24, 144, 255, 0.15);
          transform: translateY(-2px);
        }

        &.active {
          border-color: #1890ff;
          background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
          box-shadow: 0 6px 20px rgba(24, 144, 255, 0.25);
          transform: translateY(-2px);

          &::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #1890ff 0%, #52c41a 100%);
            border-radius: 12px 12px 0 0;
          }
        }

        // 横向卡片样式
        &.horizontal-card {
          margin-bottom: 0;
          min-height: 240px; // 进一步增加最小高度以容纳更多内容
          height: auto; // 允许自动高度
          display: flex;
          flex-direction: column;

          // 响应式高度调整
          @media (max-width: 1400px) {
            min-height: 230px;
          }

          @media (max-width: 1200px) {
            min-height: 220px;
          }

          @media (max-width: 900px) {
            min-height: 200px;
          }

          @media (max-width: 768px) {
            min-height: auto; // 移动端允许完全自适应高度
            padding: 16px; // 减少移动端内边距
          }

          .type-radio {
            width: 100%;
            height: 100%;
            margin: 0;

            /deep/ .el-radio__input {
              position: absolute;
              top: 16px;
              right: 16px;
              margin: 0;
              z-index: 2;
            }

            /deep/ .el-radio__label {
              width: 100%;
              padding: 0;
              height: 100%;
              display: flex;
              flex-direction: column;
              min-height: inherit; // 继承最小高度
            }
          }

          .type-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            min-height: 0; // 防止flex子元素溢出

            .type-header {
              display: flex;
              align-items: flex-start;
              margin-bottom: 12px;
              flex-shrink: 0; // 防止头部被压缩

              .icon-wrapper {
                width: 40px;
                height: 40px;
                border-radius: 8px;
                background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
                display: flex;
                align-items: center;
                justify-content: center;
                margin-right: 12px;
                flex-shrink: 0;

                .type-icon {
                  color: #fff;
                  font-size: 18px;
                }
              }

              .title-wrapper {
                flex: 1;
                min-width: 0; // 防止flex子元素溢出

                .type-title {
                  font-weight: 600;
                  color: #262626;
                  font-size: 16px;
                  display: block;
                  margin-bottom: 4px;
                  line-height: 1.2;
                }

                .recommend-tag {
                  display: inline-block;
                }
              }
            }

            .type-description {
              color: #595959;
              font-size: 13px;
              line-height: 1.6; // 增加行高以提高可读性
              margin-bottom: 16px; // 增加底部间距
              flex: 1;
              min-height: 0; // 防止flex子元素溢出
              word-wrap: break-word; // 长单词换行
              overflow-wrap: break-word; // 兼容性
              hyphens: auto; // 自动断字
              white-space: normal; // 确保正常换行
              text-align: left; // 左对齐

              // 移动端优化
              @media (max-width: 768px) {
                font-size: 12px;
                line-height: 1.5;
                margin-bottom: 12px;
              }
            }

            .type-features {
              display: flex;
              flex-wrap: wrap;
              gap: 6px;
              margin-top: auto;
              flex-shrink: 0; // 防止特性标签被压缩
              align-items: flex-start; // 顶部对齐
              justify-content: flex-start; // 左对齐

              .feature-tag {
                background: #f0f2f5;
                color: #595959;
                padding: 4px 8px;
                border-radius: 4px;
                font-size: 11px;
                line-height: 1.3;
                white-space: nowrap; // 防止标签内文字换行
                flex-shrink: 0; // 防止标签被压缩

                // 移动端优化
                @media (max-width: 768px) {
                  padding: 3px 6px;
                  font-size: 10px;
                }
              }
            }
          }

          // 会员营销卡片特殊样式
          &.marketing-card {
            min-height: 260px; // 为营销卡片提供更多高度

            @media (max-width: 1400px) {
              min-height: 250px;
            }

            @media (max-width: 1200px) {
              min-height: 240px;
            }

            @media (max-width: 1000px) {
              min-height: 220px;
            }

            @media (max-width: 768px) {
              min-height: auto;
              padding: 18px; // 增加移动端内边距
            }

            .type-content {
              .type-description {
                margin-bottom: 18px; // 增加描述区域的底部间距

                @media (max-width: 768px) {
                  margin-bottom: 14px;
                }
              }

              .type-features {
                gap: 8px; // 增加特性标签间距

                @media (max-width: 768px) {
                  gap: 6px;
                }
              }
            }
          }
        }
      }

      // 保持原有的垂直布局样式（作为备用）
      &:not(.horizontal) {
        .type-option-card {
          margin-bottom: 12px;

          .type-radio {
            width: 100%;
            margin: 0;

            /deep/ .el-radio__input {
              margin-top: 2px;
            }

            /deep/ .el-radio__label {
              width: calc(100% - 20px);
              padding-left: 8px;
            }
          }

          .type-content {
            .type-header {
              display: flex;
              align-items: center;
              margin-bottom: 8px;

              .type-icon {
                color: #1890ff;
                font-size: 16px;
                margin-right: 8px;
              }

              .type-title {
                font-weight: 600;
                color: #262626;
                font-size: 14px;
                margin-right: 8px;
              }

              .recommend-tag {
                margin-left: auto;
              }
            }

            .type-description {
              color: #8c8c8c;
              font-size: 12px;
              line-height: 1.5;
            }
          }
        }
      }
    }
  }
}

// 基本信息卡片样式
.basic-info-card {
  .form-row {
    margin-bottom: 16px;
    
    &:last-child {
      margin-bottom: 0;
    }
  }
  
  .modern-select {
    width: 100%;
    
    /deep/ .el-input__inner {
      border-radius: 6px;
      border: 1px solid #e8eaec;
      
      &:focus {
        border-color: #40a9ff;
      }
    }
  }
  
  .input-tip {
    margin-top: 8px;
    
    /deep/ .el-alert {
      border-radius: 6px;
    }
  }
}

// 短信内容卡片样式
.content-card {
  .content-input-wrapper {
    margin-bottom: 16px;
  }
  
  .content-stats {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    padding: 12px;
    background: #f5f5f5;
    border-radius: 6px;
    
    .stats-item {
      display: flex;
      align-items: center;
      
      .stats-label {
        color: #8c8c8c;
        margin-right: 4px;
      }
      
      .stats-value {
        font-weight: 600;
        color: #1890ff;
        margin-right: 4px;
      }
      
      .stats-unit {
        color: #8c8c8c;
      }
    }
    
    .stats-note {
      flex: 1;
      margin-left: 16px;
      
      /deep/ .el-alert {
        border-radius: 6px;
        margin: 0;
      }
    }
  }
  
  .content-options {
    margin-bottom: 16px;
    
    .option-group {
      display: flex;
      align-items: center;
      gap: 16px;
      
      .short-link-btn {
        border-radius: 6px;
        
        i {
          margin-right: 4px;
        }
      }
      
      .modern-checkbox {
        /deep/ .el-checkbox__label {
          color: #595959;
        }
      }
    }
  }
  
  .long-url-section {
    margin-top: 16px;
    padding: 16px;
    background: #f9f9f9;
    border-radius: 6px;
    
    .long-url-form-item {
      margin-bottom: 0;
    }
    
    .input-tip {
      margin-top: 8px;
      
      /deep/ .el-alert {
        border-radius: 6px;
      }
    }
  }
}

// 变量配置卡片样式
.variables-card {
  .variables-list {
    .variable-item {
      margin-bottom: 16px;
      padding: 16px;
      background: #f9f9f9;
      border-radius: 8px;
      border: 1px solid #e8eaec;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .variable-row {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 16px;
        
        .variable-name, .variable-type {
          .form-item-modern {
            margin-bottom: 0;
          }
        }
        
        .variable-name-input {
          /deep/ .el-input__inner {
            background: #f5f5f5;
          }
        }
      }
    }
  }
}

// 申请说明卡片样式
.remark-card {
  .remark-form-item {
    margin-bottom: 16px;
  }
  
  .remark-tips {
    /deep/ .el-alert {
      border-radius: 6px;
    }
  }
}

// 操作按钮样式
.action-buttons {
  margin: 32px 0;
  text-align: center;
  
  .button-group {
    display: inline-flex;
    gap: 16px;
    
    .submit-btn, .save-btn, .cancel-btn {
      min-width: 120px;
      border-radius: 6px;
      font-weight: 500;
      
      i {
        margin-right: 4px;
      }
    }
    
    .submit-btn {
      background: linear-gradient(135deg, #1890ff 0%, #096dd9 100%);
      border: none;
      
      &:hover {
        background: linear-gradient(135deg, #40a9ff 0%, #1890ff 100%);
      }
    }
    
    .save-btn {
      background: linear-gradient(135deg, #52c41a 0%, #389e0d 100%);
      border: none;
      
      &:hover {
        background: linear-gradient(135deg, #73d13d 0%, #52c41a 100%);
      }
    }
    
    .cancel-btn {
      background: #fff;
      border: 1px solid #d9d9d9;
      color: #595959;
      
      &:hover {
        border-color: #40a9ff;
        color: #40a9ff;
      }
    }
  }
}

// 模板规范说明卡片样式
.template-rules-card {
  .rules-content {
    .rules-list {
      .rule-item {
        margin-bottom: 12px;
        line-height: 1.6;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        strong {
          color: #262626;
        }
        
        .highlight {
          color: #ff4d4f;
          font-weight: 600;
        }
      }
    }
    
    .variable-types {
      .variable-type-item {
        display: flex;
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .variable-name {
          min-width: 180px;
          font-weight: 600;
          color: #1890ff;
          margin-right: 16px;
        }
        
        .variable-desc {
          color: #8c8c8c;
          line-height: 1.5;
        }
      }
    }
    
    .billing-rules {
      .billing-item {
        display: flex;
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .billing-condition {
          min-width: 140px;
          font-weight: 600;
          color: #262626;
          margin-right: 16px;
        }
        
        .billing-desc {
          color: #8c8c8c;
          line-height: 1.5;
          
          .highlight {
            color: #ff4d4f;
            font-weight: 600;
          }
        }
      }
    }
    
    .audit-rules {
      .audit-item {
        display: flex;
        margin-bottom: 8px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .audit-time, .audit-note {
          min-width: 100px;
          font-weight: 600;
          color: #262626;
          margin-right: 16px;
        }
        
        .audit-desc {
          color: #8c8c8c;
          line-height: 1.5;
          
          .highlight {
            color: #ff4d4f;
            font-weight: 600;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .modern-template-page {
    .page-header {
      padding: 20px 24px;
      
      .header-content {
        padding: 0 16px;
      }
    }
    
    .page-content {
      padding: 0 16px;
    }
  }
}

@media (max-width: 768px) {
  .modern-template-page {
    .page-header {
      padding: 16px 20px;
      
      .header-content {
        flex-direction: column;
        align-items: flex-start;
        
        .header-right {
          margin-top: 12px;
        }
      }
    }
    
    .page-content {
      padding: 0 12px;
    }
  }
  
  .variables-card {
    .variables-list {
      .variable-item {
        .variable-row {
          grid-template-columns: 1fr;
        }
      }
    }
  }
  
  .action-buttons {
    .button-group {
      flex-direction: column;
      width: 100%;
      
      .submit-btn, .save-btn, .cancel-btn {
        width: 100%;
      }
    }
  }
}
