// SMS签名管理通用样式
// 用于 CreateSign.vue 和 userSignature.vue 等签名相关页面

// 现代化签名页面基础样式
.modern-signature-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  
  // 页面头部样式
  .page-header {
    background: #ffffff;
    border-bottom: 1px solid #e4e7ed;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
    
    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 16px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      .header-left {
        display: flex;
        align-items: center;
        
        .back-btn {
          color: #1890ff;
          font-size: 14px;
          padding: 8px 0;
          margin-right: 16px;
          
          &:hover {
            color: #40a9ff;
          }
          
          i {
            margin-right: 4px;
          }
        }
        
        .page-title {
          font-size: 20px;
          font-weight: 600;
          color: #262626;
          margin: 0;
        }
      }
      
      .header-right {
        .el-tag {
          font-size: 12px;
          // padding: 4px 12px;
        }
      }
    }
  }
  
  // 主要内容区域
  .page-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
    
    .content-container {
      .modern-form {
        .form-card {
          margin-bottom: 24px;
          border-radius: 12px;
          border: 1px solid #e8eaec;
          transition: all 0.3s ease;
          
          &:hover {
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            border-color: #40a9ff;
          }
          
          .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            
            .card-title {
              font-size: 16px;
              font-weight: 600;
              color: #262626;
              display: flex;
              align-items: center;
              
              i {
                margin-right: 8px;
                color: #1890ff;
                font-size: 18px;
              }
            }
            
            .help-icon {
              color: #8c8c8c;
              cursor: help;
              
              &:hover {
                color: #1890ff;
              }
            }
            
            .header-actions {
              display: flex;
              align-items: center;
              gap: 8px;
              
              .example-btn, .clear-btn {
                color: #1890ff;
                
                &:hover {
                  color: #40a9ff;
                }
              }
            }
          }
        }
        
        // 表单项现代化样式
        .form-item-modern {
          margin-bottom: 24px;
          
          /deep/ .el-form-item__label {
            font-weight: 500;
            color: #262626;
            line-height: 1.5;
          }
          
          .modern-input, .modern-select, .modern-textarea {
            /deep/ .el-input__inner, /deep/ .el-textarea__inner {
              border-radius: 8px;
              border: 1px solid #d9d9d9;
              transition: all 0.3s ease;
              
              &:hover {
                border-color: #40a9ff;
              }
              
              &:focus {
                border-color: #1890ff;
                box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
              }
            }
          }
        }
      }
    }
  }
}

// 签名内容卡片样式
.signature-content-card {
  .signature-input-item {
    .signature-input-wrapper {
      position: relative;
      display: flex;
      align-items: center;
      
      .signature-bracket {
        font-size: 18px;
        font-weight: bold;
        color: #1890ff;
        padding: 0 8px;
        
        &.left-bracket {
          margin-right: 8px;
        }
        
        &.right-bracket {
          margin-left: 8px;
        }
      }
      
      .signature-input {
        flex: 1;
      }
    }
    
    .signature-preview {
      margin-top: 12px;
      padding: 12px;
      background: #f5f5f5;
      border-radius: 6px;
      border-left: 4px solid #1890ff;
      
      .preview-label {
        font-weight: 500;
        color: #595959;
        margin-right: 8px;
      }
      
      .preview-text {
        color: #262626;
        font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
      }
    }
  }
}

// 签名来源卡片样式
.signature-source-card {
  .signature-source-form-item {
    .signature-source-selector {
      .source-radio-group {
        width: 100%;
        
        .source-option-card {
          border: 1px solid #e8eaec;
          border-radius: 8px;
          margin-bottom: 12px;
          padding: 16px;
          transition: all 0.3s ease;
          cursor: pointer;
          
          &:hover {
            border-color: #40a9ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
          }
          
          &.active {
            border-color: #1890ff;
            background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
          }
          
          .source-radio {
            width: 100%;
            margin: 0;
            
            /deep/ .el-radio__input {
              margin-top: 2px;
            }
            
            /deep/ .el-radio__label {
              width: calc(100% - 20px);
              padding-left: 8px;
            }
          }
          
          .source-content {
            .source-header {
              display: flex;
              align-items: center;
              margin-bottom: 8px;
              
              .source-icon {
                color: #1890ff;
                font-size: 16px;
                margin-right: 8px;
              }
              
              .source-title {
                font-weight: 600;
                color: #262626;
                font-size: 14px;
              }
            }
            
            .source-description {
              color: #8c8c8c;
              font-size: 12px;
              line-height: 1.5;
            }
          }
        }
      }
    }
  }
}

// 签名类型卡片样式
.signature-type-card {
  .signature-type-form-item {
    .signature-type-selector {
      .type-radio-group {
        width: 100%;
        
        .type-option-card {
          border: 1px solid #e8eaec;
          border-radius: 8px;
          margin-bottom: 12px;
          padding: 16px;
          transition: all 0.3s ease;
          cursor: pointer;
          
          &:hover {
            border-color: #40a9ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
          }
          
          &.active {
            border-color: #1890ff;
            background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);
          }
          
          .type-radio {
            width: 100%;
            margin: 0;
            
            /deep/ .el-radio__input {
              margin-top: 2px;
            }
            
            /deep/ .el-radio__label {
              width: calc(100% - 20px);
              padding-left: 8px;
            }
          }
          
          .type-content {
            .type-header {
              display: flex;
              align-items: center;
              margin-bottom: 8px;
              
              .type-icon {
                color: #1890ff;
                font-size: 16px;
                margin-right: 8px;
              }
              
              .type-title {
                font-weight: 600;
                color: #262626;
                font-size: 14px;
                margin-right: 8px;
              }
              
              .recommend-tag, .note-tag {
                margin-left: auto;
              }
            }
            
            .type-description {
              color: #8c8c8c;
              font-size: 12px;
              line-height: 1.5;
            }
          }
        }
      }
    }
  }
}

// 文件上传卡片样式
.file-upload-card {
  .upload-form-item {
    .upload-section {
      .modern-upload {
        margin-bottom: 16px;
      }

      .upload-tips {
        /deep/ .el-alert {
          border-radius: 6px;

          .el-alert__content {
            p {
              margin: 4px 0;
              font-size: 12px;
            }
          }
        }
      }
    }
  }
}

// 企业信息卡片样式
.company-info-card {
  .company-info-grid {
    .form-row {
      margin-bottom: 20px;

      .input-tip {
        margin-top: 8px;

        /deep/ .el-alert {
          border-radius: 6px;

          .el-alert__content {
            font-size: 12px;
          }
        }
      }
    }
  }
}

// 操作按钮样式
.action-buttons {
  margin: 32px 0;
  text-align: center;

  .button-group {
    display: inline-flex;
    gap: 16px;

    .el-button {
      padding: 12px 24px;
      border-radius: 8px;
      font-weight: 500;
      transition: all 0.3s ease;

      &.submit-btn {
        background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
        border: none;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
        }
      }

      &.save-btn {
        background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
        border: none;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
        }
      }

      &.cancel-btn {
        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }
      }
    }
  }
}

// 签名规范卡片样式
.signature-rules-card {
  .rules-content {
    /deep/ .el-alert {
      border-radius: 8px;

      .rules-list {
        .rule-item {
          margin-bottom: 12px;
          line-height: 1.6;

          &:last-child {
            margin-bottom: 0;
          }

          .highlight {
            color: #ff4d4f;
            font-weight: 600;
          }
        }
      }
    }
  }
}

// 现代化弹窗样式
.modern-dialog {
  /deep/ .el-dialog {
    border-radius: 12px;

    .el-dialog__header {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      color: white;
      border-radius: 12px 12px 0 0;
      padding: 20px 24px;

      .el-dialog__title {
        font-size: 18px;
        font-weight: 600;
      }

      .el-dialog__close {
        color: white;

        &:hover {
          color: #f0f0f0;
        }
      }
    }

    .el-dialog__body {
      padding: 24px;
    }
  }

  .dialog-content {
    .tips-section {
      margin-bottom: 24px;

      /deep/ .el-alert {
        border-radius: 8px;

        .tips-content {
          p {
            margin: 8px 0;

            .link {
              color: #1890ff;
              text-decoration: none;

              &:hover {
                text-decoration: underline;
              }
            }
          }
        }
      }
    }

    .examples-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 20px;

      .example-item {
        .example-card {
          border: 1px solid #e8eaec;
          border-radius: 8px;
          overflow: hidden;
          transition: all 0.3s ease;

          &:hover {
            border-color: #40a9ff;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
            transform: translateY(-2px);
          }

          .example-header {
            background: #f5f5f5;
            padding: 12px 16px;
            display: flex;
            align-items: center;

            i {
              color: #1890ff;
              margin-right: 8px;
              font-size: 16px;
            }

            .example-title {
              font-weight: 500;
              color: #262626;
              font-size: 14px;
            }
          }

          .example-image {
            padding: 16px;
            text-align: center;

            .demo-image {
              width: 100%;
              height: 200px;
              border-radius: 6px;
              cursor: pointer;
              transition: all 0.3s ease;

              &:hover {
                transform: scale(1.05);
              }
            }
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .modern-signature-page {
    .page-content {
      padding: 16px;

      .content-container {
        .modern-form {
          .form-card {
            margin-bottom: 16px;
          }
        }
      }
    }

    .action-buttons {
      .button-group {
        flex-direction: column;
        gap: 12px;

        .el-button {
          width: 100%;
        }
      }
    }
  }

  .modern-dialog {
    .dialog-content {
      .examples-grid {
        grid-template-columns: 1fr;
        gap: 16px;
      }
    }
  }
}
