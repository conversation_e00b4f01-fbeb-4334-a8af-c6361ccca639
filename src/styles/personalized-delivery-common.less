// 个性化发送页面通用样式
// 用于 PersonalizedDelivery.vue 等个性化发送相关页面

// 引入发送通用样式
@import './send-details-common.less';

// 个性化发送页面特有样式
.personalized-delivery-container {
  min-height: 100vh;
  background: #f5f7fa;
  
  // 页面头部样式
  .page-header {
    background: #fff;
    padding: 24px 32px;
    margin-bottom: 24px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .header-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      max-width: 1200px;
      margin: 0 auto;
      
      .header-left {
        .page-title {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          
          .page-icon {
            font-size: 24px;
            color: #1890ff;
            margin-right: 12px;
          }
          
          .title-text {
            font-size: 24px;
            font-weight: 600;
            color: #262626;
          }
        }
        
        .page-description {
          color: #8c8c8c;
          font-size: 14px;
          line-height: 1.5;
        }
      }
      
      .header-right {
        /deep/ .el-tag {
          // padding: 8px 16px;
          font-size: 12px;
          border-radius: 16px;
        }
      }
    }
  }
  
  // 主要内容区域
  .main-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 24px;
  }
}

// 标签字段样式
.label-field-section {
  .modern-input-tag {
    margin-bottom: 12px;
  }
  
  .field-tip {
    /deep/ .el-alert {
      border-radius: 6px;
      
      .el-alert__content {
        font-size: 12px;
      }
    }
  }
}

// 任务名称样式
.task-name-section {
  .modern-input {
    margin-bottom: 8px;
  }
  
  .task-name-tip {
    font-size: 12px;
    color: #8c8c8c;
    display: flex;
    align-items: center;
    
    i {
      margin-right: 4px;
    }
  }
}

// 文件预览样式
.file-preview-section {
  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .preview-title {
      display: flex;
      align-items: center;
      font-weight: 600;
      color: #262626;
      
      i {
        margin-right: 8px;
        color: #1890ff;
      }
    }
    
    .preview-stats {
      color: #8c8c8c;
      font-size: 12px;
    }
  }
  
  .preview-table-wrapper {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    
    .modern-table {
      /deep/ .el-table__header {
        background: #fafafa;
        
        th {
          background: #fafafa !important;
          color: #262626;
          font-weight: 600;
        }
      }
      
      /deep/ .el-table__body {
        .phone-number {
          color: #1890ff;
          font-weight: 600;
        }
        
        .table-content {
          color: #595959;
          word-break: break-all;
        }
      }
    }
  }
}

// 模板配置样式
.template-config-section {
  .template-preview {
    margin-bottom: 24px;
    
    .template-preview-header {
      margin-bottom: 8px;
      
      .preview-label {
        font-weight: 600;
        color: #262626;
      }
    }
    
    .template-preview-input {
      background: #f5f5f5;
      
      /deep/ .el-textarea__inner {
        background: #f5f5f5;
        border: 1px solid #e8eaec;
        
        &:focus {
          border-color: #40a9ff;
        }
      }
    }
  }
  
  .template-fields {
    .fields-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .fields-label {
        font-weight: 600;
        color: #262626;
      }
      
      .fields-tip {
        display: flex;
        align-items: center;
        color: #8c8c8c;
        font-size: 12px;
        
        i {
          margin-right: 4px;
        }
      }
    }
    
    .fields-selection {
      .modern-checkbox-group {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 12px;
        
        .checkbox-item {
          border: 1px solid #e8eaec;
          border-radius: 8px;
          padding: 12px;
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #40a9ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
          }
          
          .field-checkbox {
            width: 100%;
            margin: 0;
            
            /deep/ .el-checkbox__input {
              margin-top: 2px;
            }
            
            /deep/ .el-checkbox__label {
              width: calc(100% - 20px);
              padding-left: 8px;
            }
          }
          
          .checkbox-content {
            .checkbox-title {
              font-weight: 600;
              color: #262626;
              font-size: 14px;
              margin-bottom: 4px;
              display: block;
            }
            
            .checkbox-desc {
              color: #8c8c8c;
              font-size: 12px;
              line-height: 1.4;
              word-break: break-all;
            }
          }
        }
      }
    }
  }
}

// 号码去重样式
.deduplication-section {
  .modern-radio-group {
    width: 100%;
    
    .radio-option {
      border: 1px solid #e8eaec;
      border-radius: 8px;
      margin-bottom: 12px;
      padding: 16px;
      transition: all 0.3s ease;
      cursor: pointer;
      
      &:hover {
        border-color: #40a9ff;
        box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
      }
      
      .modern-radio {
        width: 100%;
        margin: 0;
        
        /deep/ .el-radio__input {
          margin-top: 2px;
        }
        
        /deep/ .el-radio__label {
          width: calc(100% - 20px);
          padding-left: 8px;
        }
      }
      
      .radio-content {
        .radio-header {
          display: flex;
          align-items: center;
          margin-bottom: 8px;
          
          .radio-icon {
            color: #1890ff;
            font-size: 16px;
            margin-right: 8px;
          }
          
          .radio-title {
            font-weight: 600;
            color: #262626;
            font-size: 14px;
          }
        }
        
        .radio-description {
          color: #8c8c8c;
          font-size: 12px;
          line-height: 1.5;
        }
      }
    }
  }
  
  .deduplication-warning {
    margin-top: 16px;
    
    /deep/ .el-alert {
      border-radius: 6px;
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .personalized-delivery-container {
    .page-header {
      padding: 20px 24px;
      
      .header-content {
        padding: 0 16px;
      }
    }
    
    .main-content {
      padding: 0 16px;
    }
  }
}

@media (max-width: 768px) {
  .personalized-delivery-container {
    .page-header {
      padding: 16px 20px;
      
      .header-content {
        flex-direction: column;
        align-items: flex-start;
        
        .header-right {
          margin-top: 12px;
        }
      }
    }
    
    .main-content {
      padding: 0 12px;
    }
  }
  
  .template-config-section {
    .template-fields {
      .fields-selection {
        .modern-checkbox-group {
          grid-template-columns: 1fr;
        }
      }
    }
  }
}
