/* 模板管理通用样式（基于tempv1.vue设计） */

/* 简约页面基础样式 */
.simple-template-page,
.simple-signature-page {
  min-height: 100vh;
  background: #fafafa;
  padding: 0;
}

/* 简约页面头部 */
.page-header {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 20px 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #333;
}

.page-subtitle {
  font-size: 14px;
  color: #666;
}

/* 内容区域 */
.page-content {
  margin: 0 auto;
  padding: 20px 24px;
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.enhanced-layout {
  gap: 0;

  .table-section {
    margin-top: 0;
    border-radius: 0 0 8px 8px;
    border-top: none;

    .table-header {
      background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
      border-bottom: 2px solid #e8e8e8;
      padding: 20px;

      .table-title {
        color: #2c3e50;
        font-size: 18px;
        font-weight: 600;
      }
    }
  }
}

/* 简约提醒区域 */
.notice-section {
  background: white;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.notice-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.notice-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notice-item {
  line-height: 1.6;
  font-size: 14px;
  color: #666;
}

.notice-label {
  font-weight: 500;
  color: #333;
}

/* 固定工具栏 */
.fixed-toolbar {
  position: sticky;
  top: 0;
  z-index: 100;
  background: white;
  border-bottom: 1px solid #e8e8e8;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 20px;
}

.toolbar-container {
  padding: 16px 20px;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.action-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.action-btn {
  border-radius: 6px;
  font-size: 14px;
  padding: 10px 20px;
  border: 1px solid #d9d9d9;
  background: white;
  color: #333;
  transition: all 0.3s ease;
  font-weight: 500;

  &:hover {
    border-color: #409eff;
    color: #409eff;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
  }

  &.primary {
    background: linear-gradient(135deg, #409eff 0%, #66b1ff 100%);
    border-color: #409eff;
    color: white;

    &:hover {
      background: linear-gradient(135deg, #66b1ff 0%, #85c1ff 100%);
      border-color: #66b1ff;
      color: white;
    }
  }
}

.stats-info {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #666;
  font-size: 14px;
}

.stats-text {
  color: #666;
  font-size: 14px;
}

.search-section {
  border-top: 1px solid #f0f0f0;
  padding-top: 16px;
}

.advanced-search-form {
  margin: 0;

  .search-row {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
  }

  .search-item {
    margin-bottom: 0;

    /deep/ .el-form-item__label {
      color: #333;
      font-weight: 500;
      font-size: 14px;
    }
  }

  .search-buttons {
    margin-bottom: 0;
    margin-left: auto;
  }
}

.search-input {
  width: 200px;
}

.search-select {
  width: 140px;
}

.search-btn {
  border-radius: 4px;
  font-size: 14px;
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  background: white;
  color: #333;
  transition: all 0.2s ease;

  &:hover {
    border-color: #409eff;
    color: #409eff;
  }

  &.primary {
    background: #409eff;
    border-color: #409eff;
    color: white;

    &:hover {
      background: #66b1ff;
      border-color: #66b1ff;
    }
  }
}

/* 简约工具栏（保留兼容性） */
.toolbar-section {
  background: white;
  padding: 16px 20px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.simple-btn {
  border-radius: 4px;
  font-size: 14px;
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  background: white;
  color: #333;
  transition: all 0.2s ease;
}

.simple-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.simple-btn.primary {
  background: #409eff;
  border-color: #409eff;
  color: white;
}

.simple-btn.primary:hover {
  background: #66b1ff;
  border-color: #66b1ff;
}

.search-input {
  width: 250px;
}

.status-select {
  width: 140px;
}

.search-form {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  flex-wrap: wrap;

  /deep/ .el-form-item {
    margin-bottom: 0;
  }
}

/* 简约表格区域 */
.table-section {
  background: white;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  overflow: hidden;
}

.table-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.table-count {
  font-size: 14px;
  color: #666;
}

.table-container {
  overflow-x: auto;
}

.simple-table,
.enhanced-table {
  width: 100%;
}

.simple-table /deep/ .el-table__header th,
.enhanced-table /deep/ .el-table__header th {
  background: #fafafa;
  color: #333;
  font-weight: 500;
  border-bottom: 1px solid #e8e8e8;
  font-size: 14px;
  padding: 12px 0;
}

.simple-table /deep/ .el-table__row:hover,
.enhanced-table /deep/ .el-table__row:hover {
  background: #f5f7fa;
}

.enhanced-table /deep/ .el-table__row {
  transition: background-color 0.2s ease;
}

.enhanced-table /deep/ .el-table__body td {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.enhanced-table /deep/ .el-table__empty-text {
  color: #999;
  font-size: 14px;
}

.enhanced-table /deep/ .el-table--striped .el-table__body tr.el-table__row--striped td {
  background: #fafafa;
}

.enhanced-table /deep/ .el-table__fixed-right {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
}

/* 表格内容样式 */
.content-cell {
  max-width: 400px;
}

.content-text {
  line-height: 1.5;
  word-break: break-word;
  margin-bottom: 8px;
}

.reject-reason {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
}

.no-variables {
  color: #999;
  font-size: 12px;
}

.variables-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.variable-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.variable-name {
  color: #409eff;
  font-weight: 500;
}

.variable-type {
  color: #666;
}

/* 表格操作按钮 */
.table-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  flex-wrap: wrap;
}

.action-btn-small {
  padding: 4px 8px;
  font-size: 12px;
  border-radius: 4px;
  border: 1px solid transparent;
  transition: all 0.2s ease;

  &.edit {
    color: #E6A23C;

    &:hover {
      color: #f7ba2a;
      background: rgba(230, 162, 60, 0.1);
      border-color: rgba(230, 162, 60, 0.2);
    }
  }

  &.info {
    color: #409eff;

    &:hover {
      color: #66b1ff;
      background: rgba(64, 158, 255, 0.1);
      border-color: rgba(64, 158, 255, 0.2);
    }
  }

  &.delete {
    color: #f56c6c;

    &:hover {
      color: #f78989;
      background: rgba(245, 108, 108, 0.1);
      border-color: rgba(245, 108, 108, 0.2);
    }
  }
}

/* 操作链接（保留兼容性） */
.action-link {
  color: #409eff;
  margin-right: 8px;
  font-size: 14px;
}

.action-link:hover {
  color: #66b1ff;
}

.action-link.danger {
  color: #f56c6c;
}

.action-link.danger:hover {
  color: #f78989;
}

/* 简约分页 */
.pagination-section {
  padding: 16px 20px;
  border-top: 1px solid #e8e8e8;
  display: flex;
  justify-content: center;
}

.simple-pagination {
  /deep/ .el-pagination__total {
    color: #666;
  }

  /deep/ .el-pager li {
    border-radius: 4px;
    margin: 0 2px;
  }

  /deep/ .el-pager li:hover {
    background: #409eff;
    color: white;
  }

  /deep/ .el-pager li.active {
    background: #409eff;
    color: white;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .fixed-toolbar {
    position: relative;
    top: auto;
  }

  .toolbar-container {
    padding: 12px 16px;
  }

  .action-section {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .action-buttons {
    justify-content: center;
    gap: 8px;
  }

  .action-btn {
    flex: 1;
    min-width: 0;
    padding: 8px 12px;
    font-size: 13px;
  }

  .stats-info {
    justify-content: center;
    font-size: 13px;
  }

  .search-section {
    padding-top: 12px;
  }

  .advanced-search-form {
    .search-row {
      flex-direction: column;
      gap: 12px;
    }

    .search-item {
      width: 100%;

      /deep/ .el-form-item__content {
        width: 100%;
      }
    }

    .search-buttons {
      margin-left: 0;
      width: 100%;

      .search-btn {
        flex: 1;
      }
    }
  }

  .search-input,
  .search-select {
    width: 100%;
  }

  .toolbar-section {
    flex-direction: column;
    align-items: stretch;
  }

  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }

  .search-form {
    flex-direction: column;
    gap: 8px;

    /deep/ .el-form-item {
      width: 100%;

      .el-form-item__content {
        width: 100%;
      }
    }
  }
}
