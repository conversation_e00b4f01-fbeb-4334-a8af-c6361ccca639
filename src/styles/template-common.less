/* 模板管理通用样式（基于tempv1.vue设计） */

/* 简约页面基础样式 */
.simple-template-page,
.simple-signature-page {
  min-height: 100vh;
  background: #fafafa;
  padding: 0;
}

/* 简约页面头部 */
.page-header {
  background: white;
  border-bottom: 1px solid #e8e8e8;
  padding: 20px 24px;
}

.header-content {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-title {
  margin: 0;
  font-size: 20px;
  font-weight: 500;
  color: #333;
}

.page-subtitle {
  font-size: 14px;
  color: #666;
}

/* 内容区域 */
.page-content {
  margin: 0 auto;
  padding: 20px 24px;
}

.content-container {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* 简约提醒区域 */
.notice-section {
  background: white;
  padding: 20px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
}

.notice-title {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.notice-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.notice-item {
  line-height: 1.6;
  font-size: 14px;
  color: #666;
}

.notice-label {
  font-weight: 500;
  color: #333;
}

/* 简约工具栏 */
.toolbar-section {
  background: white;
  padding: 16px 20px;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 16px;
}

.toolbar-left {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

.simple-btn {
  border-radius: 4px;
  font-size: 14px;
  padding: 8px 16px;
  border: 1px solid #d9d9d9;
  background: white;
  color: #333;
  transition: all 0.2s ease;
}

.simple-btn:hover {
  border-color: #409eff;
  color: #409eff;
}

.simple-btn.primary {
  background: #409eff;
  border-color: #409eff;
  color: white;
}

.simple-btn.primary:hover {
  background: #66b1ff;
  border-color: #66b1ff;
}

.search-input {
  width: 250px;
}

.status-select {
  width: 140px;
}

.search-form {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  
  /deep/ .el-form-item {
    margin-bottom: 0;
  }
}

/* 简约表格区域 */
.table-section {
  background: white;
  border-radius: 4px;
  border: 1px solid #e8e8e8;
  overflow: hidden;
}

.table-header {
  padding: 16px 20px;
  border-bottom: 1px solid #e8e8e8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.table-title {
  margin: 0;
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.table-count {
  font-size: 14px;
  color: #666;
}

.table-container {
  overflow-x: auto;
}

.simple-table {
  width: 100%;
}

.simple-table /deep/ .el-table__header th {
  background: #fafafa;
  color: #333;
  font-weight: 500;
  border-bottom: 1px solid #e8e8e8;
}

.simple-table /deep/ .el-table__row:hover {
  background: #f5f5f5;
}

/* 表格内容样式 */
.content-cell {
  max-width: 400px;
}

.content-text {
  line-height: 1.5;
  word-break: break-word;
  margin-bottom: 8px;
}

.reject-reason {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
}

.no-variables {
  color: #999;
  font-size: 12px;
}

.variables-list {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.variable-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
}

.variable-name {
  color: #409eff;
  font-weight: 500;
}

.variable-type {
  color: #666;
}

/* 操作链接 */
.action-link {
  color: #409eff;
  margin-right: 8px;
  font-size: 14px;
}

.action-link:hover {
  color: #66b1ff;
}

.action-link.danger {
  color: #f56c6c;
}

.action-link.danger:hover {
  color: #f78989;
}

/* 简约分页 */
.pagination-section {
  padding: 16px 20px;
  border-top: 1px solid #e8e8e8;
  display: flex;
  justify-content: center;
}

.simple-pagination {
  /deep/ .el-pagination__total {
    color: #666;
  }

  /deep/ .el-pager li {
    border-radius: 4px;
    margin: 0 2px;
  }

  /deep/ .el-pager li:hover {
    background: #409eff;
    color: white;
  }

  /deep/ .el-pager li.active {
    background: #409eff;
    color: white;
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .toolbar-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .toolbar-left,
  .toolbar-right {
    justify-content: center;
  }
  
  .search-input {
    width: 100%;
  }
  
  .status-select {
    width: 100%;
  }
  
  .search-form {
    flex-direction: column;
    gap: 8px;
  }
}
