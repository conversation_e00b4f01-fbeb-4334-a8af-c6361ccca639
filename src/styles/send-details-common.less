// SMS发送短信页面通用样式
// 用于 sendDetails.vue 等发送相关页面

// 引入签名通用样式
@import './signature-common.less';

// 发送页面特有样式
.send-form-layout {
  .form-section {
    padding-right: 40px;
  }

  .preview-section {
    border-left: 1px solid #e8eaec;
    padding-left: 24px;
  }
}

// 短信配置卡片样式
.sms-config-card {
  // 短信类型选择器
  .sms-type-selector {
    .variable-tip {
      margin-top: 12px;
      
      /deep/ .el-alert {
        border-radius: 6px;
        
        .el-alert__content {
          font-size: 12px;
        }
      }
    }
  }

  // 模板选择器
  .template-selector {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .select-template-btn {
      background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
      border: none;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
      }
    }
    
    .template-tip {
      color: #8c8c8c;
      font-size: 12px;
    }
  }

  // 签名选择器
  .signature-selector {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .signature-select {
      flex: 1;
    }
    
    .add-signature-btn {
      background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
      border: none;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(82, 196, 26, 0.4);
      }
    }
  }

  // 短信内容编辑器
  .sms-content-editor {
    .content-textarea {
      margin-bottom: 16px;
    }
    
    .content-options {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 16px;
      
      .reject-option {
        color: #1890ff;
      }
      
      .content-actions {
        display: flex;
        gap: 8px;
      }
    }
    
    .content-tips {
      /deep/ .el-alert {
        border-radius: 6px;
        
        .tip-item {
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          .text-danger {
            color: #ff4d4f;
            font-weight: 600;
          }
        }
      }
    }
  }

  // 模板内容编辑器
  .template-content-editor {
    .template-content-wrapper {
      position: relative;
      
      .clear-content-btn {
        position: absolute;
        top: 8px;
        right: 8px;
        z-index: 10;
      }
      
      .template-textarea {
        background-color: #f5f5f5;
      }
    }
    
    .template-content-info {
      margin-top: 16px;
      
      .content-stats {
        /deep/ .el-alert {
          border-radius: 6px;
          
          .stats-item {
            margin-bottom: 8px;
            
            &:last-child {
              margin-bottom: 0;
            }
            
            .text-danger {
              color: #ff4d4f;
              font-weight: 600;
            }
            
            .download-variables-btn {
              color: #1890ff;
              
              &:hover {
                color: #40a9ff;
              }
            }
          }
        }
      }
    }
  }

  // 发送时间选择
  .send-timing-section {
    .timing-radio-group {
      width: 100%;
      
      .timing-option {
        border: 1px solid #e8eaec;
        border-radius: 8px;
        margin-bottom: 12px;
        padding: 16px;
        transition: all 0.3s ease;
        cursor: pointer;
        
        &:hover {
          border-color: #40a9ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
        }
        
        .timing-radio {
          width: 100%;
          margin: 0;
          
          /deep/ .el-radio__input {
            margin-top: 2px;
          }
          
          /deep/ .el-radio__label {
            width: calc(100% - 20px);
            padding-left: 8px;
          }
        }
        
        .timing-content {
          .timing-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            
            .timing-icon {
              color: #1890ff;
              font-size: 16px;
              margin-right: 8px;
            }
            
            .timing-title {
              font-weight: 600;
              color: #262626;
              font-size: 14px;
            }
          }
          
          .timing-description {
            color: #8c8c8c;
            font-size: 12px;
            line-height: 1.5;
          }
        }
      }
    }
    
    .timing-picker-section {
      margin-top: 16px;
      padding: 16px;
      background: #f5f5f5;
      border-radius: 8px;
      
      .timing-picker-wrapper {
        display: flex;
        align-items: center;
        
        .timing-picker-label {
          margin-right: 12px;
          font-weight: 500;
          color: #262626;
        }
        
        .modern-date-picker {
          flex: 1;
        }
      }
    }
  }

  // 发送方式选择
  .send-method-section {
    .method-radio-group {
      width: 100%;
      
      .method-option {
        border: 1px solid #e8eaec;
        border-radius: 8px;
        margin-bottom: 12px;
        padding: 16px;
        transition: all 0.3s ease;
        cursor: pointer;
        
        &:hover {
          border-color: #40a9ff;
          box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
        }
        
        .method-radio {
          width: 100%;
          margin: 0;
          
          /deep/ .el-radio__input {
            margin-top: 2px;
          }
          
          /deep/ .el-radio__label {
            width: calc(100% - 20px);
            padding-left: 8px;
          }
        }
        
        .method-content {
          .method-header {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            
            .method-icon {
              color: #1890ff;
              font-size: 16px;
              margin-right: 8px;
            }
            
            .method-title {
              font-weight: 600;
              color: #262626;
              font-size: 14px;
            }
          }
          
          .method-description {
            color: #8c8c8c;
            font-size: 12px;
            line-height: 1.5;
          }
        }
      }
    }
  }

  // 文件上传区域
  .file-upload-section {
    .file-upload-wrapper {
      .modern-upload-drag {
        width: 100%;
        
        /deep/ .el-upload-dragger {
          width: 100%;
          height: 180px;
          border-radius: 8px;
          border: 2px dashed #d9d9d9;
          transition: all 0.3s ease;
          
          &:hover {
            border-color: #40a9ff;
          }
        }
        
        .upload-icon {
          font-size: 48px;
          color: #8c8c8c;
          margin-bottom: 16px;
        }
        
        .upload-text {
          color: #606266;
          font-size: 14px;
          margin-bottom: 8px;
          
          em {
            color: #1890ff;
            font-style: normal;
          }
        }
        
        .upload-hint {
          color: #8c8c8c;
          font-size: 12px;
        }
      }
      
      .upload-result {
        margin-top: 16px;
      }
    }
    
    .upload-tips-section {
      margin-top: 16px;
      
      /deep/ .el-alert {
        border-radius: 6px;
        
        .tip-item {
          margin-bottom: 8px;
          
          &:last-child {
            margin-bottom: 0;
          }
          
          &.text-danger {
            color: #ff4d4f;
            font-weight: 600;
          }
        }
      }
    }
  }

  // 手机号码输入区域
  .mobile-input-section {
    .mobile-input-wrapper {
      .mobile-textarea-container {
        position: relative;
        
        .mobile-textarea {
          padding-bottom: 30px;
        }
        
        .mobile-count {
          position: absolute;
          bottom: 8px;
          right: 12px;
          font-size: 12px;
          color: #8c8c8c;
          
          .count-danger {
            color: #ff4d4f;
            font-weight: 600;
          }
        }
      }
      
      .mobile-actions {
        margin-top: 12px;
        
        .extract-btn {
          background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
          border: none;
          
          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.4);
          }
        }
      }
    }
    
    .mobile-tips {
      margin-top: 12px;
      
      /deep/ .el-alert {
        border-radius: 6px;
        
        .text-danger {
          color: #ff4d4f;
          font-weight: 600;
        }
      }
    }
  }

  .mobile-input-tips {
    margin-bottom: 20px;

    /deep/ .el-alert {
      border-radius: 6px;
    }
  }
}

// 预览区域样式
.preview-card {
  // 移除固定定位，让预览区域跟随页面滚动

  .card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;

    .preview-actions {
      .refresh-btn {
        color: #1890ff;

        &:hover {
          color: #40a9ff;
        }
      }
    }
  }

  .phone-preview {
    // 手机外观容器
    .phone-mockup {
      display: flex;
      justify-content: center;
      margin-bottom: 24px;

      .phone-frame {
        position: relative;
        width: 280px;
        height: 560px;
        background: linear-gradient(145deg, #2c2c2c, #1a1a1a);
        border-radius: 30px;
        padding: 20px 15px;
        box-shadow:
          0 20px 40px rgba(0, 0, 0, 0.3),
          inset 0 2px 4px rgba(255, 255, 255, 0.1);

        .phone-screen {
          width: 100%;
          height: 100%;
          background: #000;
          border-radius: 20px;
          overflow: hidden;
          position: relative;

          // 状态栏
          .status-bar {
            height: 24px;
            background: #000;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 16px;
            font-size: 12px;
            color: #fff;

            .status-left {
              .time {
                font-weight: 600;
              }
            }

            .status-right {
              display: flex;
              align-items: center;
              gap: 4px;

              .signal-icon,
              .wifi-icon {
                width: 12px;
                height: 8px;
                background: #fff;
                border-radius: 1px;

                &.signal-icon {
                  background: linear-gradient(to right,
                    #fff 0%, #fff 25%,
                    #fff 25%, #fff 50%,
                    #fff 50%, #fff 75%,
                    #fff 75%, #fff 100%);
                }

                &.wifi-icon {
                  border-radius: 50% 50% 0 0;
                }
              }

              .battery {
                font-size: 11px;
              }
            }
          }

          // 短信应用界面
          .sms-app {
            height: calc(100% - 24px);
            background: #f5f5f5;
            display: flex;
            flex-direction: column;

            .sms-header {
              background: #fff;
              padding: 12px 16px;
              border-bottom: 1px solid #e8e8e8;

              .contact-info {
                display: flex;
                align-items: center;

                .contact-avatar {
                  width: 40px;
                  height: 40px;
                  border-radius: 50%;
                  background: #1890ff;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  margin-right: 12px;

                  i {
                    color: #fff;
                    font-size: 18px;
                  }
                }

                .contact-details {
                  .contact-name {
                    font-size: 14px;
                    font-weight: 600;
                    color: #262626;
                    margin-bottom: 2px;
                  }

                  .contact-number {
                    font-size: 12px;
                    color: #8c8c8c;
                  }
                }
              }
            }

            // 短信对话区域
            .sms-conversation {
              flex: 1;
              padding: 16px;
              overflow-y: auto;

              .message-container {
                .message-bubble {
                  max-width: 80%;
                  margin-bottom: 12px;

                  &.received {
                    margin-left: 0;
                    margin-right: auto;

                    .message-content {
                      background: #fff;
                      border-radius: 18px 18px 18px 4px;
                      padding: 12px 16px;
                      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);

                      .placeholder-text {
                        color: #bfbfbf;
                        font-style: italic;
                        font-size: 13px;
                      }

                      .sms-text {
                        color: #262626;
                        font-size: 14px;
                        line-height: 1.5;
                        word-wrap: break-word;
                        word-break: break-all;
                        // white-space: pre-wrap;
                      }
                    }

                    .message-time {
                      font-size: 11px;
                      color: #8c8c8c;
                      margin-top: 4px;
                      margin-left: 16px;
                    }
                  }
                }
              }
            }
          }
        }

        // 手机按钮
        .phone-button {
          position: absolute;
          bottom: 8px;
          left: 50%;
          transform: translateX(-50%);
          width: 40px;
          height: 4px;
          background: #666;
          border-radius: 2px;
        }
      }
    }

    // 预览统计信息
    .preview-stats {
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 16px;
        margin-bottom: 20px;

        .stat-item {
          text-align: center;
          padding: 16px;
          background: #fafafa;
          border-radius: 8px;
          border: 1px solid #f0f0f0;
          transition: all 0.3s ease;

          &:hover {
            border-color: #d9d9d9;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          }

          .stat-label {
            font-size: 12px;
            color: #8c8c8c;
            margin-bottom: 8px;
          }

          .stat-value {
            display: flex;
            align-items: baseline;
            justify-content: center;

            .stat-number {
              font-size: 20px;
              font-weight: 600;
              color: #262626;

              &.over-limit {
                color: #ff4d4f;
              }
            }

            .stat-unit {
              font-size: 12px;
              color: #8c8c8c;
              margin-left: 4px;
            }
          }
        }
      }

      .stats-note {
        /deep/ .el-alert {
          border-radius: 8px;

          .note-content {
            .note-item {
              margin-bottom: 6px;
              font-size: 12px;
              line-height: 1.5;

              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
    }
  }
}

// 规则卡片样式
.rules-card, .content-rules-card {
  margin-top: 24px;

  .rules-content, .content-rules {
    /deep/ .el-alert {
      border-radius: 8px;

      .rules-list {
        .rule-item {
          margin-bottom: 12px;
          line-height: 1.6;
          font-size: 13px;

          &:last-child {
            margin-bottom: 0;
          }

          .highlight {
            color: #ff4d4f;
            font-weight: 600;
          }
        }
      }
    }
  }
}

// 响应式设计
@media (max-width: 1200px) {
  .send-form-layout {
    .form-section {
      padding-right: 20px;
    }

    .preview-section {
      padding-left: 16px;
    }
  }
}

@media (max-width: 768px) {
  .send-form-layout {
    .form-section {
      padding-right: 0;
    }

    .preview-section {
      border-left: none;
      border-top: 1px solid #e8eaec;
      padding-left: 0;
      padding-top: 24px;
      margin-top: 24px;
    }
  }

  .preview-card {
    position: static;
  }
}
