<template>
  <div class="header">
    <!-- 折叠按钮 -->
    <div class="collapse-btn">
      <div style="display: flex; margin: 10px">
        <i class="el-icon-menu" @click="collapseChage" style="margin-right: 20px; padding-top: 10px"></i>
        <!-- <img  class="logo_zt" src="../../assets/images/logo23.png"  alt="助通科技"> -->
        <span v-if="hostflag">
          <img style="cursor: pointer" @click="handleHome" class="logo_zt" src="../../assets/images/logo23.png"
            alt="助通科技" />
        </span>
        <span v-else class="logo_zt">
          <img style="cursor: pointer" v-if="img" :src="img" @click="handleHome" alt="" width="150px" height="45px" />
        </span>
        <div class="logo">
          <span class="version">{{ version }}</span>
        </div>
        <!-- <span v-if="img">
                    <img v-if="hostflag" :src="img" alt="助通科技" width="150px" height="45px">
                </span>
                <span v-else class="logo_zt">
                    <img  class="logo_zt" src="../../assets/images/logo23.png" alt="助通科技">
                </span> -->
        <!-- <img  class="logo_zt" :src="img" alt="助通科技" width="200px"> -->
        <!-- <img v-if="hostflag " class="logo_zt" src="../../assets/images/logo23.png" alt="助通科技"> -->
        <!-- <img v-if="hostnames != 'partner.zthysms.com'" class="img" src="https://images.zthysms.com/static/images/100logo.png" alt="助通科技"> -->
      </div>
    </div>

    <!-- <div class="logo">助通科技</div> -->
    <div class="header-right">
      <div class="header-user-con">
        <!-- <div v-if="Announcementlist.length">
          <div class="announcement" @click="handleAnnouncement">
            <i class="iconfont icon-gonggao"></i>
            <div class="announcement-con">
              <div class="announcement-content">
               {{Announcementlist[0].messageContent}}
              </div>
            </div>
          </div>
        </div> -->
        <!-- 全屏显示 -->
        <!-- <div
          v-if="isShowBar != 0"
          class="btn-fullscreen"
          @click="handleFullScreen"
        >
          <el-tooltip
            effect="dark"
            :content="fullscreen ? `取消全屏` : `全屏`"
            placement="bottom"
          >
            <i class="el-icon-rank"></i>
          </el-tooltip>
        </div> -->
        <div class="btn-bell">
          <el-tooltip effect="dark" content="下载" placement="bottom">
            <router-link to="/FileExport">
              <i class="iconfont icon-download-1-copy" style="color: #fff;font-size: 20px;"></i>
            </router-link>
          </el-tooltip>

          <!-- <span class="btn-bell-badge" v-if="message"></span> -->
        </div>
        <div class="btn-bell">
          <el-tooltip effect="dark" content="开发文档" placement="bottom">
            <a href="https://doc.zthysms.com" target="_blank">
              <span class="el-dropdown-link">
                <i class="icon iconfont icon-dashujukeshihuaico-" style="font-size: 20px"></i>
              </span>
            </a>
          </el-tooltip>

          <!-- <span class="btn-bell-badge" v-if="message"></span> -->
        </div>
        <!-- 消息中心 -->
        <div class="btn-bell">
          <el-tooltip effect="dark" content="消息中心" placement="bottom">
            <router-link to="/showMsg">
              <i class="el-icon-bell" style="font-size: 20px"></i>
            </router-link>
          </el-tooltip>
          <span class="btn-bell-badge" v-if="message != 0"></span>
        </div>

        <!-- 用户头像 -->
        <!-- <div class="user-avator"><img :src="portraitUrl"></div> -->
        <!-- 用户名下拉菜单 -->
        <el-dropdown class="user-name" trigger="click" @command="handleCommand">
          <span class="el-dropdown-link">
            {{ name }} <i class="el-icon-caret-bottom"></i>
          </span>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item divided command="information">个人信息</el-dropdown-item>
            <el-dropdown-item divided command="loginout">退出登录</el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown>
      </div>
    </div>
    <el-dialog title="公告" :visible.sync="showPopup" custom-class="announcement-dialog" width="45%" center
      :close-on-click-modal="false" :close-on-press-escape="false">
      <div class="dialog-content">
        <el-carousel height="550px" :interval="5000" arrow="always">
          <el-carousel-item>
            <div v-html="htmlContent2" class="word-content"></div>
          </el-carousel-item>
          <el-carousel-item>
            <div v-html="htmlContent" class="word-content"></div>
          </el-carousel-item>
        </el-carousel>
        
        <!-- <div v-else v-html="htmlContent2" style="font-size: 16px; line-height: 1.5;color:#ec6e06"></div> -->
        <!-- <i class="el-icon-warning" style="font-size: 20px; margin-right: 5px; color: #e6a23c"></i> -->
        <!-- <span style="font-size: 17px; line-height: 1.5">系统检测到您的实名资料有错漏，应运营商政策要求，请移步补充，避免影响后续短信业务的使用。感谢配合。</span> -->
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button style="font-size: 17px" type="primary" @click="submitknow">我知道了</el-button>
        <!-- <el-button v-else style="font-size: 17px" type="primary" @click="handleNextPage">下一页</el-button> -->
      </span>
    </el-dialog>
    <!-- <el-dialog
      title="提示"
      :visible.sync="showflag"
      width="34%"
      center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="dialog-content">
        <i
          class="el-icon-warning"
          style="font-size: 20px; margin-right: 5px; color: #e6a23c"
        ></i>
        <span style="font-size: 17px; line-height: 1.5"
          >系统检测到您的实名资料有错漏，应运营商政策要求，请移步补充，避免影响后续短信业务的使用。感谢配合。</span
        >
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button style="font-size: 17px" @click="handelClose"
          >我知道了</el-button
        >
        <el-button
          style="font-size: 17px"
          type="primary"
          @click="changeUserInfo"
          >立即完善</el-button
        >
      </span>
    </el-dialog> -->
  </div>
</template>
<script>
import bus from "../common/bus";
import { mapState, mapMutations, mapActions } from "vuex";
import { version } from "../../utils/version";
import mammoth from 'mammoth';
import moment from "moment";
// import document from '@/assets/announcement.docx';
export default {
  data() {
    return {
      hostnames: window.location.hostname,
      nameTile: ["partner.zthysms.com", "partner.yameidu.com"],
      hostflag: true,
      collapse: true,
      fullscreen: false,
      name: "",
      hostname: "",
      portraitUrl: "",
      message: 0,
      roleId: "",
      isShowBar: 0,
      version: version,
      img: "", //个性化logo
      showflag: false,
      showPopup: true,
      Announcementlist: [],
      htmlContent: "",
      htmlContent2: "",
      nextPage: 1,
    };
  },

  created() {
    this.getDomain();
    this.checkPopupStatus()
    this.nameTile.forEach((item) => {
      // console.log(item);
      if (this.hostnames == item) {
        // console.log(1);
        this.hostflag = false;
      }
    });
    // this.message = localStorage.getItem('count')
    this.$api.post(this.API.cpus + "consumerclientmessage/page", {}, (res) => {
      this.message = res.map.unread;
    });

    this.$api.get(this.API.contact + "contact/open", {}, (res) => {
      // this.message = res.map.unread
    });
    this.versions();
    this.$api.get(
      this.API.cpus + "consumerclientinfo/getClientInfo",
      null,
      (res) => {
        // console.log(res,'resss');
        // this.$api.post(this.API.cpus+'personalizationSettings/getSttingsByUserName',{
        // userName:res.data.username
        // },ress=>{
        //     if(ress.data)
        //     this.img = ress.data.logo
        // })
        this.name = res.data.username;
        this.roleId = res.data.roleId;
        // if (res.data.roleId == 14) {
        //   //在缓存那存储时间
        //   const storedItem = localStorage.getItem("authentication");
        //   if (storedItem) {
        //     //判断是否有缓存
        //     //将缓存转化为对象
        //     const storedObj = JSON.parse(storedItem);
        //     //获取缓存的过期时间
        //     const expirationTime = storedObj.expiration;
        //     // 当前时间
        //     const now = new Date().getTime();
        //     //判断是否过期
        //     if (now < expirationTime) {
        //       //未过期，不显示提示
        //       this.showflag = false;
        //     } else {
        //       //过期清除缓存重新弹出提示框
        //       localStorage.removeItem("authentication");
        //       this.getAstatus();
        //     }
        //   } else {
        //     //没有缓存，弹出提示框
        //     this.getAstatus();
        //   }
        // }
        // console.log(this.roleId);
        sessionStorage.setItem("isDateState", res.data.isDateState);
        localStorage.setItem(
          "balanceList",
          JSON.stringify(res.data.balanceList)
        );
        localStorage.setItem("userInfo", JSON.stringify(res.data));
        // this.portraitUrl = 'http://'+res.data.portraitUrl
        //存在vuex里
        let USER_NAME = res.data.username;
        let USER_ID = res.data.userId;
        let ROLE_ID = res.data.roleId;
        let SET_PHONE = res.data.phone;
        let SET_CREATETIME = res.data.createLocalDateTime;
        let SET_ROLEDESC = res.data.roleDesc;
        let SET_PASSWORD = res.data.password;
        let SET_COMPNAME = res.data.compName;
        let SET_CUSTOM = res.data.custom;
        let SET_WARINGREMIND = res.data.warningRemind;
        let SET_ISDATESTATE = res.data.isDateState;
        let param = {
          userName: USER_NAME,
          userId: USER_ID,
          roleId: ROLE_ID,
          phone: SET_PHONE,
          createLocalDateTime: SET_CREATETIME,
          roleDesc: SET_ROLEDESC,
          password: SET_PASSWORD,
          compName: SET_COMPNAME,
          custom: SET_CUSTOM,
          warningRemind: SET_WARINGREMIND,
          isDateState: SET_ISDATESTATE,
        };
        this.saveInfo(param);
        // this.saveInfo(param);
        let businessTypes = res.data.businessTypes; //是否开通空号检测功能
        let a = businessTypes.split(",");

        // if(window.location.hostname=='partner-test.zthysms.com'){
        //     this.$api.post(this.API.cpus+'personalizationSettings/getSttingsByReminder',{domainName:'partner-test.zthysms.com',userName:USER_NAME},res=>{
        //         this.partnerImg=res.data.logo
        //     })
        // }
        this.$api.get(
          this.API.cpus + "personalizationSettings/getPersonalSettings",
          {},
          (res) => {
            this.img = res.data.logo;
          }
        );
      }
    );
  },
  computed: {
    ...mapState({
      userName: (state) => state.userName,
    }),
    // username(){
    //     let username = localStorage.getItem('ms_username');
    //     return username ? username : this.name;
    // }
  },
  methods: {
    ...mapActions([
      //比如'movies/getHotMovies
      "saveInfo",
      "saveImg",
      "saveUrl",
      "saveMenu",
      "saveShow",
      "saveUrls",
    ]),
    async loadWordDocument() {
      try {
        const options = {
          styleMap: [
            "p[style-name='Heading 1'] => h1:fresh",
            "p[style-name='Heading 2'] => h2:fresh",
            "p[style-name='Heading 3'] => h3:fresh",
            "p[style-name='Normal'] => p:fresh",
            "p[style-name='Title'] => p.title:fresh",
            "p[style-name='Subtitle'] => p.subtitle:fresh",
            "r[style-name='Strong'] => strong",
            "r[style-name='Emphasis'] => em",
            "table => table.custom-table",
            "tr => tr.custom-tr",
            "td => td.custom-td"
          ],
          preserveStyles: true,
          includeDefaultStyleMap: true,
          ignoreEmptyParagraphs: false
        };

        const response = await fetch('/announcement.docx');
        const arrayBuffer = await response.arrayBuffer();
        const result = await mammoth.convertToHtml({ 
          arrayBuffer: arrayBuffer,
          styleMap: options.styleMap,
          includeDefaultStyleMap: options.includeDefaultStyleMap,
          ignoreEmptyParagraphs: options.ignoreEmptyParagraphs
        });

        // 处理HTML内容，添加标题居中和落款右对齐的样式
        let processedHtml = result.value;
        // 查找可能的标题并添加类
        processedHtml = processedHtml.replace(/<p>(.*?公告.*?)<\/p>/gi, '<p class="title">$1</p>');
        // processedHtml = processedHtml.replace(/<p>(.*?通知.*?)<\/p>/gi, '<p class="title">$1</p>');
        // processedHtml = processedHtml.replace(/<p>(.*?提示.*?)<\/p>/gi, '<p class="title">$1</p>');
        this.htmlContent = processedHtml
        
        const response2 = await fetch('/关于短信模板报备更新的温馨提示.docx');
        const arrayBuffer2 = await response2.arrayBuffer();
        const result2 = await mammoth.convertToHtml({ 
          arrayBuffer: arrayBuffer2,
          styleMap: options.styleMap,
          includeDefaultStyleMap: options.includeDefaultStyleMap,
          ignoreEmptyParagraphs: options.ignoreEmptyParagraphs
        });
        
        // 处理第二个文档
        let processedHtml2 = result2.value;
        // 查找可能的标题并添加类
        processedHtml2 = processedHtml2.replace(/<p>(.*?关于短信模板报备更新的温馨提示.*?)<\/p>/gi, '<p class="title">$1</p>');
        this.htmlContent2 = processedHtml2
      } catch (error) {
        console.log(error, 'error');
      }
    },
    // 获取项目绝对路径
    // ...mapActions([  //比如'movies/getHotMovies
    //     'saveInfo',
    // ]),
    // 用户名下拉菜单选择事件
    handleCommand(command) {
      if (command == "loginout") {
        this.$api.post(this.API.auth + "loginOut", {}, (res) => {
          if (res.code == 200) {
            var oDate = new Date();
            oDate.setDate(oDate.getDate() - 1);
            if (this.$common.getCookie("ZTGlS_TOKEN")) {
              document.cookie =
                "ZTGlS_TOKEN=" +
                this.$common.getCookie("ZTGlS_TOKEN") +
                ";path=/;domain=." +
                this.hostname +
                ";expires=" +
                oDate.toGMTString();
            }
            window.sessionStorage.setItem("logUrl", "home");
            window.sessionStorage.removeItem("path");
            localStorage.removeItem("authentication");
            localStorage.removeItem("formData");
            localStorage.removeItem("userInfo");
            localStorage.removeItem("balanceList");
            this.$router.push("/login");
          }
        });
      } else if (command === "information") {
        if (this.roleId == 12) {
          this.$router.push("/account");
        } else {
          this.$router.push("/PersonalInformation");
        }
        // console.log(11);
        // let logUrl={
        //     logUrl:"account"
        // }
        // this.saveUrl(logUrl);
        // window.sessionStorage.setItem('logUrl',"account")
      }
    },
    handleHome() {
      this.$router.push("/home");
    },
    // 侧边栏折叠
    collapseChage() {
      this.collapse = !this.collapse;
      bus.$emit("collapse", this.collapse);
    },
    getDomain() {
      var hostname = window.location.hostname;
      var name = hostname.split(".");
      this.hostname = name[name.length - 2] + "." + name[name.length - 1];
      // console.log(hostname,'name');
      // hostname.substring(hostname.indexof('.'))
      // console.log(hostname,'ll');
    },
    // 全屏事件
    handleFullScreen() {
      let element = document.documentElement;
      if (this.fullscreen) {
        if (document.exitFullscreen) {
          document.exitFullscreen();
        } else if (document.webkitCancelFullScreen) {
          document.webkitCancelFullScreen();
        } else if (document.mozCancelFullScreen) {
          document.mozCancelFullScreen();
        } else if (document.msExitFullscreen) {
          document.msExitFullscreen();
        }
      } else {
        if (element.requestFullscreen) {
          element.requestFullscreen();
        } else if (element.webkitRequestFullScreen) {
          element.webkitRequestFullScreen();
        } else if (element.mozRequestFullScreen) {
          element.mozRequestFullScreen();
        } else if (element.msRequestFullscreen) {
          // IE11
          element.msRequestFullscreen();
        }
      }
      this.fullscreen = !this.fullscreen;
    },
    versions() {
      //     var u = navigator.userAgent, app = navigator.appVersion;
      //     console.log(u,'u');
      //    var mobile = !!u.match(/AppleWebKit.*Mobile.*/)||!!u.match(/AppleWebKit/)
      //    console.log(mobile,'mobile');
      if (navigator.userAgent.match(/(iPhone|iPod|Android|ios|iPad)/i)) {
        // localStorage.setItem('h5Lg',"0");
        this.isShowBar = 0;
        // window.location.href = "mobile.html";
      } else {
        this.isShowBar = 1;
        //    localStorage.setItem('h5Lg',"1");
        // window.location.href = "pc.html";
      }
    },
    handelClose() {
      this.showflag = false;
      const expirationTime = new Date().getTime() + 2 * 60 * 60 * 1000; // 当前时间加上两小时
      localStorage.setItem(
        "authentication",
        JSON.stringify({ value: "true", expiration: expirationTime })
      );
    },
    changeUserInfo() {
      this.showflag = false;
      this.$router.push({ path: "/completeAuthenInfo" });
    },
    submitknow() {
      this.showPopup = false;
      const expirationTime = new Date().getTime() + 24 * 60 * 60 * 1000; // 当前时间加上24小时
      localStorage.setItem(
        "popupShown",
        JSON.stringify({ value: "true", expiration: expirationTime })
      );
    },
    handleNextPage() {
      this.nextPage = 2;
    },
    checkPopupStatus() {
      //在缓存那存储时间
      const storedItem = localStorage.getItem("popupShown");
      if (storedItem) {
        //判断是否有缓存
        //将缓存转化为对象
        const storedObj = JSON.parse(storedItem);
        //获取缓存的过期时间
        const expirationTime = storedObj.expiration;
        // 当前时间
        const now = new Date().getTime();
        //判断是否过期
        if (now < expirationTime) {
          //未过期，不显示提示
          this.showPopup = false;
        } else {
          //过期清除缓存重新弹出提示框
          localStorage.removeItem("popupShown");
          this.showPopup = true;
        }
      } else {
        //没有缓存，弹出提示框
        this.showPopup = true;
      }
    },
    // getAstatus() {
    //   this.$api.get(
    //     this.API.cpus + "consumerclientinfo/checkRealStatus",
    //     {}, // 接口地址 {},
    //     (res) => {
    //       if (res.code === 200) {
    //         this.showflag = res.data;
    //       }
    //     }
    //   );
    // },

    handleAnnouncement() {
      this.$router.push({ path: "/showMsg", query: { msg: 6 } });
    },
  },

  mounted() {
    this.loadWordDocument();
    if (document.body.clientWidth < 1500) {
      this.collapseChage();
    }
  },
};
</script>
<style scoped lang="less">
.header {
  position: relative;
  box-sizing: border-box;
  width: 100%;
  height: 70px;
  font-size: 22px;
  color: #fff;
  display: flex;
  justify-content: center;
  justify-content: space-between;
}

.collapse-btn {
  float: left;
  padding: 0 21px;
  cursor: pointer;
  line-height: 70px;
}

.header .logo {
  float: left;
  width: 250px;
  line-height: 70px;
}

.header-right {
  float: right;
  padding-right: 30px;
}

.header-user-con {
  display: flex;
  height: 70px;
  align-items: center;
}

.btn-fullscreen {
  transform: rotate(45deg);
  margin-right: 5px;
  font-size: 24px;
}

.btn-bell,
.btn-fullscreen {
  position: relative;
  width: 30px;
  height: 30px;
  text-align: center;
  border-radius: 15px;
  cursor: pointer;
  margin: 0px 8px;
}

.btn-bell-badge {
  position: absolute;
  right: 0;
  top: -2px;
  width: 8px;
  height: 8px;
  border-radius: 4px;
  background: #f56c6c;
  color: #fff;
}

.btn-bell .el-icon-bell {
  color: #fff;
}

.user-name {
  margin-left: 10px;
}

.user-avator {
  margin-left: 20px;
}

.user-avator img {
  display: block;
  width: 40px;
  height: 40px;
  border-radius: 50%;
}

.el-dropdown-link {
  color: #fff;
  cursor: pointer;
}

.el-dropdown-menu__item {
  text-align: center;
}

.logo_zt {
  width: 100%;
  /* height: 100%; */
}

.img {
  /* width: 100px; */
  height: 45px;
  margin: 13px 20px;
}

.version {
  font-size: 13px;
  position: absolute;
  top: 10px;
  left: 0;
  margin-left: 240px;
}

/deep/ .announcement-dialog {
  background: oldlace;
  border-radius: 10px;
}

/deep/ .el-dialog__title {
  color: #ec6e06;
}

/deep/ .custom-table {
  border-collapse: collapse;
  width: 100%;
  margin: 10px 0;
}

/deep/ .custom-tr {
  border: 1px solid #ddd;
}

/deep/ .custom-td {
  border: 1px solid #ddd;
  padding: 8px;
}

/deep/ .el-carousel__item {
  overflow-y: auto;
  padding: 10px 20px;
}

/deep/ .dialog-content h1,
/deep/ .dialog-content h2,
/deep/ .dialog-content h3 {
  margin-top: 16px;
  margin-bottom: 8px;
  font-weight: bold;
  color: #ec6e06;
}

/deep/ .dialog-content p {
  margin: 8px 0;
  line-height: 1.6;
}

/deep/ .dialog-content ul,
/deep/ .dialog-content ol {
  margin-left: 20px;
}

/deep/ .dialog-content img {
  max-width: 100%;
}

/deep/ .word-content {
  font-size: 16px;
  line-height: 1.5;
  color: #ec6e06;
  padding: 10px;
}

/deep/ .word-content h1,
/deep/ .word-content h2,
/deep/ .word-content h3,
/deep/ .word-content .title {
  text-align: center;
  margin: 15px 0;
  font-weight: bold;
  font-size: 20px;
}

/deep/ .word-content .signature,
/deep/ .word-content p[style*="text-align: right"],
/deep/ .word-content p:last-of-type  {
  text-align: right;
  margin-top: 20px;
  margin-right: 20px;
  font-weight: normal;
}
/deep/ .word-content p:nth-last-child(2) {
  text-align: right;
  margin-top: 20px;
  margin-right: 20px;
  font-weight: normal;
}
/deep/ .word-content .title {
  color: #ec6e06;
  font-size: 22px;
  font-weight: bold;
  text-align: center;
  margin-bottom: 20px;
}

/deep/ .word-content .greeting {
  text-align: left;
  margin-top: 20px;
  margin-left: 20px;
  margin-bottom: 20px;
  font-weight: normal;
  color: #333;
}

/deep/ .word-content .signature-wrapper {
  text-align: right;
  margin-top: 30px;
  margin-right: 30px;
}

/deep/ .word-content .signature-company {
  // text-align: right;
  margin-top: 30px;
  margin-right: 30px;
  font-weight: normal;
}

/deep/ .word-content .signature-date {
  // text-align: right;
  margin-top: 5px;
  margin-right: 30px;
  font-weight: normal;
}

/deep/ .word-content p.signature {
  text-align: right;
  margin-top: 30px;
  margin-right: 30px;
}

/deep/ .word-content table {
  border-collapse: collapse;
  width: 100%;
  margin: 15px 0;
}

/deep/ .word-content td, 
/deep/ .word-content th {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
}

/deep/ .word-content ul {
  list-style-type: disc;
  padding-left: 20px;
}

/deep/ .word-content ol {
  list-style-type: decimal;
  padding-left: 20px;
}

/deep/ .word-content strong {
  font-weight: bold;
}

/deep/ .word-content em {
  font-style: italic;
}
</style>
