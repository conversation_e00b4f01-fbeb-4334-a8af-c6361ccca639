<template>
  <el-dialog title="实名信息补充" :visible.sync="dialogVisible" width="750px" class="realname-dialog"
    :close-on-click-modal="false" :before-close="handleClose">
    <div>
      <el-form :inline="false" :model="formData" :rules="formRules" ref="realNameForm" class="demo-ruleForm"
        label-width="140px">
        <el-form-item label="企业名称" prop="companyName">
          <el-input class="input-w" placeholder="请输入企业名称" v-model="formData.companyName"></el-input>
        </el-form-item>
        <el-form-item label="社会统一信用代码" prop="creditCode">
          <el-input class="input-w" placeholder="请输入统一社会信用代码" v-model="formData.creditCode"></el-input>
        </el-form-item>
        <el-form-item label="企业法人" prop="legalPerson">
          <el-input class="input-w" placeholder="请输入企业法人姓名" v-model="formData.legalPerson"></el-input>
        </el-form-item>
        <el-form-item label="责任人姓名" prop="principalName">
          <el-input class="input-w" placeholder="请输入负责人姓名" v-model="formData.principalName"></el-input>
        </el-form-item>
        <el-form-item label="责任人证件号码" prop="principalIdCard">
          <el-input class="input-w" placeholder="请输入负责人身份证号" v-model="formData.principalIdCard"></el-input>
        </el-form-item>
        <el-form-item label="责任人手机号" prop="principalMobile" key="principalMobile">
          <el-input class="input-w" placeholder="请输入责任人手机号" v-model="formData.principalMobile"></el-input>
          <el-tooltip class="item" effect="dark" content="须填写身份证号本人使用的手机号，否则报备失败" placement="top">
            <i style="margin-left: 10px;color: #409EFF;font-size: 15px;cursor: pointer;" class="el-icon-info"></i>
          </el-tooltip>
        </el-form-item>
        <el-form-item label="签名来源" prop="signatureType" v-if="showSignatureTypes">
          <el-radio-group style="display: flex;flex-direction: column;align-items: self-start;"
            v-model="formData.signatureType">
            <el-radio style="margin-top: 10px;" :label="1">
              <span class="sig-type-title-tips">企业名称</span>
              <span style="color: #999;font-size: 12px;margin-left: 8px;">tips:推荐全称，简称字数须为全称字数60%以上，尽量连续，不可改字</span>
            </el-radio>
            <el-radio style="margin-top: 10px;" :label="2">
              <span class="sig-type-title-tips">事业单位：如机关，学校，科研单位，街道社区等</span>
            </el-radio>
            <el-radio style="margin-top: 10px;" :label="3">
              <span class="sig-type-title-tips">商标</span>
              （须提供商标注册证书图片或在在中国商标网的商标查询截图）
            </el-radio>
            <el-radio style="margin-top: 10px;" :label="4">
              <span class="sig-type-title-tips">App </span>
              （须提供app在ICP/IP/域名备案管理系统的截图）
            </el-radio>
            <el-radio style="margin-top: 10px;" :label="5">
              <span class="sig-type-title-tips">小程序</span>
              （须提供小程序在ICP/IP/域名备案管理系统的截图）
            </el-radio>
            <!-- <el-radio :label="7">
              <span class="sig-type-title-tips">网站</span>
              （须提网站在ICP/IP/域名备案管理系统截图，仅限事业单位的网站）
            </el-radio> -->
          </el-radio-group>
        </el-form-item>
        <el-form-item label="签名类型" prop="signatureSubType" v-if="showSignatureTypes">
          <el-radio-group v-model="formData.signatureSubType"
            style="display: flex;flex-direction: column;align-items: self-start;">
            <el-radio style="margin-top: 10px;" :label="0">
              <span>全称</span>
              <span style="color: #999;font-size: 12px;margin-left: 8px;">tips:推荐,报备快</span>
            </el-radio>
            <el-radio style="margin-top: 10px;" :label="1">
              <span>简称</span>
              <span style="color: #999;font-size: 12px;margin-left: 8px;">tips:请用企业/单位简称签名在企查查搜索企业唯一的图片，并按参照示例图提供</span>
            </el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="文件上传"
          :prop="(formData.signatureType == 1 || formData.signatureType == 2) && formData.signatureSubType == 0 ? '' : 'imgUrl'"
          key="imgUrl">
          <div>
            <el-upload class="upload-demo" :headers="headers" :action="uploadUrl" :limit="3" :file-list="fileList"
              list-type="picture-card" :on-preview="handlePictureCardPreview" :on-success="handleSuccess"
              :on-remove="handleRemove">
              <div>
                <i class="el-icon-plus"></i>
              </div>
            </el-upload>
            <div class="el-upload__tip">支持上传jpg, jpeg, png文件，且不超过2M</div>
          </div>
        </el-form-item>
        <el-form-item label="短信示例" prop="contentExample" v-if="showContentExample">
          <el-input type="textarea" v-model="formData.contentExample" show-word-limit maxLength="800"
            placeholder="请输入短信示例；例如：【xxx】欢迎使用xxx，祝您使用愉快！"></el-input>
        </el-form-item>
      </el-form>
      <el-button @click="handleCancel" style="width: 100px; padding: 9px 0; margin-left: 160px">
        取消
      </el-button>
      <el-button type="primary" @click="handleSubmit" style="width: 100px; padding: 9px 0">
        提交
      </el-button>
    </div>
    <!-- 图片预览 -->
    <el-dialog :visible.sync="dialogImageVisible" append-to-body>
      <img width="100%" :src="dialogImageUrl" alt="预览图片">
    </el-dialog>
  </el-dialog>
</template>

<script>
export default {
  name: "RealNameDialog",
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 初始表单数据
    initialFormData: {
      type: Object,
      default: () => ({
        companyName: "",
        creditCode: "",
        legalPerson: "",
        principalIdCard: "",
        principalName: "",
        principalMobile: "",
        signatureId: "",
        userId: "",
        imgUrl: "",
        signatureType: "",
        signatureSubType: 0,
        contentExample: ""
      })
    },
    // 显示签名类型选项
    showSignatureTypes: {
      type: Boolean,
      default: true
    },
    // 显示文件上传
    showFileUpload: {
      type: Boolean,
      default: true
    },
    // 显示短信示例
    showContentExample: {
      type: Boolean,
      default: true
    },
    // 上传文件的URL
    uploadUrl: {
      type: String,
      default: ""
    },
    // 上传文件的请求头
    headers: {
      type: Object,
      default: () => ({})
    },
    // 表单验证规则
    rules: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      dialogVisible: false,
      formData: { ...this.initialFormData },
      fileList: [],
      dialogImageUrl: '',
      dialogImageVisible: false,
      // 默认验证规则
      formRules: {
        companyName: [
          { required: true, message: '请输入企业名称', trigger: 'blur' }
        ],
        creditCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'blur' }
        ],
        legalPerson: [
          { required: true, message: '请输入企业法人姓名', trigger: 'blur' }
        ],
        principalName: [
          { required: true, message: '请输入负责人姓名', trigger: 'blur' }
        ],
        principalIdCard: [
          { required: true, message: '请输入负责人身份证号', trigger: 'blur' }
        ],
        principalMobile: [
          { required: true, message: '请输入责任人手机号', trigger: 'blur' }
        ],
        signatureType: [
          { required: true, message: '请选择签名来源', trigger: 'change' }
        ],
        signatureSubType: [
          { required: true, message: '请选择签名类型', trigger: 'change' }
        ],
        imgUrl: [
          { required: true, message: '请上传相关证明材料', trigger: 'change' }
        ],
        contentExample: [
          { required: true, message: '请输入短信示例', trigger: 'blur' }
        ]
      }
    };
  },
  watch: {
    visible(val) {
      this.dialogVisible = val;
    },
    dialogVisible(val) {
      this.$emit('update:visible', val);
      if (!val) {
        this.resetForm();
      }
    },
    initialFormData: {
      handler(val) {
        this.formData = { ...val };
        // 如果有图片URL，处理图片列表
        if (this.formData.imgUrl) {
          const urls = this.formData.imgUrl.split(",").filter(item => item.trim() !== '');
          this.fileList = urls.map((item) => {
            return {
              name: item,
              url: this.getImageUrl(item),
            };
          });
        } else {
          this.fileList = [];
        }
      },
      deep: true
    },
    rules: {
      handler(val) {
        // 合并自定义规则和默认规则
        this.formRules = { ...this.formRules, ...val };
      },
      deep: true
    }
  },
  methods: {
    // 获取图片完整URL
    getImageUrl(path) {
      // 可以从全局配置或props中获取基础URL
      //   const this.API.imgU : '';
      return this.API.imgU + path;
    },

    // 处理对话框关闭
    handleClose() {
      this.$emit('close');
      this.dialogVisible = false;
    },

    // 处理取消按钮点击
    handleCancel() {
      this.dialogVisible = false;
      this.$emit('cancel');
    },

    // 处理表单提交
    handleSubmit() {
      this.$refs.realNameForm.validate((valid) => {
        if (valid) {
          // 将文件列表转换为imgUrl字符串
          //   if (this.fileList.length > 0) {
          //     const urls = this.fileList.map(file => {
          //       if (file.response) {
          //         // 如果是新上传的文件
          //         return file.response.data;
          //       } else if (file.name) {
          //         // 如果是已有的文件
          //         return file.name;
          //       }
          //       return '';
          //     }).filter(url => url.trim() !== '');

          //     this.formData.imgUrl = urls.join(',');
          //   } else {
          //     this.formData.imgUrl = '';
          //   }

          // 触发提交事件，将表单数据传递给父组件
          this.$emit('submit', { ...this.formData });
        }
      });
    },

    // 重置表单
    resetForm() {
      this.$refs.realNameForm && this.$refs.realNameForm.resetFields();
      this.formData = { ...this.initialFormData };
      this.fileList = [];
    },

    // 处理文件上传成功
    handleSuccess(response, file, fileList) {
      if (response.code === 200) {
        this.fileList = fileList;
        this.$emit('upload-success', response, file, fileList);
      } else {
        this.$message.error(response.msg || '上传失败');
        // 移除上传失败的文件
        const index = fileList.indexOf(file);
        if (index !== -1) {
          fileList.splice(index, 1);
        }
      }
    },

    // 处理图片预览
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url;
      this.dialogImageVisible = true;
    },

    // 处理移除文件
    handleRemove(file, fileList) {
      this.fileList = fileList;
      this.$emit('file-remove', file, fileList);
    }
  }
};
</script>

<style scoped>
.realname-dialog .input-w {
  width: 300px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.upload-demo {
  margin-bottom: 10px;
}
</style>