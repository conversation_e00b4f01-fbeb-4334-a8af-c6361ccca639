/**
 * AI模板助手样式文件
 * 包含浮动面板、表单、动画等所有样式
 */

// AI助手按钮样式
.ai-toggle-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border: none;
  border-radius: 6px;
  padding: 6px 12px;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.3s ease;
  color: white;
  margin-left: 10px;

  .ai-icon {
    margin-right: 4px;
    font-size: 14px;
  }

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }

  &.active {
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
  }
}

// 浮动AI助手面板
.ai-assistant-floating-panel {
  position: fixed;
  z-index: 99999;
  width: 380px;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  // border: 1px solid #e9ecef;
  overflow: hidden;
  // 移除固定最大高度，使用JavaScript动态控制
  // max-height: 80vh;
  transition: all 0.3s ease;

  // 焦点状态样式
  &:focus {
    outline: none; // 移除默认焦点轮廓
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15), 0 0 0 2px rgba(64, 158, 255, 0.3);
  }

  // 最小化状态样式
  &.minimized {
    .ai-panel-resize-handle {
      display: none; // 最小化时隐藏调整边框
    }
  }

  .ai-panel-header {
    padding: 16px 20px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    cursor: move;
    user-select: none;
    position: relative;

    &:hover {
      background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    }

    .ai-assistant-title {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 6px;

      .ai-icon {
        margin-right: 8px;
        font-size: 18px;
      }

      .ai-panel-controls {
        display: flex;
        gap: 4px;

        .minimize-btn,
        .close-btn {
          color: white;
          padding: 4px;
          border-radius: 4px;
          transition: all 0.2s ease;

          &:hover {
            background: rgba(255, 255, 255, 0.2);
          }

          i {
            font-size: 12px;
          }
        }
      }
    }

    .ai-assistant-subtitle {
      font-size: 13px;
      opacity: 0.9;
      margin: 0;
    }
  }

  .ai-panel-content {
    // 使用JavaScript动态计算高度
    height: calc(100% - 120px); // 减去头部和调整边框的高度
    overflow-y: auto;
    overflow-x: hidden;

    // 美化滚动条
    &::-webkit-scrollbar {
      width: 6px;
    }

    &::-webkit-scrollbar-track {
      background: rgba(255, 255, 255, 0.1);
      border-radius: 3px;
    }

    &::-webkit-scrollbar-thumb {
      background: rgba(255, 255, 255, 0.3);
      border-radius: 3px;

      &:hover {
        background: rgba(255, 255, 255, 0.5);
      }
    }

    .ai-assistant-tabs {
      /deep/ .el-tabs__header {
        margin: 0;
        background: #f8f9fa;
        padding: 0 16px;

        .el-tabs__nav-wrap {
          &::after {
            display: none;
          }
        }

        .el-tabs__item {
          padding: 10px 16px;
          font-weight: 500;
          color: #606266;
          border: none;
          font-size: 13px;

          &.is-active {
            color: #667eea;
            background: white;
            border-radius: 6px 6px 0 0;
          }

          &:hover {
            color: #667eea;
          }
        }

        .el-tabs__active-bar {
          display: none;
        }
      }

      /deep/ .el-tabs__content {
        padding: 0;
      }
    }

    .ai-assistant-content {
      padding: 16px;

      .ai-section {
        .ai-section-title {
          font-size: 14px;
          font-weight: 600;
          color: #303133;
          margin: 0 0 12px 0;
          padding-bottom: 6px;
          border-bottom: 1px solid #f0f0f0;
        }

        .ai-section-desc {
          color: #909399;
          font-size: 12px;
          margin-bottom: 12px;
          line-height: 1.5;
        }

        .ai-form-item {
          margin-bottom: 12px;

          .ai-label {
            display: block;
            font-weight: 500;
            color: #606266;
            margin-bottom: 4px;
            font-size: 12px;

            &::before {
              content: '*';
              color: #f56c6c;
              margin-right: 4px;
            }
          }

          .ai-label-optional {
            display: block;
            font-weight: 500;
            color: #606266;
            margin-bottom: 4px;
            font-size: 12px;
          }

          .ai-select,
          .ai-input,
          .ai-textarea {
            width: 100%;

            /deep/ .el-input__inner,
            /deep/ .el-textarea__inner {
              border-radius: 4px;
              border: 1px solid #dcdfe6;
              transition: all 0.3s ease;
              font-size: 12px;
              padding: 6px 8px;

              &:focus {
                border-color: #667eea;
                box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
              }
            }

            // 确保select下拉框层级正确
            /deep/ .el-select-dropdown {
              z-index: 999999 !important;
            }
          }
        }

        .ai-generate-btn-section {
          display: flex;
          flex-direction: column;
          gap: 8px;
          margin-top: 16px;
          padding-top: 12px;
          border-top: 1px solid #f0f0f0;

          .ai-generate-submit-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 4px;
            padding: 8px 16px;
            font-size: 12px;
            font-weight: 500;
            transition: all 0.3s ease;
            width: 100%;

            .ai-icon {
              margin-right: 4px;
              font-size: 12px;
            }

            &:hover {
              transform: translateY(-1px);
              box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
            }

            &.is-loading {
              transform: none;
            }
          }

          .ai-generate-count {
            color: #909399;
            font-size: 11px;
            text-align: center;
          }
        }
      }
    }

    .ai-results-section {
      padding: 16px;
      border-top: 1px solid #f0f0f0;

      .ai-section-title {
        font-size: 14px;
        font-weight: 600;
        color: #303133;
        margin: 0 0 12px 0;
        display: flex;
        align-items: center;

        &::before {
          content: '✨';
          margin-right: 6px;
          font-size: 14px;
        }
      }

      .ai-loading-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px 12px;
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 6px;
        margin-bottom: 12px;

        .ai-loading-animation {
          display: flex;
          flex-direction: column;
          align-items: center;
          margin-bottom: 16px;

          .loading-dots {
            display: flex;
            gap: 4px;
            margin-bottom: 12px;

            .dot {
              width: 6px;
              height: 6px;
              border-radius: 50%;
              background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
              animation: dotPulse 1.5s ease-in-out infinite;

              &.dot1 {
                animation-delay: 0s;
              }

              &.dot2 {
                animation-delay: 0.3s;
              }

              &.dot3 {
                animation-delay: 0.6s;
              }
            }
          }

          .loading-text {
            text-align: center;

            .loading-main-text {
              font-size: 12px;
              font-weight: 600;
              color: #303133;
              margin: 0 0 4px 0;
            }

            .loading-sub-text {
              font-size: 11px;
              color: #909399;
              margin: 0;
            }
          }
        }
      }

      .ai-results-list {
        display: flex;
        flex-direction: column;
        gap: 8px;

        .ai-result-item {
          background: #f8f9fa;
          border: 1px solid #e9ecef;
          border-radius: 6px;
          padding: 12px;
          transition: all 0.3s ease;

          &:hover {
            border-color: #667eea;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.1);
          }

          .ai-result-content {
            font-size: 12px;
            line-height: 1.5;
            color: #303133;
            margin-bottom: 8px;
            padding: 8px;
            background: white;
            border-radius: 4px;
            border-left: 2px solid #667eea;
          }

          .ai-result-actions {
            display: flex;
            justify-content: flex-end;

            .el-button {
              border-radius: 3px;
              padding: 4px 12px;
              font-weight: 500;
              font-size: 11px;
            }
          }
        }
      }
    }
  }

  // 高度调整边框
  .ai-panel-resize-handle {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 12px;
    cursor: ns-resize;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;

    &:hover {
      background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0.1));
      border-top-color: rgba(255, 255, 255, 0.2);
    }

    &.resizing {
      background: linear-gradient(135deg, rgba(64, 158, 255, 0.3), rgba(64, 158, 255, 0.1));
      border-top-color: rgba(64, 158, 255, 0.5);
    }

    .resize-indicator {
      color: rgba(255, 255, 255, 0.6);
      font-size: 12px;
      transform: rotate(90deg);
      transition: all 0.2s ease;

      &:hover {
        color: rgba(255, 255, 255, 0.8);
      }
    }

    &.resizing .resize-indicator {
      color: rgba(64, 158, 255, 0.8);
    }
  }
}

// 动画定义
@keyframes dotPulse {
  0%, 20% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.3);
    opacity: 0.7;
  }
  80%, 100% {
    transform: scale(1);
    opacity: 1;
  }
}

// 全局修复AI助手面板中Element UI组件的层级问题
.el-select-dropdown {
  z-index: 999999 !important;
}

.el-popper {
  z-index: 999999 !important;
}

.el-tooltip__popper {
  z-index: 999999 !important;
}

.el-picker-panel {
  z-index: 999999 !important;
}

.el-cascader-panel {
  z-index: 999999 !important;
}

.el-date-picker__header {
  z-index: 999999 !important;
}

.el-popper[x-placement^="bottom"] {
  z-index: 999999 !important;
}

.el-popper[x-placement^="top"] {
  z-index: 999999 !important;
}

.el-popper[x-placement^="left"] {
  z-index: 999999 !important;
}

.el-popper[x-placement^="right"] {
  z-index: 999999 !important;
}
