<template>
    <div class="user-modifi-cation bag" style="
    min-width: 1100px;
">
        <div class="Top_title" style="padding: 10px;">
            <span style="display:inline-block;padding-right:10px; cursor:pointer;color:#16a589;" @click="goBack()"><i
                    class="el-icon-arrow-left"></i>返回</span>|
            <span> {{ statusOf }}</span>
        </div>
        <div class="fillet">
            <!-- 步骤导航 -->
            <el-steps v-if="!$route.query.id" :active="activeStep" finish-status="success" simple
                style="margin-bottom: 20px;">
                <el-step title="填写用户信息"></el-step>
                <el-step title="设置默认签名"></el-step>
            </el-steps>
            <!-- 第二步：其他表单信息 -->
            <el-form v-if="activeStep === 1" :inline="true" :model="formData" :rules="formRule" ref="formData"
                label-width="154px">
                <div>
                    <div :class="!$route.query.id ? 'div_S div1' : 'div_S1 div1'">

                        <el-form-item label="用户名" v-if="statusOf == '编辑用户'" prop="userName">
                            <el-input :style="{ width: dynamicWidth }" :disabled="statusOf == '编辑用户'"
                                v-model="formData.userName"></el-input>
                        </el-form-item>
                        <el-form-item label="用户名" v-else prop="userName">
                            <el-input :style="{ width: dynamicWidth }" v-model="formData.userName"></el-input>
                        </el-form-item>
                        <el-form-item label="公司名称" prop="compName">
                            <el-input :style="{ width: dynamicWidth }" v-model="formData.compName"></el-input>
                        </el-form-item>
                        <el-form-item label="行业类别" prop="industryId">
                            <el-select :style="{ width: dynamicWidth }" v-model="formData.industryId"
                                placeholder="请选择行业类别">
                                <el-option v-for="(item, index) in industrys" :label="item.industryName"
                                    :value="item.id" :key="index"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="公司地址" prop="compAddress">
                            <el-input :style="{ width: dynamicWidth }" v-model="formData.compAddress"></el-input>
                        </el-form-item>
                        <el-form-item label="电子邮箱" prop="email">
                            <el-input :style="{ width: dynamicWidth }" v-model="formData.email"></el-input>
                        </el-form-item>

                    </div>
                    <div :class="!$route.query.id ? 'div_S div1' : 'div_S1 div1'">
                        <el-form-item v-if="statusOf == '新增用户'" label="手机号码" prop="phone">
                            <el-input :style="{ width: dynamicWidth }" v-model="formData.phone"></el-input>
                        </el-form-item>
                        <el-form-item label="账户密码" v-if="statusOf == '新增用户'" prop="password">
                            <el-input :style="{ width: dynamicWidth }" v-model="formData.password"></el-input>
                            <el-button type="primary" style="margin-right:10px;margin:20px 0;" class="generatePassword"
                                @click="generatePass">生成密码</el-button>
                        </el-form-item>
                        <el-form-item label="账户状态" prop="consumerStatus" v-if="statusOf == '编辑用户'">
                            <el-select :style="{ width: dynamicWidth }" disabled v-model="formData.consumerStatus"
                                placeholder="请选择账户状态">
                                <el-option label="启用" value="0"></el-option>
                                <el-option label="停用" value="1"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="扩展号" v-if="statusOf == '编辑用户'" prop="">
                            <el-input :style="{ width: dynamicWidth }" disabled v-model="formData.ext"></el-input>
                        </el-form-item>
                        <el-form-item label="消息报告" prop="smsReportLevel">
                            <el-radio-group v-model="formData.smsReportLevel" :style="{ width: dynamicWidth }">
                                <el-radio label="1">不接收报告</el-radio>
                                <el-radio label="2">批量推送</el-radio>
                                <el-radio label="3">主动抓取</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item label="签名实名推送方式" prop="pushRealNameType">
                            <el-radio-group v-model="formData.pushRealNameType" class="input-w">
                                <el-radio label="0">不推送</el-radio>
                                <el-radio label="1" style="margin-left: 10px">合并推送</el-radio>
                                <el-radio label="2" style="margin-left: 10px">单独推送</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <el-form-item v-if="formData.pushRealNameType == 1" label="运营商" prop="mergeOperator">
                            <el-checkbox-group style="
                      width: 250px;
                      display: flex;
                      flex-shrink: 0;
                      flex-wrap: wrap;
                    " v-model="formData.mergeOperator">
                                <el-checkbox style="margin-left: 0" label="1">移动</el-checkbox>
                                <el-checkbox style="margin-left: 0" label="2">联通</el-checkbox>
                                <el-checkbox style="margin-left: 0" label="3">电信</el-checkbox>
                            </el-checkbox-group>
                        </el-form-item>
                        <el-form-item label="上行回复获取地址" v-if="formData.smsReportLevel == 2" prop="replyUrl">
                            <el-input :style="{ width: dynamicWidth }" placeholder="回复短信的地址格式  (以http/https开头)"
                                v-model="formData.replyUrl"></el-input>
                        </el-form-item>
                        <el-form-item label="下行状态报告获取地址" v-if="formData.smsReportLevel == 2" prop="reportUrl">
                            <el-input :style="{ width: dynamicWidth }" placeholder="状态报告的地址格式  (以http/https开头)"
                                v-model="formData.reportUrl"></el-input>
                        </el-form-item>
                        <el-form-item label="签名审核推送地址" prop="smsSignatureUrl">
                            <el-input :style="{ width: dynamicWidth }" placeholder="" type="textarea"
                                v-model="formData.smsSignatureUrl"></el-input>
                        </el-form-item>
                        <el-form-item label="实名状态合并推送地址" prop="mergePushUrl">
                            <el-input :style="{ width: dynamicWidth }" placeholder="" type="textarea"
                                v-model="formData.mergePushUrl"></el-input>
                        </el-form-item>
                    </div>
                    <div v-if="!$route.query.id" class="div_S div3">
                        <el-form-item label="企业名称" prop="companyName">
                            <el-input :style="{ width: dynamicWidth }" placeholder="请输入企业名称"
                                v-model="formData.companyName"></el-input>
                        </el-form-item>
                        <el-form-item label="社会统一信用代码" prop="creditCode">
                            <el-input :style="{ width: dynamicWidth }" placeholder="请输入统一社会信用代码"
                                v-model="formData.creditCode"></el-input>
                        </el-form-item>
                        <el-form-item label="企业法人" prop="legalPerson">
                            <el-input :style="{ width: dynamicWidth }" placeholder="请输入企业法人姓名"
                                v-model="formData.legalPerson"></el-input>
                        </el-form-item>
                        <el-form-item label="责任人姓名" prop="principalName">
                            <el-input :style="{ width: dynamicWidth }" placeholder="请输入负责人姓名"
                                v-model="formData.principalName"></el-input>
                        </el-form-item>
                        <el-form-item label="责任人证件号码" prop="principalIdCard">
                            <el-input :style="{ width: dynamicWidth }" placeholder="请输入负责人身份证号"
                                v-model="formData.principalIdCard"></el-input>
                        </el-form-item>
                        <el-form-item label="责任人手机号" prop="principalMobile" key="principalMobile">
                            <el-input :style="{ width: dynamicWidth }" placeholder="请输入责任人手机号"
                                v-model="formData.principalMobile"></el-input>
                            <el-tooltip class="item" effect="dark" content="须填写身份证号本人使用的手机号，否则报备失败" placement="top">
                                <i style="margin-left: 10px;color: #409EFF;font-size: 15px;cursor: pointer;"
                                    class="el-icon-info"></i>
                            </el-tooltip>
                        </el-form-item>
                    </div>
                </div>
                <div style="text-align: right;margin-right: 35px;" class="clear-div">
                    <el-button @click="goBack()">取 消</el-button>
                    <el-button v-if="!$route.query.id" type="primary" @click="validateSignature">下一步</el-button>
                    <el-button v-else type="primary" @click="submitForm('formData')">保 存</el-button>
                </div>
            </el-form>
            <!-- 第一步：设置默认签名 -->
            <el-form v-else :model="formData" :rules="signatureRule" ref="signatureForm" label-width="154px">
                <div class="signature-container">
                    <el-form-item label="签名设置方式" prop="signatureSetting">
                        <el-radio-group v-model="formData.signatureSetting" @change="changeSignatureSetting">
                            <el-radio :label="1">新建签名</el-radio>
                            <el-radio :label="2">使用管理商名下签名</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item v-if="formData.signatureSetting == 1" label="默认签名" prop="signature">
                        <div :style="{ width: '100%' }" style="position:relative">
                            <el-input :style="{ width: '100%' }" v-model="formData.signature"
                                placeholder="请输入默认签名"></el-input>
                            <span style="position:absolute;top:2px;left:0px;">【</span>
                            <span style="position:absolute;top:2px;right:0px;">】</span>
                        </div>
                    </el-form-item>
                    <el-form-item v-if="formData.signatureSetting == 2" label="默认签名" prop="signatureId">
                        <div :style="{ width: '100%' }" style="position:relative">
                            <el-select :style="{ width: '100%' }" v-model="formData.signatureId" clearable filterable
                                remote :remote-method="remoteMethod" :loading="loadingcomp" placeholder="请选择默认签名"
                                @change="changeSignature">
                                <el-option v-for="(item, index) in signList" :key="index" :label="item.signature"
                                    :value="item.signatureId">
                                    <template #default>
                                        <span>{{ item.signature }}</span>
                                        <span style="color: #999;font-size: 12px;margin-left: 5px;">{{ item.consumerName }}</span>
                                    </template>
                                </el-option>
                            </el-select>
                            <span style="position:absolute;top:2px;left:0px;">【</span>
                            <span style="position:absolute;top:2px;right:0px;">】</span>
                        </div>
                    </el-form-item>
                    <el-form-item label="签名来源" class="sig-type" prop="signatureType">
                        <el-radio-group v-model="formData.signatureType" style="display: flex;flex-direction: column;">
                            <el-radio :label="1" class="sig-type-title-tips">
                                <span class="sig-type-title-tips">企业名称</span>
                                <span
                                    style="color: #999;font-size: 12px;margin-left: 8px;">tips:推荐全称，简称字数须为全称字数60%以上，尽量连续，不可改字</span>
                                <!-- （须提供营业执照图片） -->
                            </el-radio>
                            <el-radio :label="2" class="sig-type-title-tips">
                                <span>事业单位：如机关，学校，科研单位，街道社区等</span>
                            </el-radio>
                            <el-radio :label="3" class="sig-type-title-tips">
                                <span>商标</span>
                                （须提供商标注册证书图片或在在中国商标网的商标查询截图）
                            </el-radio>
                            <el-radio :label="4" class="sig-type-title-tips">
                                <span>App </span>
                                （须提供app在ICP/IP/域名备案管理系统的截图）
                            </el-radio>
                            <el-radio :label="5" class="sig-type-title-tips">
                                <span>小程序</span>
                                （须提供小程序在ICP/IP/域名备案管理系统的截图）
                            </el-radio>
                            <!-- <el-radio :label="6" class="sig-type-title-tips">
                                <span >公众号</span>
                                （须提供小程序与主体公司存在关联关系的证明材图片）
                            </el-radio> -->
                            <!-- <el-radio :label="7" class="sig-type-title-tips">
                                <span >网站</span>
                                （须提网站在ICP/IP/域名备案管理系统截图，仅限事业单位的网站）
                            </el-radio> -->
                        </el-radio-group>
                    </el-form-item>
                    <el-form-item label="签名类型" prop="signatureSubType">
                        <el-radio-group v-model="formData.signatureSubType">
                            <el-radio :label="0">
                                <span>全称</span>
                                <span style="color: #999;font-size: 12px;margin-left: 8px;">tips:推荐,报备快</span>
                            </el-radio>
                            <el-radio :label="1">
                                <span>简称</span>
                                <span
                                    style="color: #999;font-size: 12px;margin-left: 8px;">tips:请用企业/单位简称签名在企查查搜索企业唯一的图片，并按参照示例图提供</span>
                            </el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <!-- <span class="audit-criteria"><i class="el-icon-document"></i> 查看审核标准</span> -->
                    <el-form-item label="上传图片"
                        :prop="(formData.signatureType == 1 || formData.signatureType == 2) && formData.signatureSubType == 0 ? '' : 'imgUrl'"
                        key="imgUrl">
                        <file-upload style="display: inline-block" :action="this.API.cpus + 'v3/file/upload'" :limit="3"
                            listType="picture" tip="格式要求：支持.jpg .jpeg .bmp .gif .png等格式照片，大小不超过2M"
                            :fileStyle="fileStyle" :del="del1" :fileListS="fileListS" :showfileList="true"
                            @fileup="fileup" @fileupres="fileupres">选择上传文件</file-upload>
                        <span style="margin-left: 18px;color: #409EFF;cursor: pointer;" @click="showImg">查看图片示例</span>
                    </el-form-item>
                    <el-form-item label="短信示例" prop="contentExample">
                        <el-input style="height: 100%" v-model="formData.contentExample" type="textarea"
                            placeholder="请输入短信示例；例如：【xxx】欢迎使用xxx，祝您使用愉快！" maxlength="800" show-word-limit></el-input>
                        <!-- <el-input type="textarea" v-model="formData.contentExample" show-word-limit
                            maxLength="800" placeholder="请输入短信示例；例如：【xxx】欢迎使用xxx，祝您使用愉快！"></el-input> -->
                    </el-form-item>
                    <el-form-item label="备注内容" prop="remark">
                        <el-input type="textarea" v-model="formData.remark" maxLength="50"
                            placeholder="请输入备注内容,50字以内"></el-input>
                    </el-form-item>
                    <div style="text-align: right;margin-right: 35px;">
                        <el-button @click="prevStep">上一步</el-button>
                        <el-button type="primary" @click="submitForm('signatureForm')">保 存</el-button>
                    </div>
                </div>
            </el-form>


        </div>
        <!-- //查看图片示例弹窗 -->
        <el-dialog title="查看图片示例" :visible.sync="showImgDialogVisible" width="900px" :before-close="handleClose"
            :close-on-press-escape="false">
            <div style="display: flex;justify-content: space-around;">
                <div style="display: flex;flex-direction: column;align-items: center;margin-right: 20px;">
                    <div style="margin-bottom: 10px;">唯一性示例图</div>
                    <el-image style="width: 200px; height: 200px" :src="require('../../../../assets/images/qcc.png')"
                        :preview-src-list="[require('../../../../assets/images/qcc.png')]">
                    </el-image>
                </div>
                <div style="display: flex;flex-direction: column;align-items: center;margin-right: 20px;">
                    <div style="margin-bottom: 10px;">营业执照示例</div>
                    <el-image style="width: 200px; height: 200px"
                        :src="require('../../../../assets/images/business.png')"
                        :preview-src-list="[require('../../../../assets/images/business.png')]">
                    </el-image>
                </div>
                <div style="display: flex;flex-direction: column;align-items: center;margin-right: 20px;">
                    <div style="margin-bottom: 10px;">商标示例</div>
                    <el-image style="width: 200px; height: 200px"
                        :src="require('../../../../assets/images/shangbiao.png')"
                        :preview-src-list="[require('../../../../assets/images/shangbiao.png')]">
                    </el-image>
                </div>
                <div style="display: flex;flex-direction: column;align-items: center;margin-right: 20px;">
                    <div style="margin-bottom: 10px;">app示例</div>
                    <el-image style="width: 200px; height: 200px"
                        :src="require('../../../../assets/images/productapp.png')"
                        :preview-src-list="[require('../../../../assets/images/productapp.png')]">
                    </el-image>
                </div>
                <div style="display: flex;flex-direction: column;align-items: center;margin-right: 20px;">
                    <div style="margin-bottom: 10px;">微信小程序示例</div>
                    <el-image style="width: 200px; height: 200px" :src="require('../../../../assets/images/wxapp.png')"
                        :preview-src-list="[require('../../../../assets/images/wxapp.png')]">
                    </el-image>
                </div>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传

export default {
    components: { FileUpload },
    name: "UserEditing",
    data() {
        var userName = (rule, value, callback) => {
            if (!value) {
                return callback(new Error('用户名不能为空'));
            } else {
                if (!/[^0-9a-zA-Z_]/g.test(value)) {
                    if (!/^[\da-zA-Z_]+$/.test(value)) {
                        // }else if(!/^(?![0-9]+$)(?![a-zA-Z]+$)[0-9A-Za-z]{4,20}$/.test(value)){
                        callback(new Error('由大小写字母数字_组成!'))
                    } else if (/[\u4E00-\u9FA5\uF900-\uFA2D]/.test(value)) {
                        callback(new Error('由大小写字母数字_组成!'))
                    } else if (/[`~!@#$%^&*()+<>?:"{},.\/;'[\]]/im.test(value) || /[·！#￥（——）：；""''','|《。》？、[\]]/im.test(value)) {
                        callback(new Error('由大小写字母数字_组成!'))
                    } else {
                        if (this.statusOf != '编辑用户') {
                            this.$api.get(this.API.upms + 'online/existUser/' + value, {}, res => {
                                if (res.data == 0) {
                                    callback();
                                } else {
                                    callback(new Error('用户名已经存在'))
                                }
                            })
                        } else {
                            callback();
                        }
                    }
                } else {
                    callback(new Error('由大小写字母数字_组成!'))
                }
            }
        };
        var signature = (rule, value, callback) => {
            if (!value) {
                return callback(new Error('默认签名不能为空'));
            }
            else {
                callback()
                //  if(value[0]!="【"||value[value.length-1]!="】"){
                //     callback(new Error('请正确填写签名格式【签名】'))
                //  }else if(value.length>22){
                //     callback(new Error('签名最多20位'))
                //  }else if(/[`~!@#$%^&*()_+<>?:"{},.\/;'[\]]/im.test(value)||/[·！#￥（——）：；""''','|《。》？、[\]]/im.test(value)){
                //     callback(new Error('不能填写特殊字符'))
                //  }else{
                //     callback()
                //  }      
            }
        };
        // 多个手机号验证
        var phone = (rule, value, callback) => {
            if (value == "") {
                return callback(new Error('手机号码不能为空'));
            } else if (!/^1[3|4|5|6|7|8|9]\d{9}(,1[3|4|5|6|7|8|9]\d{9})*$/.test(value)) {
                return callback(new Error('请正确填写手机号,多个请用(,)隔开'));
            } else {
                var phoneArr = this.formData.phone.split(",");
                var flag = false;
                if (phoneArr.length > 10) {
                    return callback(new Error('最多添加10个手机号'));
                } else {
                    if (phoneArr.length > 1) {
                        for (var i = 0; i < phoneArr.length; i++) {
                            for (var j = i; j < phoneArr.length; j++) {
                                if (phoneArr[i] == phoneArr[j + 1]) {
                                    flag = true
                                }
                            }
                        }
                    }
                    if (flag) {
                        return callback(new Error('手机号不可重复添加'));
                    } else {
                        callback();
                    }
                }
            }
        };
        //邮箱验证
        var email = (rule, value, callback) => {
            if (value == "" || value == null) {
                callback();
            } else {
                // /^([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+@([a-zA-Z0-9]+[_|\_|\.]?)*[a-zA-Z0-9]+\.[a-zA-Z]{2,3}$/
                if (!/^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/.test(value)) {
                    return callback(new Error('请正确填写邮箱'));
                } else {
                    callback();
                }
            }
        };
        // 验证网址
        var replyUrl = (rule, value, callback) => {
            if (!value) {
                callback();
            } else {
                if (!/(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/.test(value)) {
                    return callback(new Error('请正确输入上行地址(以http/https开头)'));
                } else {
                    callback();
                }
            }
        };
        var reportUrl = (rule, value, callback) => {
            if (!value) {
                callback();
            } else {
                if (!/(http|ftp|https):\/\/[\w\-_]+(\.[\w\-_]+)+([\w\-\.,@?^=%&:/~\+#]*[\w\-\@?^=%&/~\+#])?/.test(value)) {
                    return callback(new Error('请正确输入下行地址(以http/https开头)'));
                } else {
                    callback();
                }
            }
        };
        return {
            activeStep: 1, // 当前步骤，默认第一步
            statusOf: "",//新增还是编辑
            industrys: [],//行业类别
            dynamicWidth: '250px',
            signList: [],
            loadingcomp: false,
            formData: {
                userName: '',
                compAddress: '',
                compName: '',
                consumerStatus: '0',
                email: '',
                industryId: '',
                password: '',
                phone: '',
                replyUrl: '',
                reportUrl: '',
                smsReportLevel: '1',
                signature: '',
                signatureId:"",
                companyName: "",//公司名称
                creditCode: "",//统一社会信用代码
                legalPerson: "",//法人姓名
                principalIdCard: "",//负责人身份证号
                principalName: "",//负责人姓名
                principalMobile: "",//负责人手机号
                pushRealNameType: "0",//签名实名推送方式
                mergeOperator: [],
                smsSignatureUrl: "",
                mergePushUrl: "",
                signatureSetting: 1,
                signatureType: 1,
                signatureSubType: 0,
                remark: "",
                imgUrl: "",
                contentExample: "",
                // realNameInfo: {

                // },
            },
            signatureCory: "",
            signatureRule: {
                signature: [
                    { required: true, message: '该输入项为必填项!', trigger: 'blur' },
                    { min: 2, max: 15, message: '长度在 2 到 15 个字符', trigger: ['blur', 'change'] },
                    {
                        pattern: /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/,
                        message: '不能使用空格和特殊符号"-+=*&%#@~"等'
                    }
                ],
                signatureId: [
                    { required: true, message: '请选择签名', trigger: 'change' },
                ],
                signatureType: [
                    { required: true, message: '请选择签名来源', trigger: 'change' },
                ],
                signatureSubType: [
                    { required: true, message: '请选择签名类型', trigger: 'change' },
                ],
                imgUrl: [
                    { required: true, message: '请上传图片', trigger: 'change' },
                ],
                contentExample: [
                    { required: true, message: '请输入短信示例', trigger: 'change' },
                ],
                remark: [
                    { required: true, message: '请输入备注内容', trigger: 'change' },
                ],
            },
            formRule: {
                userName: [
                    { required: true, validator: userName, trigger: 'blur' },
                    { min: 4, max: 20, message: '长度在 4 到 20 个字符', trigger: 'change' }
                ],
                compName: [
                    { required: true, message: '请输入公司名称', trigger: 'change' },
                ],
                phone: [
                    { required: true, validator: phone, trigger: 'change' },
                ],
                consumerStatus: [
                    { required: true, message: '请选择账户状态', trigger: 'change' },
                ],
                password: [
                    { required: true, message: '请生成账户密码', trigger: 'change' },
                    { min: 6, max: 20, message: '长度在 6 到 20 个字符', trigger: 'change' }
                ],
                signature: [
                    { required: true, message: '该输入项为必填项!', trigger: 'blur' },
                    { min: 2, max: 15, message: '长度在 2 到 15 个字符', trigger: ['blur', 'change'] },
                    {
                        pattern: /^[\u4e00-\u9fa5_a-zA-Z0-9]+$/,
                        message: '不能使用空格和特殊符号"-+=*&%#@~"等'
                    }
                ],
                email: [
                    { required: false, validator: email, trigger: 'change' },
                ],
                replyUrl: [
                    { required: false, validator: replyUrl, trigger: 'change' },
                ],
                reportUrl: [
                    { required: false, validator: reportUrl, trigger: 'change' },
                ],
                companyName: [
                    { required: true, message: '请输入企业名称', trigger: 'change' },
                ],
                creditCode: [
                    { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
                ],
                legalPerson: [
                    { required: true, message: '请输入法人姓名', trigger: 'change' },
                ],
                principalIdCard: [
                    { required: true, message: '请输入负责人身份证号', trigger: 'change' },
                ],
                principalName: [
                    { required: true, message: '请输入负责人姓名', trigger: 'change' },
                ],
                principalMobile: [
                    { required: true, message: '请输入责任人手机号', trigger: 'change' },
                ],
                pushRealNameType: [
                    {
                        required: true,
                        message: "请选择是否推送实名认证状态",
                        trigger: "change",
                    },
                ],
                mergeOperator: [
                    {
                        required: true,
                        message: "请选择运营商",
                        trigger: "change",
                    },
                ],
            },
            //上传文件格式
            fileListS: "", //回显图片的地址
            fileStyle: {
                size: 5,
                type: "img",
                style: ["jpg", "jpeg", "bmp", "gif", "png"],
            },
            del1: true, //关闭弹框时清空图片
            showImgDialogVisible: false, //查看图片示例弹窗
            imgUrl: [],
        }
    },
    methods: {
        //公司名远程搜索
        remoteMethod(query) {
            // console.log(query, "query");
            if (query !== "") {
                this.loadingcomp = true;
                this.searchAccount(query);
                this.loadingcomp = false;
            } else {
                this.signList = [];
                this.searchAccount();
            }
        },
        // handleFocus(){
        //   this.searchAccount();

        // },
        searchAccount(val) {
            try {
                let signature = val ? val : '';
                this.$api.get(
                    this.API.cpus + "v3/consumer/manager/user/signature/list?" + "signature=" + signature,
                    {},
                    (res) => {
                        if (res.code == 200) {
                            this.signList = res.data;
                        }
                    }
                );
            } catch (error) {
                console.log(error, "error");
            }
        },
        changeSignature(val) {
            if (!val) {
                // 当清空选择时，重置相关字段
                this.formData.signature = '';
                this.formData.signatureType = 1;
                this.formData.signatureSubType = 0;
                this.formData.imgUrl = '';
                this.formData.remark = '';
                this.formData.contentExample = '';
                this.fileListS = '';
                return;
            }

            let signature = this.signList.find(item => item.signatureId == val);
            
            if (signature) {
                this.formData.signature = signature.signature.replace(/【|】/g, '');
                this.formData.signatureType = signature.signatureType;
                this.formData.signatureSubType = signature.signatureSubType;
                this.formData.imgUrl = signature.imgUrl;
                this.formData.remark = signature.remark;
                this.formData.contentExample = signature.contentExample;
                
                if (signature.imgUrl) {
                    this.fileListS = signature.imgUrl;
                    this.formData.imgUrl = signature.imgUrl;
                } else {
                    this.fileListS = '';
                }
            }
        },
        changeSignatureSetting(val) {
            this.formData.signature = ''
            this.formData.signatureId = ''
            this.formData.signatureType = 1
            this.formData.signatureSubType = 0
            this.formData.imgUrl = ''
            this.formData.remark = ''
            this.formData.contentExample = ''
            this.fileListS = ''
        },
        // 验证签名并前进到下一步
        validateSignature() {
            this.$refs['formData'].validate((valid) => {
                if (valid) {
                    this.activeStep = 2;
                }
            });
        },
        // 返回上一步
        prevStep() {
            this.activeStep = 1;
        },
        goBack() {
            this.$router.push({ path: '/UserManagement' })
        },
        /**获取行业类别列表*/
        getIndustrys() {
            this.$api.get(this.API.cpus + 'v3/consumer/manager/selectAllIndustry', {}, res => {
                this.industrys = res.data;
            })
        },
        //生成密码
        generatePass() {
            this.$confirms.confirmation('get', '确认要生成密码？', this.API.cpus + 'consumerclientinfo/generatePassword', {}, res => {
                this.formData.password = res
            });
        },
        // 新增编辑
        submitForm(val) {
            this.$refs[val].validate((valid, value) => {
                if (valid) {
                    let a = JSON.parse(JSON.stringify(this.formData));
                    a.mergeOperator = a.mergeOperator.join(",")
                    a.realNameInfo = {
                        companyName: a.companyName,
                        creditCode: a.creditCode,
                        legalPerson: a.legalPerson,
                        principalIdCard: a.principalIdCard,
                        principalName: a.principalName,
                        principalMobile: a.principalMobile,
                        signatureType: a.signatureType,
                        signatureSubType: a.signatureSubType,
                        imgUrl: a.imgUrl,
                        remark: a.remark,
                        contentExample: a.contentExample
                    }
                    if (a.signature) {
                        a.signature = `【${a.signature}】`
                    }
                    if (this.statusOf == '新增用户') {
                        if (!((a.realNameInfo.signatureType == 1 || a.realNameInfo.signatureType == 2) && a.realNameInfo.signatureSubType == 0)) {
                            if (a.realNameInfo.imgUrl == "") {
                                this.$message.error("请上传图片")
                                return
                            }
                        }
                        this.$confirms.confirmation("post", "确认创建新用户？", this.API.cpus + "v3/consumer/manager/user/create", a, res => {
                            if (res.code == 200) {
                                this.$router.push({ path: 'UserManagement' })
                            }
                        })
                    } else {
                        this.formData.userId = this.$route.query.id
                        delete a.realNameInfo

                        this.$confirms.confirmation("post", "确认编辑用户？", this.API.cpus + "v3/consumer/manager/user/info", a, res => {
                            if (res.code == 200) {
                                if (res.data.skip === true) {
                                    this.$confirm('修改默认签名需补充实名信息, 是否去补充?', '提示', {
                                        confirmButtonText: '去补充',
                                        cancelButtonText: '取消',
                                        type: 'warning'
                                    }).then(() => {
                                        this.$router.push({ path: 'UserDetails', query: { id: this.$route.query.id, type: "1", signature: res.data.signature } })
                                    }).catch(() => {
                                        this.$router.push({ path: 'UserManagement' })
                                    });
                                } else {
                                    this.$router.push({ path: 'UserManagement' })
                                }
                            }
                        })
                    }
                }
            });
        },
        //移除文件
        fileup(file, fileList) {
            if (file.response) {
                this.imgUrl.splice(this.imgUrl.indexOf(file.response.data.fullpath), 1);
                this.formData.imgUrl = this.imgUrl.join(",");
            } else {
                this.imgUrl.splice(this.imgUrl.indexOf(file.name), 1);
                this.formData.imgUrl = this.imgUrl.join(",");
            }
        },
        //文件上传成功
        fileupres(val) {
            if (val && val.data && val.data.fullpath) {
                this.imgUrl.push(val.data.fullpath);
                this.formData.imgUrl = this.imgUrl.join(",");
                console.log(this.formData.imgUrl, '1');
            } else {
                console.error('文件上传响应数据格式不正确', val);
                this.$message.error('文件上传失败，请重试');
            }
        },
        handleClose() {
            this.showImgDialogVisible = false;
        },
        showImg() {
            this.showImgDialogVisible = true;
        }
    },
    created() {
        if (this.$route.query.id) {
            this.dynamicWidth = '350px';
            this.statusOf = '编辑用户';
            this.$api.get(this.API.cpus + 'v3/consumer/manager/user/' + this.$route.query.id, {}, res => {
                res.data.smsReportLevel += ""
                this.formData = res.data
                if (res.data.pushRealNameType) {
                    this.formData.pushRealNameType = res.data.pushRealNameType + ""
                } else {
                    this.formData.pushRealNameType = "0"
                }
                if (res.data.mergeOperator) {
                    this.formData.mergeOperator = res.data.mergeOperator.split(",")
                } else {
                    this.formData.mergeOperator = []
                }
                if (res.data.signature) {
                    this.formData.signature = res.data.signature.replace(/【/g, "").replace(/】/g, "")
                    this.signatureCory = res.data.signature.replace(/【/g, "").replace(/】/g, "")
                } else {
                    this.formData.signature = ''
                    this.signatureCory = ''
                }
            })
        } else {
            this.dynamicWidth = '250px';
            this.statusOf = '新增用户';
            this.$api.get(this.API.cpus + 'v3/consumer/manager/user/ext', {}, res => {
                this.formData.ext = res.data
            })
        }
        this.getIndustrys(); /**获取行业类别列表*/
    },

} 
</script>
<style scoped>
.div_S {
    display: inline-block;
    width: 32%;
    float: left;
}

.div_S1 {
    display: inline-block;
    width: 49%;
    float: left;
}

.generatePassword {
    background-color: #16A589;
    border-color: #16A589;
    position: absolute;
    top: -18px;
    right: -93px;
}

.generatePassword:hover {
    background: rgb(69, 183, 161);
    border-color: rgb(69, 183, 161);
    color: #fff;
}

.input-w {
    width: 350px !important;
}

.fillet {
    padding: 20px;
}

.signature-container {
    min-height: 300px;
}

.input-w-2 {
    width: 320px !important;
}

.input-w-3 {
    width: 320px !important;
}

.input-w-4 {
    width: 190px !important;
}

.input-w-sm {
    width: 126px !important;
}

.input-w-f {
    width: 80px !important;
}

.float-div-1 {
    float: left;
}

.float-div-2 {
    float: left;
}

.float-div-3 {
    float: left;
}

.clear-div {
    clear: both;
}

.red {
    color: red;
}

.sig-type-title-tips {
    margin-top: 10px;
}

@media screen and (max-width:1845px) {
    .float-div-3 {
        float: none;
        clear: both;
    }
}
</style>
<style>
.user-modifi-cation .el-input-number__decrease,
.user-modifi-cation .el-input-number__increase {
    top: 1px !important
}

.radioLeft .el-form-item__content {
    margin-left: 74px !important;
}
</style>
