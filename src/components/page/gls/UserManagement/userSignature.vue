<template>
  <div class="modern-signature-page">
    <!-- 现代化页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button
            type="text"
            @click="goBack()"
            class="back-btn"
          >
            <i class="el-icon-arrow-left"></i>
            返回
          </el-button>
          <el-divider direction="vertical"></el-divider>
          <h1 class="page-title">{{ statusOf }}</h1>
        </div>
        <div class="header-right">
          <el-tag :type="statusOf === '添加签名' ? 'success' : 'warning'" size="medium">
            {{ statusOf === '添加签名' ? '新建签名' : '编辑签名' }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container">
        <!-- 签名配置表单 -->
        <el-form
          :model="signatureFrom.formData"
          :rules="signatureFrom.formRule"
          label-width="140px"
          ref="signatureFrom"
          class="modern-form"
        >
          <!-- 签名内容卡片 -->
          <el-card shadow="hover" class="form-card signature-content-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-edit-outline"></i>
                签名内容
              </span>
              <el-tooltip content="签名将显示在短信开头，用于标识发送方" placement="top">
                <i class="el-icon-question help-icon"></i>
              </el-tooltip>
            </div>

            <el-form-item label="签名内容" prop="signature" class="signature-input-item">
              <div class="signature-input-wrapper">
                <span class="signature-bracket left-bracket">【</span>
                <el-input
                  v-model="signatureFrom.formData.signature"
                  placeholder="签名限制中文16个字，英文32个字"
                  size="large"
                  class="signature-input"
                  maxlength="32"
                  show-word-limit
                >
                  <i slot="prefix" class="el-icon-edit"></i>
                </el-input>
                <span class="signature-bracket right-bracket">】</span>
              </div>
              <div class="signature-preview">
                <span class="preview-label">预览效果：</span>
                <span class="preview-text">【{{ signatureFrom.formData.signature || '您的签名' }}】您的短信内容...</span>
              </div>
            </el-form-item>
          </el-card>

          <!-- 签名来源卡片 -->
          <el-card shadow="hover" class="form-card signature-source-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-office-building"></i>
                签名来源
              </span>
              <el-tooltip content="选择与您签名内容相符的来源类型" placement="top">
                <i class="el-icon-question help-icon"></i>
              </el-tooltip>
            </div>

            <el-form-item label="签名来源" prop="signatureType" class="signature-source-form-item">
              <div class="signature-source-selector">
                <el-radio-group v-model="signatureFrom.formData.signatureType" class="source-radio-group">
                  <div class="source-option-card"
                       :class="{ 'active': signatureFrom.formData.signatureType === 1 }">
                    <el-radio :label="1" class="source-radio">
                      <div class="source-content">
                        <div class="source-header">
                          <i class="el-icon-office-building source-icon"></i>
                          <span class="source-title">企业名称</span>
                        </div>
                        <div class="source-description">
                          推荐全称，简称字数须为全称字数60%以上，尽量连续，不可改字
                        </div>
                      </div>
                    </el-radio>
                  </div>

                  <div class="source-option-card"
                       :class="{ 'active': signatureFrom.formData.signatureType === 2 }">
                    <el-radio :label="2" class="source-radio">
                      <div class="source-content">
                        <div class="source-header">
                          <i class="el-icon-school source-icon"></i>
                          <span class="source-title">事业单位</span>
                        </div>
                        <div class="source-description">
                          如机关、学校、科研单位、街道社区等
                        </div>
                      </div>
                    </el-radio>
                  </div>

                  <div class="source-option-card"
                       :class="{ 'active': signatureFrom.formData.signatureType === 3 }">
                    <el-radio :label="3" class="source-radio">
                      <div class="source-content">
                        <div class="source-header">
                          <i class="el-icon-medal source-icon"></i>
                          <span class="source-title">商标</span>
                        </div>
                        <div class="source-description">
                          须提供商标注册证书图片或在中国商标网的商标查询截图
                        </div>
                      </div>
                    </el-radio>
                  </div>

                  <div class="source-option-card"
                       :class="{ 'active': signatureFrom.formData.signatureType === 4 }">
                    <el-radio :label="4" class="source-radio">
                      <div class="source-content">
                        <div class="source-header">
                          <i class="el-icon-mobile-phone source-icon"></i>
                          <span class="source-title">App应用</span>
                        </div>
                        <div class="source-description">
                          须提供app在ICP/IP/域名备案管理系统的截图
                        </div>
                      </div>
                    </el-radio>
                  </div>

                  <div class="source-option-card"
                       :class="{ 'active': signatureFrom.formData.signatureType === 5 }">
                    <el-radio :label="5" class="source-radio">
                      <div class="source-content">
                        <div class="source-header">
                          <i class="el-icon-cpu source-icon"></i>
                          <span class="source-title">小程序</span>
                        </div>
                        <div class="source-description">
                          须提供小程序在ICP/IP/域名备案管理系统的截图
                        </div>
                      </div>
                    </el-radio>
                  </div>
                </el-radio-group>
              </div>
            </el-form-item>
          </el-card>
          <!-- 签名类型卡片 -->
          <el-card shadow="hover" class="form-card signature-type-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-price-tag"></i>
                签名类型
              </span>
              <el-tooltip content="选择使用全称还是简称" placement="top">
                <i class="el-icon-question help-icon"></i>
              </el-tooltip>
            </div>

            <el-form-item label="签名类型" prop="signatureSubType" class="signature-type-form-item">
              <div class="signature-type-selector">
                <el-radio-group v-model="signatureFrom.formData.signatureSubType" class="type-radio-group">
                  <div class="type-option-card"
                       :class="{ 'active': signatureFrom.formData.signatureSubType === 0 }">
                    <el-radio :label="0" class="type-radio">
                      <div class="type-content">
                        <div class="type-header">
                          <i class="el-icon-check type-icon"></i>
                          <span class="type-title">全称</span>
                          <el-tag type="success" size="mini" class="recommend-tag">推荐</el-tag>
                        </div>
                        <div class="type-description">
                          使用完整的企业/单位名称，报备速度快，通过率高
                        </div>
                      </div>
                    </el-radio>
                  </div>

                  <div class="type-option-card"
                       :class="{ 'active': signatureFrom.formData.signatureSubType === 1 }">
                    <el-radio :label="1" class="type-radio">
                      <div class="type-content">
                        <div class="type-header">
                          <i class="el-icon-edit type-icon"></i>
                          <span class="type-title">简称</span>
                          <el-tag type="warning" size="mini" class="note-tag">需证明</el-tag>
                        </div>
                        <div class="type-description">
                          请用企业/单位简称签名在企查查搜索企业唯一的图片，并按参照示例图提供
                        </div>
                      </div>
                    </el-radio>
                  </div>
                </el-radio-group>
              </div>
            </el-form-item>
          </el-card>
          <!-- 文件上传卡片 -->
          <el-card shadow="hover" class="form-card file-upload-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-upload"></i>
                上传证明材料
              </span>
              <div class="header-actions">
                <el-button type="text" @click="showImg" class="example-btn">
                  <i class="el-icon-view"></i>
                  查看示例
                </el-button>
              </div>
            </div>

            <el-form-item label="上传图片" class="upload-form-item">
              <div class="upload-section">
                <file-upload
                  :action="this.API.cpus + 'v3/file/upload'"
                  :limit="3"
                  listType="picture"
                  tip="格式要求：支持.jpg .jpeg .bmp .gif .png等格式照片，大小不超过2M"
                  :fileStyle="fileStyle"
                  :del="del1"
                  :fileListS="fileListS"
                  :showfileList="true"
                  @fileup="fileup"
                  @fileupres="fileupres"
                  class="modern-upload"
                >
                  选择上传文件
                </file-upload>
                <div class="upload-tips">
                  <el-alert
                    title="上传说明"
                    type="info"
                    :closable="false"
                    show-icon
                  >
                    <template slot="default">
                      <p>• 支持格式：jpg、jpeg、bmp、gif、png</p>
                      <p>• 文件大小：不超过2M</p>
                      <p>• 最多上传：3张图片</p>
                    </template>
                  </el-alert>
                </div>
              </div>
            </el-form-item>
          </el-card>

          <!-- 短信示例和备注卡片 -->
          <el-card shadow="hover" class="form-card example-remark-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-chat-line-square"></i>
                短信示例与备注
              </span>
            </div>

            <div class="form-row">
              <el-form-item label="短信示例" prop="contentExample" class="form-item-modern">
                <el-input
                  v-model="signatureFrom.formData.contentExample"
                  type="textarea"
                  placeholder="请输入短信示例；例如：【xxx】欢迎使用xxx，祝您使用愉快！"
                  maxlength="800"
                  show-word-limit
                  :rows="4"
                  class="modern-textarea"
                />
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item label="备注内容" prop="remark" class="form-item-modern">
                <el-input
                  type="textarea"
                  v-model="signatureFrom.formData.remark"
                  maxLength="50"
                  placeholder="请输入备注内容,50字以内"
                  :rows="3"
                  show-word-limit
                  class="modern-textarea"
                />
              </el-form-item>
            </div>
          </el-card>
          <!-- 企业信息卡片 -->
          <el-card shadow="hover" class="form-card company-info-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-office-building"></i>
                企业信息
              </span>
              <div class="header-actions">
                <el-button type="text" @click="clearPrincipalInfo" class="clear-btn">
                  <i class="el-icon-delete"></i>
                  清空信息
                </el-button>
              </div>
            </div>

            <div class="company-info-grid">
              <div class="form-row">
                <el-form-item label="企业名称" prop="companyName" class="form-item-modern">
                  <el-input
                    v-model="signatureFrom.formData.companyName"
                    placeholder="请输入企业名称"
                    size="large"
                    class="modern-input"
                  >
                    <i slot="prefix" class="el-icon-office-building"></i>
                  </el-input>
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item label="社会统一信用代码" prop="creditCode" class="form-item-modern">
                  <el-input
                    v-model="signatureFrom.formData.creditCode"
                    placeholder="请输入统一社会信用代码"
                    size="large"
                    class="modern-input"
                  >
                    <i slot="prefix" class="el-icon-postcard"></i>
                  </el-input>
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item label="企业法人" prop="legalPerson" class="form-item-modern">
                  <el-input
                    v-model="signatureFrom.formData.legalPerson"
                    placeholder="请输入企业法人姓名"
                    size="large"
                    class="modern-input"
                  >
                    <i slot="prefix" class="el-icon-user"></i>
                  </el-input>
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item label="责任人姓名" prop="principalName" class="form-item-modern">
                  <el-input
                    v-model="signatureFrom.formData.principalName"
                    placeholder="请输入负责人姓名"
                    size="large"
                    class="modern-input"
                  >
                    <i slot="prefix" class="el-icon-user-solid"></i>
                  </el-input>
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item label="责任人证件号码" prop="principalIdCard" class="form-item-modern">
                  <el-input
                    v-model="signatureFrom.formData.principalIdCard"
                    placeholder="请输入负责人身份证号"
                    size="large"
                    class="modern-input"
                  >
                    <i slot="prefix" class="el-icon-postcard"></i>
                  </el-input>
                </el-form-item>
              </div>

              <div class="form-row">
                <el-form-item label="责任人手机号" prop="principalMobile" class="form-item-modern">
                  <el-input
                    v-model="signatureFrom.formData.principalMobile"
                    placeholder="请输入责任人手机号"
                    size="large"
                    class="modern-input"
                  >
                    <i slot="prefix" class="el-icon-mobile-phone"></i>
                  </el-input>
                  <div class="input-tip">
                    <el-alert
                      title="须填写身份证号本人使用的手机号，否则报备失败"
                      type="warning"
                      :closable="false"
                      show-icon
                    />
                  </div>
                </el-form-item>
              </div>
            </div>
          </el-card>
        </el-form>

        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <div class="button-group">
            <el-button
              type="primary"
              size="large"
              @click="signature_add('signatureFrom', 'signatureFrom.title', '1')"
              class="submit-btn"
            >
              <i class="el-icon-check"></i>
              提交审核
            </el-button>
            <el-button
              size="large"
              @click="goBack"
              class="cancel-btn"
            >
              <i class="el-icon-close"></i>
              取消
            </el-button>
          </div>
        </div>

        <!-- 签名规范说明卡片 -->
        <el-card shadow="hover" class="form-card signature-rules-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-info"></i>
              签名规范说明
            </span>
          </div>
          <div class="rules-content">
            <el-alert
              title="重要提醒"
              type="warning"
              :closable="false"
              show-icon
            >
              <template slot="default">
                <div class="rules-list">
                  <div class="rule-item">
                    <strong>签名内容：</strong>公司名称或产品名称，字数要求中文在<span class="highlight">2-16个字符</span>，英文在<span class="highlight">2-32个字符</span>，不能使用空格和特殊符号" - + = * & % # @ ~等
                  </div>
                  <div class="rule-item">
                    <strong>签名审核：</strong>签名由客服人工审核，审核通过后可使用
                  </div>
                  <div class="rule-item">
                    <strong>签名规范：</strong>无须在签名内容前后添加【】、（）、{}，系统会自动添加
                  </div>
                </div>
              </template>
            </el-alert>
          </div>
        </el-card>
      </div>
    </div>
    <!-- 现代化图片示例弹窗 -->
    <el-dialog
      title="证明材料示例"
      :visible.sync="showImgDialogVisible"
      width="1000px"
      :before-close="handleClose"
      :close-on-press-escape="false"
      class="modern-dialog"
    >
      <div class="dialog-content">
        <!-- 温馨提示 -->
        <div class="tips-section">
          <el-alert
            title="温馨提示"
            type="info"
            :closable="false"
            show-icon
          >
            <template slot="default">
              <div class="tips-content">
                <p><strong>中国商标网查询网址：</strong>
                  <a href="https://sbj.cnipa.gov.cn/sbj/index.html" target="_blank" class="link">
                    https://sbj.cnipa.gov.cn/sbj/index.html
                  </a>
                </p>
                <p><strong>工信部备案管理系统：</strong>
                  <a href="https://beian.miit.gov.cn/#/Integrated/recordQuery" target="_blank" class="link">
                    https://beian.miit.gov.cn/#/Integrated/recordQuery
                  </a>
                </p>
              </div>
            </template>
          </el-alert>
        </div>

        <!-- 示例图片展示 -->
        <div class="examples-grid">
          <div class="example-item">
            <div class="example-card">
              <div class="example-header">
                <i class="el-icon-search"></i>
                <span class="example-title">唯一性示例图</span>
              </div>
              <div class="example-image">
                <el-image
                  :src="require('../../../../assets/images/qcc.png')"
                  :preview-src-list="[require('../../../../assets/images/qcc.png')]"
                  fit="cover"
                  class="demo-image"
                >
                </el-image>
              </div>
            </div>
          </div>

          <div class="example-item">
            <div class="example-card">
              <div class="example-header">
                <i class="el-icon-office-building"></i>
                <span class="example-title">营业执照示例</span>
              </div>
              <div class="example-image">
                <el-image
                  :src="require('../../../../assets/images/business.png')"
                  :preview-src-list="[require('../../../../assets/images/business.png')]"
                  fit="cover"
                  class="demo-image"
                >
                </el-image>
              </div>
            </div>
          </div>

          <div class="example-item">
            <div class="example-card">
              <div class="example-header">
                <i class="el-icon-medal"></i>
                <span class="example-title">商标示例</span>
              </div>
              <div class="example-image">
                <el-image
                  :src="require('../../../../assets/images/shangbiao.png')"
                  :preview-src-list="[require('../../../../assets/images/shangbiao.png')]"
                  fit="cover"
                  class="demo-image"
                >
                </el-image>
              </div>
            </div>
          </div>

          <div class="example-item">
            <div class="example-card">
              <div class="example-header">
                <i class="el-icon-mobile-phone"></i>
                <span class="example-title">App/小程序示例</span>
              </div>
              <div class="example-image">
                <el-image
                  :src="require('../../../../assets/images/productapp.png')"
                  :preview-src-list="[require('../../../../assets/images/productapp.png')]"
                  fit="cover"
                  class="demo-image"
                >
                </el-image>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传
export default {
  components: { FileUpload },
  name: "CreateSign",
  data() {
    return {
      statusOf: "", //是新增还是编辑
      fileListS: "", //回显图片的地址
      signatures: "", //签名
      roleId: "",
      signatureFrom: {
        title: "",
        formData: {
          signature: "",
          signatureType: 1,
          signatureSubType: 0,
          remark: "",
          imgUrl: "",
          contentExample: "",
          signatureId: "",
          // direct: 1,
          companyName: "",
          creditCode: "",
          legalPerson: "",
          principalName: "",
          principalIdCard: "",
          principalMobile: "",
        },
        imgUrl: [],
        formRule: {
          signature: [
            { required: true, message: "该输入项为必填项!", trigger: "blur" },
            {
              validator: (_, value, callback) => {
                const englishPattern = /^[a-zA-Z\s]*$/; // 仅英文字符
                const chinesePattern = /^[\u4e00-\u9fa5\s]*$/; // 仅中文字符
                const mixedPattern = /^(?!\d+$)([a-zA-Z\u4e00-\u9fa5\s\d]+)$/; // 不包含特殊字符（仅限字母和汉字）
                if (value) {
                  // 检查是否为纯英文
                  if (englishPattern.test(value)) {
                    if (value.length > 32) {
                      callback(new Error("英文长度不能超过32个字符"));
                    } else {
                      callback(); // 验证通过
                    }
                  }
                  // 检查是否为纯中文
                  else if (chinesePattern.test(value)) {
                    if (value.length > 16) {
                      callback(new Error("中文长度不能超过16个字符"));
                    } else {
                      callback(); // 验证通过
                    }
                  }
                  // 检查是否为中英组合
                  else if (mixedPattern.test(value)) {
                    if (value.length > 16) {
                      callback(new Error("中英文组合长度不能超过16个字符"));
                    } else {
                      callback(); // 验证通过
                    }
                  } else {
                    callback(new Error("请输入有效的字符（英文或中文）"));
                  }
                } else {
                  callback(new Error("该输入项为必填项!"));
                }
              },
              trigger: ["blur", "change"],
            },
          ],
          signatureType: [
            { required: true, message: "请选择签名来源", trigger: "change" },
          ],
          signatureSubType: [
            { required: true, message: "请选择签名类型", trigger: "change" },
          ],
          imgUrl: [
            { required: true, message: "请选择上传图片", trigger: "change" },
          ],
          contentExample: [
            { required: true, message: "该输入短信示例", trigger: "blur" },
            {
              min: 1,
              max: 800,
              message: "长度在 1 到 800 个字符",
              trigger: ["blur", "change"],
            },
          ],
          // direct: [
          //   { required: true, message: "请选择主体", trigger: "change" },
          // ],
          companyName: [
            { required: true, message: '请输入企业名称', trigger: 'change' },
          ],
          creditCode: [
            { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
          ],
          legalPerson: [
            { required: true, message: '请输入法人姓名', trigger: 'change' },
          ],
          principalIdCard: [
            { required: true, message: '请输入负责人身份证号', trigger: 'change' },
          ],
          principalName: [
            { required: true, message: '请输入负责人姓名', trigger: 'change' },
          ],
          principalMobile: [
            { required: true, message: '请输入责任人手机号', trigger: 'change' },
          ],
        },
        signature: "", //签名
        signatureId: "", //签名id
      },
      //上传文件格式
      fileStyle: {
        size: 5,
        type: "img",
        style: ["jpg", "jpeg", "bmp", "gif", "png"],
      },
      del1: true, //关闭弹框时清空图片
      showImgDialogVisible: false, //查看图片示例弹窗
    };
  },
  created() {
    let userInfo = JSON.parse(localStorage.getItem("userInfo"));
    this.roleId = userInfo.roleId;
    if (this.$route.query.id && this.$route.query.d) {
      this.statusOf = "编辑签名";
      this.handleEdit();
    } else {
      this.statusOf = "添加签名";
      this.getRealNameInfo()
    }
  },
  methods: {
    //获取编辑信息
    //编辑的赋值
    getRealNameInfo() {
      try {
        this.$api.get(
          this.API.cpus + "v3/consumer/manager/realName/" + this.$route.query.id,
          {},
          (res) => {
            if (res.code == 200) {
              if (res.data) {
                this.signatureFrom.formData.companyName = res.data.companyName;
                this.signatureFrom.formData.creditCode = res.data.creditCode;
                this.signatureFrom.formData.legalPerson = res.data.legalPerson;
                this.signatureFrom.formData.principalName = res.data.principalName;
                this.signatureFrom.formData.principalIdCard = res.data.principalIdCard;
                this.signatureFrom.formData.principalMobile = res.data.principalMobile;
              }
            } else {
              this.$message.error(res.msg);
            }
          }
        );
      } catch (error) {
        console.log(error, "err");
      }
    },
    clearPrincipalInfo() {
      this.signatureFrom.formData.companyName = "";
      this.signatureFrom.formData.creditCode = "";
      this.signatureFrom.formData.legalPerson = "";
      this.signatureFrom.formData.principalName = "";
      this.signatureFrom.formData.principalIdCard = "";
      this.signatureFrom.formData.principalMobile = "";
      // if (val == 1) {
      //   this.getRealNameInfo()
      // } else {

      // }

    },
    handleEdit() {
      let b = this.$route.query.d;
      //获取上传的图片地址
      this.$api.get(
        this.API.cpus + "signature/findModel?signatureId=" + b + "&userId=" + this.$route.query.id,
        {},
        (res) => {
          try {
            let aa = res.data.signature;
            this.signatureFrom.signature = aa.slice(1, aa.length - 1);
            if (res.data.imgUrl) {
              this.fileListS = res.data.imgUrl;
              // console.log(this.fileListS,'fileListS');
              let a = res.data.imgUrl.split(",");
              for (let i = 0; i < a.length; i++) {
                if (a[i] != "") {
                  this.signatureFrom.imgUrl.push(a[i]);
                }
              }
            } else {
              this.fileListS = '';
            }
            //存储签名
            this.signatures = res.data.signature;
            this.signatureFrom.formData.imgUrl =
              this.signatureFrom.imgUrl.join(",");
            Object.assign(this.signatureFrom.formData, res.data);
            this.signatureFrom.formData.signature =
              this.signatureFrom.signature;
            this.signatureFrom.formData.signatureType =
              res.data.signatureType;
          } catch (error) {
            console.log(error, "err");
          }
        }
      );
      // console.log(val);
      // this.$nextTick(()=>{
      //     this.$refs.signatureFrom.resetFields(); //清空表单
      //     Object.assign(this.signatureFrom.formData,val);

      // });
    },
    //返回
    goBack() {
      this.$router.go(-1);
    },
    //提交表单
    signature_add(formName, _, val) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let formDates = {};
          Object.assign(formDates, this.signatureFrom.formData);
          formDates.signature =
            "【" + this.signatureFrom.formData.signature + "】";
          delete formDates.signatureId;
          formDates.auditStatus = val;
          formDates.userId = this.$route.query.id;
          if (this.statusOf == "添加签名") {
            if (!((this.signatureFrom.formData.signatureType == 1 || this.signatureFrom.formData.signatureType == 2) && this.signatureFrom.formData.signatureSubType == 0)){
              console.log(this.signatureFrom.formData.imgUrl,'this.signatureFrom.formData.imgUrl');
              
              if (this.signatureFrom.formData.imgUrl != "") {
                this.sendRequest(
                  "post",
                  "确定新增签名",
                  this.API.cpus + "v3/consumer/manager/user/signature/saveInfo",
                  formDates
                );
              } else {
                this.$message({
                  message: "请上传图片！",
                  type: "warning",
                });
              }
            } else {
              this.sendRequest(
                "post",
                "确定新增签名",
                this.API.cpus + "v3/consumer/manager/user/signature/saveInfo",
                formDates
              );
            }
          } else {
            formDates.signatureId = this.$route.query.d;
            formDates.auditStatus = val;
            if (!((this.signatureFrom.formData.signatureType == 1 || this.signatureFrom.formData.signatureType == 2) && this.signatureFrom.formData.signatureSubType == 0)) {
              if (this.signatureFrom.formData.imgUrl != "") {
                this.$confirms.confirmation(
                  "put",
                  "确定修改签名",
                  this.API.cpus + "signature/update",
                  formDates,
                  (res) => {
                    // this.gettableData();
                    // this.SigDialogVisible = false;//隐藏弹窗
                    if (res.code == 200) {
                      this.signatureFrom.formData = {
                        signature: "",
                        signatureType: 1,
                        remark: "",
                        imgUrl: "",
                        signatureId: "",
                      };
                      this.$router.push({ path: "subSignatureManagement" });
                    }
                    // this.$router.push({ path: "SignatureManagement" });
                  }
                );
              } else {
                this.$message({
                  message: "请上传图片！",
                  type: "warning",
                });
              }
            } else {
              this.$confirms.confirmation(
                "put",
                "确定修改签名",
                this.API.cpus + "signature/update",
                formDates,
                (res) => {
                  // this.gettableData();
                  // this.SigDialogVisible = false;//隐藏弹窗
                  if (res.code == 200) {
                    this.signatureFrom.formData = {
                      signature: "",
                      signatureType: 1,
                      remark: "",
                      imgUrl: "",
                      signatureId: "",
                    };
                    this.$router.push({ path: "subSignatureManagement" });
                  }
                }
              );
            }
          }

        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //发送编辑和新增的请求
    sendRequest(type, title, action, formDates) {
      //验证签名是否存在
      this.$confirms.confirmation(
        type,
        title,
        action,
        formDates,
        (res) => {
          if (res.code == 200) {
            this.$router.push({ path: "UserDetails", query: { id: this.$route.query.id, type: '1' } });
          }
        }
      );
      // this.$api.get(
      //   this.API.cpus +
      //   "signature/findModelBySignature?signature=【" +
      //   this.signatureFrom.formData.signature +
      //   "】",
      //   {},
      //   (res) => {
      //     if (res.code == 200 && res.data == "0") {
      //       //发送编辑和新增的请求

      //     } else {
      //       this.$message({
      //         message: "签名已存在，切勿重复！",
      //         type: "warning",
      //       });
      //     }
      //   }
      // );
    },
    //移除文件
    fileup(file, _) {
      if (file.response) {
        this.signatureFrom.imgUrl.splice(this.signatureFrom.imgUrl.indexOf(file.response.data.fullpath), 1);
        this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(",");
      } else {
        this.signatureFrom.imgUrl.splice(this.signatureFrom.imgUrl.indexOf(file.name), 1);
        this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(",");
      }
      // if (val2.length) {
      //   if (
      //     this.signatureFrom.formData.imgUrl.indexOf(
      //       val2[0].response.data.fullpath
      //     ) == -1
      //   ) {
      //     let aa = this.signatureFrom.formData.imgUrl.split(",");
      //     if (val.response) {
      //       aa.splice(aa.indexOf(val.response.fullPath), 1);
      //     } else {
      //       let c = val.url;
      //       let d = c.slice(c.indexOf("group1"));
      //       aa.splice(aa.indexOf(d), 1);
      //     }
      //     this.signatureFrom.imgUrl = aa;
      //     console.log(this.signatureFrom.imgUrl, 'this.signatureFrom.imgUrl');

      //     this.signatureFrom.formData.imgUrl = aa.join(",");
      //   }
      // }else{
      //   this.signatureFrom.formData.imgUrl = "";
      //   this.signatureFrom.imgUrl = [];
      // }

    },
    //文件上传成功
    fileupres(val) {
      if (val && val.data && val.data.fullpath) {
        this.signatureFrom.imgUrl.push(val.data.fullpath);
        this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(",");
      } else {
        console.error('文件上传响应数据格式不正确', val);
        this.$message.error('文件上传失败，请重试');
      }
    },
    handleClose() {
      this.showImgDialogVisible = false;
    },
    showImg() {
      this.showImgDialogVisible = true;
    },
    // showImgDialogVisible() {
    //   this.showImgDialogVisible = false;
    // },
  },
};
</script>
<style lang="less" scoped>
// 引入通用签名样式
@import '~@/styles/signature-common.less';
</style>