<template>
  <div class="user-modifi-cation bag">
    <div class="Top_title" style="padding: 10px">
      <span style="
            display: inline-block;
            padding-right: 10px;
            cursor: pointer;
            color: #16a589;
          " @click="goBack()"><i class="el-icon-arrow-left"></i>返回</span>|
      <span> {{ statusOf }}</span>
    </div>
    <div class="fillet">
      <div class="sign">
        <el-form :model="signatureFrom.formData" :rules="signatureFrom.formRule" label-width="140px"
          style="padding-right: 14px; margin-top: 20px" ref="signatureFrom">
          <el-form-item label="签名内容" prop="signature" style="position: relative">
            <el-input v-model="signatureFrom.formData.signature" placeholder="签名限制中文16个字，英文32个字"></el-input>
            <span style="position: absolute; top: 2px; left: 0px">【</span>
            <span style="position: absolute; top: 2px; right: 0px">】</span>
          </el-form-item>

          <el-form-item label="签名来源" class="sig-type" prop="signatureType">
            <el-radio-group v-model="signatureFrom.formData.signatureType">
              <el-radio :label="1">
                <span class="sig-type-title-tips">企业名称</span>
                <span style="color: #999;font-size: 12px;margin-left: 8px;">tips:推荐全称，简称字数须为全称字数60%以上，尽量连续，不可改字</span>
                <!-- （须提供营业执照图片） -->
                <!-- （须提供营业执照图片） -->
              </el-radio>
              <el-radio :label="2">
                <span class="sig-type-title-tips">事业单位：如机关，学校，科研单位，街道社区等</span>
              </el-radio>
              <el-radio :label="3">
                <span class="sig-type-title-tips">商标</span>
                （须提供商标注册证书图片或在在中国商标网的商标查询截图）
              </el-radio>
              <el-radio :label="4">
                <span class="sig-type-title-tips">App </span>
                （须提供app在ICP/IP/域名备案管理系统的截图）
              </el-radio>
              <el-radio :label="5">
                <span class="sig-type-title-tips">小程序</span>
                （须提供小程序在ICP/IP/域名备案管理系统的截图）
              </el-radio>
              <!-- <el-radio :label="6">
                <span class="sig-type-title-tips">公众号</span>
                （须提供小程序与主体公司存在关联关系的证明材图片）
              </el-radio> -->
              <!-- <el-radio :label="7">
                <span class="sig-type-title-tips">网站</span>
                （须提网站在ICP/IP/域名备案管理系统截图，仅限事业单位的网站）
              </el-radio> -->
            </el-radio-group>
          </el-form-item>
          <el-form-item label="签名类型" prop="signatureSubType">
            <el-radio-group v-model="signatureFrom.formData.signatureSubType">
              <el-radio :label="0">
                <span class="sig-type-title-tips">全称</span>
                <span style="color: #999;font-size: 12px;margin-left: 8px;">tips:推荐,报备快</span>
              </el-radio>
              <el-radio :label="1">
                <span class="sig-type-title-tips">简称</span>
                <span style="color: #999;font-size: 12px;margin-left: 8px;">tips:请用企业/单位简称签名在企查查搜索企业唯一的图片，并按参照示例图提供</span>
              </el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="上传图片">
            <file-upload style="display: inline-block" :action="this.API.cpus + 'v3/file/upload'" :limit="3"
              listType="picture" tip="格式要求：支持.jpg .jpeg .bmp .gif .png等格式照片，大小不超过2M" :fileStyle="fileStyle" :del="del1"
              :fileListS="fileListS" :showfileList="true" @fileup="fileup" @fileupres="fileupres">选择上传文件</file-upload>
            <span style="margin-left: 18px;color: #409EFF;cursor: pointer;" @click="showImg">查看图片示例</span>
          </el-form-item>
          <el-form-item label="短信示例" prop="contentExample">
            <el-input style="height: 100%" v-model="signatureFrom.formData.contentExample" type="textarea"
              placeholder="请输入短信示例；例如：【xxx】欢迎使用xxx，祝您使用愉快！" maxlength="800" show-word-limit></el-input>
          </el-form-item>
          <el-form-item label="备注内容" prop="remark">
            <el-input type="textarea" v-model="signatureFrom.formData.remark" maxLength="50"
              placeholder="请输入备注内容,50字以内"></el-input>
          </el-form-item>
          <!-- <el-form-item v-if="!$route.query.d" label="选择主体" prop="direct">
            <el-radio-group v-model="signatureFrom.formData.direct" @change="changeDirect">
              <el-radio :label="1">当前主体</el-radio>
              <el-radio :label="0">其他主体</el-radio>
            </el-radio-group>
            <span style="margin-left: 15px;color: #E6A23C;font-size: 15px;"><i class="el-icon-info"></i>
              选择"其他主体"后，需填写企业名称、社会统一信用代码、企业法人、负责人姓名、负责人身份证号等信息</span>
          </el-form-item> -->
          <div>
            <div>
              <el-form-item label="企业名称" prop="companyName" key="companyName">
                <el-input class="input-w" placeholder="请输入企业名称" v-model="signatureFrom.formData.companyName"></el-input>
              </el-form-item>
              <el-form-item label="社会统一信用代码" prop="creditCode" key="creditCode">
                <el-input class="input-w" placeholder="请输入统一社会信用代码"
                  v-model="signatureFrom.formData.creditCode"></el-input>
              </el-form-item>
              <el-form-item label="企业法人" prop="legalPerson" key="legalPerson">
                <el-input class="input-w" placeholder="请输入企业法人姓名"
                  v-model="signatureFrom.formData.legalPerson"></el-input>
              </el-form-item>
              <el-form-item label="责任人姓名" prop="principalName" key="principalName">
                <el-input class="input-w" placeholder="请输入负责人姓名"
                  v-model="signatureFrom.formData.principalName"></el-input>
              </el-form-item>
              <el-form-item label="责任人证件号码" prop="principalIdCard" key="principalIdCard">
                <el-input class="input-w" placeholder="请输入负责人身份证号"
                  v-model="signatureFrom.formData.principalIdCard"></el-input>
                <el-tooltip class="item" effect="dark" content="如果您想使用其他主体，请点击删除按钮" placement="top">
                  <i class="el-icon-delete" style="margin-left: 10px;color: #F56C6C;font-size: 15px;cursor: pointer;"
                    @click="clearPrincipalInfo"></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="责任人手机号" prop="principalMobile" key="principalMobile">
                  <el-input class="input-w" placeholder="请输入责任人手机号"
                    v-model="signatureFrom.formData.principalMobile"></el-input>
                <el-tooltip class="item" effect="dark" content="须填写身份证号本人使用的手机号，否则报备失败" placement="top">
                  <i style="margin-left: 10px;color: #409EFF;font-size: 15px;cursor: pointer;" class="el-icon-info"></i>
                </el-tooltip>
              </el-form-item>
            </div>
            <!-- <div v-else-if="signatureFrom.formData.direct == 1">
              <el-form-item label="企业名称" prop="">
                <el-input disabled class="input-w" placeholder="请输入企业名称"
                  v-model="signatureFrom.formData.companyName"></el-input>
              </el-form-item>
              <el-form-item label="社会统一信用代码" prop="">
                <el-input disabled class="input-w" placeholder="请输入统一社会信用代码"
                  v-model="signatureFrom.formData.creditCode"></el-input>
              </el-form-item>
              <el-form-item label="企业法人" prop="">
                <el-input disabled class="input-w" placeholder="请输入企业法人姓名"
                  v-model="signatureFrom.formData.legalPerson"></el-input>
              </el-form-item>
              <el-form-item label="责任人姓名" prop="">
                <el-input disabled class="input-w" placeholder="请输入负责人姓名"
                  v-model="signatureFrom.formData.principalName"></el-input>
              </el-form-item>
              <el-form-item label="责任人证件号码" prop="">
                <el-input disabled class="input-w" placeholder="请输入负责人身份证号"
                  v-model="signatureFrom.formData.principalIdCard"></el-input>
              </el-form-item>
            </div> -->
          </div>

        </el-form>
        <span slot="footer" class="dialog-footer" style="margin-left: 80px">
          <el-button type="primary" @click="signature_add('signatureFrom', 'signatureFrom.title', '1')">提交审核</el-button>
          <!-- <el-button type="primary"
            @click="signature_add('signatureFrom', 'signatureFrom.title', '0')">保存但不提交</el-button> -->
          <el-button @click="goBack">取 消</el-button>
        </span>
        <div class="Signature-matter">
          <p style="font-weight: bolder">签名规范：</p>
          <p>
            • 签名内容：公司名称或产品名称，字数要求中文在<span style="color: #ff0000">2-16个字符</span>，英文在<span
              style="color: #ff0000">2-32个字符</span>，不能使用空格和特殊符号" - + = * & % # @ ~等;
          </p>
          <p>• 签名审核：签名由客服人工审核，审核通过后可使用，</p>
          <p>• 签名规范：无须在签名内容前后添加【】、（）、{}、以避免重复。</p>
        </div>
      </div>
    </div>
    <!-- //查看图片示例弹窗 -->
    <el-dialog title="查看图片示例" :visible.sync="showImgDialogVisible" width="900px" :before-close="handleClose"
      :close-on-press-escape="false">
      <div class="tips">
        <div class="tips-title">温馨提示：</div>
        <div>中国商标网查询网址为：<a href="https://sbj.cnipa.gov.cn/sbj/index.html"
            target="_blank">https://sbj.cnipa.gov.cn/sbj/index.html</a></div>
        <p>工信部 ICP/IP地址/域名信息备案管理系统网址为：<a href="https://beian.miit.gov.cn/#/Integrated/recordQuery"
            target="_blank">https://beian.miit.gov.cn/#/Integrated/recordQuery</a></p>
      </div>
      <div style="display: flex;justify-content: space-around;">
        <div style="display: flex;flex-direction: column;align-items: center;margin-right: 20px;">
          <div style="margin-bottom: 10px;">唯一性示例图</div>
          <el-image style="width: 200px; height: 200px" :src="require('../../../../assets/images/qcc.png')"
            :preview-src-list="[require('../../../../assets/images/qcc.png')]">
          </el-image>
        </div>
        <div style="display: flex;flex-direction: column;align-items: center;margin-right: 20px;">
          <div style="margin-bottom: 10px;">营业执照示例</div>
          <el-image style="width: 200px; height: 200px" :src="require('../../../../assets/images/business.png')"
            :preview-src-list="[require('../../../../assets/images/business.png')]">
          </el-image>
        </div>
        <div style="display: flex;flex-direction: column;align-items: center;margin-right: 20px;">
          <div style="margin-bottom: 10px;">商标示例</div>
          <el-image style="width: 200px; height: 200px" :src="require('../../../../assets/images/shangbiao.png')"
            :preview-src-list="[require('../../../../assets/images/shangbiao.png')]">
          </el-image>
        </div>
        <div style="display: flex;flex-direction: column;align-items: center;margin-right: 20px;">
          <div style="margin-bottom: 10px;">app、微信小程序示例</div>
          <el-image style="width: 200px; height: 200px" :src="require('../../../../assets/images/productapp.png')"
            :preview-src-list="[require('../../../../assets/images/productapp.png')]">
          </el-image>
        </div>
        <!-- <div style="display: flex;flex-direction: column;align-items: center;margin-right: 20px;">
          <div style="margin-bottom: 10px;">微信小程序示例</div>
          <el-image style="width: 200px; height: 200px" :src="require('../../../../../../assets/images/wxapp.png')"
            :preview-src-list="[require('../../../../../../assets/images/wxapp.png')]">
          </el-image>
        </div> -->
      </div>
    </el-dialog>
  </div>
</template>
<script>
import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传
export default {
  components: { FileUpload },
  name: "CreateSign",
  data() {
    return {
      statusOf: "", //是新增还是编辑
      fileListS: "", //回显图片的地址
      signatures: "", //签名
      roleId: "",
      signatureFrom: {
        title: "",
        formData: {
          signature: "",
          signatureType: 1,
          signatureSubType: 0,
          remark: "",
          imgUrl: "",
          contentExample: "",
          signatureId: "",
          // direct: 1,
          companyName: "",
          creditCode: "",
          legalPerson: "",
          principalName: "",
          principalIdCard: "",
          principalMobile: "",
        },
        imgUrl: [],
        formRule: {
          signature: [
            { required: true, message: "该输入项为必填项!", trigger: "blur" },
            {
              validator: (rule, value, callback) => {
                const englishPattern = /^[a-zA-Z\s]*$/; // 仅英文字符
                const chinesePattern = /^[\u4e00-\u9fa5\s]*$/; // 仅中文字符
                const mixedPattern = /^(?!\d+$)([a-zA-Z\u4e00-\u9fa5\s\d]+)$/; // 不包含特殊字符（仅限字母和汉字）
                if (value) {
                  // 检查是否为纯英文
                  if (englishPattern.test(value)) {
                    if (value.length > 32) {
                      callback(new Error("英文长度不能超过32个字符"));
                    } else {
                      callback(); // 验证通过
                    }
                  }
                  // 检查是否为纯中文
                  else if (chinesePattern.test(value)) {
                    if (value.length > 16) {
                      callback(new Error("中文长度不能超过16个字符"));
                    } else {
                      callback(); // 验证通过
                    }
                  }
                  // 检查是否为中英组合
                  else if (mixedPattern.test(value)) {
                    if (value.length > 16) {
                      callback(new Error("中英文组合长度不能超过16个字符"));
                    } else {
                      callback(); // 验证通过
                    }
                  } else {
                    callback(new Error("请输入有效的字符（英文或中文）"));
                  }
                } else {
                  callback(new Error("该输入项为必填项!"));
                }
              },
              trigger: ["blur", "change"],
            },
          ],
          signatureType: [
            { required: true, message: "请选择签名来源", trigger: "change" },
          ],
          signatureSubType: [
            { required: true, message: "请选择签名类型", trigger: "change" },
          ],
          imgUrl: [
            { required: true, message: "请选择上传图片", trigger: "change" },
          ],
          contentExample: [
            { required: true, message: "该输入短信示例", trigger: "blur" },
            {
              min: 1,
              max: 800,
              message: "长度在 1 到 800 个字符",
              trigger: ["blur", "change"],
            },
          ],
          // direct: [
          //   { required: true, message: "请选择主体", trigger: "change" },
          // ],
          companyName: [
            { required: true, message: '请输入企业名称', trigger: 'change' },
          ],
          creditCode: [
            { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
          ],
          legalPerson: [
            { required: true, message: '请输入法人姓名', trigger: 'change' },
          ],
          principalIdCard: [
            { required: true, message: '请输入负责人身份证号', trigger: 'change' },
          ],
          principalName: [
            { required: true, message: '请输入负责人姓名', trigger: 'change' },
          ],
          principalMobile: [
            { required: true, message: '请输入责任人手机号', trigger: 'change' },
          ],
        },
        signature: "", //签名
        signatureId: "", //签名id
      },
      //上传文件格式
      fileStyle: {
        size: 5,
        type: "img",
        style: ["jpg", "jpeg", "bmp", "gif", "png"],
      },
      del1: true, //关闭弹框时清空图片
      showImgDialogVisible: false, //查看图片示例弹窗
    };
  },
  created() {
    let userInfo = JSON.parse(localStorage.getItem("userInfo"));
    this.roleId = userInfo.roleId;
    if (this.$route.query.id && this.$route.query.d) {
      this.statusOf = "编辑签名";
      this.handleEdit();
    } else {
      this.statusOf = "添加签名";
      this.getRealNameInfo()
    }
  },
  methods: {
    //获取编辑信息
    //编辑的赋值
    getRealNameInfo() {
      try {
        this.$api.get(
          this.API.cpus + "v3/consumer/manager/realName/" + this.$route.query.id,
          {},
          (res) => {
            if (res.code == 200) {
              if (res.data) {
                this.signatureFrom.formData.companyName = res.data.companyName;
                this.signatureFrom.formData.creditCode = res.data.creditCode;
                this.signatureFrom.formData.legalPerson = res.data.legalPerson;
                this.signatureFrom.formData.principalName = res.data.principalName;
                this.signatureFrom.formData.principalIdCard = res.data.principalIdCard;
                this.signatureFrom.formData.principalMobile = res.data.principalMobile;
              }
            } else {
              this.$message.error(res.msg);
            }
          }
        );
      } catch (error) {
        console.log(error, "err");
      }
    },
    clearPrincipalInfo() {
      this.signatureFrom.formData.companyName = "";
      this.signatureFrom.formData.creditCode = "";
      this.signatureFrom.formData.legalPerson = "";
      this.signatureFrom.formData.principalName = "";
      this.signatureFrom.formData.principalIdCard = "";
      this.signatureFrom.formData.principalMobile = "";
      // if (val == 1) {
      //   this.getRealNameInfo()
      // } else {

      // }

    },
    handleEdit() {
      let b = this.$route.query.d;
      //获取上传的图片地址
      this.$api.get(
        this.API.cpus + "signature/findModel?signatureId=" + b + "&userId=" + this.$route.query.id,
        {},
        (res) => {
          try {
            let aa = res.data.signature;
            this.signatureFrom.signature = aa.slice(1, aa.length - 1);
            if (res.data.imgUrl) {
              this.fileListS = res.data.imgUrl;
              // console.log(this.fileListS,'fileListS');
              let a = res.data.imgUrl.split(",");
              for (let i = 0; i < a.length; i++) {
                if (a[i] != "") {
                  this.signatureFrom.imgUrl.push(a[i]);
                }
              }
            } else {
              this.fileListS = '';
            }
            //存储签名
            this.signatures = res.data.signature;
            this.signatureFrom.formData.imgUrl =
              this.signatureFrom.imgUrl.join(",");
            Object.assign(this.signatureFrom.formData, res.data);
            this.signatureFrom.formData.signature =
              this.signatureFrom.signature;
            this.signatureFrom.formData.signatureType =
              res.data.signatureType;
          } catch (error) {
            console.log(error, "err");
          }
        }
      );
      // console.log(val);
      // this.$nextTick(()=>{
      //     this.$refs.signatureFrom.resetFields(); //清空表单
      //     Object.assign(this.signatureFrom.formData,val);

      // });
    },
    //返回
    goBack() {
      this.$router.go(-1);
    },
    //提交表单
    signature_add(formName, title, val) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let formDates = {};
          Object.assign(formDates, this.signatureFrom.formData);
          formDates.signature =
            "【" + this.signatureFrom.formData.signature + "】";
          delete formDates.signatureId;
          formDates.auditStatus = val;
          formDates.userId = this.$route.query.id;
          if (this.statusOf == "添加签名") {
            if (!((this.signatureFrom.formData.signatureType == 1 || this.signatureFrom.formData.signatureType == 2) && this.signatureFrom.formData.signatureSubType == 0)){
              console.log(this.signatureFrom.formData.imgUrl,'this.signatureFrom.formData.imgUrl');
              
              if (this.signatureFrom.formData.imgUrl != "") {
                this.sendRequest(
                  "post",
                  "确定新增签名",
                  this.API.cpus + "v3/consumer/manager/user/signature/saveInfo",
                  formDates
                );
              } else {
                this.$message({
                  message: "请上传图片！",
                  type: "warning",
                });
              }
            } else {
              this.sendRequest(
                "post",
                "确定新增签名",
                this.API.cpus + "v3/consumer/manager/user/signature/saveInfo",
                formDates
              );
            }
          } else {
            formDates.signatureId = this.$route.query.d;
            formDates.auditStatus = val;
            if (!((this.signatureFrom.formData.signatureType == 1 || this.signatureFrom.formData.signatureType == 2) && this.signatureFrom.formData.signatureSubType == 0)) {
              if (this.signatureFrom.formData.imgUrl != "") {
                this.$confirms.confirmation(
                  "put",
                  "确定修改签名",
                  this.API.cpus + "signature/update",
                  formDates,
                  (res) => {
                    // this.gettableData();
                    // this.SigDialogVisible = false;//隐藏弹窗
                    if (res.code == 200) {
                      this.signatureFrom.formData = {
                        signature: "",
                        signatureType: 1,
                        remark: "",
                        imgUrl: "",
                        signatureId: "",
                      };
                      this.$router.push({ path: "subSignatureManagement" });
                    }
                    // this.$router.push({ path: "SignatureManagement" });
                  }
                );
              } else {
                this.$message({
                  message: "请上传图片！",
                  type: "warning",
                });
              }
            } else {
              this.$confirms.confirmation(
                "put",
                "确定修改签名",
                this.API.cpus + "signature/update",
                formDates,
                (res) => {
                  // this.gettableData();
                  // this.SigDialogVisible = false;//隐藏弹窗
                  if (res.code == 200) {
                    this.signatureFrom.formData = {
                      signature: "",
                      signatureType: 1,
                      remark: "",
                      imgUrl: "",
                      signatureId: "",
                    };
                    this.$router.push({ path: "subSignatureManagement" });
                  }
                }
              );
            }
          }

        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //发送编辑和新增的请求
    sendRequest(type, title, action, formDates) {
      //验证签名是否存在
      this.$confirms.confirmation(
        type,
        title,
        action,
        formDates,
        (res) => {
          if (res.code == 200) {
            this.$router.push({ path: "UserDetails", query: { id: this.$route.query.id, type: '1' } });
          }
        }
      );
      // this.$api.get(
      //   this.API.cpus +
      //   "signature/findModelBySignature?signature=【" +
      //   this.signatureFrom.formData.signature +
      //   "】",
      //   {},
      //   (res) => {
      //     if (res.code == 200 && res.data == "0") {
      //       //发送编辑和新增的请求

      //     } else {
      //       this.$message({
      //         message: "签名已存在，切勿重复！",
      //         type: "warning",
      //       });
      //     }
      //   }
      // );
    },
    //移除文件
    fileup(file, fileList) {
      if (file.response) {
        this.signatureFrom.imgUrl.splice(this.signatureFrom.imgUrl.indexOf(file.response.data.fullpath), 1);
        this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(",");
      } else {
        this.signatureFrom.imgUrl.splice(this.signatureFrom.imgUrl.indexOf(file.name), 1);
        this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(",");
      }
      // if (val2.length) {
      //   if (
      //     this.signatureFrom.formData.imgUrl.indexOf(
      //       val2[0].response.data.fullpath
      //     ) == -1
      //   ) {
      //     let aa = this.signatureFrom.formData.imgUrl.split(",");
      //     if (val.response) {
      //       aa.splice(aa.indexOf(val.response.fullPath), 1);
      //     } else {
      //       let c = val.url;
      //       let d = c.slice(c.indexOf("group1"));
      //       aa.splice(aa.indexOf(d), 1);
      //     }
      //     this.signatureFrom.imgUrl = aa;
      //     console.log(this.signatureFrom.imgUrl, 'this.signatureFrom.imgUrl');

      //     this.signatureFrom.formData.imgUrl = aa.join(",");
      //   }
      // }else{
      //   this.signatureFrom.formData.imgUrl = "";
      //   this.signatureFrom.imgUrl = [];
      // }

    },
    //文件上传成功
    fileupres(val) {
      if (val && val.data && val.data.fullpath) {
        this.signatureFrom.imgUrl.push(val.data.fullpath);
        this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(",");
      } else {
        console.error('文件上传响应数据格式不正确', val);
        this.$message.error('文件上传失败，请重试');
      }
    },
    handleClose() {
      this.showImgDialogVisible = false;
    },
    showImg() {
      this.showImgDialogVisible = true;
    },
    // showImgDialogVisible() {
    //   this.showImgDialogVisible = false;
    // },
  },
};
</script>
<style lang="less" scoped>
.sign {
  padding: 20px;
}

.sig-type .el-radio+.el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 10px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}

.Signature-matter {
  margin-top: 20px;
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px 14px;
  border-radius: 5px;
  font-size: 12px;
}

.Signature-matter>p {
  padding: 5px 0px;
}

/deep/ .el-textarea__inner {
  height: 80px;
}

.tips {
  margin-bottom: 10px;
  font-size: 12px;
  color: #606266;
  margin-left: 20px;
  background-color: #f5f7fa;
  padding: 10px;
  border-radius: 5px;

  a {
    color: #409EFF;
    text-decoration: underline;
  }

  p {
    padding: 5px 0px;
    font-size: 14px;
  }
}

.tips-title {
  font-weight: bold;
  font-size: 18px;
  margin-bottom: 10px;
}
</style>