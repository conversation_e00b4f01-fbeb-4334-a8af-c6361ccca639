<template>
  <div class="bag">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 用户管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form :inline="true" :model="formInline" class="demo-form-inline" ref="formInline">
          <el-form-item label="用户名" label-width="80px" prop="userName">
            <el-input v-model="formInline.userName" placeholder class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="公司名称" label-width="80px" prop="compName">
            <el-input v-model="formInline.compName" placeholder class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="账号状态" label-width="80px" prop="userStatus">
            <el-select v-model="formInline.userStatus" placeholder="请选择" class="input-w">
              <el-option label="全部" value></el-option>
              <el-option label="启用" value="0"></el-option>
              <el-option label="停用" value="1"></el-option>
            </el-select>
          </el-form-item>
          <!-- <el-form-item label="创建时间" label-width="80px" prop="dataCreat">
                <el-date-picker
                class="input-w"
                v-model="formInline.dataCreat"
                type="daterange"
                range-separator="-"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                @change="hande"
                ></el-date-picker>
            </el-form-item> -->
          <el-form-item label-width="80px" label="开始时间" prop="beginTime">
            <el-date-picker v-model="formInline.beginTime" :picker-options="pickerOptions" value-format="yyyy-MM-dd"
              type="date" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label-width="80px" label="结束时间" prop="endTime">
            <el-date-picker v-model="formInline.endTime" :picker-options="pickerOptions" value-format="yyyy-MM-dd"
              type="date" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div class="boderbottom">
        <el-button type="primary" plain @click="Query">查询</el-button>
        <el-button type="primary" plain @click="Reload('formInline')">重置</el-button>
        <el-button type="primary" plain @click="exportD()">导出</el-button>
      </div>
      <div class="Signature-search-fun">
        <span class="Signature-list-header">用户列表</span>
        <el-button type="primary" style="margin-left: 15px" @click="addUser">创建用户</el-button>
        <el-button type="primary" style="margin-left: 15px" @click="batchEnable('Enable')">批量启用</el-button>
        <el-button type="primary" style="margin-left: 15px" @click="batchEnable('Disable')">批量停用</el-button>
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!-- <table-tem
        :tableDataObj="tableDataObj"
        @handelOptionButton="handelOptionButton"
        @handelSelection="handelSelection"
        ></table-tem> -->
        <el-table class="productInfoTable1" ref="multipleTable2" v-loading="tableDataObj.loading2" border
          element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)" :data="tableDataObj.tableData"
          @selection-change="handelSelection" style="width: 100%">
          <el-table-column type="selection" width="55"> </el-table-column>
          <!-- <el-table-column label="用户ID" >
            <template slot-scope="scope" >
                {{ scope.row.userId }}
            </template>
</el-table-column> -->
          <el-table-column label="用户名">
            <template slot-scope="scope">
              <span style="color: #16a589; cursor: pointer" @click="
                $router.push({
                  path: '/UserDetails',
                  query: { id: scope.row.userId },
                })
                ">{{ scope.row.userName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="公司名称">
            <template slot-scope="scope">
              {{ scope.row.company }}
            </template>
          </el-table-column>
          <el-table-column label="默认签名">
            <template slot-scope="scope">
              {{ scope.row.signature }}
            </template>
          </el-table-column>
          <el-table-column label="余额" width="400">
            <template slot-scope="scope">
              <div style="display:flex;">
                <div v-for="(item, index) in scope.row.balanceList" :key="index">
                  <el-tag v-if="item.name == '短信' || item.name == '彩信' || item.name == '视频短信'"
                    :style="'margin: 5px 10px 5px 10px;color:' + tagColor[index]">{{ item.name }} : {{ item.num
                    }}</el-tag>
                </div>
                <div @click="more(scope.row)" style="cursor: pointer;margin: 5px 0px 5px 0px;">
                  <el-tag>更多</el-tag>
                </div>
              </div>
              <!-- <el-tooltip class="item" effect="light" placement="top">
                    <div class="tooltip" slot="content">{{scope.row.balanceList}}</div>
                                
                    <div v-for="(item,index) in scope.row.balanceList" :key="index">
                        <el-tag :style="'margin: 5px 0px 5px 10px;color:'+tagColor[index]">{{item.name }} : {{ item.num}}</el-tag>
                    </div>
                </el-tooltip> -->

              <!-- <span style="margin-right: 10px;" v-for="(item,index) in scope.row.balanceList" :key="index">{{item.name }}:{{item.num}}</span> -->
            </template>
          </el-table-column>
          <el-table-column label="用户状态" width="70">
            <template slot-scope="scope">
              <span v-if="scope.row.userStatus == 0" style="color: #16a589">启用</span>
              <span v-else style="color: red">停用</span>
            </template>
          </el-table-column>
          <el-table-column label="实名状态" width="120">
            <template slot-scope="scope">
              <el-tag effect="light" type="info" v-if="scope.row.realNameStatus == '0'">待校验</el-tag>
              <el-tag effect="light" type="danger" v-else-if="scope.row.realNameStatus == '1'">校验不通过</el-tag>
              <el-tag effect="light" type="success" v-else-if="scope.row.realNameStatus == '2'">校验通过</el-tag>
              <el-tag effect="light" type="warning" v-else-if="scope.row.realNameStatus == '3'">未实名</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="原因">
            <template slot-scope="scope">
              <span>{{ scope.row.realNameReason }}</span>
            </template>
          </el-table-column>
          <el-table-column label="创建人">
            <template slot-scope="scope">
              {{ scope.row.createName }}
            </template>
          </el-table-column>
          <el-table-column label="创建时间" width="160">
            <template slot-scope="scope">
              {{ scope.row.createTime }}
            </template>
          </el-table-column>
          <el-table-column label="操作" width="140px">
            <template slot-scope="scope">
              <el-dropdown trigger="click" @command="(command) => handleCommand(command, scope)">
                <el-button type="primary">
                  更多设置<i class="el-icon-arrow-down el-icon--right"></i>
                </el-button>
                <el-dropdown-menu slot="dropdown">
                  <el-dropdown-item command="edit" style="color: #409EFF;">编辑</el-dropdown-item>
                  <el-dropdown-item command="stop" v-if="scope.row.userStatus == 0"
                    style="color: orange">停用</el-dropdown-item>
                  <el-dropdown-item command="start" v-if="scope.row.userStatus == 1"
                    style="color: #67C23A;">启用</el-dropdown-item>
                  <el-dropdown-item command="pwd" style="color: #409EFF;">修改密码</el-dropdown-item>
                  <el-dropdown-item command="dataOn" v-if="scope.row.isDateState == 2 && isDateState == 1"
                    style="color: #409EFF;">数据开启</el-dropdown-item>
                  <el-dropdown-item command="dataOff" v-if="scope.row.isDateState == 1 && isDateState == 1"
                    style="color: orange">数据关闭</el-dropdown-item>
                  <el-dropdown-item command="del" style="color: red;">删除</el-dropdown-item>
                  <el-dropdown-item command="realName" style="color: #409EFF;">实名信息补充</el-dropdown-item>
                  <el-dropdown-item command="phone" style="color: #409EFF;">设置登录手机号</el-dropdown-item>
                  <el-dropdown-item command="sign" style="color: #409EFF;">设置默认签名</el-dropdown-item>
                </el-dropdown-menu>
              </el-dropdown>
              <!-- <el-button type="text" style="margin-left: 10px;" @click="
                $router.push({
                  path: '/UserEditing',
                  query: { id: scope.row.userId },
                })
                "><i class="el-icon-edit mr-3"></i>编辑</el-button>
              <el-button type="text" v-if="scope.row.userStatus == 0" style="color: orange" @click="Enable(scope)"><i
                  class="el-icon-circle-close-outline mr-2"></i>停用</el-button>
              <el-button type="text" v-if="scope.row.userStatus == 1" @click="Enable(scope)"><i
                  class="el-icon-circle-check-outline mr-2"></i>启用</el-button>
              <el-button type="text" @click="getPhoneArr(scope)"><i class="el-icon-edit mr-3"></i>修改密码</el-button>
              <el-button type="text" v-if="scope.row.isDateState == 2 && isDateState == 1" @click="DataOn(scope)"><i
                  class="el-icon-circle-check-outline mr-2"></i>数据开启</el-button>
              <el-button type="text" v-if="scope.row.isDateState == 1 && isDateState == 1" style="color: orange"
                @click="DataOn(scope)"><i class="el-icon-circle-close-outline mr-2"></i>数据关闭</el-button>
              <el-button style="color: red;" type="text" @click="delAllS(scope.row)">
                <i class="el-icon-delete"></i>
                删除
              </el-button>
              <el-button type="text" @click="setPhone(scope.row)">
                <i class="el-icon-s-tools"></i>
                设置登录手机号
              </el-button>
              <el-button type="text" @click="getRealNameInfo(scope.row)">
                <i class="el-icon-edit mr-3"></i>
                实名信息补充
              </el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
          style="background: #fff; padding: 10px 0; text-align: right">
          <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage" :page-size="tabelAlllist.pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total"></el-pagination>
        </el-col>
        <!-- 表格和分页结束 -->
      </div>
      <el-dialog title="修改接口密码" :visible.sync="LcpDialogVisible" width="560px" class="LoginCellPhoneDialog"
        :close-on-click-modal="false" :before-close="handelClose">
        <!-- <el-steps :active="setPhoneSteps" simple style="margin-bottom: 26px">
          <el-step title="手机号验证" icon="el-icon-edit"></el-step>
          <el-step title="接口密码" icon="el-icon-upload"></el-step>
        </el-steps> -->
        <!-- <div v-show="setPhoneSteps == 1">
          <el-table :data="tableD" class="Login-c-p-getPhone" border style="width: 100%">
            <el-table-column align="center" prop="name" label="编号" width="120">
            </el-table-column>
            <el-table-column prop="address" align="center" label="手机号">
            </el-table-column>
            <el-table-column align="center" width="80" label="选择">
              <template slot-scope="scope">
                <el-radio @change.native="getCurrentRow(scope.$index)" :label="scope.$index" v-model="radio"
                  class="textRadio">&nbsp;</el-radio>
              </template>
            </el-table-column>
          </el-table>
          <el-form :model="setphoneFrom.ruleForm1" :rules="setphoneFrom.rules1" ref="ruleForm1" class="demo-ruleForm"
            label-width="120px">
            <el-form-item label="手机验证码" prop="verCode" style="margin: 40px auto">
              <el-input v-model="setphoneFrom.ruleForm1.verCode" style="display: inline-block; width: 180px"></el-input>
              <el-button type="primary" plain style="width: 124px; padding: 9px 0px" @click="CountdownCode"
                v-if="nmb == 120">获取验证码</el-button>
              <el-button type="primary" plain style="width: 124px; padding: 9px 0px" disabled v-else>重新获取({{ nmb
              }})</el-button>
            </el-form-item>
            <el-form-item style="">
              <el-button @click="cancel()" style="width: 100px; padding: 9px 0">取消</el-button>
              <el-button type="primary" @click="submitForm('ruleForm1')"
                style="width: 100px; padding: 9px 0">下一步</el-button>
            </el-form-item>
          </el-form>
        </div> -->
        <div>
          <el-form :inline="true" :model="formPassword" :rules="formRules" ref="formRule" class="demo-ruleForm"
            style="padding-left: 70px" label-width="130px">
            <!-- <el-form-item label="原密码" label-width="80px" prop="oldPassword">
                        <el-input type="password" v-model="formPassword.oldPassword" placeholder="请输入原密码进行身份验证" class="input-w"></el-input>
                    </el-form-item> -->
            <el-form-item label="新密码" label-width="80px" prop="newPassword">
              <el-input type="password" v-model="formPassword.newPassword" show-password
                placeholder="包含字母大小写和数字的8-16位的密码" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="确认密码" label-width="80px" prop="passWord">
              <el-input type="password" v-model="formPassword.passWord" show-password placeholder="请确认新密码"
                class="input-w"></el-input>
            </el-form-item>
            <!-- <el-form-item>
                        <el-button @click="cancel()" style="width:100px; padding:9px 0;">取消</el-button>
                        <el-button type="primary" @click="submitForm('formRule')" style="width:100px; padding:9px 0;">提交</el-button>
                    </el-form-item> -->
          </el-form>
          <el-button @click="cancel()" style="width: 100px; padding: 9px 0; margin-left: 160px">取消</el-button>
          <el-button type="primary" @click="submitForm('formRule')" style="width: 100px; padding: 9px 0">提交</el-button>
        </div>
      </el-dialog>
      <el-dialog title="实名信息补充" :visible.sync="realNameDialogVisible" width="560px" class="LoginCellPhoneDialog"
        :close-on-click-modal="false" :before-close="handelClose">
        <div>
          <el-form :inline="true" :model="realNameInfo" :rules="formRules" ref="realNameInfo" class="demo-ruleForm"
            style="padding-left: 70px" label-width="140px">
            <el-form-item label="企业名称" prop="companyName">
              <el-input class="input-w" placeholder="请输入企业名称" v-model="realNameInfo.companyName"></el-input>
            </el-form-item>
            <el-form-item label="社会统一信用代码" prop="creditCode">
              <el-input class="input-w" placeholder="请输入统一社会信用代码" v-model="realNameInfo.creditCode"></el-input>
            </el-form-item>
            <el-form-item label="企业法人" prop="legalPerson">
              <el-input class="input-w" placeholder="请输入企业法人姓名" v-model="realNameInfo.legalPerson"></el-input>
            </el-form-item>
            <el-form-item label="责任人姓名" prop="principalName">
              <el-input class="input-w" placeholder="请输入负责人姓名" v-model="realNameInfo.principalName"></el-input>
            </el-form-item>
            <el-form-item label="责任人证件号码" prop="principalIdCard">
              <el-input class="input-w" placeholder="请输入负责人身份证号" v-model="realNameInfo.principalIdCard"></el-input>
            </el-form-item>
            <el-form-item label="责任人手机号" prop="principalMobile">
              <el-input class="input-w" placeholder="请输入负责人手机号" v-model="realNameInfo.principalMobile"></el-input>
            </el-form-item>
          </el-form>
          <el-button @click="realNameDialogVisible = false"
            style="width: 100px; padding: 9px 0; margin-left: 160px">取消</el-button>
          <el-button type="primary" @click="submitFormRealNameInfo('realNameInfo')"
            style="width: 100px; padding: 9px 0">提交</el-button>
        </div>
      </el-dialog>
      <el-dialog :title="usreTitle" :visible.sync="moreFlag" custom-class="way" width="30%">
        <div>
          <div style="font-size: 18px; color: #000; margin: 10px">余额</div>
          <div v-for="(item, index) in balanceLists" :key="index">
            <el-tag :style="'margin: 5px 0px 5px 10px;color:' + tagColor[index]">{{ item.name }} : {{ item.num
            }}</el-tag>
          </div>
        </div>
      </el-dialog>
      <el-dialog title="设置默认签名" :visible.sync="signDialogVisible" width="560px" class="LoginCellPhoneDialog"
        :close-on-click-modal="false" :before-close="handelClose" center>
        <div>
          <el-form :inline="true" :model="signForm" :rules="formRules" ref="signForm" class="demo-ruleForm"
            label-width="140px">
            <el-form-item label="默认签名" prop="signatureId">
              <el-select v-model="signForm.signatureId" clearable filterable remote :remote-method="remoteMethod"
                :loading="loadingcomp" placeholder="请选择默认签名">
                <el-option v-for="(item, index) in signList" :key="index"
                  :value="item.signatureId" :label="item.signature">
                  <template #label>
                    <span>{{ item.signature }}</span>
                    <span style="color: #999;font-size: 12px;">{{ item.consumerName }}</span>
                  </template>
                </el-option>
              </el-select>
              <el-button style="margin-left: 10px;" type="primary" @click="addSign">创建子用户签名</el-button>
              <div style="font-size: 12px;margin-top: 10px;color: #F56C6C;">
                tips：仅支持选择已审核通过的签名
              </div>
            </el-form-item>
          </el-form>
        </div>
        <template slot="footer" class="dialog-footer">
          <el-button style="width: 100px;" @click="signDialogVisible = false">取消</el-button>
          <el-button type="primary" style="width: 100px;" @click="submitFormSign('signForm')">确定</el-button>
        </template>
      </el-dialog>
    </div>
  </div>
</template>
<script>
import TableTem from "@/components/publicComponents/TableTem";
import { mapState, mapMutations, mapActions } from "vuex";
export default {
  name: "UserManagement",
  components: {
    TableTem,
  },
  data() {
    var code = (rule, value, callback) => {
      if (!this.phoneData) {
        return callback(new Error("请选中手机号"));
      } else if (value == "") {
        return callback(new Error("请输入验证码"));
      } else {
        callback();
      }
    };
    var oldPassword = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("原密码不能为空"));
      } else {
        this.$api.get(
          this.API.cpus +
          "consumerclientinfo/validatePassword/" +
          this.formPassword.oldPassword,
          {},
          (res) => {
            if (res.code == 200) {
              callback();
            } else {
              callback(new Error("与原密码不相符"));
            }
          }
        );
      }
    };
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("新密码不能为空"));
      } else if (!/^[0-9a-zA-Z]+$/.test(value)) {
        callback(new Error("请输入正确的密码格式"));
      } else {
        if (this.formPassword.passWord !== "") {
          this.$refs.formRule.validateField("passWord");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("请输入确认新密码"));
      } else if (!/^[0-9a-zA-Z]+$/.test(value)) {
        callback(new Error("请输入正确的密码格式"));
      } else if (value !== this.formPassword.newPassword) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        },
      },
      signDialogVisible: false,
      signList: [],
      loadingcomp: false,
      signForm: {
        signatureId: "",
        userId: "",
      },
      moreFlag: false,
      realNameDialogVisible: false,
      balanceLists: [],
      usreTitle: "",
      formInline: {
        compName: "",
        userName: "",
        userStatus: "0",
        beginTime: "",
        endTime: "",
        currentPage: 1,
        pageSize: 10,
        dataCreat: [],
      },
      tabelAlllist: {
        //存储查询数据
        compName: "",
        userName: "",
        userStatus: "0",
        beginTime: "",
        endTime: "",
        currentPage: 1,
        pageSize: 10,
        dataCreat: [],
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        total: 0,
        tableData: [],
        // tableLabel: [
        //     {
        //         prop: "userId",
        //         showName: "用户ID",
        //         fixed: false,
        //     },
        //     {
        //         prop: "userName",
        //         showName: "用户名",
        //         fixed: false,
        //     },
        //     {
        //         prop: "company",
        //         showName: "公司名称",
        //         fixed: false,
        //     },
        //     {
        //         prop: "productBalance",
        //         showName: "剩余短信(条)",
        //         fixed: false,
        //     },
        //     {
        //         prop: "userStatus",
        //         showName: "用户状态",
        //         fixed: false,
        //         width:'70',
        //         formatData:function (val) {
        //             if (val== 0) {
        //                 return val='启用'
        //             }else if (val==1) {
        //                 return val='停用'
        //             }else{
        //                 return val='全部'
        //             }
        //         },
        //     },
        //     {
        //         prop: "createName",
        //         showName: "创建人",
        //         fixed: false,
        //     },
        //     {
        //         prop: "createTime",
        //         showName: "创建时间",
        //         fixed: false,
        //     }
        // ],
        // tableStyle: {
        //   isSelection: true, //是否复选框
        //   // height:250,//是否固定表头
        //   isExpand: false, //是否是折叠的
        //   style: {
        //     //表格样式,表格宽度
        //     width: "100%"
        //   },
        //   optionWidth: "300", //操作栏宽度
        //   border: true, //是否边框
        //   stripe: false //是否有条纹
        // },
        // conditionOption: [
        //     {
        //         // contactCondition:'',//关联的表格属性
        //         // contactData:'',//关联的表格属性-值
        //         optionName:'编辑',//按钮的显示文字
        //         optionMethod:'details',//按钮的方法
        //         icon:'el-icon-edit',//按钮图标
        //         optionButtonColor:'',//按钮颜色
        //     },
        //     {
        //         contactCondition:'userStatus',//关联的表格属性
        //         contactData:'1',//关联的表格属性-值
        //         optionName:'停用',//按钮的显示文字
        //         optionMethod:'Enable',//按钮的方法
        //         icon:'el-icon-error',//按钮图标
        //         optionButtonColor:'#f56c6c',//按钮颜色
        //         otherOptionName:'启用',//其他条件的按钮显示文字
        //         otherOptionMethod:'Enable',//其他条件的按钮方法
        //         otherIcon:'el-icon-success',//其他条件按钮的图标
        //         optionOtherButtonColor:''//其他条件按钮的颜色
        //     },
        //     {
        //         // contactCondition:'',//关联的表格属性
        //         // contactData:'',//关联的表格属性-值
        //         optionName:'修改密码',//按钮的显示文字
        //         optionMethod:'modifyPsd',//按钮的方法
        //         icon:'el-icon-edit',//按钮图标
        //         optionButtonColor:'',//按钮颜色
        //     },
        //                 {
        //         // contactCondition:'',//关联的表格属性
        //         // contactData:'',//关联的表格属性-值
        //         optionName:'数据开启',//按钮的显示文字
        //         optionMethod:'details',//按钮的方法
        //         icon:'el-icon-success',//按钮图标
        //         optionButtonColor:'',//按钮颜色
        //     },
        // ]
      },
      tagColor: [
        "#67c23a",
        "#e6a23c",
        "#ff0000b3",
        "#909399",
        "#3c61e6d1",
        "#3cafe6bf",
        "#16A589",
        "#303133",
      ],
      selectId: "",
      LcpDialogVisible: false, //手机验证弹出框显示隐藏
      setPhoneSteps: 1, // 设置手机号的步骤
      formPassword: {
        // oldPassword:"",
        newPassword: "",
        passWord: "",
      },
      userId: "",
      nmb: 120,
      radio: "",
      tableD: [],
      formRules: {
        oldPassword: [
          // { required: true, message: '原密码不能为空', trigger: 'blur' },
          { required: true, validator: oldPassword, trigger: "blur" },
          // ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$
          // {pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$/gi,message:'密码必须包含数字以及大小写字母8-16位'}
        ],
        newPassword: [
          { required: true, validator: validatePass, trigger: "blur" },
        ],
        passWord: [
          { required: true, validator: validatePass2, trigger: "blur" },
        ],
        companyName: [
          { required: true, message: '请输入企业名称', trigger: 'change' },
        ],
        creditCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
        ],
        legalPerson: [
          { required: true, message: '请输入法人姓名', trigger: 'change' },
        ],
        principalIdCard: [
          { required: true, message: '请输入负责人身份证号', trigger: 'change' },
        ],
        principalName: [
          { required: true, message: '请输入负责人姓名', trigger: 'change' },
        ],
        principalMobile: [
          { required: true, message: '请输入负责人手机号', trigger: 'change' },
        ],
        signatureId: [
          { required: true, message: '请选择默认签名', trigger: 'change' },
        ],
      },
      setphoneFrom: {
        ruleForm1: {
          verCode: "",
        },
        rules1: {
          verCode: [
            { required: true, validator: code, trigger: "blur" },
            { min: 6, max: 6, message: "请输入6位数字验证码" },
          ],
        },
        ruleForm2: {
          setNewPhone: "",
        },
      },
      realNameInfo: {
        companyName: "",//公司名称
        creditCode: "",//统一社会信用代码
        legalPerson: "",//法人姓名
        principalIdCard: "",//负责人身份证号
        principalName: "",//负责人姓名
        principalMobile: "",//负责人手机号
        userId: ""//用户id
      },
    };
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      isDateState: (state) => state.isDateState,
    }),
  },
  methods: {
    //----------------------------列表数据-------------------
    gettableLIst() {
      //获取行业类型归属列表
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.cpus + "v3/consumer/manager/user/list",
        this.tabelAlllist,
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.loading2 = false;
            this.tableDataObj.total = res.data.total;
            this.tableDataObj.tableData = res.data.records;
          }
        }
      );
    },
    more(row) {
      this.moreFlag = true;
      this.balanceLists = row.balanceList;
      this.usreTitle = row.userName;
    },
    // 时间操作
    hande: function (val) {
      if (val) {
        //获取查询时间框的值
        this.formInline.beginTime =
          this.moment(val[0]).format("YYYY-MM-DD ") + "00:00:00";
        this.formInline.endTime =
          this.moment(val[1]).format("YYYY-MM-DD ") + "23:59:59";
      } else {
        this.formInline.beginTime = "";
        this.formInline.endTime = "";
      }
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size;
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage;
    },
    Query() {
      //查询
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    Reload() {
      this.formInline.beginTime = "";
      this.formInline.endTime = "";
      this.$refs["formInline"].resetFields();
      Object.assign(this.tabelAlllist, this.formInline);
    },
    handleCommand(command, scope) {
      console.log(command, scope);
      if (command === "edit") {
        this.$router.push({
          path: '/UserEditing',
          query: { id: scope.row.userId },
        });
      } else if (command === "stop") {
        this.Enable(scope);
      } else if (command === "start") {
        this.Enable(scope);
      } else if (command === "pwd") {
        this.getPhoneArr(scope);
      } else if (command === "dataOn") {
        this.DataOn(scope);
      } else if (command === "dataOff") {
        this.DataOn(scope);
      } else if (command === "del") {
        this.delAllS(scope.row);
      } else if (command === "realName") {
        this.getRealNameInfo(scope.row);
      } else if (command === "phone") {
        this.setPhone(scope.row);
      } else if (command === "sign") {
        this.signDialogVisible = true;
        this.userId = scope.row.userId;
        // this.$api.get(
        //   this.API.cpus + "v3/consumer/manager/user/signature/list/" + scope.row.userId,
        //   {},
        //   (res) => {
        //     if (res.code == 200) {
        //       this.signList = res.data;
        //     }
        //   }
        // );
      }
    },
    //公司名远程搜索
    remoteMethod(query) {
      // console.log(query, "query");
      if (query !== "") {
        this.loadingcomp = true;
        this.searchAccount(query);
        this.loadingcomp = false;
      } else {
        this.signList = [];
        this.searchAccount();
      }
    },
    // handleFocus(){
    //   this.searchAccount();

    // },
    searchAccount(val) {
      try {
        let signature = val ? val : '';
        this.$api.get(
          this.API.cpus + "v3/consumer/manager/user/signature/list?" + "signature=" + signature,
          {},
          (res) => {
            if (res.code == 200) {
              this.signList = res.data;
            }
          }
        );
      } catch (error) {
        console.log(error, "error");
      }
    },
    addSign() {
      this.$router.push({
        path: '/userSignature',
        query: { id: this.userId },
      });
    },
    submitFormSign(formData) {
      this.$refs[formData].validate((valid) => {
        if (valid) {
          this.signForm.userId = this.userId;
          this.$api.put(this.API.cpus + "v3/consumer/manager/user/signature/default", this.signForm, res => {
            if (res.code == 200) {
              this.signDialogVisible = false;
              this.$message({
                type: "success",
                duration: "2000",
                message: res.msg,
              });
              this.gettableLIst();
            } else {
              this.$message({
                type: "error",
                duration: "2000",
                message: res.msg,
              });
            }
          })
        } else {
          return false;
        }
      })
    },
    // 停用启用
    Enable(val) {
      this.$api.post(
        this.API.cpus + "v3/consumer/manager/user/status",
        {
          userIds: [val.row.userId],
          statusType: val.row.userStatus == 0 ? "1" : "0",
        },
        (res) => {
          this.gettableLIst();
        }
      );
    },
    // 批量停用启用
    batchEnable(val) {
      let a = this.selectId.split(",");
      if (a.length > 0 && a[0] != "") {
        this.$api.post(
          this.API.cpus + "v3/consumer/manager/user/status",
          { userIds: a, statusType: val == "Disable" ? "1" : "0" },
          (res) => {
            this.gettableLIst();
          }
        );
      } else {
        this.$message({
          type: "error",
          duration: "2000",
          message: "请先批量选择",
        });
      }
    },
    // 数据开启
    DataOn(val) {
      this.$api.post(
        this.API.cpus + "v3/consumer/manager/user/isState",
        {
          smsId: val.row.smsId,
          isDateState: val.row.isDateState == 2 ? "1" : "2",
        },
        (res) => {
          this.gettableLIst();
        }
      );
    },
    //导出
    exportD() {
      let aa = {};
      Object.assign(aa, this.tabelAlllist);
      aa.isDownload = 1;
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: "列表无数据，不可导出！",
          type: "warning",
        });
      } else {
        this.$File.export(
          this.API.cpus + "v3/consumer/manager/user/export",
          aa,
          "用户列表.xlsx"
        );
      }
    },
    delAllS(row) {
      this.$confirms.confirmation(
        "delete",
        "此操作将永久删除该数据",
        this.API.cpus + 'v3/consumer/manager/user/del/' + row.userName,
        {},
        () => {
          this.gettableLIst();
        }
      );
      // this.$api.delete(this.API.cpus+'v3/consumer/manager/user/del/'+'',{},res=>{
      //     console.log(res);
      // })
    },
    setPhone(row) {
      this.$router.push({
        path: "/settingPhone",
        query: {
          id: row.userId,
        }
      });
    },
    // handelOptionButton: function(val) {
    //   this.userId=val.row.userId
    //   if (val.methods == "modifyPsd") {
    //     this.getPhoneArr(val);
    //   }else if (val.methods == "Enable") {
    //     this.Enable(val);
    //   }else if(val.methods=="details"){
    //     this.$router.push({ name: 'UserEditing',query:{id:val.row.userId}})
    //   }
    // },
    // 新增用户
    addUser() {
      this.$router.push({ path: "/UserEditing" });
    },
    handelSelection(val) {
      //列表复选框的值
      let selectId = [];
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].userId);
      }
      this.selectId = selectId.join(",");
      console.log(this.selectId);
    },
    //获取弹出框手机号
    getPhoneArr(val) {
      this.userId = val.row.userId;
      this.$api.get(
        this.API.cpus + "v3/consumer/manager/user/phone/" + val.row.userId,
        {},
        (res) => {
          if (res.code == 200) {
            let resdata = [];
            let tabledata = [];
            let phonelength = res.data;
            this.phoneData = phonelength[0].phone;
            this.phoneOriginal = [];
            for (var i = 0; i < phonelength.length; i++) {
              // 列表数据
              let a = {};
              a.Numbering = i + 1;
              a.username = phonelength[0].consumerName;
              a.phone = phonelength[i].phone;
              resdata[resdata.length] = a
              this.phoneOriginal.push(phonelength[i].phone);
              // 登录手机号列表
              let b = {};
              b.index = i;
              b.name = i + 1;
              b.address = phonelength[i].phone;
              tabledata[tabledata.length] = b;
            }
            if (phonelength[0].phone == "") {
              this.tableD = [];
            } else {
              this.tableD = tabledata;
            }
          }
        }
      );
      this.radio = 0;
      this.LcpDialogVisible = true;
    },
    // 修改登录密码
    handelClose() {
      //×号关闭弹窗
      // 点击关闭
      this.LcpDialogVisible = false; //关闭弹出框
      this.realNameDialogVisible = false; //关闭实名信息弹出框
      this.signDialogVisible = false;
    },
    cancel() {
      this.LcpDialogVisible = false; //关闭弹出框
      this.setPhoneSteps = 1; //步进改为1
      this.setphoneFrom.ruleForm1.verCode = ""; //验证码置空
      this.setphoneFrom.ruleForm2.setNewPhone = ""; //手机号置空
    },
    submitForm(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          let param = {};
          // param.oldPassword=this.formPassword.oldPassword;
          param.password = this.formPassword.newPassword;
          param.userId = this.userId;
          this.$confirms.confirmation(
            "post",
            "确定修改密码？",
            this.API.cpus + "v3/consumer/manager/user/pwd",
            param,
            (res) => {
              this.LcpDialogVisible = false; //关闭弹出框
            }
          );
        } else {
          return false;
        }
        // if (val == "ruleForm1") {
        //   if (valid) {
        //     this.$api.get(
        //       this.API.cpus +
        //       "code/checkVerificationCode?code=" +
        //       this.setphoneFrom.ruleForm1.verCode +
        //       "&flag=2",
        //       {},
        //       (res) => {
        //         if (res.code == 200) {
        //           this.setPhoneSteps = 2;
        //         } else {
        //           this.$message({
        //             type: "error",
        //             duration: "2000",
        //             message: "验证码无效！",
        //           });
        //         }
        //       }
        //     );
        //   } else {
        //     console.log("error submit!!");
        //     return false;
        //   }
        // } else {
        //   this.$refs[val].validate((valid) => {
        //     if (valid) {

        //     } else {
        //       return false;
        //     }
        //   });
        // }
      });
    },
    // 获取验证码倒计时
    CountdownCode() {
      if (this.phoneData) {
        --this.nmb;
        const timer = setInterval((res) => {
          --this.nmb;
          if (this.nmb < 1) {
            this.nmb = 120;
            clearInterval(timer);
          }
        }, 1000);
        this.$api.get(
          this.API.cpus +
          "code/sendVerificationCode?phone=" +
          this.phoneData +
          "&flag=2",
          {},
          (res) => {
            if (res.code == 200) {
              this.$message({
                type: "success",
                duration: "2000",
                message: "验证码已发送至手机!",
              });
            } else {
              this.$message({
                type: "warning",
                message: "验证码未失效，需失效后重新获取!",
              });
            }
          }
        );
      } else {
        this.$message({
          message: "请先选中手机号码",
          type: "warning",
        });
      }
    },
    showRow(row) {
      //赋值给radio
      this.radio = this.tableD.indexOf(row);
    },
    getCurrentRow(val) {
      this.phoneData = this.tableD[val].address; //赋值手机号
    },
    getRealNameInfo(row) {
      this.realNameDialogVisible = true; //打开实名信息弹出框
      this.realNameInfo.userId = row.userId;
      this.$api.get(this.API.cpus + 'v3/consumer/manager/realName/' + row.userId, {}, res => {
        if (res.code == 200) {
          this.realNameInfo.companyName = res.data.companyName;
          this.realNameInfo.creditCode = res.data.creditCode;
          this.realNameInfo.legalPerson = res.data.legalPerson;
          this.realNameInfo.principalIdCard = res.data.principalIdCard;
          this.realNameInfo.principalName = res.data.principalName;
          this.realNameInfo.principalMobile = res.data.principalMobile;
        }
      })
    },
    submitFormRealNameInfo(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation("put", "确认修改实名信息？", this.API.cpus + "v3/consumer/manager/realName", this.realNameInfo, res => {
            if (res.code == 200) {
              this.realNameDialogVisible = false; //关闭实名信息弹出框
            }
          })
        }
      })
    },
  },
  //   activated() {
  //     this.gettableLIst();
  //   },
  watch: {
    //监听查询框对象的变化
    tabelAlllist: {
      handler() {
        this.gettableLIst();
      },
      deep: true, //深度监听
      immediate: true,
    },
    LcpDialogVisible: function (val) {
      if (!val) {
        // this.setphoneFrom.ruleForm1.verCode = ""; //验证码置空
        // // this.setphoneFrom.ruleForm2.setNewPhone=''; //手机号置空
        // this.setPhoneSteps = 1; //步进改为1
        // this.radio = "";
        // this.phoneData = this.phoneOriginal[0];
        this.$refs.formRule.resetFields();
        // this.$refs.ruleForm2.resetFields()
      }
    },
    realNameDialogVisible: function (val) {
      if (!val) {
        this.realNameInfo.companyName = "";
        this.realNameInfo.creditCode = "";
        this.realNameInfo.legalPerson = "";
        this.realNameInfo.principalIdCard = "";
        this.realNameInfo.principalName = "";
        this.realNameInfo.userId = "";
        this.realNameInfo.principalMobile = "";
        // this.$refs.formRealNameInfo.resetFields();
      }
    },
    signDialogVisible: function (val) {
      if (!val) {
        this.signList = [];
        this.userId = "";
        this.signForm.signatureId = "";
      }
    },
  },
};
</script>
<style scoped>
.OuterFrame {
  padding: 20px;
}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}

.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}

.Signature-box {
  padding: 20px;
}

.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.Signature-matter>div {
  height: 26px;
  line-height: 26px;
}

.Signature-set {
  color: #0066cc;
}

.Signature-creat {
  margin-top: 20px;
}

.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Mail-table {
  padding-bottom: 40px;
}

.sig-type .el-radio+.el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
