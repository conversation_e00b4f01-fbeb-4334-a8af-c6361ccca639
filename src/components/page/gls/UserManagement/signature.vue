<template>
  <div class="bag">
    <div class="fillet Signature-box">
      <div>
        <el-form :model="tableDataObj.tablecurrent" :inline="true" ref="tablecurrent" class="demo-tablecurrent">
          <el-form-item label="签名" prop="signature">
            <el-input v-model="tableDataObj.tablecurrent.signature"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="submitForm('tablecurrent')">查询</el-button>
            <el-button type="primary" @click="resetForm('tablecurrent')">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)" ref="multipleTable"
          border :data="tableDataObj.tableData" style="width: 100%">
          <el-table-column prop="signatureId" label="ID" width="90"></el-table-column>
          <el-table-column label="签名">
            <template slot-scope="scope">
              <span>{{ scope.row.signature }}</span>
              <span v-if="scope.row.auditReason == null"></span>
              <span v-else-if="scope.row.auditReason == ''"></span>
              <span v-else-if="scope.row.auditReason == '审核通过'"></span>
              <span
                v-else-if="scope.row.auditStatus == '3'"
                style="color: #f56c6c"
                >( 驳回原因：{{ scope.row.auditReason }} )</span
              >
            </template>
          </el-table-column>
          <el-table-column label="短信示例">
            <template slot-scope="scope">
              <Tooltip v-if="scope.row.contentExample" :content="scope.row.contentExample" className="wrapper-text"
                effect="light">
              </Tooltip>
              <!-- <span>{{ scope.row.signature }}</span> -->
            </template>
          </el-table-column>
          <el-table-column label="申请时间" width="180">
            <template slot-scope="scope">{{
              scope.row.createTime | fmtDate
            }}</template>
          </el-table-column>
          <el-table-column label="审核状态" width="120">
            <template slot-scope="scope">
              <el-tag effect="light" type="info" v-if="scope.row.auditStatus == '0'">编辑中</el-tag>
              <el-tag effect="light" type="warning" v-else-if="scope.row.auditStatus == '1'">待审核</el-tag>
              <el-tag effect="light" type="success" v-else-if="scope.row.auditStatus == '2'">审核通过</el-tag>
              <el-tag effect="light" type="danger" v-else-if="scope.row.auditStatus == '3'">审核不通过</el-tag>
            </template>
          </el-table-column>
          <el-table-column label="实名状态" width="140">
            <template slot-scope="scope">
              <div style="display: flex">
                <div>
                  <el-tooltip v-if="scope.row.ydRealNameStatus == 0" class="item" effect="dark" content="未实名"
                    placement="top-start">
                    <i class="iconfont icon-yidong" style="font-size: 20px"></i>
                  </el-tooltip>
                  <el-tooltip v-if="scope.row.ydRealNameStatus == 1" class="item" effect="dark" content="已实名"
                    placement="top-start">
                    <i class="iconfont icon-yidong" style="color: #409eff; font-size: 20px"></i>
                  </el-tooltip>
                </div>
                <div style="margin: 0 10px">
                  <el-tooltip v-if="scope.row.ltRealNameStatus == 0" class="item" effect="dark" content="未实名"
                    placement="top-start">
                    <i class="iconfont icon-liantong" style="font-size: 20px"></i>
                  </el-tooltip>
                  <el-tooltip v-if="scope.row.ltRealNameStatus == 1" class="item" effect="dark" content="已实名"
                    placement="top-start">
                    <i class="iconfont icon-liantong" style="color: #409eff; font-size: 20px"></i>
                  </el-tooltip>
                </div>
                <div>
                  <el-tooltip v-if="scope.row.dxRealNameStatus == 0" class="item" effect="dark" content="未实名"
                    placement="top-start">
                    <i class="iconfont icon-dianxin" style="font-size: 20px"></i>
                  </el-tooltip>
                  <el-tooltip v-if="scope.row.dxRealNameStatus == 1" class="item" effect="dark" content="已实名"
                    placement="top-start">
                    <i class="iconfont icon-dianxin" style="color: #409eff; font-size: 20px"></i>
                  </el-tooltip>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="160">
            <template slot-scope="scope">
              <div>
                <el-button v-if="scope.row.auditStatus != '1'" type="text" @click="getRealNameInfo(scope.row)"><i
                  class="el-icon-success"></i>&nbsp;实名信息更改</el-button>
              </div>
              <div>
                <el-button type="text" style="color: #67C23A;" @click="setDefaultSignature(scope.row)"><i
                  class="el-icon-s-tools"></i>&nbsp;设置默认签名</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <!-- <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton"></table-tem> -->
        <!--分页-->
        <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page pageStyle" slot="pagination">
          <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="1" :page-sizes="[10, 20, 50, 100, 300]" layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total">
          </el-pagination>
        </el-col>
        <!-- 表格和分页结束 -->
      </div>
      <!-- 编辑弹框 -->
      <RealNameDialog
      :visible.sync="realNameDialogVisible"
      :initial-form-data="realNameInfo"
      :show-signature-types="true"
      :show-file-upload="true"
      :show-content-example="true"
      :upload-url="this.API.cpus + 'v3/file/upload'"
      :headers="header"
      @submit="submitFormRealNameInfo"
      @cancel="handleCancel"
      @close="handelClose"
      @upload-success="handleSuccess"
      @file-remove="handleRemove1"
    />
      <!-- <el-dialog title="实名信息补充" :visible.sync="realNameDialogVisible" width="750px" class="LoginCellPhoneDialog"
        :close-on-click-modal="false" :before-close="handelClose">
        <div>
          <el-form :inline="false" :model="realNameInfo" :rules="formRules" ref="realNameInfo" class="demo-ruleForm"
             label-width="140px">
            <el-form-item label="企业名称" prop="companyName">
              <el-input class="input-w" placeholder="请输入企业名称" v-model="realNameInfo.companyName"></el-input>
            </el-form-item>
            <el-form-item label="社会统一信用代码" prop="creditCode">
              <el-input class="input-w" placeholder="请输入统一社会信用代码" v-model="realNameInfo.creditCode"></el-input>
            </el-form-item>
            <el-form-item label="企业法人" prop="legalPerson">
              <el-input class="input-w" placeholder="请输入企业法人姓名" v-model="realNameInfo.legalPerson"></el-input>
            </el-form-item>
            <el-form-item label="责任人姓名" prop="principalName">
              <el-input class="input-w" placeholder="请输入负责人姓名" v-model="realNameInfo.principalName"></el-input>
            </el-form-item>
            <el-form-item label="责任人证件号码" prop="principalIdCard">
              <el-input class="input-w" placeholder="请输入负责人身份证号" v-model="realNameInfo.principalIdCard"></el-input>
            </el-form-item>
            <el-form-item label="签名来源" prop="signatureType">
              <el-radio-group style="display: flex;flex-direction: column;align-items: self-start;" v-model="realNameInfo.signatureType">
                <el-radio style="margin-top: 10px;" :label="1">
                  <span class="sig-type-title-tips">企业名称</span>
                </el-radio>
                <el-radio style="margin-top: 10px;" :label="2">
                  <span class="sig-type-title-tips">事业单位：如机关，学校，科研单位，街道社区等</span>
                </el-radio>
                <el-radio style="margin-top: 10px;" :label="3">
                  <span class="sig-type-title-tips">商标</span>
                  （须提供商标注册证书图片或在在中国商标网的商标查询截图）
                </el-radio>
                <el-radio style="margin-top: 10px;" :label="4">
                <span class="sig-type-title-tips">App </span>
                （须提供app在ICP/IP/域名备案管理系统的截图）
              </el-radio>
              <el-radio style="margin-top: 10px;" :label="5">
                <span class="sig-type-title-tips">小程序</span>
                （须提供小程序在ICP/IP/域名备案管理系统的截图）
              </el-radio>
              <el-radio :label="7">
                <span class="sig-type-title-tips">网站</span>
                （须提网站在ICP/IP/域名备案管理系统截图，仅限事业单位的网站）
              </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="签名类型" prop="signatureSubType">
              <el-radio-group v-model="realNameInfo.signatureSubType">
                <el-radio :label="0">
                  <span>全称</span>
                </el-radio>
                <el-radio :label="1">
                  <span>简称</span>
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item v-if="realNameInfo.signatureType != '2'" label="文件上传" :prop="realNameInfo.signatureType==1?'':'imgUrl'" key="imgUrl">
              <div>
                <el-upload class="upload-demo" :headers="header" :action="this.API.cpus + 'v3/file/upload'" :limit="3"
                  :file-list="fileList" list-type="picture-card" :on-preview="handlePictureCardPreview"
                  :on-success="handleSuccess" :on-remove="handleRemove1">
                  <div>
                    <i class="el-icon-plus"></i>
                  </div>
                </el-upload>
                <div class="el-upload__tip">支持上传jpg, jpeg, png文件，且不超过2M</div>
              </div>
            </el-form-item>
            <el-form-item label="短信示例" prop="contentExample">
              <el-input type="textarea" v-model="realNameInfo.contentExample" show-word-limit
                              maxLength="800" placeholder="请输入短信示例；例如：【xxx】欢迎使用xxx，祝您使用愉快！"></el-input>
            </el-form-item>
          </el-form>
          <el-button @click="realNameDialogVisible = false"
            style="width: 100px; padding: 9px 0; margin-left: 160px">取消</el-button>
          <el-button type="primary" @click="submitFormRealNameInfo('realNameInfo')"
            style="width: 100px; padding: 9px 0">提交</el-button>
        </div>
      </el-dialog> -->
      <el-dialog v-model="dialogVisible">
        <img w-full :src="dialogImageUrl" alt="Preview Image" />
      </el-dialog>
    </div>
  </div>
</template>

<script>
import Tooltip from "@/components/publicComponents/tooltip";
import RealNameDialog from '@/components/common/RealNameDialog.vue';
import { formatDate } from "@/assets/js/date.js";
export default {
  name: "SignatureManagement",
  components: { Tooltip,RealNameDialog },
  data() {
    return {
      name: "Signature",
      tableDataObj: {
        //列表数据
        loading2: false,
        tablecurrent: {
          //分页参数
          userId: "",
          signature: "",
          currentPage: 1,
          pageSize: 10,
        },
        total: 0,
        tableData: [],

      },
      realNameDialogVisible: false, //实名信息弹框显示隐藏
      realNameInfo: {
        companyName: "",//公司名称
        creditCode: "",//统一社会信用代码
        legalPerson: "",//法人姓名
        principalIdCard: "",//负责人身份证号
        principalMobile: "",//负责人手机号
        principalName: "",//负责人姓名
        signatureId: "",//签名id
        userId:"",
        imgUrl: "",
        signatureType: "",
        signatureSubType: 0,
        contentExample: "",
      }, //实名信息
      formRules: {
        companyName: [
          { required: true, message: '请输入企业名称', trigger: 'change' },
        ],
        creditCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
        ],
        legalPerson: [
          { required: true, message: '请输入法人姓名', trigger: 'change' },
        ],
        principalIdCard: [
          { required: true, message: '请输入负责人身份证号', trigger: 'change' },
        ],
        principalName: [
          { required: true, message: '请输入负责人姓名', trigger: 'change' },
        ],
        imgUrl: [
          { required: true, message: '请上传文件', trigger: 'change' },
        ],
        signatureType: [
          { required: true, message: '请选择签名来源', trigger: 'change' },
        ],
        signatureSubType: [
          { required: true, message: '请选择签名类型', trigger: 'change' },
        ],
        contentExample: [
          { required: true, message: '请输入短信示例', trigger: 'blur' },
        ],
      },
      fileList: [],
      flieULR: [],
      copyUrl: "",
      copyUrlList: [],
      header: {},
      dialogVisible: false,
      dialogImageUrl: "",
    };
  },
  methods: {
    /* --------------- 列表展示 ------------------*/
    gettableData() {
      this.tableDataObj.loading2 = true;
      this.tableDataObj.tablecurrent.userId = this.$route.query.id;
      this.$api.post(
        this.API.cpus + "v3/consumer/manager/sub/user/signature/check",
        { userId: this.$route.query.id },
        (res) => {
          if (res.code == 200) {
            if (res.data) {
              //获取列表数据
              this.$api.post(
                this.API.cpus + "signature/signatureList",
                this.tableDataObj.tablecurrent,
                (res) => {
                  this.tableDataObj.tableData = res.records;
                  this.tableDataObj.total = res.total;
                  this.tableDataObj.loading2 = false;
                }
              );
            } else {
              this.tableDataObj.loading2 = false;
              this.$message.warning("该账号不支持查看！");
            }
          } else {
            this.$message.error(res.msg);
          }
        }
      );
    },
    submitForm() {
      this.gettableData();
    },
    //重置
    resetForm() {
      this.$refs.tablecurrent.resetFields();
      this.tableDataObj.tablecurrent.signature = "";
      this.gettableData();
    },
    handleSizeChange(size) {
      //分页一页的size
      this.tableDataObj.tablecurrent.pageSize = size;
      this.gettableData();
    },
    handleCurrentChange: function (currentPage) {
      //分页第几页
      this.tableDataObj.tablecurrent.currentPage = currentPage;
      this.gettableData();
    },
    getRealNameInfo(row) {
      this.realNameDialogVisible = true; //打开实名信息弹出框
      this.realNameInfo.signatureId = row.signatureId;
      this.realNameInfo.userId = row.userId;
      this.$api.get(this.API.cpus + 'signature/findModel/realName?signatureId=' + row.signatureId+'&userId='+row.userId, {}, res => {
        if (res.code == 200) {
          this.realNameInfo.companyName = res.data.companyName;
          this.realNameInfo.creditCode = res.data.creditCode;
          this.realNameInfo.legalPerson = res.data.legalPerson;
          this.realNameInfo.principalIdCard = res.data.principalIdCard;
          this.realNameInfo.principalName = res.data.principalName;
          this.realNameInfo.principalMobile = res.data.principalMobile;
          this.realNameInfo.signatureType = res.data.signatureType;
          this.realNameInfo.signatureSubType = res.data.signatureSubType;
          this.realNameInfo.contentExample = res.data.contentExample;
          if (res.data.imgUrl) {
            this.realNameInfo.imgUrl = res.data.imgUrl;
            this.copyUrl = res.data.imgUrl;
            this.flieULR = res.data.imgUrl.split(",").filter(item => item.trim() !== '');
            this.fileList = this.flieULR.map((item) => {
              return {
                name: item,
                url: this.API.imgU + item,
              };
            });
            let imgUrlList = JSON.stringify(this.fileList)
            this.copyUrlList = JSON.parse(imgUrlList)
            
          } else {
            this.fileList = [];
          }
        }
      })
    },
    submitFormRealNameInfo(formData) {
      console.log(formData,'formData');
      this.$confirms.confirmation("post", "确认修改实名信息？", this.API.cpus + "signature/realName/edit", formData, res => {
            if (res.code == 200) {
              this.realNameDialogVisible = false; //关闭实名信息弹出框
              this.gettableData();
            }
      })
      // this.submitResult = formData;
      // this.$refs[val].validate((valid) => {
      //   if (valid) {
         
      //   }
      // })
    },
    setDefaultSignature(row){
      this.$confirms.confirmation("put", "确认设置默认签名？", this.API.cpus + "v3/consumer/manager/user/signature/default", {signatureId: row.signatureId, userId: this.$route.query.id}, res => {
        if (res.code == 200) {
          this.gettableData();
        }
      })
    },
    changeSignatureType(val) {
      if (val == '2') {
        this.realNameInfo.imgUrl = "";
        this.fileList = [];
      } else {
        this.fileList = this.copyUrlList;
        this.realNameInfo.imgUrl = this.copyUrl;
      }
    },
    handelClose() {
      // this.realNameDialogVisible = false; //关闭实名信息弹出框
    },
    handleCancel() {
      this.$message({
        message: '已取消操作',
        type: 'info'
      });
    },
    handleSuccess(res) {
      if (res.code == 200) {
        this.flieULR.push(res.data.fullpath);
        this.realNameInfo.imgUrl = this.flieULR.join(",");
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    handleRemove1(file, fileList) {
      if (file.response) {
        this.flieULR.splice(this.flieULR.indexOf(file.response.data.fullpath), 1);
        this.realNameInfo.imgUrl = this.flieULR.join(",");
      } else {
        this.flieULR.splice(this.flieULR.indexOf(file.name), 1);
        this.realNameInfo.imgUrl = this.flieULR.join(",");
      }
      
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    }
    /* --------------- 上传图片功能 -----------------------*/
  },
  created() {
    this.header = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
    };
    if(this.$route.query.signature){
      this.tableDataObj.tablecurrent.signature= this.$route.query.signature
    }
    this.gettableData();
  },
  watch: {
    realNameDialogVisible: function (val) {
      if (!val) {
        this.realNameInfo.companyName = "";
        this.realNameInfo.creditCode = "";
        this.realNameInfo.legalPerson = "";
        this.realNameInfo.principalIdCard = "";
        this.realNameInfo.principalName = "";
        this.realNameInfo.signatureId = "";
        this.realNameInfo.userId = "";
        this.flieULR = [];
        this.fileList = [];
        this.copyUrl = "";
        this.copyUrlList = [];
        this.realNameInfo.imgUrl = "";
        this.realNameInfo.signatureType = "";
        this.realNameInfo.signatureSubType = "";
        this.realNameInfo.contentExample = "";
        // this.$refs.realNameInfo.resetFields();
      }
    },
  }
  // activated(){
  //     this.gettableData();
  // }
};
</script>

<style scoped>
.Signature-box {
  padding: 20px;
}

.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px 14px;
  border-radius: 5px;
  font-size: 12px;
}

.Signature-matter>p {
  padding: 5px 0px;
}

.Signature-set {
  color: #0066cc;
}

.Signature-creat {
  margin-top: 20px;
}

.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}

.Mail-table {
  padding-bottom: 40px;
}

.sig-type .el-radio+.el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 10px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.sig-type-title-tips {
  font-weight: bold;
}
</style>
<style>
.el-table--small th {
  background: #f5f5f5;
}
</style>