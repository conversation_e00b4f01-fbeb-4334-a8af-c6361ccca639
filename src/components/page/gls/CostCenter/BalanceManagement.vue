<template>
  <div class="bag">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 余额管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="OuterFrame fillet">
      <div>
        <el-form :inline="true" :model="formInline" class="demo-form-inline" ref="formInline">
          <el-form-item label="用户名" label-width="82px" prop="userName">
            <el-input v-model="formInline.userName" placeholder class="input-w"></el-input>
          </el-form-item>

          <el-form-item label="公司名称" label-width="82px" prop="compName">
            <el-input v-model="formInline.compName" placeholder class="input-w"></el-input>
          </el-form-item>
          <!-- <el-form-item label="创建时间" label-width="82px" prop="dataCreat">
            <el-date-picker
              class="input-w"
              v-model="formInline.dataCreat"
              type="daterange"
              range-separator="-"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              @change="hande"
            ></el-date-picker>
          </el-form-item> -->
          <el-form-item label-width="82px" label="开始时间" prop="beginTime">
            <el-date-picker v-model="formInline.beginTime" :picker-options="pickerOptions"
              value-format="yyyy-MM-dd 00:00:00" type="date" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item label-width="82px" label="结束时间" prop="endTime">
            <el-date-picker v-model="formInline.endTime" :picker-options="pickerOptions"
              value-format="yyyy-MM-dd 23:59:59" type="date" placeholder="选择日期">
            </el-date-picker>
          </el-form-item>
        </el-form>
      </div>
      <div class="boderbottom">
        <el-button type="primary" plain style @click="Query">查询</el-button>
        <el-button type="primary" plain style @click="Reload('formInline')">重置</el-button>
      </div>
      <div class="Signature-search-fun">
        <!-- <span class="Signature-list-header">用户余额列表</span> -->
      </div>
      <div class="Mail-table">
        <!-- 表格和分页开始 -->
        <!-- <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
          @handelSelection="handelSelection"
          @sortthiscolumn="sortthiscolumn"
        ></table-tem> -->
        <el-table class="productInfoTable1" ref="multipleTable2" v-loading="tableDataObj.loading2" border
          :data="tableDataObj.tableData" style="width: 100%">
          <el-table-column label="用户名" width="250">
            <template slot-scope="scope">
              {{ scope.row.consumerName }}
            </template>
          </el-table-column>
          <el-table-column label="公司名称" width="300">
            <template slot-scope="scope">
              {{ scope.row.compName }}
            </template>
          </el-table-column>
          <el-table-column label="余额">
            <template slot-scope="scope">
              <!-- <div style="display:flex;"  >
                <div v-for="(item,index) in scope.row.balanceList" :key="index">
                    <el-tag v-if="item.name=='短信'||item.name=='彩信'||item.name=='视频短信'||item.name=='国际短信'" :style="'margin: 5px 10px 5px 10px;color:'+tagColor[index]">{{item.name }} : {{ item.num}}</el-tag>
                </div>

              </div> -->
              <div style="display:flex;">
                <div v-for="(item, index) in scope.row.balanceList" :key="index">
                  <el-tag v-if="item.name == '短信' || item.name == '彩信' || item.name == '视频短信'"
                    :style="'margin: 5px 10px 5px 10px;color:' + tagColor[index]">{{ item.name }} : {{ item.num
                    }}</el-tag>
                </div>
                <div @click="more(scope.row)" style="cursor: pointer;margin: 5px 0px 5px 0px;">
                  <el-tag>更多</el-tag>
                </div>
              </div>
              <!-- <el-tag :type="tagColor[index]" style="margin-right: 10px;margin-top:10px" v-for="(item,index) in scope.row.balanceList" :key="index">{{item.name }} : {{ item.num}}</el-tag> -->
              <!-- <span style="margin-right: 10px;" v-for="(item,index) in scope.row.balanceList" :key="index">{{item.name }}:{{item.num}}</span> -->
            </template>
          </el-table-column>
          <!-- <el-table-column label="创建时间">
              <template slot-scope="scope" >
                  {{ scope.row.rechargeTime }}
              </template>
          </el-table-column> -->
          <el-table-column label="操作" width='200px'>
            <template slot-scope="scope">
              <el-button type="text" @click="addphone(scope)">充值</el-button>
              <el-button type="text" @click="detailsRow(scope)">扣款</el-button>
              <el-button type="text" @click="remind(scope)">余额提醒</el-button>
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
          style="background:#fff;padding:10px 0;text-align:right;">
          <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="tabelAlllist.currentPage" :page-size="tabelAlllist.pageSize" :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total"></el-pagination>
        </el-col>
        <!-- 表格和分页结束 -->
      </div>

      <!-- 充值 -->
      <el-dialog title="充值" :visible.sync="LcpDialogVisible" width="600px" class="LoginCellPhoneDialog"
        :close-on-click-modal="false" :before-close='handelClose'>
        <el-form :model="setphoneFrom.ruleForm2" :rules="setphoneFrom.rules2" ref="ruleForm2" class="demo-ruleForm"
          label-width="130px">

          <el-form-item label="用户名" prop="username">
            <el-input v-model="setphoneFrom.ruleForm2.username" style="width:290px;" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="公司名称" prop="compName">
            <el-input v-model="setphoneFrom.ruleForm2.compName" style="width:290px;" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="充值类型" prop="productId">
            <el-select clearable v-model="setphoneFrom.ruleForm2.productId" @change="changeImg" style="width:290px;"
              filterable placeholder="请选择产品">
              <el-option v-for="item in balanceData" :key="item.productId" :label="item.name" :value="item.productId"></el-option>
              <!-- <el-option label="短信" value="1"></el-option>
              <el-option label="彩信" value="2"></el-option>
              <el-option label="视频短信" value="3"></el-option>
              <el-option label="国际短信" value="4"></el-option>
              <el-option label="闪验" value="5"></el-option>
              <el-option label="语音验证码" value="6"></el-option>
              <el-option label="语音通知" value="7"></el-option> -->
            </el-select>
          </el-form-item>
          <el-form-item v-if="priceFlag" label="充值条数/金额" prop="rechargeNum">
            <el-input v-model="setphoneFrom.ruleForm2.rechargeNum" style="width:290px;"></el-input>
          </el-form-item>
          <el-form-item v-else label="充值条数/金额" prop="rechargeNum">
            <el-input v-model="setphoneFrom.ruleForm2.rechargeNum" style="width:290px;"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="rechargeNote">
            <el-input type="textarea" v-model="setphoneFrom.ruleForm2.rechargeNote" style="width:290px;"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="cancel()" style="width:100px; padding:9px 0;">取消</el-button>
            <!-- <el-button type="primary" @click="beforeStep()" style="width:100px; padding:9px 0;">上一步</el-button> -->
            <el-button type="primary" @click="submitForm('ruleForm2')" style="width:100px; padding:9px 0;">提交</el-button>

          </el-form-item>
        </el-form>

      </el-dialog>
      <!-- 充值结束 -->
      <!-- 扣款 -->
      <el-dialog title="扣款" :visible.sync="dialogFormVisible" :close-on-click-modal="false" width="600px">
        <el-form :model="formop" :rules="rules" ref="formop" label-width="140px" style="padding:0 28px 0 20px;">
          <el-form-item label="用户名" prop="username">
            <el-input v-model="formop.username" style="width:290px;" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="公司名称" prop="compName">
            <el-input v-model="formop.compName" style="width:290px;" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="账户余额（条）" prop="restSumSms">
            <el-input v-model="formop.restSumSms" style="width:290px;" :disabled="true"></el-input>
          </el-form-item>
          <el-form-item label="充值扣款" prop="productId">
            <el-select clearable v-model="formop.productId" style="width:290px;"
              @change="productIdChange(formop.productId)" filterable placeholder="请选择通道名称">
              <el-option v-for="item in balanceData" :key="item.productId" :label="item.name" :value="item.productId"></el-option>
              <!-- <el-option label="短信" value="1"></el-option>
              <el-option label="彩信" value="2"></el-option>
              <el-option label="视频短信" value="3"></el-option>
              <el-option label="国际短信" value="4"></el-option>
              <el-option label="闪验" value="5"></el-option>
              <el-option label="语音验证码" value="6"></el-option>
              <el-option label="语音通知" value="7"></el-option> -->
            </el-select>
          </el-form-item>
          <el-form-item label="扣除条数/金额" prop="rechargeNum">
            <el-input v-model="formop.rechargeNum" style="width:290px;"></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="rechargeNote">
            <el-input type="textarea" v-model="formop.rechargeNote" style="width:290px;"></el-input>
          </el-form-item>
        </el-form>

        <div slot="footer" class="dialog-footer">
          <el-button type="primary" @click="submitForms('formop')">提 交</el-button>
          <el-button @click="dialogFormVisible = false">取 消</el-button>
        </div>
      </el-dialog>
      <!-- 扣款结束 -->
      <el-dialog title="余额提醒设置" :visible.sync="warnDialogVisible" :close-on-click-modal="false"
        :before-close='handelClose1' width="500px">
        <!-- <p style="padding-bottom:20px;">当前绑定手机号：{{iphone}}</p> -->
        <div>当前账号余额条数 在不足 <el-input style="width:180px;" v-model="NumberBalances"></el-input> 条时提醒</div>
        <span slot="footer" class="dialog-footer">
          <el-button @click="warnDialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="determine">确 定</el-button>
        </span>
      </el-dialog>
    </div>
    <el-dialog :title="usreTitle" :visible.sync="moreFlag" custom-class="way" width="30%">
      <div v-for="(item, index) in balanceLists" :key="index">
        <el-tag :style="'margin: 5px 0px 5px 10px;color:' + tagColor[index]">{{ item.name }} : {{ item.num }}</el-tag>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import TableTem from '@/components/publicComponents/TableTem'
export default {
  name: "BalanceManagement",
  components: {
    TableTem,
  },

  data() {
    // 验证IP规则
    var code = (rule, value, callback) => {
      if (!this.phoneData) {
        return callback(new Error('请选中手机号'));
      } else if (value == "") {
        return callback(new Error('请输入验证码'));
      } else {
        callback();
      }
    };
    // var rechargeNumIms = (rule, value, callback)=>{
    //   if(!/[^\d\.]/g.test(value)){
    //     return callback(new Error(''));
    //   }else{

    //   }
    // }
    return {
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now();
        }
      },
      priceFlag: true,
      moreFlag: false,
      activeNames: ['1'],
      rechargeNumIms: "",
      tagColor: ['#67c23a', '#e6a23c', '#ff0000b3', '#909399', "#3c61e6d1", '#3cafe6bf', '#16A589', '#303133'],
      mindId: '',//余额提醒id
      productId: '',//c产品id
      dialogStatus: "", //新增编辑标题
      NumberBalances: '',// 余额条数
      warnDialogVisible: false,//账户余额弹出层
      dialogFormVisible: false, //新增弹出框显示隐藏
      LcpDialogVisible: false,//弹出框显示隐藏
      operatorId: "", //运营商id
      services: [],//客服
      sales: [],//销售
      tableRow: '',//当前行列表数据
      balanceList: [],//存储当前短信彩信条数
      selectId: '',
      // 存储个人信息
      roleInfo: {},
      formop: {
        //表单数据
        restSumSms: '',
        rechargeNum: "",
        productId: 1,
        rechargeNote: '',
        userId: ''
      },
      rules: {
        //验证规则
        rechargeNum: [
          { required: true, message: '请输入扣款条数', trigger: 'blur' },
          { min: 1, max: 8, message: '最多输入8位字符' },
          { pattern: /^[1-9]\d*$/, message: "含有非法字符（只能输入数字！）" }
        ],
        rechargeCategory: [
          { required: true, message: '请选择充值类型', trigger: ['blur', 'change'] },
        ],
        rechargeNote: [
          { required: true, message: '请输入备注', trigger: 'blur' },
          { min: 1, max: 50, message: '最多输入50位字符' }
        ]
      },
      tableD: [],
      setphoneFrom: {
        ruleForm2: {
          rechargeNum: '',
          productId: 1,
          rechargeIsInvoice: '1',
          rechargeNote: '正式',
        },
        rules2: {
          rechargeIsInvoice: [
            { required: true, message: '请选择是否发票类型', trigger: ['blur', 'change'] },
          ],
          rechargeNum: [
            { required: true, message: '请输入充值条数/金额', trigger: 'blur' },
            { min: 1, max: 8, message: '最大3000万条' },
            { pattern: /^[1-9]\d*$/, message: "含有非法字符（只能输入数字！）" }
          ],

          rechargeNote: [
            { required: true, message: '请选择充值类型', trigger: ['blur', 'change'] },
          ],
          // rechargeNote: [
          //     { required: false, message: '请输入备注', trigger: 'blur' },
          //     { min: 1, max: 50, message: '请输入50位字符' }
          // ]
        }
      },
      tabelAlllist: {
        //存储查询数据
        userName: "",
        compName: "",
        dataCreat: "",
        beginTime: "",
        endTime: "",
        orderBy: '',
        currentPage: 1,
        pageSize: 10
      },
      formInline: {
        //查询数据
        userName: "",
        compName: "",
        dataCreat: "",
        beginTime: "",
        endTime: "",
        orderBy: '',
        currentPage: 1,
        pageSize: 10
      },
      tableDataObj: {
        //列表数据
        currentPage: "1",
        pageSize: "10",
        total: 0,
        loading2: false,
        tableData: [],
      },
      pldel: false,
      balanceLists: [],
      balanceData:[],//产品类型
      usreTitle: ""
    };
  },
  methods: {
    //-*-----------------列表数据------------------
    gettableLIst() {
      //获取运营商列表
      this.tableDataObj.loading2 = true;
      this.$api.post(this.API.cpus + "v3/consumer/record/user/manager/list", this.tabelAlllist, res => {
        this.tableDataObj.loading2 = false;
        this.tableDataObj.total = res.data.total;
        this.tableDataObj.tableData = res.data.records;
      });
    },
    changeImg(val) {
      console.log(val, '11');
      if (val == 4) {
        this.priceFlag = false
      } else {
        this.priceFlag = true
      }
    },
    more(row) {
      this.moreFlag = true
      this.balanceLists = row.balanceList
      this.usreTitle = row.consumerName
      // console.log(row,'row');
    },
    remind(val) {
      this.$api.get(this.API.recharge + "partner/balance/notice/info", { productId: '1', username: val.row.consumerName }, res => {
        this.NumberBalances = res.data.num + '';
        this.warnDialogVisible = true;
        this.mindId = val.row.consumerName;
      });
    },
    sortthiscolumn(val) {
      if (val.order == 'ascending') {
        this.formInline.orderBy = 'ASC'
        Object.assign(this.tabelAlllist, this.formInline);
      } else if (val.order == 'descending') {
        this.formInline.orderBy = 'DESC'
        Object.assign(this.tabelAlllist, this.formInline);
      }
    },
    //点击余额提醒确定
    determine() {
      const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
      if (testNum.test(this.NumberBalances)) {
        this.$confirms.confirmation('post', '确定余额条数不足' + this.NumberBalances + '条时提醒', this.API.recharge + 'partner/balance/notice/open',
          {
            num: this.NumberBalances,
            username: this.mindId,
            productId: '1'
          },
          res => {

            this.warnDialogVisible = false;//隐藏弹窗
            this.gettableLIst();
          });
      } else {
        this.$message({
          type: 'error',
          duration: '2000',
          message: "请填写1-10000000的余额条数"
        });
      }
    },
    handleSizeChange(size) { //分页每一页的有几条
      this.tabelAlllist.pageSize = size;
    },
    handleCurrentChange: function (currentPage) {//分页的第几页
      this.tabelAlllist.currentPage = currentPage;
    },
    //----------------------列表操作-------------------
    hande: function (val) {
      //获取查询时间框的值
      if (val) {
        this.formInline.beginTime = this.moment(val[0]).format("YYYY-MM-DD ");
        this.formInline.endTime = this.moment(val[1]).format("YYYY-MM-DD ");
      } else {
        this.formInline.beginTime = '';
        this.formInline.endTime = '';
      }

    },
    handelClose1() {//余额提醒 ×号关闭弹窗
      this.warnDialogVisible = false; //关闭弹出框
    },
    Query(val) {
      //查询运营商
      Object.assign(this.tabelAlllist, this.formInline);
      this.gettableLIst();
    },
    handelSelection(val) { //列表复选框的值
      let selectId = [];
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].operatorId)
      }
      this.selectId = selectId.join(',');
    },
    Reload() {
      //重置
      this.formInline.beginTime = "";
      this.formInline.endTime = "";
      this.formInline.orderBy = ''
      this.$refs.formInline.resetFields();
      Object.assign(this.tabelAlllist, this.formInline);
    },
    //--------------------------------------------------------
    //----------------充值-----------------------------
    // 发送请求方法
    InquireList() {
      this.$api.post(this.API.omcs + 'operatinguser/getUserinfo', {}, res => {
        let resdata = []
        let tabledata = []
        let phonelength = res.data.phone.split(",")
        console.log(phonelength)
        this.phoneData = phonelength[0]
        this.phoneOriginal = phonelength
        for (var i = 0; i < phonelength.length; i++) {
          // 列表数据
          let a = {};
          a.Numbering = i + 1;
          a.username = res.data.username;
          a.phone = phonelength[i];
          resdata[resdata.length] = a;
          // 登录手机号列表
          let b = {};
          b.index = i;
          b.name = i + 1;
          b.address = phonelength[i];
          tabledata[tabledata.length] = b
        }
        // this.tableDataObj.tableData=resdata
        this.tableD = tabledata
        console.log(tabledata, 'data');
        // this.tableDataObj.loading2=false;
        // 存储个人信息
        this.roleInfo = res.data
        console.log(res.data, 'data1');
      })
    },

    //充值弹出层
    addphone(val) {
      this.tableRow = val.row;
      this.setphoneFrom.ruleForm2.username = val.row.consumerName
      this.setphoneFrom.ruleForm2.compName = val.row.compName
      this.LcpDialogVisible = true
    },
    //扣款弹出层
    detailsRow(val) {
      console.log(val);
      this.balanceList = val.row.balanceList
      this.tableRow = val.row;
      this.formop.username = val.row.consumerName;
      this.formop.compName = val.row.compName;
      this.formop.restSumSms = val.row.restSumSms;
      this.dialogFormVisible = true;
    },
    getCurrentRow(val) {
      console.log(val) //获取选中的index
      console.log(this.tableD[val].address)  //获取对应的手机号
      this.phoneData = this.tableD[val].address //赋值手机号
    },
    cancel() {
      this.LcpDialogVisible = false; //关闭弹出框
      // this.InquireList()
    },
    handelClose() {//×号关闭弹窗
      //点击关闭
      this.LcpDialogVisible = false; //关闭弹出框
    },
    // 扣款类型选择改变
    productIdChange(val) {
      console.log(val);
      if (val == 1) {
        this.formop.restSumSms = this.balanceList[0].num
      } else if (val == 2) {
        this.formop.restSumSms = this.balanceList[1].num
      } else if (val == 3) {
        this.formop.restSumSms = this.balanceList[2].num
      } else if (val == 4) {
        this.formop.restSumSms = this.balanceList[3].num
      } else if (val == 5) {
        this.formop.restSumSms = this.balanceList[4].num
      } else if (val == 6) {
        this.formop.restSumSms = this.balanceList[5].num
      } else if (val == 7) {
        this.formop.restSumSms = this.balanceList[6].num
      }
    },
    // 充值
    submitForm(val) {
      // var obj = {}
      // obj.rechargeNum = this.setphoneFrom.ruleForm2.rechargeNum || this.setphoneFrom.ruleForm2.imsinput 
      // obj.productId="1"
      // obj.rechargeIsInvoice="1"
      // obj.rechargeNote = "正式"
      this.$refs[val].validate((valid) => {
        if (val == "ruleForm2") {
          if (valid) {
            this.setphoneFrom.ruleForm2.orderNumber = 'gls' + (new Date()).valueOf() + Math.ceil(Math.random() * 10000000);
            this.setphoneFrom.ruleForm2.type = '1';
            if (this.setphoneFrom.ruleForm2.rechargeNum > 30000000) {
              this.$message({
                type: 'warning',
                duration: '2000',
                message: "充值条数最多3000万"
              });
            } else {
              this.$confirms.confirmation("post", "确认充值吗", this.API.recharge + "partner/recharge",
                this.setphoneFrom.ruleForm2,
                res => {
                  if (res.code == 200) {
                    this.$message({
                      type: 'success',
                      duration: '2000',
                      message: "充值成功"
                    });
                    this.cancel();
                    this.gettableLIst();
                  } else {
                    this.$message({
                      type: 'warning',
                      duration: '2000',
                      message: "充值失败"
                    });
                    this.cancel();
                    this.gettableLIst();
                  }
                })

            }
            console.log('submit!!');
          } else {
            console.log('error submit!!');
            return false;
          }

        }

      });
    },
    //------------------------扣款------------------
    submitForms(formop) {
      this.$refs[formop].validate(valid => {
        if (valid) {
          this.formop.orderNumber = 'gls' + (new Date()).valueOf() + Math.ceil(Math.random() * 10000000);
          this.formop.type = '2';
          this.$confirms.confirmation("post", "确认扣款吗", this.API.recharge + "partner/recharge",
            this.formop,
            res => {
              if (res.code == 200) {
                this.dialogFormVisible = false;
                this.gettableLIst();
              } else {
                this.$message({
                  type: 'waring',
                  duration: '2000',
                  message: res.msg
                });
                this.gettableLIst();
              }
            })

        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //列表操作
    // handelOptionButton: function(val) {
    //   if (val.methods == "details") {//充值
    //     this.addphone(val);
    //   }

    //   if (val.methods == "dellog") {//扣款
    //     this.detailsRow(val);
    //   }
    //   if (val.methods == "remind") {//余额提醒
    //     this.remind(val);
    //   }
    // },

  },
  created() {
    this.InquireList()
    if(JSON.parse(localStorage.getItem('balanceList'))){
      this.balanceData = JSON.parse(localStorage.getItem('balanceList'))
    }
  },
  mounted() { },
  // activated(){
  //   this.gettableLIst();
  // },
  watch: {
    tabelAlllist: {
      handler() {
        this.gettableLIst();
      },
      deep: true,//深度监听
      immediate: true
    },
    //监听到款金额
    // "setphoneFrom.ruleForm2.rechargeAmount": function(val) {
    //   if (val) {
    //     let a = this.setphoneFrom.ruleForm2.smsPrice;
    //     let b = this.setphoneFrom.ruleForm2.rechargeAmount;
    //     let c = b/a;
    //     if (c.toString().indexOf('.') != -1) {
    //       this.setphoneFrom.ruleForm2.rechargeNum = c.toFixed(0).toString();
    //     } else {
    //       this.setphoneFrom.ruleForm2.rechargeNum = c.toString()
    //     }
    //   }else{
    //     this.setphoneFrom.ruleForm2.rechargeNum = '';
    //   }
    // },
    LcpDialogVisible: function (val) {
      if (val == false) {
        this.setphoneFrom.ruleForm2.rechargeNum = ''; //充值条数置空
        this.$refs.ruleForm2.resetFields()
        // this.InquireList()
      }
    },
    warnDialogVisible(val) {
      if (val == false) {
        this.NumberBalances = ''
      }
    },
    dialogFormVisible(val) {
      if (val == false) {
        this.$refs.formop.resetFields();
      }
    }
  }
};
</script>
<style scoped>
.OuterFrame {
  padding: 20px;
}

.demo-form-inline .el-form-item {
  margin-right: 50px;
}

.boderbottom {
  border-bottom: 1px solid #f5f5f5;
  padding-bottom: 20px;
}

.Signature-list-header {
  display: inline-block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}

.Signature-box {
  padding: 20px;
}

.Signature-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.Signature-matter>div {
  height: 26px;
  line-height: 26px;
}

.Signature-set {
  color: #0066cc;
}

.Signature-creat {
  margin-top: 20px;
}

.Signature-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.Mail-table {
  padding-bottom: 40px;
}

.sig-type .el-radio+.el-radio {
  margin-left: 0px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;


}

.stag {
  overflow-y:auto
  /* &::-webkit-scrollbar{
    display：none
  } */

}

.stag::-webkit-scrollbar {
  width: 10px;
  height: 2px
    /* display: none; */
}

.stag::-webkit-scrollbar-track {
  background: rgb(239, 239, 239);
  border-radius: 2px;
}

.stag::-webkit-scrollbar-thumb {
  background: #bfbfbf;
  border-radius: 10px;
}

.stag::-webkit-scrollbar-thumb:hover {
  background: #333;
}

.stag::-webkit-scrollbar-corner {
  background: #179a16;
}
</style>
<style>
.dialog-fade-enter-active .el-dialog.way {
  animation: anim-open 0.3s;
}

.dialog-fade-leave-active .el-dialog.way {
  animation: anim-close 0.3s;
}
