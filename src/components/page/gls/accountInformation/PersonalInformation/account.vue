<template>
  <div class="bag">
    <div class="Top_title" style="padding: 10px;">
      个人信息设置
    </div>
    <div class="fillet PersonalInformation-main">
      <header>基本信息</header>
      <div class="basicInfo">
        <div class="basicInfo-right">
          <div class="basicInfo-line">
            <span class="basicInfo-title">用户名：</span>
            <span class="user-name">{{ consumerName }}</span>
          </div>
          <div class="basicInfo-line">
            <span class="basicInfo-title">公司名称：</span>
            <span class="comp-name">{{ compName }}</span>
          </div>
          <div class="basicInfo-line">
            <span class="basicInfo-title">创建时间：</span>
            <span class="create-time">{{ createTime }}</span>
          </div>
          <!-- <div class="basicInfo-line">
                        <span class="basicInfo-title">登录密码：</span>
                        <span class="create-time">******</span>
                        <span class="changePassword" style="margin-left: 15px;" @click="addphone('changePassword')">修改登录密码</span>
                    </div> -->
          <div class="basicInfo-line">
            <span class="basicInfo-title">接口密码：</span>
            <span
              class="create-time"
              v-if="ispwd == false"
              style="display: inline-block"
              >******</span
            >
            <span
              class="create-time"
              v-if="ispwd == true"
              style="display: inline-block"
              >{{ password }}</span
            >
            <el-button
              :disabled="loginInfo.isAdmin == 1 ? false : true"
              style="margin-left: 15px; font-size: 14px"
              type="text"
              @click="modifyPsd()"
              >修改接口密码</el-button
            >
            <el-tooltip
              class="item"
              effect="dark"
              content="修改接口密码请联系管理员！"
              placement="top"
            >
              <i
                v-if="loginInfo.isAdmin != 1"
                style="color: #409eff"
                class="el-icon-question"
              ></i>
            </el-tooltip>
            <div class="basic-cfg-tips">
              接口密码是用来校验短信发送请求合法性的密码，与用户名对应，需要业务方高度保密，切勿把密码存储在客户端。
            </div>
          </div>
          <div class="basicInfo-line" v-if="roleId == '12'">
            <span class="basicInfo-title">秘钥：</span>
            <span class="create-time">******</span>
            <el-button
              :disabled="loginInfo.isAdmin == 1 ? false : true"
              style="margin-left: 15px; font-size: 14px"
              type="text"
              @click="addKey()"
              >修改秘钥</el-button
            >
            <el-tooltip
              class="item"
              effect="dark"
              content="修改密钥请联系管理员！"
              placement="top"
            >
              <i
                v-if="loginInfo.isAdmin != 1"
                style="color: #409eff"
                class="el-icon-question"
              ></i>
            </el-tooltip>
            <!-- <span class="changePassword" style="margin-left: 15px" @click="addKey('secretKey')">修改秘钥</span> -->
          </div>
          <div class="basicInfo-line" v-if="roleId == '12' && cipherMode == 2">
            <span class="basicInfo-title">加密盐：</span>
            <span class="create-time">******</span>
            <el-button
              :disabled="loginInfo.isAdmin == 1 ? false : true"
              style="margin-left: 15px; font-size: 14px"
              type="text"
              @click="editSalt()"
              >修改加密盐</el-button
            >
            <el-tooltip
              class="item"
              effect="dark"
              content="修改加密盐请联系管理员！"
              placement="top"
            >
              <i
                v-if="loginInfo.isAdmin != 1"
                style="color: #409eff"
                class="el-icon-question"
              ></i>
            </el-tooltip>
          </div>
        </div>
      </div>
    </div>
    <div class="fillet PersonalInformation-main">
      <header style="margin: 20px 0">登录手机号管理</header>
      <div class="fillet LoginCellPhone-box" style="padding-bottom: 60px">
        <div class="LoginCellPhone-matter">
          <div>温馨提示</div>
          <div>1.默认登录手机号为您注册账号时的手机号。</div>
          <div>
            2.该登录手机号至多可添加10个，至少一个。当只有一个手机号时不允许删除！
          </div>
        </div>
        <div class="LoginCellPhone-creat">
          <el-button
            v-if="loginInfo.isAdmin == 1"
            type="primary"
            @click="addphone()"
            >添加登录手机号</el-button
          >
        </div>
        <div class="LoginCellPhone-search-fun">
          <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :stripe="true"
            :data="tableDataObj.tableData"
            style="width: 100%"
          >
            <el-table-column label="用户名">
              <template slot-scope="scope">
                <span>{{ scope.row.consumerName }}</span>
              </template>
            </el-table-column>
            <el-table-column label="手机号码">
              <template slot-scope="scope">
                <div  v-if="loginInfo.isAdmin == 1" class="spanColor">
                  <span
                    v-if="scope.$index == mobileIndex"
                    style="cursor: pointer; color: #67c23a"
                    >{{ scope.row.mobile }}</span
                  >
                  <span
                    v-else
                    style="cursor: pointer; color: #67c23a"
                    @click="handelDecode(scope.row, scope.$index)"
                    >{{ scope.row.maskMobile }}</span
                  >
                  <el-tooltip effect="dark" content="管理员" placement="top">
                    <i
                      v-if="scope.row.isAdmin == 1"
                      style="color: #409eff; margin-left: 10px"
                      class="iconfont icon-yonghuming"
                    ></i>
                  </el-tooltip>
                </div>
                <div v-else>
                <span>{{ scope.row.maskMobile }}</span>
                <el-tooltip effect="dark" content="管理员" placement="top">
                  <i
                    v-if="scope.row.isAdmin == 1"
                    style="color: #409eff; margin-left: 10px"
                    class="iconfont icon-yonghuming"
                  ></i>
                </el-tooltip>
              </div>
              </template>
            </el-table-column>
            <!-- <el-table-column label="是否管理员">
              <template slot-scope="scope">
                <span v-if="scope.row.isAdmin == 1">管理员</span>
                <span v-else>用户</span>
              </template>
            </el-table-column> -->
            <el-table-column label="备注">
              <template slot-scope="scope">
                <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="220">
              <template slot-scope="scope">
                <el-button
                  v-if="loginInfo.isAdmin == 1"
                  style="color: #e6a23c"
                  icon="el-icon-edit"
                  type="text"
                  @click="editphone(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  v-if="loginInfo.isAdmin == 1 && scope.row.isAdmin != 1"
                  style="color: #f56c6c"
                  icon="el-icon-delete"
                  type="text"
                  @click="deletephone(scope.row)"
                  >删除</el-button
                >
                <el-button
                  v-if="loginInfo.isAdmin == 1 && scope.row.isAdmin != 1"
                  style="color: #409eff"
                  icon="el-icon-s-unfold"
                  type="text"
                  @click="transferAdmin(scope.row)"
                  >管理员转让</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
      </div>
    </div>
    <!-- 添加手机号 -->
    <!-- <el-dialog :title="SwitchName" :visible.sync="LcpDialogVisible" width="560px" class="LoginCellPhoneDialog"
      :close-on-click-modal="false" :before-close="handelClose">
      <el-steps :active="setPhoneSteps" simple style="margin-bottom: 26px">
        <el-step title="手机号验证" icon="el-icon-edit"></el-step>
        <el-step v-if="SwitchJudgment == 'changePassword'" title="输入新密码" icon="el-icon-upload"></el-step>
        <el-step v-if="SwitchJudgment == 'AddPhoneNumber'" title="新手机号添加" icon="el-icon-upload"></el-step>
        <el-step v-if="SwitchJudgment == 'secretKey'" title="输入秘钥" icon="el-icon-upload"></el-step>
      </el-steps>
      <div v-show="setPhoneSteps == 1">
        <el-table :data="tableD" class="Login-c-p-getPhone" border style="width: 100%">
          <el-table-column align="center" prop="name" label="编号" width="120">
          </el-table-column>
          <el-table-column prop="address" align="center" label="手机号">
          </el-table-column>
          <el-table-column align="center" width="80" label="选择">
            <template slot-scope="scope">
              <el-radio @change.native="getCurrentRow(scope.$index)" :label="scope.$index" v-model="radio"
                class="textRadio">&nbsp;</el-radio>
            </template>
          </el-table-column>
        </el-table>
        <el-form :model="setphoneFrom.ruleForm1" :rules="setphoneFrom.rules1" ref="ruleForm1" class="demo-ruleForm"
          label-width="120px">
          <el-form-item label="手机验证码" prop="verCode" style="margin: 40px auto">
            <el-input v-model="setphoneFrom.ruleForm1.verCode" style="display: inline-block; width: 180px"></el-input>
            <el-button type="primary" plain style="width: 124px; padding: 9px 0px" @click="CountdownCode"
              v-if="nmb == 120">获取验证码</el-button>
            <el-button type="primary" plain style="width: 124px; padding: 9px 0px" disabled v-else>重新获取({{ nmb
            }})</el-button>
          </el-form-item>
          <el-form-item style="">
            <el-button @click="cancel()" style="width: 100px; padding: 9px 0">取消</el-button>
            <el-button type="primary" @click="submitForm('ruleForm1')"
              style="width: 100px; padding: 9px 0">下一步</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div v-show="setPhoneSteps == 2" v-if="SwitchJudgment == 'changePassword'">
        <el-form :inline="true" :model="formPassword" :rules="formRules" ref="formRule" class="demo-ruleForm"
          style="padding-left: 70px" label-width="130px">
          <el-form-item label="新密码" label-width="80px" prop="newPassword">
            <el-input type="password" v-model="formPassword.newPassword" placeholder="包含字母大小写和数字的8-16位的密码"
              class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="确认密码" label-width="80px" prop="passWord">
            <el-input type="password" v-model="formPassword.passWord" placeholder="请确认新密码" class="input-w"></el-input>
          </el-form-item>
        </el-form>
        <el-button @click="cancel()" style="width: 100px; padding: 9px 0; margin-left: 160px">取消</el-button>
        <el-button type="primary" @click="submitForm('formRule')" style="width: 100px; padding: 9px 0">提交</el-button>
      </div>
      <div v-show="setPhoneSteps == 2" v-if="SwitchJudgment == 'AddPhoneNumber'">
        <el-form :model="setphoneFrom.ruleForm2" :rules="setphoneFrom.rules2" ref="ruleForm2" class="demo-ruleForm"
          label-width="130px">
          <el-form-item label="输入手机号" prop="setNewPhone" :rules="filter_rules({
            required: true,
            type: 'mobile',
            message: '手机号不能为空',
          })
            " style="margin-bottom: 200px">
            <el-input v-model="setphoneFrom.ruleForm2.setNewPhone" style="width: 290px"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="cancel()" style="width: 100px; padding: 9px 0">取消</el-button>
            <el-button type="primary" @click="submitForm('ruleForm2')" style="width: 100px; padding: 9px 0">提交</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div v-show="setPhoneSteps == 2" v-if="SwitchJudgment == 'secretKey'">
        <el-form :model="setphoneFrom.ruleForm3" :rules="setphoneFrom.rules3" ref="ruleForm3" class="demo-ruleForm"
          label-width="130px">
          <el-form-item label="输入秘钥" prop="secretKey" style="margin-bottom: 200px">
            <el-input v-model="setphoneFrom.ruleForm3.secretKey" style="width: 290px"></el-input>
          </el-form-item>
          <el-form-item>
            <el-button @click="cancel()" style="width: 100px; padding: 9px 0">取消</el-button>
            <el-button type="primary" @click="submitForm('ruleForm3')" style="width: 100px; padding: 9px 0">提交</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog> -->
    <!-- 修改接口密码 -->
    <el-dialog
      title="接口密码修改"
      :visible.sync="dialogFormVisible"
      width="520px"
      :close-on-click-modal="false"
      :before-close="beforec"
      class="dialogBasics"
    >
      <!-- <el-steps :active="active" simple>
        <el-step title="获取手机验证码" icon="el-icon-edit"></el-step>
        <el-step title="接口密码修改" icon="el-icon-upload"></el-step>
      </el-steps>
      <div v-show="active == 1">
        <el-table :data="dialogTable" border style="width: 100%; margin-top: 10px" class="passWord_table">
          <el-table-column align="center" type="index" label="序号" width="60"></el-table-column>
          <el-table-column align="center" prop="phone" label="手机号"></el-table-column>
          <el-table-column label="选择" align="center" width="60">
            <template slot-scope="scope">
              <el-radio :label="scope.$index" v-model="templateRadio"
                @change.native="getTemplateRow(scope.$index, scope.row)">&nbsp;</el-radio>
            </template>
          </el-table-column>
        </el-table>
        <el-form :model="updateFormDialog.formData" ref="updateFormDialog" label-width="146px" style="margin-top: 16px">
          <el-form-item label="手机验证码" prop="code" :rules="filter_rules({
            required: true,
            type: 'code',
            message: '验证码必填！',
          })
            ">
            <el-input v-model="updateFormDialog.formData.code" style="width: 180px"></el-input>
            <el-button type="primary" plain style="width: 124px; padding: 9px 0px" @click.prevent="send"
              v-if="nmb == 120">获取验证码</el-button>
            <el-button type="primary" plain style="width: 124px; padding: 9px 0px" disabled v-else>重新获取({{ nmb
            }})</el-button>
          </el-form-item>
          <el-form-item style="margin-top: 26px">
            <el-button class="footer-center-button" @click="dialogFormVisible = false">取 消</el-button>
            <el-button class="footer-center-button" type="primary"
              @click="updateFormDialog_ok('updateFormDialog', 'pwd')">确 认</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div v-show="active == 2">
        <div class="passWord-main">
          <el-form label-position="right" ref="passWordForm" :rules="passWordForm.formRule" :model="passWordForm.formData"
            label-width="100px">
            <el-form-item label="接口密码" prop="password" :rules="filter_rules({
              required: true,
              message: '接口密码必填！',
              type: 'passwords',
              min: 8,
              max: 16,
            })
              ">
              <el-input v-model="passWordForm.formData.password"></el-input>
            </el-form-item>
          </el-form>
        </div>
        <div class="passWord-footer">
          <el-button @click="dialogFormVisible = false">取消</el-button>
          <el-button @click="updateFormOk('passWordForm')" type="primary">确定</el-button>
        </div>
      </div> -->
      <el-form
        label-position="right"
        ref="passWordForm"
        :rules="formRules"
        :model="passWordForm.formData"
        label-width="100px"
      >
        <div class="Login-c-p-getPhone">
          验证码将会发送至管理员绑定的手机号：{{
            loginInfo.mobile
          }}，请注意查收！
        </div>
        <el-form-item
          label="手机验证码"
          prop="verifyCode"
          :rules="
            filter_rules({
              required: true,
              type: 'code',
              message: '验证码必填！',
            })
          "
        >
          <el-input
            v-model="passWordForm.formData.verifyCode"
            style="width: 250px"
          ></el-input>
          <el-button
            type="primary"
            plain
            style="width: 124px; padding: 9px 0px"
            @click.prevent="send"
            v-if="nmb == 120"
            >获取验证码</el-button
          >
          <el-button
            type="primary"
            plain
            style="width: 124px; padding: 9px 0px"
            disabled
            v-else
            >重新获取({{ nmb }})</el-button
          >
        </el-form-item>
        <el-form-item label="接口密码" prop="password">
          <el-input
            show-password
            v-model="passWordForm.formData.password"
            placeholder="密码由数字、大小写字母组成，密码长度8-16位"
          ></el-input>
        </el-form-item>
        <div style="margin-top: 30px">
          <el-form-item label="确认密码" prop="confirmPwd">
            <el-input
              show-password
              v-model="passWordForm.formData.confirmPwd"
              placeholder="密码由数字、大小写字母组成，密码长度8-16位"
            ></el-input>
          </el-form-item>
        </div>
      </el-form>
      <div class="passWord-footer">
        <el-button @click="dialogFormVisible = false">取消</el-button>
        <el-button @click="updateFormOk('passWordForm')" type="primary"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 修改加密盐 -->
    <el-dialog
      title="修改加密盐"
      :visible.sync="dialogSaltVisible"
      width="520px"
      :close-on-click-modal="false"
      :before-close="beforec"
      class="dialogBasics"
    >
      <el-form
        label-position="right"
        ref="saltForm"
        :rules="saltForm.formRule"
        :model="saltForm.formData"
        label-width="100px"
      >
        <div class="Login-c-p-getPhone">
          验证码将会发送至管理员绑定的手机号：{{
            loginInfo.mobile
          }}，请注意查收！
        </div>
        <el-form-item
          label="手机验证码"
          prop="verifyCode"
          :rules="
            filter_rules({
              required: true,
              type: 'code',
              message: '验证码必填！',
            })
          "
        >
          <el-input
            v-model="saltForm.formData.verifyCode"
            style="width: 250px"
          ></el-input>
          <el-button
            type="primary"
            plain
            style="width: 124px; padding: 9px 0px"
            @click.prevent="send"
            v-if="nmb == 120"
            >获取验证码</el-button
          >
          <el-button
            type="primary"
            plain
            style="width: 124px; padding: 9px 0px"
            disabled
            v-else
            >重新获取({{ nmb }})</el-button
          >
        </el-form-item>
        <el-form-item label="加密盐" prop="salt">
          <el-input v-model="saltForm.formData.salt"></el-input>
        </el-form-item>
      </el-form>
      <div class="passWord-footer">
        <el-button @click="dialogSaltVisible = false">取消</el-button>
        <el-button @click="updateSalt('saltForm')" type="primary"
          >确定</el-button
        >
      </div>
    </el-dialog>
    <!-- 修改密钥 -->
    <el-dialog
      title="修改密钥"
      :visible.sync="dialogkeyVisible"
      width="520px"
      :close-on-click-modal="false"
      :before-close="beforec"
      class="dialogBasics"
    >
      <el-form
        label-position="right"
        ref="keyForm"
        :rules="saltForm.formRule"
        :model="keyForm"
        label-width="100px"
      >
        <div class="Login-c-p-getPhone">
          验证码将会发送至管理员绑定的手机号：{{
            loginInfo.mobile
          }}，请注意查收！
        </div>
        <el-form-item
          label="手机验证码"
          prop="verifyCode"
          :rules="
            filter_rules({
              required: true,
              type: 'code',
              message: '验证码必填！',
            })
          "
        >
          <el-input
            v-model="keyForm.verifyCode"
            style="width: 250px"
          ></el-input>
          <el-button
            type="primary"
            plain
            style="width: 124px; padding: 9px 0px"
            @click.prevent="send"
            v-if="nmb == 120"
            >获取验证码</el-button
          >
          <el-button
            type="primary"
            plain
            style="width: 124px; padding: 9px 0px"
            disabled
            v-else
            >重新获取({{ nmb }})</el-button
          >
        </el-form-item>
        <el-form-item label="密钥" prop="secretKey">
          <el-input v-model="keyForm.secretKey"></el-input>
        </el-form-item>
      </el-form>
      <div class="passWord-footer">
        <el-button @click="dialogkeyVisible = false">取消</el-button>
        <el-button @click="updatekey('keyForm')" type="primary">确定</el-button>
      </div>
    </el-dialog>
    <!-- 添加手机号 -->
    <el-dialog
      title="添加登录手机号"
      :visible.sync="LcpDialogVisible"
      width="800px"
      class="LoginCellPhoneDialog"
      :close-on-click-modal="false"
      :before-close="handelClose"
    >
      <el-form
        :inline="true"
        :model="addPhoneForm"
        :rules="addPhoneRules"
        ref="ruleForm2"
        class="demo-ruleForm"
      >
        <div v-if="!codeStatus" class="Login-c-p-getPhone">
          验证码将会发送至您的手机号：{{ loginInfo.mobile }}，请注意查收！
        </div>
        <div v-if="!codeStatus">
          <el-form-item
            label-width="130px"
            label="手机验证码"
            prop="verifyCode"
          >
            <el-input
              v-model="addPhoneForm.verifyCode"
              style="display: inline-block; width: 180px"
            ></el-input>
            <el-button
              type="primary"
              plain
              style="width: 110px; padding: 9px 0px"
              @click="CountdownCode"
              v-if="nmb == 120"
              >获取验证码</el-button
            >
            <el-button
              type="primary"
              plain
              style="width: 110px; padding: 9px 0px"
              disabled
              v-else
              >重新获取({{ nmb }})</el-button
            >
          </el-form-item>
        </div>
        <div v-if="addPhoneForm.list.length">
          <template v-for="(row, index) in addPhoneForm.list">
            <el-form-item
              label-width="130px"
              :rules="addPhoneRules.phone"
              label="登录手机号"
              :prop="'list.' + index + '.phone'"
            >
              <el-input v-model="row.phone" class="input-t"></el-input>
            </el-form-item>
            <el-form-item
              :rules="addPhoneRules.remark"
              label="备注"
              :prop="'list.' + index + '.remark'"
            >
              <el-input v-model="row.remark" class="input-t"></el-input>
            </el-form-item>
            <i
              style="font-size: 24px; color: #f56c6c; cursor: pointer"
              class="el-icon-remove-outline"
              @click="addPhoneForm.list.splice(index, 1)"
            ></i>
          </template>
          <div class="add-white" @click="addWhiteListAction">添加手机号</div>
        </div>
        <div v-else class="add-white-list" @click="addWhiteListAction">
          添加手机号
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          @click="LcpDialogVisible = false"
          style="width: 100px; padding: 9px 0"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click="submitForm('ruleForm2')"
          style="width: 100px; padding: 9px 0"
          >提交</el-button
        >
      </span>
    </el-dialog>
    <!-- 删除登录手机号 -->
    <el-dialog
      :title="title"
      :visible.sync="DeletePhone"
      width="560px"
      class="LoginCellPhoneDialog"
      :close-on-click-modal="false"
      :before-close="handelClose"
    >
      <el-form
        :model="delphone"
        :rules="setphoneFrom.rules2"
        ref="ruleForm3"
        class="demo-ruleForm"
        label-width="130px"
      >
        <div v-if="title != '编辑'" class="Login-c-p-getPhone">
          验证码将会发送至您的手机号：{{ loginInfo.mobile }}，请注意查收！
        </div>
        <el-form-item
          v-if="title != '编辑'"
          label="手机验证码"
          prop="verifyCode"
        >
          <el-input
            v-model="delphone.verifyCode"
            style="display: inline-block; width: 180px"
          ></el-input>
          <el-button
            type="primary"
            plain
            style="width: 110px; padding: 9px 0px"
            @click="CountdownCode"
            v-if="nmb == 120"
            >获取验证码</el-button
          >
          <el-button
            type="primary"
            plain
            style="width: 110px; padding: 9px 0px"
            disabled
            v-else
            >重新获取({{ nmb }})</el-button
          >
        </el-form-item>
        <div v-else>
          <el-form-item label="手机号" prop="mobile">
            <el-input
              disabled
              v-model="delphone.mobile"
              class="input-t"
            ></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="delphone.remark"
              type="textarea"
              class="input-t"
            ></el-input>
          </el-form-item>
        </div>
        <el-form-item>
          <el-button
            @click="DeletePhone = false"
            style="width: 100px; padding: 9px 0"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="delSubmitForm('ruleForm3')"
            style="width: 100px; padding: 9px 0"
            >{{
              title == "删除手机号"
                ? "确认删除"
                : title == "管理员转让"
                ? "确认转让"
                : "确认编辑"
            }}</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>
<script>
import { mapState, mapMutations, mapActions } from "vuex";
import TableTem from "@/components/publicComponents/TableTem";
import ResetNumberVue from "@/components/publicComponents/ResetNumber.vue";
import bus from "../../../../common/bus";
import common from "../../../../../assets/js/common";
export default {
  name: "account",
  components: { TableTem,ResetNumberVue },
  data() {
    // 验证IP规则
    var code = (rule, value, callback) => {
      // if (!this.phoneData) {
      //     return callback(new Error('请选中手机号'));
      // } else
      if (value == "") {
        return callback(new Error("请输入验证码"));
      } else {
        callback();
      }
    };
    var oldPassword = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("原密码不能为空"));
      } else {
        this.$api.get(
          this.API.cpus +
            "consumerclientinfo/validatePassword/" +
            this.formPassword.oldPassword,
          {},
          (res) => {
            if (res.code == 200) {
              callback();
            } else {
              callback(new Error("与原密码不相符"));
            }
          }
        );
      }
    };
    var validatePass = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("新密码不能为空"));
      } else if (
        !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$/g.test(
          value
        )
      ) {
        callback(
          new Error(
            "请输入正确的密码格式！密码由数字、大小写字母组成，密码长度8-16位，且不能包含中文"
          )
        );
      } else {
        if (this.formPassword.passWord !== "") {
          this.$refs.formRule.validateField("passWord");
        }
        callback();
      }
    };
    var validatePass2 = (rule, value, callback) => {
      if (value === "") {
        callback(new Error("密码不能为空"));
      } else if (
        !/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$/g.test(
          value
        )
      ) {
        callback(
          new Error(
            "请输入正确的密码格式！密码由数字、大小写字母组成，密码长度8-16位，且不能包含中文"
          )
        );
      } else if (value !== this.passWordForm.formData.password) {
        callback(new Error("两次输入密码不一致!"));
      } else {
        callback();
      }
    };
    var phone = (rule, value, callback) => {
      if (!/^([，；,;]*1\d{10}[，；,;]*)*$/.test(value)) {
        return callback(new Error("请输入正确手机号"));
      } else if (value == "") {
        return callback(new Error("请输入手机号"));
      } else {
        callback();
      }
    };
    return {
      // SendSettings:{
      //     overrunCode:"",
      //     overrunIndustry:"",
      //     overrunMarket:""
      // },
      title: "",
      SendSettingsArry: ["1", "2", "3", "4", "5", "6"],
      SendSettingsArrys: ["1", "2", "3", "4", "5", "6", "7", "8", "9", "10"],
      sendfrequency: true,
      requencyEnable: false,
      DeletePhone: false,
      delphone: {
        verifyCode: "",
        flag: "2",
        id: "",
        isAdmin: "",
        mobile: "",
        remark: "",
      },
      ispwd: false,
      active: 1,
      cipherMode: "", //加密方式
      activeSalt: 1,
      LcpDialogVisible: false, //手机验证弹出框显示隐藏
      setPhoneSteps: 1, // 设置手机号的步骤
      saltForm: {
        formData: {
          flag: "1",
          verifyCode: "",
          salt: "",
        },
        formRule: {
          salt: [{ required: true, message: "请输入加密盐", trigger: "blur" }],
          verifyCode: [{ required: true, validator: code, trigger: "blur" }],
          secretKey: [
            { required: true, message: "请输入密钥 ", trigger: "blur" },
          ],
        },
      },
      passWordForm: {
        //修改密码的表格--下面表格
        formData: {
          verifyCode: "",
          password: "",
          confirmPwd: "",
          flag: "1",
        },
        // formRule:{
        //     product: [
        //         { type: 'array', required: true, message: '请至少选择一个适用产品', trigger: 'change' }
        //     ]
        // }
      },
      // 余额条数
      NumberBalances: "",
      //余额提醒条数
      reminderBalances: "",
      updateFormDialog: {
        //修改接口密码--弹窗
        formData: {
          code: "", //验证码
        },
      },
      dialogFormVisible: false, //弹窗显示状态
      dialogSaltVisible: false,
      dialogkeyVisible: false,
      keyForm: {
        flag: "1",
        secretKey: "",
        verifyCode: "",
      },
      //弹窗里的表格
      dialogTable: [],
      templateRadio: 0,
      templateSelection: "",
      nmb: 120,
      // ---------------------
      imageUrl: "",
      // actionHttp:'',
      isIndividualization: "",
      token: {},
      formPassword: {
        oldPassword: "",
        newPassword: "",
        passWord: "",
      },
      formLogo: {
        userName: this.$store.state.userName,
        domainName: "",
        reminder: "",
        signature: "",
        title: "",
        recordInformation: "",
        icon: "",
        logo: "",
      },
      changePassword: false,
      // Personalization:false,
      formRules: {
        // oldPassword: [
        //   // { required: true, message: '原密码不能为空', trigger: 'blur' },
        //   { required: true, validator: oldPassword, trigger: 'blur' },
        //   // ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$
        //   // {pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$/gi,message:'密码必须包含数字以及大小写字母8-16位'}
        // ],
        newPassword: [
          { required: true, validator: validatePass, trigger: "blur" },
        ],
        password: [
          { required: true, validator: validatePass2, trigger: "blur" },
        ],
        confirmPwd: [
          { required: true, validator: validatePass2, trigger: "blur" },
        ],
        verifyCode: [{ required: true, validator: code, trigger: "blur" }],
      },
      formLogoRules: {
        domainName: [
          { required: true, message: "请输入域名", trigger: "blur" },
          {
            min: 1,
            max: 50,
            message: "长度在 1 到 50 个字符",
            trigger: "blur",
          },
        ],
        reminder: [
          { required: true, message: "请输入提示语", trigger: "blur" },
          {
            min: 1,
            max: 20,
            message: "长度在 1 到 20 个字符",
            trigger: "blur",
          },
        ],
        signature: [
          { required: true, message: "请输入签名", trigger: "blur" },
          {
            min: 1,
            max: 20,
            message: "长度在 1 到 20 个字符",
            trigger: "blur",
          },
        ],
        title: [
          { required: true, message: "请输入登录title", trigger: "blur" },
          {
            min: 1,
            max: 20,
            message: "长度在 1 到 20 个字符",
            trigger: "blur",
          },
        ],
        recordInformation: [
          { required: true, message: "请输入备案信息", trigger: "blur" },
          {
            min: 1,
            max: 100,
            message: "长度在 1 到 100 个字符",
            trigger: "blur",
          },
        ],
      },
      fileList: [],
      fileList1: [],

      nmb: 120,
      radio: "",
      tableD: [],
      addPhoneForm: {
        verifyCode: "",
        // phone: "",
        // remark: "",
        list: [
          {
            phone: "",
            remark: "",
          },
        ],
      },
      addPhoneRules: {
        verifyCode: [
          { required: true, validator: code, trigger: "blur" },
          { min: 6, max: 6, message: "请输入6位数字验证码" },
        ],
        remark: [
          { required: true, message: "请输入备注" },
          {
            min: 2,
            max: 10,
            message: "长度在 2 到 10 个字符",
            trigger: "blur",
          },
        ],
        phone: [
          { required: true, validator: phone, trigger: "blur" },
          // { min: 6, max: 6, message: '请输入6位数字验证码' }
        ],
      },
      setphoneFrom: {
        ruleForm1: {
          verCode: "",
        },
        rules1: {
          verCode: [
            { required: true, validator: code, trigger: "blur" },
            { min: 6, max: 6, message: "请输入6位数字验证码" },
          ],
        },
        ruleForm2: {
          verifyCode: "",
          phone: "",
          remark: "",
          list: [],
        },
        rules2: {
          verifyCode: [
            { required: true, validator: code, trigger: "blur" },
            { min: 6, max: 6, message: "请输入6位数字验证码" },
          ],
          remark: [
            { required: true, message: "请输入备注" },
            {
              min: 2,
              max: 10,
              message: "长度在 2 到 10 个字符",
              trigger: "blur",
            },
          ],
          phone: [
            { required: true, validator: phone, trigger: "blur" },
            // { min: 6, max: 6, message: '请输入6位数字验证码' }
          ],
        },
        ruleForm3: {
          secretKey: "",
        },
        rules3: {
          secretKey: [
            { required: true, message: "请输入秘钥", trigger: "blur" },
            { min: 8, max: 8, message: "请输入8位字符秘钥", trigger: "blur" },
          ],
        },
      },
      SwitchName: "",
      SwitchJudgment: "",
      tableDataObj: {
        //列表数据
        // loading2:false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        tableLabel: [
          {
            prop: "Numbering",
            showName: "编号",
            width: "60",
            fixed: false,
          },
          {
            prop: "username",
            showName: "用户名",
            fixed: false,
          },
          {
            prop: "phone",
            showName: "手机号码",
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          isDefaultExpand: false, //默认打开折叠
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          // optionWidth: optionWidths, //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: "删除",
            type: "",
            size: "mini",
            optionMethod: "dele",
            icon: "el-icon-error",
          },
        ],
      },
      loginInfo: {
        isAdmin: null,
        consumerName: "",
        mobile: "",
        remark: "",
        id: "",
      },
      codeStatus: null,
      adminform: {
        destId: "",
        sourceId: "",
      },
      phoneId: "",
      mobileIndex: null,
      resetVideo: false,
      infoData: {},
    };
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      compName: (state) => state.compName,
      consumerName: (state) => state.userName,
      createTime: (state) => state.createLocalDateTime,
      userID: (state) => state.userId,
      roleId: (state) => state.roleId,
    }),
  },
  methods: {
    getLoginInfo() {
      this.$api.get(
        this.API.cpus + "userLoginAdmin/loginPhoneInfo",
        {},
        (res) => {
          if (res.code == 200) {
            this.loginInfo = res.data;
          }
        }
      );
    },
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
        {},
        (res) => {
          // let resdata = [];
          // let tabledata = [];
          // let phonelength = res.data.data;
          // this.phoneData = phonelength[0].mobile;
          // this.phoneOriginal = [];
          // for (var i = 0; i < phonelength.length; i++) {
          //   // 列表数据
          //   let a = {};
          //   a.Numbering = i + 1;
          //   a.username = phonelength[0].consumerName;
          //   a.phone = phonelength[i].mobile;
          //   resdata[resdata.length] = a;
          //   this.phoneOriginal.push(phonelength[i].mobile);
          //   // 登录手机号列表
          //   let b = {};
          //   b.index = i;
          //   b.name = i + 1;
          //   b.address = phonelength[i].mobile;
          //   tabledata[tabledata.length] = b;
          // }
          // this.tableDataObj.tableData = resdata;
          // this.tableD = tabledata;
          // this.tableDataObj.loading2 = false;
          // 存储个人信息
          this.tableDataObj.tableData = res.data.data;
          this.tableDataObj.tableData.forEach((item) => {
            item.maskMobile = item.mobile;
          });
          this.tableDataObj.loading2 = false;
          this.roleInfo = res.data[0];
        }
      );
    },
    handelOptionButton(val) {
      //操作列表的点击
      if (val.methods == "dele") {
        //点击删除
        if (this.tableDataObj.tableData.length <= 1) {
          this.$message({
            type: "error",
            duration: "2000",
            message: "最少存在一个手机号",
          });
        } else {
          this.$confirms.confirmation(
            "get",
            "确认删除该手机号",
            this.API.cpus +
              "consumerclientinfo/deleteLoginPhone/" +
              val.row.phone,
            {},
            (res) => {
              this.InquireList();
            }
          );
        }
      }
    },
    //防轰炸设置
    // bombing(){
    //     this.$api.post(this.API.cpus+"consumerclientsms/overrun",{
    //         overrunIndustry:this.SendSettings.overrunIndustry,
    //         overrunCode:this.SendSettings.overrunCode,
    //         overrunMarket:this.SendSettings.overrunMarket,
    //     },res=>{})
    // },
    delFun(obj) {
      this.$confirms.confirmation(
        "post",
        "确认删除该手机号",
        this.API.cpus + "userLoginAdmin/deleteLoginPhoneV2",
        obj,
        (res) => {
          if (res.code == 200) {
            this.DeletePhone = false; //关闭弹出框
            this.nmb = 120;
            clearInterval(this.timer);
            this.InquireList();
          }
        }
      );
    },
    getCodeStatus(type, obj) {
      this.$api.get(
        this.API.cpus + "userLoginAdmin/verifiedStatus",
        {},
        (res) => {
          if (res.code == 200) {
            this.codeStatus = res.data;
            if (type == "del") {
              if (res.data === true) {
                let data = {
                  // verifyCode: this.delphone.verifyCode,
                  flag: this.delphone.flag,
                  phoneId: obj.id,
                };
                this.delFun(data);
              } else {
                this.delphone.id = obj.id;
                this.delphone.isAdmin = obj.isAdmin;
                this.DeletePhone = true;
              }
            } else if (type == "admin") {
              if (res.data === true) {
                let data = {
                  flag: this.delphone.flag,
                  destId: obj.destId,
                  sourceId: obj.sourceId,
                };
                this.adminFun(data);
              } else {
                this.adminform.destId = obj.destId;
                this.adminform.sourceId = obj.sourceId;
                this.DeletePhone = true;
              }
            }
          }
        }
      );
    },
    //点击修改密码按钮
    modifyPsd() {
      this.dialogFormVisible = true; //显示弹窗
    },
    editSalt() {
      this.dialogSaltVisible = true;
    },
    addKey() {
      this.dialogkeyVisible = true;
    },
    editphone(row) {
      this.title = "编辑";
      this.phoneId = row.id;
      this.delphone.mobile = row.mobile;
      this.delphone.remark = row.remark || "";
      this.DeletePhone = true;
    },
    deletephone(row) {
      this.title = "删除手机号";
      if (row.isAdmin == 1) {
        this.$message({
          type: "error",
          duration: "2000",
          message: "管理员账号不可删除",
        });
      } else {
        let data = {
          id: row.id,
          isAdmin: row.isAdmin,
          DeletePhone: true,
        };
        this.getCodeStatus("del", data);
      }
    },
    adminFun(data) {
      this.$confirms.confirmation(
        "post",
        "是否将该账号设置为管理员",
        this.API.cpus + "userLoginAdmin/transferAdmin",
        data,
        (res) => {
          if (res.code == 200) {
            this.DeletePhone = false;
            this.getLoginInfo();
            this.InquireList();
          }
        }
      );
    },
    transferAdmin(row) {
      let data = {
        destId: row.id,
        sourceId: "",
      };
      for (let i = 0; i < this.tableDataObj.tableData.length; i++) {
        if (this.tableDataObj.tableData[i].isAdmin == 1) {
          data.sourceId = this.tableDataObj.tableData[i].id;
          break;
        }
      }
      this.title = "管理员转让";
      this.getCodeStatus("admin", data);
    },
    //获取弹出框手机号
    getPhoneArr(type) {
      this.dialogTable = [];
      this.$api.post(
        this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
        {},
        (res) => {
          let phonelength = res.data.data;
          this.templateSelection = phonelength[0].mobile; //把第一个手机号设置为默认选中

          for (var i = 0; i < phonelength.length; i++) {
            this.dialogTable.push({ phone: phonelength[i].mobile });
          }
          if (type == "pwd") {
            this.dialogFormVisible = true; //显示弹窗
          } else {
            this.dialogSaltVisible = true;
          }
        }
      );
    },
    //获取验证码
    send() {
      this.$api.get(
        this.API.cpus +
          "userLoginAdmin/sendVerificationCode?flag=1&phoneId=" +
          this.loginInfo.id,
        {},
        (res) => {
          if (res.code == 200) {
            this.$message({
              type: "success",
              duration: "2000",
              message: "验证码已发送至手机!",
            });
            --this.nmb;
            this.timer = setInterval((res) => {
              --this.nmb;
              if (this.nmb < 1) {
                this.nmb = 120;
                clearInterval(this.timer);
              }
            }, 1000);
          } else {
            this.$message({
              type: "warning",
              message: "验证码未失效，请失效后再次申请!",
            });
          }
        }
      );
      // if (this.templateSelection) {
      // } else {
      //   this.$message({
      //     message: "选择发送验证码的手机号码！",
      //     type: "warning",
      //   });
      // }
    },
    ispeds() {
      this.ispwd = !this.ispwd;
    },
    //获取选中手机号码
    getTemplateRow(index, row) {
      this.templateSelection = row.phone;
    },
    beforec() {
      this.dialogFormVisible = false; //隐藏弹窗
      this.dialogSaltVisible = false;
    },
    updateFormDialog_ok(formName, type) {
      //弹框第一步确认
      this.$refs[formName].validate((valid) => {
        if (valid) {
          //提交验证码
          this.$api.get(
            this.API.cpus +
              "code/checkVerificationCode?code=" +
              this.updateFormDialog.formData.code +
              "&flag=1",
            {},
            (res) => {
              if (res.code == 200) {
                if (type == "pwd") {
                  this.active = 2;
                } else {
                  this.activeSalt = 2;
                }
              } else {
                this.$message.error("验证码无效!");
              }
            }
          );
        } else {
          return false;
        }
      });
    },
    addWhiteListAction() {
      let obj = {
        phone: "",
        remark: "",
      };
      this.addPhoneForm.list.push(obj);
    },
    updateFormOk(formName) {
      //弹框第二步确认
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            verifyCode: this.passWordForm.formData.verifyCode,
            password: this.passWordForm.formData.password,
            flag: this.passWordForm.formData.flag,
          };
          this.$confirms.confirmation(
            "put",
            "此操作将修改接口密码数据, 是否继续？",
            this.API.cpus + "consumerclientinfo/updPasswordV2",
            data,
            (res) => {
              if (res.code == 200) {
                this.dialogFormVisible = false; //隐藏弹窗
                this.nmb = 120;
                clearInterval(this.timer);
              }
            }
          );
        } else {
          return false;
        }
      });
    },
    //修改加密盐
    updateSalt(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            flag: this.saltForm.formData.flag,
            verifyCode: this.saltForm.formData.verifyCode,
            salt: this.saltForm.formData.salt,
          };
          this.$confirms.confirmation(
            "put",
            "此操作将修改加密盐, 是否继续？",
            this.API.cpus + "consumerclientinfo/saltV2",
            data,
            (res) => {
              if (res.code == 200) {
                this.dialogSaltVisible = false; //隐藏弹窗
                this.nmb = 120;
                clearInterval(this.timer);
              }
            }
          );
        } else {
          return false;
        }
      });
    },
    updatekey(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let data = {
            flag: this.keyForm.flag,
            verifyCode: this.keyForm.verifyCode,
            secretKey: this.keyForm.secretKey,
          };
          this.$confirms.confirmation(
            "post",
            "此操作将修改密钥, 是否继续？",
            this.API.cpus + "v3/consumer/manager/user/secrectKeyV2",
            data,
            (res) => {
              if (res.code == 200) {
                this.dialogkeyVisible = false; //隐藏弹窗
                this.nmb = 120;
                clearInterval(this.timer);
              }
            }
          );
        } else {
          return false;
        }
      });
    },
    handelDecode(val, index) {
      this.mobileIndex = index;
      this.$api.post(
        this.API.upms + "generatekey/decryptMobile",
        {
          keyId: val.keyId,
          smsInfoId: val.smsInfoId,
          cipherMobile: val.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data;
            // this.$nextTick(() => {
            //     this.$set(this.tableDataObj.tableData[index], "maskMobile", res.data);
            // });
            // console.log(this.tableDataObj.tableData, 'this.tableDataObj.tableData');
          } else if (res.code == 4004002) {
            common.fetchData().then((res) => {
              if (res.code == 200) {
                if (res.data.isAdmin == 1) {
                  this.resetVideo = true;
                  this.infoData = res.data;
                } else {
                  this.$message({
                    message:
                      "您今日解密次数已超限，如需重置解密次数，请联系管理员！",
                    type: "warning",
                  });
                }
              } else {
                this.$message({
                  message: res.msg,
                  type: "error",
                });
              }
            });
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
          // this.tableDataObj.tableData[index].mobile=res.data
        }
      );
    },
    // 点击启用
    // handleRequencyEnable(){
    //     this.$api.post(this.API.cpus+'/consumerClientBalanceNotice/save',{smsNum:this.reminderBalances},res=>{

    //     })
    //     this.requencyEnable = true;
    //     this.sendfrequency = true;
    // },
    //设置
    // SetBalance(){
    //     this.sendfrequency = !this.sendfrequency;
    // },
    //点击确定
    // determine(){
    //     const testNum= /^([1-9][0-9]{0,6}|10000000)$/;
    //     if(testNum.test(this.NumberBalances)){
    //         this.$confirms.confirmation('post','确定余额条数不足'+this.NumberBalances+'条时提醒',this.API.cpus + '/consumerClientBalanceNotice/save',{smsNum:this.NumberBalances},res =>{
    //             // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
    //             //     this.reminderBalances=res.smsNum
    //             // })
    //             this.sendfrequency = !this.sendfrequency;
    //             this.dialogFormVisible=false;//隐藏弹窗
    //         });
    //     }else{
    //         this.$message({
    //             type: 'error',
    //             duration:'2000',
    //             message:"请填写1-10000000的余额条数"
    //         });
    //     }
    // },
    // shutDownSetBalance(){
    //     this.$confirms.confirmation('get','确认关闭余额提醒设置',this.API.cpus+'consumerClientBalanceNotice/close',{},res =>{
    //         this.requencyEnable = !this.requencyEnable;
    //     });
    // },
    // =======================================
    // 图片上传
    // handleRemove(file, fileList) {
    //     this.formLogo.icon=""
    // },
    // handlePreview(file) {
    //     // console.log(file);
    // },
    // handleRemove1(file, fileList) {
    //     this.formLogo.logo=""
    // },
    // handlePreview1(file) {
    //     // console.log(file);
    // },
    // // 打上传成功
    // handleAvatarSuccess(res, file) {
    //     this.imageUrl = URL.createObjectURL(file.raw);
    //     // this.getImgUrl();
    // },
    // handleAvatarSuccess1(res, file) {
    //     this.formLogo.icon=res.data
    // },
    // handleAvatarSuccess2(res, file) {
    //     this.formLogo.logo=res.data
    // },
    // 获取项目绝对路径
    // ...mapActions([  //比如'movies/getHotMovies
    //     'saveImg',
    // ]),
    // beforeAvatarUpload(file) {
    //     console.log(file)
    //     const isJPG = file.type === 'image/jpg';
    //     const isjpeg = file.type === 'image/jpeg';
    //     const isJPG1 = file.type === 'image/png';
    //     const isJPG2 = file.type === 'image/gif';
    //     // const isLt2M = file.size / 1024 / 1024 < 2;
    //     if (!isJPG && !isJPG1 && !isJPG2 && !isjpeg) {
    //         this.$message.error('上传头像图片只能是 jpg、png、gif 格式!');
    //         return false;
    //     }
    //     // if (!isLt2M) {
    //     //     this.$message.error('上传头像图片大小不能超过 2MB!');
    //     // }

    //     // return isJPG && isLt2M;
    // },
    // beforeAvatarUpload1(file) {
    //     console.log(file)
    //     const isico = file.type === 'image/x-icon';
    //     // const isLt2M = file.size / 1024 / 1024 < 2;
    //     if (!isico) {
    //         this.$message.error('上传头像图片只能是 ico 格式!');
    //         return false;
    //     }
    //     // if (!isLt2M) {
    //     //     this.$message.error('上传头像图片大小不能超过 2MB!');
    //     // }

    //     // return isJPG && isLt2M;
    // },
    // beforeAvatarUpload2(file) {
    //     console.log(file)
    //     const isJPG1 = file.type === 'image/png';
    //     const isLt2M = file.size / 1024 / 1024 < 1;
    //     if (!isJPG1) {
    //         this.$message.error('上传头像图片只能是 png 格式!');
    //         return false;
    //     }
    //     if (!isLt2M) {
    //         this.$message.error('上传头像图片大小不能超过 1MB!');
    //     }

    //     // return isJPG && isLt2M;
    // },
    //获取img
    getImgUrl() {
      this.$api.get(
        this.API.cpus + "consumerclientinfo/getClientInfo",
        null,
        (res) => {
          if (res.code == 200) {
            if (res.data.cipherMode) {
              this.cipherMode = res.data.cipherMode;
            }
          }
        }
      );
    },
    // avatarClick(){
    //     this.$refs.avatar.click()
    // },
    //确定修改密码
    changePwd(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          let param = {};
          param.oldPassword = this.formPassword.oldPassword;
          param.newPassword = this.formPassword.newPassword;
          this.$confirms.confirmation(
            "get",
            "确定修改密码？",
            this.API.cpus + "consumerclientinfo/updateLoginPassword",
            param,
            (res) => {
              this.changePassword = false; //关闭弹出框
            }
          );
        } else {
          return false;
        }
      });
    },
    // 个性化设置提交
    // personalise(val){
    //     this.$refs[val].validate((valid) => {
    //         if (valid) {
    //             if(!this.formLogo.icon){
    //                 this.$message({
    //                     type: 'error',
    //                     duration:'2000',
    //                     message:"请上传icon"
    //                 });
    //             }else if(!this.formLogo.logo){
    //                 this.$message({
    //                     type: 'error',
    //                     duration:'2000',
    //                     message:"请上传logo"
    //                 });
    //             }else{
    //                 this.$confirms.confirmation('post','保存设置？',this.API.cpus+'personalizationSettings/insertSettings',this.formLogo,res =>{
    //                     this.Personalization=false; //关闭弹出框
    //                 });
    //             }
    //         }else{
    //             return false;
    //         }
    //     });
    // },
    // LogoSetting(){
    //     this.$api.post(this.API.cpus + 'personalizationSettings/getSttingsByUserName',{userName:this.$store.state.userName},res=>{
    //         if(res.data!=null){
    //             this.formLogo=res.data
    //             this.fileList=[{name: '', url:res.data.icon}]
    //             this.fileList1=[{name: '', url:res.data.logo}]
    //         }
    //         this.Personalization=true;
    //     })
    // },
    //登录手机号弹出层
    addphone() {
      if (this.tableDataObj.tableData.length >= 10) {
        this.$message({
          type: "error",
          duration: "2000",
          message: "用户最多添加10个手机号码",
        });
      } else {
        this.radio = 0;
        this.getCodeStatus();
        this.LcpDialogVisible = true;
      }
      // 判断是否为修改密码
      // if (val == "changePassword") {
      //   this.SwitchName = "修改登录密码";
      //   this.SwitchJudgment = "changePassword";
      //   this.LcpDialogVisible = true;
      // } else if (val == "AddPhoneNumber") {
      //   if (this.tableDataObj.tableData.length >= 10) {
      //     this.$message({
      //       type: "error",
      //       duration: "2000",
      //       message: "用户最多添加10个手机号码",
      //     });
      //     return false;
      //   } else {
      //     this.SwitchName = "添加登陆手机号";
      //     this.SwitchJudgment = "AddPhoneNumber";
      //     this.LcpDialogVisible = true;
      //   }
      // } else if (val == "secretKey") {
      //   this.SwitchName = "修改秘钥";
      //   this.SwitchJudgment = "secretKey";
      //   this.LcpDialogVisible = true;
      // }
      // this.$api.post(
      //   this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
      //   {},
      //   (res) => {
      //     let resdata = [];
      //     let tabledata = [];
      //     let phonelength = res.data.data;
      //     this.phoneData = phonelength[0].mobile;
      //     this.phoneOriginal = [];
      //     for (var i = 0; i < phonelength.length; i++) {
      //       // 列表数据
      //       let a = {};
      //       a.Numbering = i + 1;
      //       a.username = phonelength[0].consumerName;
      //       a.phone = phonelength[i].mobile;
      //       resdata[resdata.length] = a;
      //       this.phoneOriginal.push(phonelength[i].mobile);
      //       // 登录手机号列表
      //       let b = {};
      //       b.index = i;
      //       b.name = i + 1;
      //       b.address = phonelength[i].mobile;
      //       tabledata[tabledata.length] = b;
      //     }
      //     this.tableD = tabledata;
      //   }
      // );
      // // if(this.tableDataObj.tableData.length>=5){
      // //     this.$message({
      // //         type: 'error',
      // //         duration:'2000',
      // //         message:"最多添加5个手机号码"
      // //     });
      // // }else{
      // this.radio = 0;
      // // }
    },
    handelClose() {
      //×号关闭弹窗
      // 点击关闭
      this.LcpDialogVisible = false; //关闭弹出框
    },
    cancel() {
      this.LcpDialogVisible = false; //关闭弹出框
      this.setPhoneSteps = 1; //步进改为1
      this.setphoneFrom.ruleForm1.verCode = ""; //验证码置空
      this.setphoneFrom.ruleForm2.setNewPhone = ""; //手机号置空
      this.setphoneFrom.ruleForm3.secretKey = ""; //秘钥置空
      this.InquireList();
    },
    // 新增登录手机号
    submitForm(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          let data = {
            flag: 2,
            phoneList: this.addPhoneForm.list,
            verifyCode: this.addPhoneForm.verifyCode,
            // phone: this.setphoneFrom.ruleForm2.phone,
            // verifyCode: this.setphoneFrom.ruleForm2.verifyCode,
            // remark: this.setphoneFrom.ruleForm2.remark,
          };
          this.$api.post(
            this.API.cpus + "userLoginAdmin/addLoginPhoneV2",
            data,
            (res) => {
              if (res.code == 200) {
                this.LcpDialogVisible = false; //关闭弹出框
                // this.cancel();
                this.nmb = 120;
                clearInterval(this.timer);
                this.InquireList(); //刷新列表
              } else {
                this.$message({
                  type: "error",
                  duration: "2000",
                  message: res.msg,
                });
              }
            }
          );
        } else {
          return false;
        }
        // if (val == "ruleForm1") {
        //   if (valid) {
        //     this.$api.get(
        //       this.API.cpus +
        //       "code/checkVerificationCode?code=" +
        //       this.setphoneFrom.ruleForm1.verCode +
        //       "&flag=2",
        //       {},
        //       (res) => {
        //         if (res.code == 200) {
        //           this.setPhoneSteps = 2;
        //         } else {
        //           this.$message({
        //             type: "error",
        //             duration: "2000",
        //             message: "验证码无效！",
        //           });
        //         }
        //       }
        //     );
        //   } else {
        //     console.log("error submit!!");
        //     return false;
        //   }
        // } else if (val == "formRule") {
        //   this.$refs[val].validate((valid) => {
        //     if (valid) {
        //       let param = {};
        //       param.oldPassword = this.formPassword.oldPassword;
        //       param.newPassword = this.formPassword.newPassword;
        //       this.$confirms.confirmation(
        //         "get",
        //         "确定修改密码？",
        //         this.API.cpus + "consumerclientinfo/updateLoginPassword",
        //         param,
        //         (res) => {
        //           this.LcpDialogVisible = false; //关闭弹出框
        //         }
        //       );
        //     } else {
        //       return false;
        //     }
        //   });
        // } else if (val == "ruleForm2") {
        //   if (valid) {
        //     let flag = true;
        //     for (var i = 0; i < this.phoneOriginal.length; i++) {
        //       if (
        //         this.phoneOriginal[i] == this.setphoneFrom.ruleForm2.setNewPhone
        //       ) {
        //         flag = false;
        //         break;
        //       }
        //     }
        //     if (flag == true) {
        //       this.$confirms.confirmation(
        //         "get",
        //         "确认添加该手机号",
        //         this.API.cpus +
        //         "consumerclientinfo/addLoginPhone/" +
        //         this.setphoneFrom.ruleForm2.setNewPhone,
        //         {},
        //         (res) => {
        //           this.cancel();
        //         }
        //       );
        //     } else {
        //       this.$message({
        //         type: "error",
        //         duration: "2000",
        //         message: "该手机号已存在,不可重复添加",
        //       });
        //     }
        //   } else {
        //     console.log("error submit!!");
        //     return false;
        //   }
        // } else if (val == "ruleForm3") {
        //   if (valid) {
        //     this.$confirms.confirmation(
        //       "post",
        //       "确认修改秘钥",
        //       this.API.cpus + "v3/consumer/manager/user/secrectKey",
        //       { secretKey: this.setphoneFrom.ruleForm3.secretKey },
        //       (res) => {
        //         this.cancel();
        //       }
        //     );
        //   }
        // }
      });
    },
    delSubmitForm(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (this.title == "删除手机号") {
            let data = {
              verifyCode: this.delphone.verifyCode,
              flag: this.delphone.flag,
              phoneId: this.delphone.id,
            };
            this.delFun(data);
          } else if (this.title == "管理员转让") {
            let data = {
              verifyCode: this.delphone.verifyCode,
              destId: this.adminform.destId,
              sourceId: this.adminform.sourceId,
              flag: this.delphone.flag,
            };
            this.adminFun(data);
          } else if (this.title == "编辑") {
            let data = {
              phoneId: this.phoneId,
              remark: this.delphone.remark,
            };
            this.$api.post(
              this.API.cpus + "userLoginAdmin/updateLoginPhone",
              data,
              (res) => {
                if (res.code == 200) {
                  this.DeletePhone = false;
                  this.InquireList(); //刷新列表
                } else {
                  this.$message({
                    type: "error",
                    duration: "2000",
                    message: res.msg,
                  });
                }
              }
            );
          }
          // if (this.delphone.isAdmin == 1) {
          //   this.$message({
          //     type: "error",
          //     duration: "2000",
          //     message: "管理员账号不可删除",
          //   });
          // } else {
          //   this.$confirms.confirmation(
          //     "post",
          //     "确认删除该手机号",
          //     this.API.cpus +
          //     "userLoginAdmin/deleteLoginPhoneV2",
          //     data,
          //     (res) => {
          //       if (res.code == 200) {
          //         this.DeletePhone = false; //关闭弹出框
          //         this.nmb = 120;
          //         clearInterval(this.timer);
          //         this.InquireList();
          //       }
          //     }
          //   );
          // }
        } else {
          return false;
        }
      });
    },
    // 获取验证码倒计时
    CountdownCode() {
      this.$api.get(
        this.API.cpus +
          "userLoginAdmin/sendVerificationCode?flag=2&phoneId=" +
          this.loginInfo.id,
        {},
        (res) => {
          if (res.code == 200) {
            this.flag = true;
            --this.nmb;
            this.timer = setInterval((res) => {
              --this.nmb;
              if (this.nmb < 1) {
                this.nmb = 120;
                this.flag = false;
                clearInterval(this.timer);
              } else {
                this.flag = true;
              }
            }, 1000);
            this.$message({
              type: "success",
              duration: "2000",
              message: "验证码已发送至手机!",
            });
          } else {
            this.flag = true;
            this.$message({
              type: "warning",
              message: "验证码未失效，需失效后重新获取!",
            });
          }
        }
      );
      // if (this.phoneData) {

      // } else {
      //     this.$message({
      //         message: "请先选中手机号码",
      //         type: "warning",
      //     });
      // }
    },
    showRow(row) {
      //赋值给radio
      this.radio = this.tableD.indexOf(row);
    },
    getCurrentRow(val) {
      this.phoneData = this.tableD[val].address; //赋值手机号
    },
  },
  watch: {
    dialogFormVisible(val) {
      if (!val) {
        // this.$refs.updateFormDialog.resetFields(); //清空表单
        this.$refs.passWordForm.resetFields(); //清空表单
        // this.active = 1;
      }
    },
    dialogkeyVisible(val) {
      if (!val) {
        // this.$refs.updateFormDialog.resetFields(); //清空表单
        this.$refs.keyForm.resetFields(); //清空表单
        // this.active = 1;
      }
    },
    dialogSaltVisible(val) {
      if (!val) {
        // this.$refs.updateFormDialog.resetFields(); //清空表单
        this.$refs.saltForm.resetFields(); //清空表单
        // this.active = 1;
      }
    },
    // ===============================
    changePassword(newVal, oldVal) {
      if (newVal == false) {
        this.$refs.formRule.resetFields();
      }
    },
    // Personalization(newVal,oldVal){
    //     if(newVal==false){
    //         this.$refs.formLogos.resetFields();
    //         this.fileList=[]
    //         this.fileList1=[]
    //      }
    // },

    LcpDialogVisible: function (val) {
      if (!val) {
        this.$refs.ruleForm2.resetFields();
        this.addPhoneForm.list = [
          {
            phone: "",
            remark: "",
          },
        ];
        // this.$refs.ruleForm2.resetFields()
      }
    },
    DeletePhone(val) {
      if (!val) {
        this.$refs.ruleForm3.resetFields();
      }
    },
    resetVideo(val) {
      if (!val) {
        this.mobileIndex = null;
      }
    }
  },

  // activated(){
  //     this.InquireList();
  // },
  created() {
    bus.$on("closeVideo", (msg) => {
      this.resetVideo = msg;
    });
    this.getLoginInfo();
    this.InquireList();
    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
    //     if(res){
    //         if(res.smsIsOpen==2){
    //             this.requencyEnable=false
    //         }else{
    //             this.requencyEnable=true
    //         }
    //             this.reminderBalances=res.smsNum
    //     }else{
    //         this.requencyEnable=false
    //     }
    // })
    // =====================================
    // this.token = {Authorization :"Bearer"  + this.$common.getCookie('ZTGlS_TOKEN')};
    // this.actionHttp = this.API.cpus + "consumerclientinfo/updatePortrait";
    // this.actionHttp1= this.API.cpus + "consumerclientinfo/uploadImage"
    this.getImgUrl();
    // this.$api.get(this.API.cpus+'consumerclientsms/overrun',{},res=>{
    //     this.SendSettings.overrunCode=res.data.overrunCode
    //     this.SendSettings.overrunIndustry=res.data.overrunIndustry
    //     this.SendSettings.overrunMarket=res.data.overrunMarket
    // })
  },
};
</script>
<style scoped>
.Login-c-p-getPhone {
  margin-top: 20px;
  color: #909399;
  font-size: 14px;
  margin-left: 16px;
  margin-bottom: 10px;
}

.add-white-list {
  width: 650px;
  height: 50px;
  line-height: 50px;
  border: 1px dashed #66ccff;
  text-align: center;
  color: #66ccff;
  cursor: pointer;
  margin: 0 auto;
}

.add-white {
  width: 640px;
  height: 30px;
  line-height: 30px;
  border: 1px dashed #66ccff;
  text-align: center;
  color: #66ccff;
  cursor: pointer;
  margin: 0 auto;
}

.input-t {
  width: 250px;
}

@media screen and (min-width: 1600px) {
  .LoginCellPhone-box {
    padding: 20px;
    background: #fff;
  }

  .LoginCellPhone-matter {
    border: 1px solid #66ccff;
    background: #e5f0ff;
    padding: 10px;
    font-size: 12px;
  }

  .LoginCellPhone-matter > div {
    height: 26px;
    line-height: 26px;
  }

  .LoginCellPhone-creat {
    margin: 20px 0px;
  }

  .appkeyShow {
    display: inline-block;
    padding-left: 10px;
    color: #16a589;
    cursor: pointer;
  }

  .bas-block {
    margin-bottom: 10px;
    padding: 30px;
    position: relative;
    height: 220px;
  }

  .basic-cfg-title {
    font-weight: bold;
    padding-bottom: 32px;
    color: #333;
  }

  .basic-cfg-tit {
    width: 92px;
    display: inline-block;
  }

  .basic-cfg-cot {
    margin-bottom: 26px;
  }

  .basic-cfg-tips {
    padding-left: 96px;
    padding-top: 4px;
    font-size: 12px;
    color: #999;
  }

  .appkeyShow {
    color: #16a589;
    cursor: pointer;
  }

  .dialogTable {
    margin: 0 0 30px 0;
  }

  .passWord-footer {
    padding-top: 20px;
    text-align: right;
  }

  .footer-center-button {
    width: 100px;
    padding: 9px 0px;
  }

  .passWord-main {
    padding: 35px 24px 71px 0px;
  }

  .tips-box {
    position: absolute;
    background: #fff;
    padding: 20px;
    border: 1px solid #e6e6e6;
  }

  .fade-enter-active {
    animation: show-in 1s;
    transition: all 1s;
  }

  .fade-leave-active {
    animation: show-out 1s;
    transition: all 1s;
  }

  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }

  .basic-help {
    margin: 30px 0 20px 0;
    padding-bottom: 10px;
    width: 500px;
    border-bottom: 1px solid #e6e6e6;
  }

  @keyframes show-in {
    0% {
      transform: rotateX(0deg);
    }

    100% {
      transform: rotateX(360deg);
    }
  }

  @keyframes show-out {
    0% {
      transform: rotateX(360deg);
    }

    100% {
      transform: rotateX(0deg);
    }
  }

  .bac-color-red {
    color: red;
  }

  /* ---------------------------------- */
  header {
    font-weight: bold;
    color: #333;
  }

  .PersonalInformation-main {
    margin-bottom: 10px;
  }

  .PersonalInformation-main,
  .PersonalPass-main {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
  }

  .ChangeAvatar {
    padding-right: 20px;
    text-align: center;
  }

  .ChangeAvatar,
  .information {
    display: inline-block;
  }

  .avatarClick {
    cursor: pointer;
    margin-top: 12px;
    display: inline-block;
    width: 100%;
  }

  .avatarClick:hover {
    color: rgb(22, 160, 133);
  }

  .basicInfo {
    margin-top: 26px;
    clear: both;
    overflow: hidden;
  }

  .basicInfo-left {
    float: left;
  }

  .headerPg {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 1px solid #ccc;
    overflow: hidden;
  }

  .headerPg img {
    width: 100%;
    height: 100%;
  }

  .basicInfo-right {
    float: left;
    padding-top: 23px;
    padding-left: 40px;
    background: #fff;
    width: 100%;
  }

  .basicInfo-line {
    margin-bottom: 14px;
  }

  .basicInfo-title {
    display: inline-block;
    width: 90px;
    text-align: right;
    color: #333;
  }

  .avatar-uploader {
    border: 1px solid;
    border-radius: 50%;
    overflow: hidden;
    width: 120px;
    height: 120px;
    border-color: #55555561;
  }

  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
  }

  .avatar {
    width: 120px;
    height: 120px;
    display: block;
  }

  .changePassword {
    color: rgb(22, 160, 133);
    cursor: pointer;
  }

  .changePassword:hover {
    color: red;
  }
}

@media screen and (max-width: 1700px) {
  .LoginCellPhone-box {
    padding: 20px;
    background: #fff;
  }

  .LoginCellPhone-matter {
    border: 1px solid #66ccff;
    background: #e5f0ff;
    padding: 10px;
    font-size: 12px;
  }

  .LoginCellPhone-matter > div {
    line-height: 26px;
    width: 100%;
    /* height: 80px; */
  }

  .LoginCellPhone-creat {
    margin: 20px 0px;
  }

  .appkeyShow {
    display: inline-block;
    padding-left: 10px;
    color: #16a589;
    cursor: pointer;
  }

  .bas-block {
    margin-bottom: 10px;
    padding: 30px;
    position: relative;
    height: 220px;
  }

  .basic-cfg-title {
    font-weight: bold;
    padding-bottom: 32px;
    color: #333;
  }

  .basic-cfg-tit {
    width: 92px;
    display: inline-block;
  }

  .basic-cfg-cot {
    margin-bottom: 26px;
  }

  .basic-cfg-tips {
    padding-left: 96px;
    padding-top: 4px;
    font-size: 12px;
    color: #999;
  }

  .appkeyShow {
    color: #16a589;
    cursor: pointer;
  }

  .dialogTable {
    margin: 0 0 30px 0;
  }

  .passWord-footer {
    padding-top: 20px;
    text-align: right;
  }

  .footer-center-button {
    width: 100px;
    padding: 9px 0px;
  }

  .passWord-main {
    padding: 35px 24px 71px 0px;
  }

  .tips-box {
    position: absolute;
    background: #fff;
    padding: 20px;
    border: 1px solid #e6e6e6;
  }

  .fade-enter-active {
    animation: show-in 1s;
    transition: all 1s;
  }

  .fade-leave-active {
    animation: show-out 1s;
    transition: all 1s;
  }

  .fade-enter,
  .fade-leave-to {
    opacity: 0;
  }

  .basic-help {
    margin: 30px 0 20px 0;
    padding-bottom: 10px;
    width: 500px;
    border-bottom: 1px solid #e6e6e6;
  }

  @keyframes show-in {
    0% {
      transform: rotateX(0deg);
    }

    100% {
      transform: rotateX(360deg);
    }
  }

  @keyframes show-out {
    0% {
      transform: rotateX(360deg);
    }

    100% {
      transform: rotateX(0deg);
    }
  }

  .bac-color-red {
    color: red;
  }

  /* ---------------------------------- */
  header {
    font-weight: bold;
    color: #333;
  }

  .PersonalInformation-main {
    margin-bottom: 10px;
  }

  .PersonalInformation-main,
  .PersonalPass-main {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
  }

  .ChangeAvatar {
    padding-right: 20px;
    text-align: center;
  }

  .ChangeAvatar,
  .information {
    display: inline-block;
  }

  .avatarClick {
    cursor: pointer;
    margin-top: 12px;
    display: inline-block;
    width: 100%;
  }

  .avatarClick:hover {
    color: rgb(22, 160, 133);
  }

  .basicInfo {
    margin-top: 26px;
    clear: both;
    overflow: hidden;
  }

  .basicInfo-left {
    float: left;
  }

  .headerPg {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 1px solid #ccc;
    overflow: hidden;
  }

  .headerPg img {
    width: 100%;
    height: 100%;
  }

  .basicInfo-right {
    float: left;
    padding-top: 23px;
    /* padding-left: 40px; */
    background: #fff;
  }

  .basicInfo-line {
    margin-bottom: 14px;
  }

  .basicInfo-title {
    display: inline-block;
    width: 90px;
    text-align: right;
    color: #333;
  }

  .avatar-uploader {
    border: 1px solid;
    border-radius: 50%;
    overflow: hidden;
    width: 120px;
    height: 120px;
    border-color: #55555561;
  }

  .avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }

  .avatar-uploader .el-upload:hover {
    border-color: #409eff;
  }

  .avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
  }

  .avatar {
    width: 120px;
    height: 120px;
    display: block;
  }

  .changePassword {
    color: rgb(22, 160, 133);
    cursor: pointer;
  }

  .changePassword:hover {
    color: red;
  }
}
</style>



