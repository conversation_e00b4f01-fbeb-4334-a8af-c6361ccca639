<template>
  <div class="login_cell_phone bag">
    <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 国际价格表</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <!-- 查询框开始 -->
        <el-form
          :inline="true"
          :model="passageformInline"
          label-width="80px"
          class="demo-form-inline"
          ref="passageform"
        >
          <el-form-item label="用户名称" prop="userName">
            <el-input
              v-model="passageformInline.userName"
              placeholder="请输入用户名"
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label-width="80px">
            <el-button type="primary" plain @click="Query()">查询</el-button>
            <el-button type="primary" plain @click="reSet()">重置</el-button>
            <el-button type="primary" plain @click="allAdjustmentPirce()"
              >一键调价</el-button
            >
            <el-button type="primary" plain @click="restPirce()"
              >恢复默认价</el-button
            >
          </el-form-item>
        </el-form>
        <div class="Mail-table" style="padding-bottom: 40px; display: flex">
          <el-table
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj"
            style="width: 100%"
            :header-cell-style='rowClass'
          >
            <el-table-column label="地区">
              <template slot-scope="scope">{{
                scope.row.area1 + " (" + scope.row.type1 + ")"
              }}</template>
            </el-table-column>
            <el-table-column label="定价——底价">
              <template slot-scope="scope">
                <div
                  @dblclick="price1(scope.row.price1)"
                  style="display: flex; justify-content: space-between"
                >
                  <span
                    :class="
                      scope.row.price1 > scope.row.glsPrice1
                        ? 'text'
                        : scope.row.price1 == scope.row.glsPrice1
                        ? 'text1'
                        : scope.row.price1 < scope.row.glsPrice1
                        ? 'text2'
                        : ''
                    "
                    v-if="flag"
                    >{{ scope.row.price1 }}</span
                  >
                  <!-- <span :class="scope.row.price1 == scope.row.glsPrice1?'text1':''" v-if="flag">{{ scope.row.price1 }}</span>
                  <span :class="scope.row.price1 < scope.row.glsPrice1?'text2':''" v-if="flag">{{ scope.row.price1 }}</span> -->
                  <input
                    v-else
                    v-model="scope.row.price1"
                    @change="change(scope.row)"
                    @blur="input"
                    class="input1"
                    type="text"
                  />
                  <span>{{ scope.row.glsPrice1 }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="地区">
              <template slot-scope="scope">{{
                scope.row.area2 + " (" + scope.row.type2 + ")"
              }}</template>
            </el-table-column>
            <el-table-column label="定价——底价">
              <template slot-scope="scope">
                <div
                  @dblclick="price1"
                  style="display: flex; justify-content: space-between"
                >
                  <span
                    :class="
                      scope.row.price2 > scope.row.glsPrice2
                        ? 'text'
                        : scope.row.price2 == scope.row.glsPrice2
                        ? 'text1'
                        : scope.row.price2 < scope.row.glsPrice2
                        ? 'text2'
                        : ''
                    "
                    v-if="flag"
                    >{{ scope.row.price2 }}</span
                  >
                  <!-- <span v-if="flag">{{ scope.row.price2 }}</span> -->
                  <input
                    v-else
                    v-model="scope.row.price2"
                    @change="change1(scope.row)"
                    @blur="input"
                    class="input1"
                    type="text"
                  />
                  <span>{{ scope.row.glsPrice2 }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="地区">
              <template slot-scope="scope">{{
                scope.row.area3 + " (" + scope.row.type3 + ")"
              }}</template>
            </el-table-column>
            <el-table-column label="定价——底价">
              <template slot-scope="scope">
                <div
                  @dblclick="price1"
                  style="display: flex; justify-content: space-between"
                >
                  <!-- <span v-if="flag">{{ scope.row.price3 }}</span> -->
                  <span
                    :class="
                      scope.row.price3 > scope.row.glsPrice3
                        ? 'text'
                        : scope.row.price3 == scope.row.glsPrice3
                        ? 'text1'
                        : scope.row.price3 < scope.row.glsPrice3
                        ? 'text2'
                        : ''
                    "
                    v-if="flag"
                    >{{ scope.row.price3 }}</span
                  >
                  <input
                    v-else
                    v-model="scope.row.price3"
                    @change="change2(scope.row)"
                    @blur="input"
                    class="input1"
                    type="text"
                  />
                  <span>{{ scope.row.glsPrice3 }}</span>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="地区">
              <template slot-scope="scope">{{
                scope.row.area4 + " (" + scope.row.type4 + ")"
              }}</template>
            </el-table-column>
            <el-table-column label="定价——底价">
              <template slot-scope="scope">
                <div
                  @dblclick="price1"
                  style="display: flex; justify-content: space-between"
                >
                  <!-- <span v-if="flag" @dblclick="price1">{{
                    scope.row.price4
                  }}</span> -->
                  <span
                    :class="
                      scope.row.price4 > scope.row.glsPrice4
                        ? 'text'
                        : scope.row.price4 == scope.row.glsPrice4
                        ? 'text1'
                        : scope.row.price4 < scope.row.glsPrice4
                        ? 'text2'
                        : ''
                    "
                    v-if="flag"
                    >{{ scope.row.price4 }}</span
                  >
                  <input
                    v-else
                    v-model="scope.row.price4"
                    @change="change3(scope.row)"
                    @blur="input"
                    class="input1"
                    type="text"
                  />
                  <span>{{ scope.row.glsPrice4 }}</span>
                </div>
              </template>
            </el-table-column>
          </el-table>
        </div>
        <el-dialog
          title="一键调价"
          :visible.sync="adjustmentPirce"
          width="485px"
          :close-on-click-modal="false"
          :before-close="handleClose1"
        >
          <div class="addC" style="padding-right: 50px">
            <el-form
              ref="formAdd"
              :rules="rules"
              :model="formAdd"
              label-width="100px"
            >
              <el-form-item label="用户名称" prop="username">
                <el-input
                  v-model="formAdd.username"
                  placeholder="请输入用户名"
                ></el-input>
              </el-form-item>
              <el-form-item label="百分比" prop="percent">
                <el-input
                  v-model="formAdd.percent"
                  placeholder="请输入百分比"
                ></el-input>
              </el-form-item>
            </el-form>
          </div>
          <span slot="footer" class="dialog-footer">
            <el-button @click="colse()">取 消</el-button>
            <el-button type="primary" @click="addIPDialog">确 定</el-button>
          </span>
        </el-dialog>
      </div>
    </div>
  </div>
</template>
<script>
export default {
  name: "ImsPirce",
  data() {
    var percent = (rule, value, callback) => {
      if (value == "") {
        return callback(new Error("请输入百分比"));
      } else if (value < 0) {
        console.log(value);
        return callback(new Error("请输入正确数值,不包含负数！"));
      } else {
        callback();
      }
    };
    return {
      flag: true,
      adjustmentPirce: false,
      userName: "",
      price: "",
      tableDataObj: [],
      passageformInline: {
        userName: "",
      },
      passageformInline1: {
        userName: "",
      },
      formAdd: {
        username: "",
        percent: "",
      },
      rules: {
        username: [
          { required: true, message: "请输入用户名", trigger: "blur" },
        ],
        percent: [{ required: true, validator: percent, trigger: "blur" }],
      },
    };
  },
  methods: {
    price1(val) {
      console.log(1);
      this.flag = false;
      this.price = val;
    },
    input() {
      this.flag = true;
      // console.log(this.price);
    },
    // tableRowStyle({ row, rowIndex }) {
    //     console.log(rowIndex,'jj');
    //   return 'background-color: pink'
    // },
    rowClass({ row,column, rowIndex,columnIndex}) {
        if(columnIndex==0 || columnIndex==1){
            return 'background:#E0E0E0;color: #000'
        }else if(columnIndex==2 || columnIndex==3){
            return 'background:#f0f0f0;color: #000'
        }else if(columnIndex==4 || columnIndex==5){
             return 'background:#E0E0E0;color: #000'
        }else{
            return 'background:#f0f0f0;color: #000'
        }
        
    },
    restPirce(){
        this.$confirms.confirmation('post','确认恢复默认价吗？',this.API.recharge+'partner/gjPrice/reset',{username:this.passageformInline.userName},res =>{
                 this.getImsPirce()
            });
    },
    colse() {
      // this.userName = ''
      this.formAdd.username = "";
      this.formAdd.percent = "";
      this.adjustmentPirce = false;
    },
    singlamend(obj) {
      this.$api.post(
        this.API.recharge + "partner/gjPrice/change",
        obj,
        (res) => {
        //   console.log(res);
          if (res.code == 200) {
            this.$message({
              type: "success",
              duration: "2000",
              message: "操作成功!",
            });
            this.getImsPirce();
            this.adjustmentPirce = false;
          } else {
            this.getImsPirce();
            this.$message({
              type: "error",
              duration: "2000",
              message: "金额小于0！",
            });
            this.adjustmentPirce = false;
          }
        }
      );
    },
    change(val) {
      console.log(this.tableDataObj);
      console.log(val);
      const obj = {};
      obj.id = val.id1;
      obj.price = val.price1;
      obj.username = this.userName;
      if (val.price1 >= val.glsPrice1) {
        if (/[^\d\.]/g.test(val.price1)) {
          this.$message({
            type: "error",
            duration: "2000",
            message: "无效字符",
          });
          this.getImsPirce();
        } else {
          this.singlamend(obj);
        }
      } else {
        this.$message({
          type: "error",
          duration: "2000",
          message: "不能低于底价",
        });
        this.getImsPirce();
      }
    },
    change1(val) {
      const obj = {};
      obj.id = val.id2;
      obj.price = val.price2;
      obj.username = this.userName;
      //   this.singlamend(obj)
      if (val.price2 >= val.glsPrice2) {
        if (/[^\d\.]/g.test(val.price2)) {
          this.$message({
            type: "error",
            duration: "2000",
            message: "无效字符",
          });
          this.getImsPirce();
        } else {
          this.singlamend(obj);
        }
      } else {
        this.$message({
          type: "error",
          duration: "2000",
          message: "不能低于底价",
        });
        this.getImsPirce();
      }
    },
    change2(val) {
      const obj = {};
      obj.id = val.id3;
      obj.price = val.price3;
      obj.username = this.userName;
      //   this.singlamend(obj)
      if (val.price3 >= val.glsPrice3) {
        if (/[^\d\.]/g.test(val.price3)) {
          this.$message({
            type: "error",
            duration: "2000",
            message: "无效字符",
          });
          this.getImsPirce();
        } else {
          this.singlamend(obj);
        }
      } else {
        this.$message({
          type: "error",
          duration: "2000",
          message: "不能低于成本价",
        });
        this.getImsPirce();
      }
    },
    change3(val) {
      console.log(val);
      const obj = {};
      obj.id = val.id4;
      obj.price = val.price4;
      obj.username = this.userName;
      //   this.singlamend(obj)
      if (val.price4 >= val.glsPrice4) {
        if (/[^\d\.]/g.test(val.price4)) {
          this.$message({
            type: "error",
            duration: "2000",
            message: "无效字符",
          });
          this.getImsPirce();
        } else {
          this.singlamend(obj);
        }
      } else {
        this.$message({
          type: "error",
          duration: "2000",
          message: "不能低于成本价",
        });
        this.getImsPirce();
      }
    },
    Query() {
      console.log(1);
      this.getImsPirce();
      this.userName = this.passageformInline.userName;
      console.log(this.userName);
    },
    addIPDialog() {
      this.$refs["formAdd"].validate((valid) => {
        if (valid) {
          this.$api.post(
            this.API.recharge + "partner/gjPrice/batchChange",
            this.formAdd,
            (res) => {
              console.log(res);
              if (res.code == 200) {
                this.$message({
                  type: "success",
                  duration: "2000",
                  message: "操作成功!",
                });
                this.getImsPirce();
                this.formAdd.username = "";
                this.formAdd.percent = "";
                this.adjustmentPirce = false;
              } else {
                this.$message({
                  type: "error",
                  duration: "2000",
                  message: res.msg,
                });
                this.formAdd.username = "";
                this.formAdd.percent = "";
                this.adjustmentPirce = false;
              }
            }
          );
        }
      });
    },
    handleClose1(done) {
      this.adjustmentPirce = false;
      this.formAdd.username = "";
      this.formAdd.percent = "";
    },
    reSet() {
      this.passageformInline.userName = "";
      this.userName = "";
      this.getImsPirce();
    },
    allAdjustmentPirce() {
      this.adjustmentPirce = true;
      this.formAdd.username = this.userName;
      // console.log(this.formAdd.userName,'11');
    },
    getImsPirce() {
      this.$api.get(
        this.API.recharge + "partner/gjPrice",
        { username: this.passageformInline.userName },
        (res) => {
          console.log(res);
          let obj = res.data;
          var newObj = [];
          var item = {};
          var j = 1;
          obj.forEach(function (v, i) {
            var k = "area" + j;
            var q = "price" + j;
            var t = "type" + j;
            var id = "id" + j;
            var g = "glsPrice" + j;
            item[k] = v.area;
            item[q] = v.price;
            item[t] = v.type;
            item[id] = v.id;
            item[g] = v.glsPrice;
            j++;
            if ((i + 1) % 4 === 0) {
              newObj.push(item);
              item = {};
              j = 1;
            }
          });
          this.tableDataObj = newObj;
        }
      );
    },
  },
  watch: {},
  mounted() {
    this.getImsPirce();
  },
  // activated(){
  //   this.getImsPirce();
  // },
};
</script>
<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
.input1 {
  width: 50%;
  height: 100%;
}
.text {
  margin-right: 30px;
  color: red;
}
.text1 {
  margin-right: 30px;
}
.el-table__header >>> .el-table_1_column_1{
    background: red;
}
</style>
