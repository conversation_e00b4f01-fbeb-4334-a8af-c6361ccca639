<template>
    <div class="bag">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 子用户短信发送明细</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <!-- <div class="Top_title">子用户短信发送明细</div> -->
        <div class="fillet Statistics-box" style="padding:6px 18px 18px 18px">
            <div class="passageWay-title">
                <!-- 查询框开始 -->
                <el-form :inline="true" :model="passageformInline" label-width="80px" class="demo-form-inline"
                    ref="passageform">
                    <!-- <el-date-picker
                        v-model="datePluginValue"
                        type="datetimerange"
                        format="yyyy-MM-dd HH:mm:ss"
                        value-format="yyyy-MM-dd HH:mm:ss"
                        range-separator="至"
                        @change="timeClick"
                        :picker-options="pickerOptions"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期">
                    </el-date-picker> -->
                    <el-form-item label="用户名称" prop="clientName">
                        <el-input v-model="passageformInline.clientName" placeholder="请输入用户名"></el-input>
                    </el-form-item>
                    <el-form-item label="手机号" prop="mobile">
                        <el-input v-model="passageformInline.mobile" placeholder="请输入手机号"></el-input>
                    </el-form-item>
                    <el-form-item label="模板ID" prop="temId">
                        <el-input v-model="passageformInline.temId" placeholder="请输入模板ID"></el-input>
                    </el-form-item>
                    <el-form-item label="签名" prop="signature">
                        <!-- <el-select
                            class="lableW"
                            v-model="passageformInline.signature"
                            filterable
                            >
                            <el-option
                                v-for="item in sigOptions"
                                :key="item.signatureId"
                                :label="item.signature"
                                :value="item.signatureId"
                            ></el-option>
                        </el-select> -->
                        <el-input v-model="passageformInline.signature" placeholder=""></el-input>
                    </el-form-item>
                    <el-form-item label="状态" prop="smsStatus">
                        <el-select v-model="passageformInline.smsStatus" placeholder="请选择状态">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="成功" value="1"></el-option>
                            <el-option label="失败" value="2"></el-option>
                            <el-option label="待返回" value="3"></el-option>
                        </el-select>
                    </el-form-item>

                    <el-form-item label="开始时间" prop="sendBeginTime">
                        <el-date-picker v-model="passageformInline.sendBeginTime" :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd HH:mm:ss" default-time="00:00:00" type="datetime" placeholder="选择日期时间">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label="结束时间" prop="sendEndTime">
                        <el-date-picker v-model="passageformInline.sendEndTime" :picker-options="pickerOptions"
                            value-format="yyyy-MM-dd HH:mm:ss" default-time="23:59:59" type="datetime" placeholder="选择日期时间">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item label-width="80px">
                        <el-button type="primary" plain @click="Query()">查询</el-button>
                        <el-button type="primary" plain @click="reSet()">重置</el-button>
                        <!-- <el-button type="primary" plain @click="export1()">导出</el-button> -->
                    </el-form-item>
                </el-form>
                <!-- 查询框结束 -->
                <div class="passage-table">
                    <!-- 表格和分页开始 -->
                    <table-tem :tableDataObj="tableDataObj" @handPhone="handPhone"></table-tem>
                    <!--分页-->
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
                        style="background:#fff;padding:10px 0;text-align:right;">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange"
                            @current-change="handleCurrentChange" :current-page="passageformInline1.currentPage"
                            :page-size="passageformInline1.pageSize" :page-sizes="[10, 20, 50, 100]"
                            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total">
                        </el-pagination>
                    </el-col>
                    <!-- 表格和分页结束 -->
                </div>
            </div>
        </div>
        <ResetNumberVue v-if="resetVideo" ref="resetNumber" :infoData="infoData" :visible="resetVideo"></ResetNumberVue>
    </div>
</template>
<script>
import DatePlugin from '@/components/publicComponents/DatePlugin'  //时间
import TableTem from '@/components/publicComponents/TableTem' //列表
import moment from 'moment'
import ResetNumberVue from "@/components/publicComponents/ResetNumber.vue";
import bus from "../../../../common/bus"
import common from "../../../../../assets/js/common";
export default {
    name: 'subSendDetails',
    components: { DatePlugin, TableTem,ResetNumberVue },
    data() {
        return {
            name: 'subSendDetails',
            flag: '', //选择那一天
            // pickerOptions:{
            //     onPick: ({ maxDate, minDate }) => {
            //         this.pickerMinDate = minDate.getTime();
            //         if (maxDate) {
            //             this.pickerMinDate = ''
            //         }
            //     },
            //     disabledDate: (time) => {
            //         if(this.pickerMinDate&&this.pickerMinDate>Date.now()){
            //             return false
            //         }
            //         if (this.pickerMinDate !=='') {
            //             const day7 = 6 * 24 * 3600 * 1000
            //             let maxTime = this.pickerMinDate + day7
            //             if (maxTime >  Date.now()) {
            //                 maxTime =  Date.now()
            //             }
            //             const minTime = this.pickerMinDate - day7
            //             return time.getTime() < minTime || time.getTime() > maxTime
            //         }
            //         return time.getTime() > Date.now();
            //     }
            // },
            // datePluginValue: '',
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() > Date.now();
                }
            },
            // datePluginValueList: { //日期选择器
            //     type:"daterange",
            //     start:"",
            //     end:'',
            //     range:'-',
            //     clearable:false,
            //     pickerOptions:{
            //         onPick: ({ maxDate, minDate }) => {
            //             this.pickerMinDate = minDate.getTime();
            //             if (maxDate) {
            //                 this.pickerMinDate = ''
            //             }
            //         },
            //         disabledDate: (time) => {
            //             if(this.pickerMinDate&&this.pickerMinDate>Date.now()){
            //                 return false
            //             }
            //             if (this.pickerMinDate !=='') {
            //                 const day30 = 30 * 24 * 3600 * 1000
            //                 let maxTime = this.pickerMinDate + day30
            //                 if (maxTime >  Date.now() ) {
            //                     maxTime =  Date.now()
            //                 }
            //                 const minTime = this.pickerMinDate - day30
            //                 return time.getTime() < minTime || time.getTime() > maxTime
            //             }
            //             return time.getTime() > Date.now() ;
            //         }
            //     },
            //     datePluginValue: ''
            // },
            passageformInline: {
                clientName: '',
                mobile: '',
                smsStatus: '',
                signature: "",
                currentPage: 1,
                pageSize: 10,
                sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                sendEndTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            },
            sigOptions: [],
            passageformInline1: {
                clientName: '',
                mobile: '',
                smsStatus: '',
                signature: "",
                currentPage: 1,
                pageSize: 10,
                sendBeginTime: moment().startOf('day').format('YYYY-MM-DD HH:mm:ss'),
                sendEndTime: moment().endOf('day').format('YYYY-MM-DD HH:mm:ss'),
            },
            tableDataObj: { //列表数据
                total: 0,
                loading2: false,
                tableData: [],
                tableLabel: [
                    { prop: "clientName", showName: '用户名', fixed: false },
                    // {prop:"compName",showName:'公司名称',fixed:false},
                    { prop: "mobile", showName: '手机号码', width: "100", fixed: false },
                    { prop: "temId", showName: '模板ID', fixed: false, width: "120" },
                    // {prop:"userType",showName:'用户类型',width:'120',fixed:false},
                    { prop: "content", showName: '内容', fixed: false, width: "500" },
                    { prop: "msgid", showName: 'msgId', fixed: false, width: "180" },
                    { prop: "signature", showName: '签名', fixed: false },
                    {
                        prop: "smsStatus", showName: '状态', width: "70", formatData: function (val) {
                            if (val == '1') {
                                return val = '成功'
                            } else if (val == '2') {
                                return val = '失败'
                            } else if (val == '3') {
                                return val = '待返回'
                            }
                        }, fixed: false
                    },
                    { prop: "createTime", showName: '提交时间', fixed: false },
                    { prop: "sendTime", showName: '发送时间', fixed: false },
                    { prop: "reportTime", showName: '回执时间', fixed: false },
                    { prop: "originalCode", showName: '备注', fixed: false },
                ],
                tableStyle: {
                    isSelection: false,//是否复选框
                    isExpand: false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width: "100%"
                    },
                    border: true,//是否边框
                    stripe: false,//是否有条纹
                },
            },
            resetVideo: false,
            infoData: {},
        }
    },
    methods: {
        //获取列表数据
        getSendReportDate() {
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.cpus + 'v3/consumer/manager/sms/sub-accounts/message', this.passageformInline1, res => {
                this.tableDataObj.loading2 = false;
                this.tableDataObj.tableData = res.data.records;
                this.tableDataObj.total = res.data.total;
            })
        },
        getSignature() {
            this.$api.post(this.API.cpus + 'signature/signatureList', {
                pageSize: 200,
                currentPage: 1
            }, res => {
                let list = []
                res.records.map(item => {
                    if (item.auditStatus == 2) {
                        list.push(item)
                    }
                    // console.log(item,'item');
                })
                // console.log(list,'list');
                this.sigOptions = list
                // this.tableDataObj.tableData = res.records;
                // this.tableDataObj.tablecurrent.total = res.total; 
                // this.tableDataObj.loading2 = false;
            })
        },
        //手机号解密
        handPhone(val, index) {
            console.log(val);
            this.$api.post(this.API.upms + '/generatekey/decryptMobile', {
                keyId: val.keyId,
                smsInfoId: val.smsInfoId,
                cipherMobile: val.cipherMobile
            }, res => {
                if (res.code == 200) {
                    this.tableDataObj.tableData[index].mobile = res.data;
                } else if (res.code == 4004002) {
                    common.fetchData().then((res) => {
                        if (res.code == 200) {
                            if (res.data.isAdmin == 1) {
                                this.resetVideo = true;
                                this.infoData = res.data;
                            } else {
                                this.$message({
                                    message: '您今日解密次数已超限，如需重置解密次数，请联系管理员！',
                                    type: "warning",
                                });
                            }
                        } else {
                            this.$message({
                                message: res.msg,
                                type: "error",
                            });
                        }

                    })
                } else {
                    this.$message({
                        message: res.msg,
                        type: 'warning'
                    });
                }
                // this.tableDataObj.tableData[index].mobile=res.data

            })
        },
        //导出
        export1() {
            let aa = {};
            Object.assign(aa, this.passageformInline1);
            aa.isDownload = 1;
            if (this.tableDataObj.tableData.length == 0) {
                this.$message({
                    message: '列表无数据，不可导出！',
                    type: 'warning'
                });
            } else {
                this.$File.export(this.API.cpus + 'consumerdataoverviewday/page', aa, '用户短信发送报表.xlsx');
            }
        },
        //选择日趋
        handledatepluginVal: function (val1, val2) {
            this.passageformInline.sendBeginTime = val1
            this.passageformInline.sendEndTime = val2
        },
        //重置
        reSet() {
            this.$refs.passageform.resetFields();
            this.passageformInline.sendBeginTime = '';
            this.passageformInline.sendEndTime = '';
            this.datePluginValue = '';
            Object.assign(this.passageformInline1, this.passageformInline);
        },
        //查询
        Query() {
            Object.assign(this.passageformInline1, this.passageformInline);
            this.getSendReportDate();
        },
        //改变分页的数量
        handleSizeChange(size) {
            this.passageformInline1.pageSize = size;
        },
        //改变分页的页数
        handleCurrentChange: function (currentPage) {
            this.passageformInline1.currentPage = currentPage;
        },
        timeClick(val) {
            if (val) {
                this.passageformInline.sendBeginTime = this.moment(val[0]).format("YYYY-MM-DD HH:mm:ss");
                this.passageformInline.sendEndTime = this.moment(val[1]).format("YYYY-MM-DD HH:mm:ss");
            } else {
                this.passageformInline.sendBeginTime = ''
                this.passageformInline.sendEndTime = ''
            }
        },
    },
    created() {
        bus.$on("closeVideo", (msg) => {
            this.resetVideo = msg;
        });
    },
    // created(){
    //     if(this.$store.state.isDateState == 1){
    //         this.tableDataObj.tableLabel.push(
    //             {prop:"chargeSuccessNum",showName:'成功计费数',fixed:false},
    //                 {prop:"chargeFailNum",showName:'失败计费数',fixed:false},
    //                 {prop:"chargeWaitNum",showName:'待返回计费数',fixed:false},
    //                 {prop:"successRate",showName:'成功率',fixed:false,
    //                     formatData:function(val) { return val ? val +' %' :  val;},
    //                     showColorTag: {
    //                         color: "red"
    //                     }
    //                 },
    //                 {prop:"failRate",showName:'失败率',fixed:false,
    //                  formatData:function(val) { return val ? val +' %' :  val;}},
    //                 {prop:"waitRate",showName:'待返回率',fixed:false,
    //                  formatData:function(val) { return val ? val +' %' :  val;}}
    //         )
    //     }
    // },
    mounted() {
        this.getSendReportDate();
        // this.getSignature()
    },
    // activated(){
    //     this.getSendReportDate();
    // },
    watch: {
        passageformInline1: {
            handler(val) {
                this.getSendReportDate();
            },
            deep: true
        }
    }
}
</script>
<style scoped>
.search-date {
    position: relative;
    top: 2px;
    margin-bottom: 6px;
    margin-top: 10px;
}

.demo-form-inline {
    margin-top: 12px;
}

.passage-table {
    margin-bottom: 40px;
}
</style>