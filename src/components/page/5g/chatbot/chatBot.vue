<template>
  <div class="bag">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><i class="el-icon-lx-emoji"></i>{{ statusOf }}</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div style="display: flex; justify-content: space-between">
      <div class="chatbot" style="background: #fff">
        <el-form
          :model="ruleForm"
          :rules="rules"
          ref="ruleForm"
          label-width="130px"
          class="demo-ruleForm"
        >
        <el-form-item label="企业名称" prop="customerId">
          <el-select style="width:300px" clearable  v-model="ruleForm.customerId" placeholder="请选择">
            <el-option v-for="item in customerList" :key="item.customerId" :label="item.customerName" :value="item.customerId"> </el-option>
          </el-select>
        </el-form-item>
          <el-form-item label="ChatBot名称" prop="chatbotName">
            <el-input
              :maxlength="20"
              style="width: 500px"
              show-word-limit
              v-model="ruleForm.chatbotName"
            ></el-input>
            <p style="margin-left: 10px">
              针对具备5G消息能力的终端，在终端的消息列表及聊天页面顶端展示
            </p>
          </el-form-item>
          <!-- <el-form-item label="ChatBotID" prop="name">
          <el-input style="width:300px" v-model="ruleForm.name"></el-input>
        </el-form-item> -->
          <el-form-item label="ChatBot头像" prop="logoUrl">
            <div style="display: flex">
              <div v-if="!logoFlag" style="display: flex; position: relative">
                <img
                  :src="ruleForm.logoUrl"
                  alt=""
                  width="150px"
                  height="150px"
                />
                <el-button
                  style="
                    height: 30px;
                    position: absolute;
                    bottom: 0;
                    right: 0;
                    margin-right: -80px;
                  "
                  icon="el-icon-plus"
                  type="primary"
                  @click="Remove"
                  >上传</el-button
                >
                <!-- <div  class="logo">
                <div class="logoicon">
                  <i
                    @click="handlePictureCardPreview"
                    class="el-icon-zoom-in"
                  ></i>
                  <i
                    @click="Remove"
                    class="el-icon-delete"
                    style="margin-left: 10px"
                  ></i>
                </div>
              </div> -->
              </div>
              <div v-else>
                <div style="display: flex">
                  <el-upload
                    :action="action"
                    :headers="token"
                    list-type="picture-card"
                    :limit="1"
                    :file-list="fileList"
                    :on-preview="handlePictureCardPreview"
                    :before-upload="beforeAvatarUpload"
                    :on-remove="handleRemove"
                    :on-success="handleSuccess"
                  >
                    <i class="el-icon-plus"></i>
                    <!-- <span>图片尺寸：400*400</span> -->
                  </el-upload>
                  <div class="photo_active">
                    <div></div>
                    <div>
                      <p>图片尺寸：400*400！</p>
                      <p>图片大小：不能超过50k！</p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </el-form-item>
          <!-- <el-form-item label="扩展号" prop="ext">
          <el-input
            :disabled="id ? true : false"
            :maxlength="4"
            show-word-limit
            style="width: 500px"
            placeholder="最大4位数"
            v-model="ruleForm.ext"
          ></el-input>
        </el-form-item> -->
          <el-form-item label="签名" prop="autoGraph">
            <el-input
              :maxlength="20"
              show-word-limit
              style="width: 500px"
              placeholder="不能超过20个字符"
              v-model="ruleForm.autoGraph"
            ></el-input>
            <p style="margin-left: 10px">
              针对回落5G行业消息（标准版）的终端，在每条消息内容前携带此内容
            </p>
          </el-form-item>
          <!-- <el-form-item label="气泡颜色" prop="bubbleColor">
            <el-color-picker v-model="ruleForm.bubbleColor"></el-color-picker>
        </el-form-item> -->
          <!-- <el-form-item label="行业类型" prop="category">
            <el-select
              v-model="ruleForm.category"
              placeholder="请选择区域"
            >
              <el-option
                v-for="item in categoryData"
                :key="item.iindustryName"
                :label="item.industryName"
                :value="item.industryName"
              >
              </el-option>
            </el-select>
        </el-form-item> -->
          <!-- <el-form-item label="实际下发行业" prop="industry">
          <el-select v-model="ruleForm.industry" placeholder="请选择区域">
            <el-option label="党政军" value="1"> </el-option>
            <el-option label="民生" value="2"> </el-option>
            <el-option label="金融" value="3"> </el-option>
            <el-option label="物流" value="4"> </el-option>
            <el-option label="游戏" value="5"> </el-option>
            <el-option label="电商" value="6"> </el-option>
            <el-option label="微商（个人）" value="7"> </el-option>
            <el-option label="沿街商铺（中小）" value="8"> </el-option>
            <el-option label="企业（大型）" value="9"> </el-option>
            <el-option label="教育培训" value="10"> </el-option>
            <el-option label="房地产" value="11"> </el-option>
            <el-option label="医疗器械、药店" value="12"> </el-option>
            <el-option label="其他" value="13"> </el-option>
          </el-select>
        </el-form-item>
        <div style="display: flex">
          <el-form-item label="区域" prop="officeCode">
            <el-select
              v-model="ruleForm.officeCode"
              placeholder="请选择区域"
              @change="provinces(ruleForm.officeCode)"
            >
              <el-option
                v-for="item in office"
                :key="item.id"
                :label="item.regionName"
                :value="item.regionCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="省份" prop="provinceCode">
            <el-select
              v-model="ruleForm.provinceCode"
              placeholder="请选择省份"
              @change="citys(ruleForm.provinceCode)"
            >
              <el-option
                v-for="item in province"
                :key="item.id"
                :label="item.provinceName"
                :value="item.provinceCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="城市" prop="cityCode">
            <el-select v-model="ruleForm.cityCode" placeholder="请选择城市">
              <el-option
                v-for="item in city"
                :key="item.id"
                :label="item.cityName"
                :value="item.cityCode"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </div> -->
          <!-- <el-form-item label="扩展号" prop="ext">
          <el-input style="width: 500px" v-model="ruleForm.ext"></el-input>
        </el-form-item> -->
          <el-form-item label="邮箱" prop="email">
            <el-input
              :maxlength="50"
              show-word-limit
              style="width: 500px"
              placeholder="不能超过50个字符"
              v-model="ruleForm.email"
            ></el-input>
          </el-form-item>
          <el-form-item label="chatbot主页地址" prop="website">
            <el-input
              placeholder="请输入chatbot主页地址"
              style="width: 500px"
              v-model="ruleForm.website"
            ></el-input>
          </el-form-item>
          <el-form-item label="chatbot办公地址" prop="address">
            <el-input
              placeholder="请输入chatbot办公地址"
              style="width: 500px"
              v-model="ruleForm.address"
            ></el-input>
          </el-form-item>
          <el-form-item label="经度" prop="longitude">
            <el-input
              placeholder="请输入经度"
              style="width: 500px"
              v-model="ruleForm.longitude"
            ></el-input>
          </el-form-item>
          <el-form-item label="纬度" prop="latitude">
            <el-input
              placeholder="请输入纬度"
              style="width: 500px"
              v-model="ruleForm.latitude"
            ></el-input>
            <p style="margin-left: 10px">
              请到相关网站查询您公司所在经纬度，便于支持类似“附近”功能
            </p>
          </el-form-item>
          <el-form-item label="服务电话" prop="callbackPhone">
            <el-input
              :maxlength="21"
              show-word-limit
              placeholder="请输入服务电话"
              style="width: 500px"
              v-model="ruleForm.callbackPhone"
            ></el-input>
          </el-form-item>
          <el-form-item label="服务条款" prop="terms">
            <el-input
              placeholder="请输入服务条款"
              style="width: 500px"
              v-model="ruleForm.terms"
            ></el-input>
          </el-form-item>
          <el-form-item label="服务描述" prop="description">
            <el-input
              :maxlength="166"
              show-word-limit
              type="textarea"
              placeholder="请输入服务描述"
              style="width: 500px"
              v-model="ruleForm.description"
            ></el-input>
          </el-form-item>
          <el-form-item label="附件上传" prop="attachment">
            <el-upload
              :action="action"
              :headers="token"
              :limit="1"
              :file-list="attachmentList"
              :before-upload="beforeAvatarUploadAtt"
              :on-remove="handleRemoveAtt"
              :on-success="handleSuccessAtt"
              multiple
            >
              <el-button size="small" type="primary">点击上传</el-button>
              <!-- <div slot="tip" class="el-upload__tip">只能上传pdf、doc、jpg、jpeg、gif、docx、rar、zip格式!文件，且不超过5MB</div> -->
            </el-upload>
          </el-form-item>
          <!-- <el-form-item label="Chatbot调试白名单" prop="debugWhiteAddress">
          <div style="display: flex">
            <el-input
              :maxlength="239"
              show-word-limit
              type="textarea"
              placeholder="不能超过239个字符"
              style="width: 500px"
              v-model="ruleForm.debugWhiteAddress"
            ></el-input>
            <div style="line-height: 18px; margin-left: 10px">
              1、最多录入20个调试手机号码，用中英文逗号隔开。<br />
              2、用于实际测试Chatbot的消息接收和发送。
            </div>
          </div>
        </el-form-item> -->
          <el-form-item>
            <div class="btn">
              <el-button
                v-if="!id"
                icon="el-icon-check"
                type="primary"
                @click="submitForm('ruleForm')"
                >保存</el-button
              >
              <el-button
                v-else
                icon="el-icon-check"
                type="primary"
                @click="submitForm('ruleForm')"
                >更新</el-button
              >
              <el-button
                v-if="!id"
                type="primary"
                @click="submitAudit('ruleForm')"
                >提交审核</el-button
              >
              <el-button
                icon="el-icon-caret-left"
                type="primary"
                @click="resetForm('ruleForm')"
                >返回</el-button
              >
            </div>
          </el-form-item>
        </el-form>
      </div>
      <div class="chatbot_right">
        <div class="sso_view_tit">阅览效果</div>
        <div class="sso_view_content">
          <div class="sso_navbar">
            <img
              src="../../../../assets/images/navbar.png"
              alt=""
              class="view_img_bag"
            />
          </div>
          <div class="chatbot_content">
            <div class="logo_h">
              <img
                v-if="ruleForm.logoUrl"
                :src="ruleForm.logoUrl"
                alt=""
                width="100px"
                height="100px"
                style="border-radius: 10px"
              />
              <div v-else class="active_img"></div>
              <div>
                {{ ruleForm.chatbotName }}
              </div>
              <div>
                {{ ruleForm.callbackPhone }}
              </div>
            </div>
            <div class="view_c">
              <div>
                <span>服务描述</span>
                <p style="color: #ccc; margin-top: 10px">
                  {{ ruleForm.description }}
                </p>
              </div>
              <div style="margin-top: 30px">
                <span>短信客服</span
                ><span style="margin-left: 10px">{{
                  ruleForm.callbackPhone
                }}</span>
              </div>
              <div style="margin-top: 30px">
                <span>邮箱</span
                ><span style="margin-left: 10px">{{ ruleForm.email }}</span>
              </div>
              <div style="margin-top: 30px">
                <span>提供商</span
                ><span style="margin-left: 10px">{{
                  ruleForm.chatbotName
                }}</span>
              </div>
            </div>
          </div>
          <div class="sso_view_footer">
            <div class="btns">进入聊天</div>
            <div class="btns">关注</div>
            <!-- <img
            src="../../../../assets/images/foot.png"
            alt=""
            class="view_img_bag"
          /> -->
          </div>
        </div>
      </div>
    </div>

    <el-dialog :visible.sync="dialogVisible">
      <img width="100%" :src="ruleForm.logoUrl" alt="" />
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "chatBot",
  data() {
    var coderules = (rule, value, callback) => {
      if (value) {
        let reg = /^\d+$/;
        if (!reg.test(value)) {
          callback(new Error("扩展号只允许输入数字"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入扩展码"));
      }
    };
    var longitude = (rule, value, callback) => {
      if (value) {
        let reg = /^[+-]?\d+(?:\.\d{1,7})?$/;
        if (!reg.test(value)) {
          callback(new Error("小数点后最多七位，请重新输入"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入经度"));
      }
    };
    var latitude = (rule, value, callback) => {
      if (value) {
        let reg = /^[+-]?\d+(?:\.\d{1,7})?$/;
        if (!reg.test(value)) {
          callback(new Error("小数点后最多七位，请重新输入"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入纬度"));
      }
    };

    return {
      dialogVisible: false,
      // office: [],
      id: "",
      // city: [],
      // province: [],
      attachmentList:[],
      fileList: [],
      disabled: false,
      flag: false,
      logoFlag: true,
      action: this.API.cpus + "v3/file/uploadFile",
      token: {},
      categoryData: [],
      ruleForm: {
        customerId:"",
        chatbotName: "", //chatbot名称
        autoGraph: "", //签名
        bubbleColor: "#FFFFFF", //气泡颜色
        // ext: "", //扩展号
        // industry: "", //实际下发行业
        // category: "", //行业类型
        callbackPhone: "", //服务电话
        // cityCode: "", //地市编码
        // officeCode: "", //区编码
        // provinceCode: "", //省份编码
        // ext: "", //扩展号
        logoUrl: "", //头像
        email: "", //邮箱
        description: "", //描述信息
        terms: "", //服务条款
        website: "", //chatbot主页地址
        longitude: "", //经度
        latitude: "", //纬度
        address: "", //chatbot办公地址
        // debugWhiteAddress: "", //调试白名单
        attachment:"",//附件地址
        statusOf: "新增chatbot信息",
      },
      rules: {
        chatbotName: [
          { required: true, message: "请输入chatbot名称", trigger: "blur" },
        ],
        customerId: [
          { required: true, message: "请选择企业名称", trigger: "blur" },
        ],
        logoUrl: [
          { required: true, message: "请上传chatbot头像", trigger: "blur" },
        ],
        // attachment: [
        //   { required: true, message: "请上传附件", trigger: "blur" },
        // ],
        // category: [
        //   { required: true, message: "请选择行业类型", trigger: "blur" },
        // ],
        // industry: [
        //   { required: true, message: "请选择实际下发行业", trigger: "blur" },
        // ],
        bubbleColor: [
          { required: true, message: "请选择气泡颜色", trigger: "blur" },
        ],
        autoGraph: [{ required: true, message: "请输入签名", trigger: "blur" }],
        callbackPhone: [
          { required: true, message: "请输入服务电话", trigger: "blur" },
        ],
        // cityCode: [
        //   { required: true, message: "请选择地市编码", trigger: "blur" },
        // ],
        // officeCode: [
        //   { required: true, message: "请选择区编码", trigger: "blur" },
        // ],
        terms: [{ required: true, message: "请输入服务条款", trigger: "blur" }],
        website: [
          { required: true, message: "请输入chatbot主页地址", trigger: "blur" },
        ],
        longitude: [{ required: true, validator: longitude, trigger: "blur" }],
        latitude: [{ required: true, validator: latitude, trigger: "blur" }],
        address: [
          { required: true, message: "chatbot办公地址", trigger: "blur" },
        ],
        // provinceCode: [
        //   { required: true, message: "请选择省份编码", trigger: "blur" },
        // ],
        // ext: [{ required: true, validator: coderules, trigger: "blur" }],
        email: [{ required: true, message: "请输入邮箱", trigger: "blur" }],
        description: [
          { required: true, message: "描述信息不能为空", trigger: "blur" },
        ],
        debugWhiteAddress: [
          { required: true, message: "调试白名单不能为空", trigger: "blur" },
        ],
      },
      addUserDialog_status: 1, //用户信息是否是新增---1为新增，0为编辑
      customerList:[]
    };
  },
  created() {
    this.token = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
    };
    this.getFindCustomerByUser()
    if (!this.$route.query.chatbotId) {
      this.statusOf = "新增chatbot信息";
      this.addUserDialog_status = 1;
      // window.location.reload()
    } else {
      this.id = this.$route.query.chatbotId;
      this.statusOf = "编辑chatbot信息";
      this.addUserDialog_status = 0;
      
      this.handleEdit();
    }
    
    // this.region();
    // this.getcategory();
  },
  methods: {
    // citys(code) {
    //   this.$api.get(
    //     this.API.fiveWeb + "/city/list/" + code,
    //     {
    //       //   provinceCode:this.ruleForm.provinceCode
    //     },
    //     (res) => {
    //       this.city = res.data;
    //     }
    //   );
    // },
    // provinces(code) {
    //   this.$api.get(
    //     this.API.fiveWeb + "/province/list/" + code,
    //     {
    //       //   regionCode:this.ruleForm.officeCode
    //     },
    //     (res) => {
    //       this.province = res.data;
    //     }
    //   );
    // },
    //根据用户查找对应的5G企业
    getFindCustomerByUser() {
      this.$api.post(
        this.API.fiveWeb + "/customer/findCustomerByUser",
        {},
        (res) => {
          if (res.code == 200) {
            this.customerList = res.data;
            this.ruleForm.customerId = res.data[0].customerId
          } else {
            this.$message({
              type: "error",
              message: res.msg
            });
          }
        }
      );
    },
    getcategory() {
      this.$api.get(this.API.fiveWeb + "/industry/firstIndustry", {}, (res) => {
        this.categoryData = res.data;
      });
    },
    //根据用户查找对应的5G企业
    getFindCustomerByUser() {
      this.$api.post(
        this.API.fiveWeb + "/customer/findCustomerByUser",
        {},
        (res) => {
          if (res.code == 200) {
            this.customerList = res.data;
            this.ruleForm.customerId = res.data[0].customerId
          } else {
            this.$message({
              type: "error",
              message: res.msg
            });
          }
        }
      );
    },
    // handelchatName(val) {
    //   this.ruleForm.autoGraph = val;
    // },
    // region() {
    //   this.$api.post(
    //     this.API.fiveWeb + "/region/page",
    //     {
    //       currentPage: 1,
    //       pageSize: 10,
    //     },
    //     (res) => {
    //       this.office = res.data.records;
    //     }
    //   );
    // },

    handleRemove(file, fileList) {
      this.ruleForm.logoUrl = "";
    },
    handleRemoveAtt(){
      this.ruleForm.attachment = "";
    },
    Remove() {
      this.ruleForm.logoUrl = "";
      this.logoFlag = true;
    },
    handlePictureCardPreview(file) {
      this.dialogVisible = true;
    },
    beforeAvatarUpload(file) {
      // console.log(file,'file');
      const siJPGGIF = file.name.split(".")[1];
      const is4kb = file.size;
      // const isLt5M = Math.round(file.size/1024*100)/100 < 4 //单位为KB;
      function objectSize(size) {
        var divisor = 1;
        var unit = "bytes";
        if (size >= 1024 * 1024) {
          divisor = 1024 * 1024;
          unit = "MB";
        } else if (size >= 1024) {
          divisor = 1024;
          unit = "KB";
        }
        if (divisor == 1) return size + " " + unit;

        return (size / divisor).toFixed(2) < 50;
      }
      let flagSize = objectSize(is4kb);
      if (siJPGGIF != "jpg" && siJPGGIF != "jpeg" && siJPGGIF != "png") {
        this.$message.warning("上传头像图片只能是 jpeg 、png格式!");
        return false;
      }
      if (!flagSize) {
        this.$message.warning("上传缩略图大小不能超过50kb！");
        return false;
      }

      // return isJPG && isLt2M;
    },
    beforeAvatarUploadAtt(file){
      const siJPGGIF = file.name.split(".")[file.name.split(".").length - 1];
        const isLt5M = file.size / 1024 / 1024 < 5;
        const fileType = ["jpg", "jpeg", "gif","pdf","doc","docx","rar","zip"];
        if (fileType.indexOf(siJPGGIF) == -1) {
          this.$message.warning("上传文件只能是 pdf、doc、jpg、jpeg、gif、docx、rar、zip格式!");
          return false;
        }
        if (!isLt5M) {
          this.$message.warning("上传文件大小不能超过 5MB!");
          return false;
        }
    },
    handleSuccess(res, fileList) {
      if (res.code == 200) {
        this.ruleForm.logoUrl = res.data;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    handleSuccessAtt(res, fileList){
      if (res.code == 200) {
        this.ruleForm.attachment = res.data;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    resetForm() {
      this.$router.push({
        path: "/ChatBotManage",
      });
    },
    submitForm(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          if (this.addUserDialog_status == 1) {
            this.$api.post(
              this.API.fiveWeb + "/chatbot",
              this.ruleForm,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: "success",
                  });
                  this.$router.push({
                    path: "/ChatBotManage",
                  });
                } else {
                  this.$message({
                    message: res.msg,
                    type: "error",
                  });
                }
              }
            );
          } else {
            this.ruleForm.chatbotId = this.$route.query.chatbotId;
            this.$api.put(
              this.API.fiveWeb + "/chatbot",
              this.ruleForm,
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    message: res.msg,
                    type: "success",
                  });
                  this.$router.push({
                    path: "/ChatBotManage",
                  });
                } else {
                  this.$message({
                    message: res.msg,
                    type: "error",
                  });
                }
              }
            );
          }
        }
      });
    },
    submitAudit(formop) {
      this.$refs[formop].validate((valid) => {
        if (valid) {
          this.$confirms.confirmation(
            "post",
            "提交审核！",
            this.API.fiveWeb + "/chatbot/toAudit",
            this.ruleForm,
            (res) => {
              if (res.code == 200) {
                this.$message({
                  message: res.msg,
                  type: "success",
                });
                // this.getchatList();
                this.$router.push({
                  path: "/ChatBotManage",
                });
              } else {
                this.$message({
                  message: res.msg,
                  type: "error",
                });
              }
            }
          );
        }
      });
      // this.$confirms.confirmation(
      //   "post",
      //   "提交审核！",
      //   this.API.fiveWeb + "/chatbot/toAudit",
      //   {
      //     chatbotId: row.chatbotId,
      //   },
      //   (res) => {
      //     this.getchatList();
      //   }
      // );
    },
    //编辑赋值
    handleEdit() {
      this.$api.get(
        this.API.fiveWeb + "/chatbot/" + this.$route.query.chatbotId,
        {
          //   provinceCode:this.ruleForm.provinceCode
        },
        (res) => {
          // console.log(res.data);
          Object.assign(this.ruleForm, res.data);
          if(res.data.customerId){
            this.ruleForm.customerId = res.data.customerId
          }
          if(this.ruleForm.attachment){
            let a = this.ruleForm.attachment.split('/')
            this.attachmentList[0] = {
              name:a[a.length-1],
              url:this.ruleForm.attachment
            }
          }
          if (this.ruleForm.logoUrl) {
            this.logoFlag = false;
          } else {
            this.logoFlag = true;
          }
          if (!res.data.bubbleColor) {
            this.ruleForm.bubbleColor = "#FFFFFF";
          }
          // this.fileList[0] = this.API.img+res.data.logoUrl
          // this.provinces(this.ruleForm.officeCode);
          // this.citys(this.ruleForm.provinceCode);
          // this.ruleForm.provinceCode = this.ruleForm.provinceCode+""
          // this.ruleForm.cityCode = this.ruleForm.cityCode+""
          //   this.city = res.data;
        }
      );
    },
  },
};
</script>

<style scoped>
.chatbot {
  padding: 15px;
  flex: 1;
}
.btn {
  /* text-align: center; */
}
.logo {
  width: 150px;
  height: 150px;
  position: absolute;
  /* position: relative; */
  background: rgba(0, 0, 0, 0.5);
  cursor: pointer;
}
.logoicon {
  color: #fff;
  font-size: 18px;
  text-align: center;
  line-height: 150px;
}
.photo_active {
  margin-left: 10px;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  font-size: 12px;
  color: red;
}
.chatbot_right {
  width: 380px;
  /* height: 800px; */
  overflow: hidden;
  position: relative;
  background: #fff;
  margin-left: 10px;
}
.sso_view_tit {
  width: 100%;
  height: 70px;
  line-height: 70px;
  text-align: center;
  font-size: 14px;
}
.sso_view_content {
  width: 310px;
  height: 660px;
  margin: 0 auto;
  border: 7px solid #000;
  border-radius: 25px;
  position: relative;
}
.sso_navbar {
  width: 100%;
  height: 70px;
}
.view_img_bag {
  width: 100%;
  height: 100%;
}
.sso_view_footer {
  position: absolute;
  width: 100%;
  height: 50px;
  bottom: 0;
  display: flex;
}
.chatbot_content {
  /* width: 100%; */
  flex: 1;
}
.logo_h {
  width: 150px;
  text-align: center;
  margin: 10px auto;
  line-height: 20px;
}
.view_c {
  margin-left: 25px;
  /* line-height: 50px; */
}
.btns {
  flex: 1;
  height: 30;
  background: #eee;
  line-height: 30px;
  text-align: center;
  border-radius: 25px;
  margin: 10px;
  color: #4e95ff;
}
.active_img {
  width: 100px;
  height: 100px;
  background: #eee;
  border-radius: 10px;
  margin-left: 26px;
}
</style>