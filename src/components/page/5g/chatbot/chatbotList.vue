<template>
  <div class="bag">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><i class="el-icon-lx-emoji"></i>chatBot管理</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div style="padding: 20px">
      <el-form
        :model="ruleForm"
        :inline="true"
        ref="ruleForm"
        class="demo-ruleForm"
      >
        <el-form-item label="ChatBot名称" prop="chatbotName">
          <el-input v-model="ruleForm.chatbotName"></el-input>
        </el-form-item>
        <el-form-item label="企业名称" prop="customerIdArr">
          <el-select style="width:300px" multiple v-model="ruleForm.customerIdArr" placeholder="请选择">
            <el-option v-for="item in customerList" :key="item.customerId" :label="item.customerName" :value="item.customerId"> </el-option>
          </el-select>
        </el-form-item>
        <!-- <el-form-item label="联通" prop="ltStatus">
          <el-select v-model="ruleForm.ltStatus" placeholder="请选择">
            <el-option label="待审核" value="0"> </el-option>
            <el-option label="审核中" value="1"> </el-option>
            <el-option label="待审通过" value="2"> </el-option>
            <el-option label="待审失败" value="3"> </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="电信" prop="dxStatus">
          <el-select v-model="ruleForm.dxStatus" placeholder="请选择">
            <el-option label="待审核" value="0"> </el-option>
            <el-option label="审核中" value="1"> </el-option>
            <el-option label="待审通过" value="2"> </el-option>
            <el-option label="待审失败" value="3"> </el-option>
          </el-select>
        </el-form-item> -->
        <el-form-item>
          <el-button type="primary" @click="submitForm('ruleForm')"
            >查询</el-button
          >
          <el-button type="primary" @click="resetForm('ruleForm')"
            >重置</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <div style="padding: 0 20px">
      <el-button
        icon="el-icon-plus"
        type="primary"
        @click="chatbotAdd('ruleForm')"
        >新增</el-button
      >
    </div>
    <div style="padding: 20px">
      <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        :data="tableDataObj.tableData"
      >
        <el-table-column label="Chatbot头像" width="100">
          <template slot-scope="scope">
            <span
              style="cursor: pointer"
              v-if="scope.row.availableChannelCount > 0"
              @click="sendMessage(scope.row)"
            >
              <img :src="scope.row.logoUrl" alt="" width="40px" />
            </span>
            <span v-else>
              <img :src="scope.row.logoUrl" alt="" width="40px" />
            </span>
          </template>
        </el-table-column>
        <el-table-column label="Chatbot名称" width="180">
          <template slot-scope="scope">
            <span
              v-if="scope.row.availableChannelCount > 0"
              class="send_msg"
              @click="sendMessage(scope.row)"
              >{{ scope.row.chatbotName }}</span
            >
            <span v-else>{{ scope.row.chatbotName }}</span>
          </template>
        </el-table-column>
        <el-table-column label="chatbotId" width="180">
          <template slot-scope="scope">
            <span>{{ scope.row.chatbotId }}</span>
          </template>
        </el-table-column>
        <el-table-column label="签名" width="150">
          <template slot-scope="scope">
            <span>{{ scope.row.autoGraph }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="服务电话">
          <template slot-scope="scope">
            <span>{{ scope.row.callbackPhone }}</span>
          </template>
        </el-table-column> -->
        <el-table-column label="描述信息">
          <template slot-scope="scope">
            <Tooltip v-if="scope.row.description" :content="scope.row.description" className="wrapper-text" effect="light">
            </Tooltip>
            <!-- <span>{{ scope.row.description }}</span> -->
          </template>
        </el-table-column>
        <el-table-column label="状态"  width="100">
          <template slot-scope="scope">
            <span v-if="scope.row.status == 0">待提交</span>
            <span style="color: red" v-if="scope.row.status == 5">审核中</span>
            <!-- <span style="color: red" v-if="scope.row.status == 10"
              >运营商审核中</span
            > -->
            <span style="color: red" v-if="scope.row.status == 20"
              >审核失败</span
            >
            <span style="color: #409eff" v-if="scope.row.status == 30"
              >审核通过</span
            >
          </template>
        </el-table-column>
        <el-table-column label="原因">
          <template slot-scope="scope">
            <Tooltip v-if="scope.row.auditReason" :content="scope.row.auditReason" className="wrapper-text" effect="light">
            </Tooltip>
            <!-- <span>{{ scope.row.auditReason }}</span> -->
          </template>
        </el-table-column>
        <el-table-column label="创建时间" width="160">
          <template slot-scope="scope">
            <span>{{
              moment(scope.row.createdTime).format("YYYY-MM-DD HH:mm:ss")
            }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="140">
          <template slot-scope="scope">
            <el-button
              v-if="
                scope.row.status == 0 ||
                scope.row.status == 20 ||
                scope.row.status == 30
              "
              type="text"
              style="color: #16a589; margin-left: 0px"
              @click="edit(scope.row)"
              ><i class="el-icon-edit"></i> 编辑</el-button
            >
            <el-button
              v-if="scope.row.availableChannelCount > 0"
              type="text"
              style="color: #16a589; margin-left: 0px"
              @click="sendMessage(scope.row)"
              ><i class="el-icon-position"></i> 消息发送</el-button
            >
            <!-- <el-button
              v-if="scope.row.status == 0 || scope.row.status == 20"
              type="text"
              style="color: #16a589; margin-left: 0px"
              @click="submitAudit(scope.row)"
              ><i class="el-icon-edit"></i> 提交审核</el-button
            > -->
            <!-- <el-button
              type="text"
              style="color: #e6a23c; margin-left: 10px"
              @click="logout(scope.row)"
              ><i class="el-icon-error"></i> 注销</el-button
            >
            <el-button
              v-if="scope.row.delTag == 1"
              type="text"
              style="color: red; margin-left: 10px"
              @click="delChat(scope.row)"
              ><i class="el-icon-delete"></i> 删除</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>

      <el-pagination
        class="page_bottom"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="tabelAlllist.currentPage"
        :page-size="tabelAlllist.pageSize"
        :page-sizes="[10, 20, 50, 100]"
        layout="total, sizes, prev, pager, next, jumper"
        :total="tableDataObj.total"
      >
      </el-pagination>
    </div>
    <el-dialog
      title="实名认证"
      :visible.sync="dialogSmrzFlag"
      width="30%"
      center
    >
      <span
        >尊敬的客户，根据《中华人民共和国网络安全法》及相关法律的规定，请您尽快完成实名认证。如需帮助，请联系在线售后客服。</span
      >
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="goSmrz">前往实名认证</el-button>
      </span>
    </el-dialog>
    <el-dialog title="5G消息" :visible.sync="messageFlag" width="30%" center>
      <p style="line-height: 30px">
        &nbsp;&nbsp;&nbsp;&nbsp;5G消息云平台是基于5G消息为基础构建的开放生态平台，在兼顾传统短信服务能力的同时，围绕5G消息丰富的展现形式，动态的场景设定，以及多元的智慧能力融合，形成了丰富的5G消息一站式解决方案，可帮助短信服务商快速转型5G消息服务商;5G消息云平台可以为合作伙伴提供强大的5G消息的内容管理及运营能力，通过平台提供的素材管理、模板管理、场景管理等等服务，让客户仅需配置就能够立即使用5G消息;客户还可基于5G消息的开发者平台，使用助通提供的更多的SaaS增值服务，帮助客户构建一站式业务体验。
      </p>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="message5g">同意</el-button>
        <el-button type="danger" @click="messageFlag = false">不同意</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Tooltip from "@/components/publicComponents/tooltip";
import { mapState } from "vuex";
export default {
  name: "ChatBotManage",
  components: {
    Tooltip
  },
  data() {
    return {
      dialogSmrzFlag: false,
      messageFlag: false,
      gFlag: false,
      flag: false,
      name: "",
      ruleForm: {
        ltStatus: "",
        ydStatus: "",
        dxStatus: "",
        userId: "",
        customerIdArr:[],
        currentPage: 1,
        pageSize: 10,
      },
      tabelAlllist: {
        ltStatus: "",
        ydStatus: "",
        dxStatus: "",
        userId: "",
        customerIdArr:[],
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      customerList: [],
    };
  },
  created() {
    this.ruleForm.userId = this.userId;
    this.tabelAlllist.userId = this.userId;
    this.getFindCustomerByUser();
    this.getchatList();
    this.getDomain();
    this.getClientInfo();
  },
  computed: {
    ...mapState({
      userId: (state) => state.userId,
    }),
  },
  methods: {
    goSmrz() {
      this.dialogSmrzFlag = false;
      this.$router.push("/authentication");
    },
    //判断是否认证以及同意开启5G
    getClientInfo() {
      this.$api.get(
        this.API.cpus + "consumerclientinfo/getClientInfo",
        null,
        (res) => {
          // console.log(res.data);
          if (res.data.certificate == 0) {
            this.flag = false;
            this.dialogSmrzFlag = true;
          } else {
            this.flag = true;
            this.register();
          }
        }
      );
    },
    //开启5G
    register() {
      this.$api.post(this.API.fiveWeb + "/customer/isRegister", {}, (res) => {
        if (res.code == 200 && res.data) {
          this.messageFlag = false;
          this.gFlag = true;
        } else {
          this.messageFlag = true;
          this.gFlag = false;
        }
      });
    },
    message5g() {
      this.$api.post(this.API.fiveWeb + "/customer/register", {}, (res) => {
        if (res.code == 200) {
          this.register();
        } else {
          this.$message.error(res.msg);
          this.messageFlag = false;
        }
        // this.messageFlag = false;
      });
    },
    getchatList() {
      this.tableDataObj.loading2 = true;
      // this.tabelAlllist.customerIdArr[0] = this.customerList[0].customerId
      this.$api.post(
        this.API.fiveWeb + "/chatbot/page",
        this.tabelAlllist,
        (res) => {
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.total = res.data.total;
          this.tableDataObj.loading2 = false;
          // console.log(res, "res");
        }
      );
    },
    //根据用户查找对应的5G企业
    getFindCustomerByUser() {
      this.$api.post(
        this.API.fiveWeb + "/customer/findCustomerByUser",
        {},
        (res) => {
          if (res.code == 200) {
            this.customerList = res.data;
            this.ruleForm.customerIdArr[0] = this.customerList[0].customerId
            Object.assign(this.tabelAlllist, this.ruleForm);
            this.getchatList()
          } else {
            this.$message({
              type: "error",
              message: res.msg
            });
          }
        }
      );
    },
    handleSizeChange(size) {
      this.tabelAlllist.pageSize = size;
      this.getchatList();
    },
    handleCurrentChange(currentPage) {
      this.tabelAlllist.currentPage = currentPage;
      this.getchatList();
    },
    submitForm() {
      Object.assign(this.tabelAlllist, this.ruleForm);
      this.getchatList();
    },
    resetForm() {
      this.$refs.ruleForm.resetFields();
      this.ruleForm.customerIdArr[0] = this.customerList[0].customerId
      Object.assign(this.tabelAlllist, this.ruleForm);
      this.getchatList();
    },
    chatbotAdd() {
      if (this.flag) {
        if (this.gFlag) {
          // console.log(this.gFlag);
          this.$router.push({
            path: "/chatBot",
          });
        } else {
          this.messageFlag = true;
        }
      } else {
        this.dialogSmrzFlag = true;
      }
    },
    //删除
    delChat(row) {
      this.$confirms.confirmation(
        "delete",
        "确认删除该Chatbot？",
        this.API.fiveWeb + "/chatbot/" + row.chatbotId,
        {},
        (res) => {
          this.getchatList();
        }
      );
    },
    submitAudit(row) {
      this.$confirms.confirmation(
        "post",
        "提交审核！",
        this.API.fiveWeb + "/chatbot/toAudit",
        {
          chatbotId: row.chatbotId,
        },
        (res) => {
          this.getchatList();
        }
      );
    },
    //注销
    logout(row) {
      this.$confirms.confirmation(
        "get",
        "确认注销该Chatbot？注销后，数据将被全部清除",
        this.API.fiveWeb + "/chatbot/cancel" + row.chatbotId,
        {},
        (res) => {
          this.getchatList();
        }
      );
    },
    //跳转5G平台
    sendMessage(row) {
      let d = new Date();
      d.setTime(d.getTime() + 1000 * 60 * 240);
      let expires = "expires=" + d.toUTCString();
      document.cookie =
        "charbotId=" +
        row.chatbotId +
        ";path=/;domain=." +
        this.name +
        ";expires=" +
        expires;+"secure";

      var tempwindow = window.open("_blank");
      tempwindow.location = this.API.fiveGHerf;
    },
    //获取域名
    getDomain() {
      var hostname = window.location.hostname;
      var name = hostname.split(".");
      this.name = name[name.length - 2] + "." + name[name.length - 1];
    },
    edit(row) {
      this.$router.push({
        path: "/chatBot",
        query: {
          chatbotId: row.chatbotId,
        },
      });
    },
  },
};
</script>

<style scoped>
.page_bottom {
  padding: 10px;
  text-align: right;
}
.send_msg {
  cursor: pointer;
  color: #16a589;
}
</style>