<template>
    <div class="docs docssing"  style="height:100%;position:relative;overflow-x:hidden;" >
        <el-container >
                <el-aside width="200px" style="height:auto;overflow-x:hidden;position:fixed;bottom:75px;top:110px" class="docNav_left bag"> 
                    <div class="navLeft_top">
                        <p class="navLeft_top_title">常见问题</p>
                    </div>
                    <help-center-left  @clickWhere='clickPlace' @clickFirstIndex='clickFirstIndex' :defaultActive='defaultActive'></help-center-left>
                </el-aside>
                <el-main id="docsBody" class="bag" style="position:relative;margin-left:200px;" v-bind:style="{height: myHeight}">
                        <!-- <router-view></router-view> -->
                        <component v-bind:is="currentTabComponent" :hostflag="hostflag"></component>
                        <transition name="fade">
                            <div class="backToTop" @click="backToTop" v-show="showToolTip"><i class="el-icon-caret-top"></i></div>  
                        </transition>  
                </el-main>
        </el-container>
    </div>    
</template>

<script>
import helpCenterLeft from './commponents/helpCenterLeft'
import docsBasicConfiguration from './docsBasicConfiguration'
import docsBlacklistManage from './docsBlacklistManage'
import docsContact from './docsContact'
import docsNotificationAlarm from './docsNotificationAlarm'
export default {
    name: "helpCenterHome",
    components:{
        helpCenterLeft,
        docsBasicConfiguration,
        docsBlacklistManage,
        docsContact,
        docsNotificationAlarm
    },
    data(){
      return {
        currentTabComponent:'docsBasicConfiguration',
        myHeight:'800px',
        defaultActive:'1',
        showToolTip:false,
        product: [],
        hostname: window.location.hostname,
        nameTile: ["partner.zthysms.com", "partner.yameidu.com"],
      }
    },
    created(){
        let f = true
        this.nameTile.forEach((item) => {
        // console.log(item);
            if (this.hostname == item) {
                // console.log(1);
                // this.hostflag = false;
                f = false
            }
            // else{
            //     this.hostflag = true;
            // }
        });
        if (!f) {
            this.hostflag = false;
        } else {
            this.hostflag = true;
        }
    },
    methods:{
        clickPlace (val) {
            //检测点击的当前的选项是否在这个URL下面
            // var url=this.$route.path.replace(/\//g,'')
            // if(val[0].indexOf(url)==-1){
            //      this.$router.push(val[0].replace(/\d+/g,''))
            // }
            this.$nextTick(() => {
                var docName=val[0]+'_'+val[1];
                var rollHtml=document.getElementsByClassName(docName)
                if(rollHtml[0]) {    
                    var total= rollHtml[0].offsetTop//div到顶部的距离
                    var odiv=document.getElementById('docsBody')
                    var distance=odiv.scrollTop//滚动条滚动的距离
                    var step = total / 50
                    if (total > distance) {//向下滚动
                        smoothDown()
                    } else {//向上滚动
                        var newTotal = distance - total
                        step = newTotal / 50 
                        smoothUp()
                    }
                }
                function smoothDown () {
                        if (distance < total) {
                            distance += step
                            odiv.scrollTop=distance
                            setTimeout(smoothDown, 10)
                        } else {
                            odiv.scrollTop=total
                        }
                    }
                    function smoothUp () {
                        if (distance > total) {
                            distance -= step
                            odiv.scrollTop=distance
                            setTimeout(smoothUp, 10)
                        } else {
                            odiv.scrollTop=total
                        }
                    }

            })
        },
        clickFirstIndex(val) {
        //  this.$router.push(val.url)
         this.defaultActive=val.num
          this.currentTabComponent = val.url;
        },
        scrollDiv() {
            var docsBoxs=document.getElementsByClassName('docsBox');
            var docsBox=document.getElementById('docsBody');
            var _index;
            for(var i=0;i<docsBoxs.length;i++){
               if(docsBoxs[i].offsetTop<docsBox.scrollTop+40) {
                _index=docsBoxs[i].getAttribute("index")
                this.defaultActive=_index
               }
            }
           docsBox.scrollTop>400?this.showToolTip=true: this.showToolTip=false
         },
         backToTop() {//返回顶部
            var oDiv=document.getElementById('docsBody')
            var scrollDistance=oDiv.scrollTop
            var step=scrollDistance/50
            backTop ()
            function backTop (){
               if(scrollDistance>0){
                    oDiv.scrollTop=scrollDistance
                   scrollDistance-=step
                    setTimeout(backTop,10)    
                 }else {
                     oDiv.scrollTop=0
                 } 
            }          
         },
         windowResize () {//屏幕大小变化
         // 通过捕获系统的onresize事件触发我们需要执行的事件
            this.myHeight = (document.querySelector('.el-main').offsetHeight - 50) + 'px';
         }
    }, 
    mounted(){
        document.getElementById('docsBody').addEventListener('scroll', this.scrollDiv,{ passive: true });
        window.addEventListener('resize',this.windowResize,{ passive: true }); 
        this.myHeight=(document.querySelector('.el-main').offsetHeight - 50) + 'px'; 
  
        
    } ,
    beforeDestroy(){
        window.removeEventListener('resize',this.windowResize);  
    }
}
</script>

<style scoped>
.backToTop {
      position: fixed;
    width:50px;
    height:50px;
    border-radius: 50%;
    bottom: 100px;
    right: 50px;
    text-align:center;
    line-height: 50px;
    font-size: 30px;
    color:#16A589;
    background: #fff;
    box-shadow: 0px  0px 2px 2px #eee;
    cursor: pointer;
}
.navLeft_top {
    padding: 13px 0 0 20px;
    line-height: 40px;
    color: #333;
}
.navLeft_top_title {
    font-size: 16px;
    font-weight: bolder;
    margin-bottom: 10px;
}
.navLeft_top_subtitle {
    font-weight: bold;
}
.fade-enter-active, .fade-leave-active {
  transition: opacity .5s;
}
.fade-enter, .fade-leave-to /* .fade-leave-active below version 2.1.8 */ {
  opacity: 0;
}

</style>
<style>
.docssing .el-submenu__title{
    height:40px !important;
    line-height:40px !important;
} 
.docssing .el-menu-item{
    height:32px !important;
    line-height:32px !important;
}
</style>

