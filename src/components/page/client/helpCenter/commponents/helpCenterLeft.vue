<template>
    <div class="docs">
        
        <el-col  class="docsMenues">
            <el-menu 
            class="el-menu-vertical-demo"
            @open="pushUrl"
            @select="selectIndex"
           :unique-opened="true"
            :default-active="defaultActive"
            active-text-color="#16A589"
            >
               <!-- <docMenueList :getMenu="getMenu"></docMenueList> -->
               <el-submenu index="docsBasicConfiguration1">
                    <template slot="title" >
                        <span>个人账户</span>
                    </template>               
                    <el-menu-item index="1">账号问题</el-menu-item>
                    <el-menu-item index="2">基础配置问题</el-menu-item>
                    <!-- <el-menu-item index="3">发送超量提醒</el-menu-item>
                    <el-menu-item index="4">事件回调配置</el-menu-item> -->
                </el-submenu>
                <el-submenu index="docsNotificationAlarm11">
                    <template slot="title">
                        <span>支付问题</span>
                    </template>               
                    <el-menu-item index="11">账号充值</el-menu-item>
                    <el-menu-item index="22">开票问题</el-menu-item>
                    <!-- <el-menu-item index="33">发送超量提醒</el-menu-item>
                    <el-menu-item index="44">事件回调配置</el-menu-item> -->
                </el-submenu>
                <el-submenu index="docsBlacklistManage111">
                    <template slot="title"> 
                        <span>短信服务问题</span>
                    </template>               
                    <el-menu-item index="111">接口问题</el-menu-item>
                    <el-menu-item index="222">模板问题</el-menu-item>
                    <el-menu-item index="333">发送问题</el-menu-item>
                    <el-menu-item index="444">使用问题</el-menu-item>
                </el-submenu>
                <el-submenu index="docsContact1111">
                    <template slot="title">
                        <span>审核常见问题</span>
                    </template>               
                    <el-menu-item index="1111">规范和要求</el-menu-item>
                    <el-menu-item index="2222">审核问题</el-menu-item>
                    <!-- <el-menu-item index="3333">发送超量提醒</el-menu-item>
                    <el-menu-item index="4444">事件回调配置</el-menu-item> -->
                </el-submenu>
            </el-menu>
        </el-col>
    </div>    
</template>

<script>
// import docMenueList from './docMenueList'
export default {
    name: "helpCenterLeft",
    components:{
    //    docMenueList 
    },
    props:{
        defaultActive:{
            type: String
        } 
    },
    methods:{
        pushUrl(a,b){
            var testNum=/\d+/g;
            var num=a.match(testNum)[0]
            var url=a.replace(/\d+/g,'');         
            // this.$router.push(url)
            var obj={
                url:url,
                num:num
            }
            this.$emit('clickFirstIndex', obj) 
        },
        selectIndex(key, keyPath){
          this.$emit('clickWhere', keyPath)
        }
    },
    data () {
        return {

       
        }
    }
}
</script>

<style>
.docsMenues .el-submenu .el-menu-item {
    color: #666;
    /* background: #fff; */
}
.docsMenues .el-submenu.is-active .el-submenu__title{
    border-bottom: 1px solid #e2e2e2;
}
.docsMenues .el-menu {
    background: #fff;
    /* background: rgb(241, 242, 242); */
}
.docsMenues .el-menu-item:focus,.docsMenues .el-menu-item:hover,.docsMenues .el-submenu__title:hover  {
     background: #fff;
}
.docsMenues  .el-submenu__title:hover {
    color: #0fa284;
    
    /* transition: all .3s; */
}
.docsMenues .docsMenues .el-submenu .el-menu-item {
    border-bottom: 1px solid #e2e2e2;
}

</style>
