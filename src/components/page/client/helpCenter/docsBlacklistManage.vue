<template>
    <div class="docsBlacklistManage111">
        <div class="docsBlacklistManage111_111 docsBox" index="111">
            <h1 class="title" style="margin:20px 0 30px 0;">. 短信服务问题</h1>
            <h2 class="title title1 f-red mar-b">接口问题</h2>
            <div class="analysis">
                <div  class="mar-b mar-w"> 1. 用接口怎么发送短信？</div>
                <div class="mar-b">A: 我们有详细的接口使用文档帮助您对接，我们的技术人员也会协助您完成。</div>
                <div class="mar-b mar-w">2. 平台API账号是否需要认证？</div>
                <div class="mar-b">A: 没有进行认证的账号是无效的，是无法发送短信的。</div>                              
            </div>
        </div>
        <div class="docsBlacklistManage111_222 docsBox" index="222">
            <h2 class="title title1 f-red mar-b">模板问题</h2>
            <div class="analysis">
                <div  class="mar-b mar-w"> 1. 模板为什么需要审核？</div>
                <div class="mar-b">A: 当您提交了模板内容后，会有客服进行审核，若包含敏感词或存在违法违规行为会被驳回，审核通过的模板在下次发送短信时可直接使用。</div>
                <div class="mar-b mar-w"> 2. 什么是短信签名？</div>
                <div class="mar-b">A: 短信签名是加在短信的开头，以【】为标记，在【】里加入您的公司名称或店铺名称等，如 <span v-if="flag">【助通科技】</span><span v-else>【xxxx】</span>。根据运营商的规定，每条短信前必须附加短信签名，否则将无法正常发送。</div>
                <div class="mar-b mar-w"> 3. 模板发送和自定义内容发送有什么区别？</div>
                <div class="mar-b">A: 当您使用审核通过的模板进行短信发送时，会跳过人工审核流程，直接发送出去；而使用自定义发送短信时，系统会对短信内容进行校验，若含有敏感词，短信将被拦截，进入人工审核阶段。</div>                
            </div>
        </div>
        <div class="docsBlacklistManage111_333 docsBox" index="333">
           <h2 class="title title1 f-red mar-b">发送问题</h2>
            <div class="analysis">
                <div  class="mar-b mar-w"> 1. 可以同时发多个手机吗？</div>
                <div class="mar-b">A: 可以，相同内容使用Web一次最多可提交 <span style="color:red;">100</span> 万个号码，使用API接口提交一次最多是 <span style="color:red;">100</span> 万个号码。</div>
                <div class="mar-b mar-w"> 2. 三网（移动、联通、电信）都可以发吗？</div>
                <div class="mar-b">A: 是的。三网都支持，全国号码都可以发。</div>
                <div class="mar-b mar-w"> 3. 发送后为什么没有收到短信？</div>
                <div class="mar-b">A: 首先确认一下，短信发送是否返回成功。如果返回失败，请按具体返回的失败代码和出错提示排查； 如果返回成功，请检查： 1）手机是否处于关机或欠费停机状态，可以拨打手机号码确认； 2）请检查手机信号是否正常，必要时重启一下手机； 3）是否被短信屏蔽软件拦截，检查是否在屏蔽的短信列表中； 4）通过接口查询短信接受状态和错误代码 </div> 
                <div class="mar-b mar-w"> 4. 短信发送字数有限制吗？</div>
                <div class="mar-b">A: 一条短信最多包含 <span style="color:red;">1000</span> 个字，短信字数<=70个字，按照70个字一条短信计算 ；短信字数>70个字，即为长短信，按照67个字记为一条短信计算（注：短信签名是包含算在计费字数里）。 </div>                               
            </div>
        </div>
        <div class="docsBlacklistManage111_444 docsBox" index="444">
            <h2 class="title title1 f-red mar-b">使用问题</h2>
            <div class="analysis">
                <div  class="mar-b mar-w"> 1. 短信能测试吗？</div>
                <div class="mar-b">A: 可以，可以先跟您的专属销售联系，给您开通账号赠送短信条数进行测试。</div>
                <div class="mar-b mar-w"> 2. 手机号进黑名单了怎么办？</div>
                <div class="mar-b">A: 可联系客服解除黑名单限制。</div>
                <div class="mar-b mar-w"> 3. 哪些情况手机号会进入黑名单？</div>
                <div class="mar-b">A: 之前投诉过运营商，如打过10086、10010或10000投诉的，可能会被运营商加入黑名单；有过退订历史，如回复过含有T、TD、 退订或取消等代表拒绝接收短信的指令。</div>  
                <div  class="mar-b mar-w"> 4. 提交短信的频率有限制吗？</div>
                <div class="mar-b">A: 没有限制。如想更快的提交短信，可以使用多线程提交，但相同手机号为防止骚扰，会限制频率和日上限，如有需要可以与客服联系取消限制。</div>
                <div class="mar-b mar-w"> 5. 怎么预防短信轰炸？</div>
                <div class="mar-b">A: 我们有完善的验证码防轰炸机制：对同一手机号用户可收到的信息数量进行限制：<span style="color:red;">①一分钟内不可超过1条，②一小时内不可超过5条，③一天内不超过20条。</span>同时，用户可自己在平台上进行发送频率的设置，设置单日提交次数和同一号码单日提交次数。</div>
                <div class="mar-b mar-w"> 6. 提交时间、发送时间、回执时间有什么区别？</div>
                <div class="mar-b">A: 提交时间是客户短信提交至我们平台的时间；发送时间是<span v-if="flag">助通平台</span>向通道提交的时间；状态回执时间是通道向我们返回状态时，记录的当前服务器时间。</div>               
            </div>
        </div>
    </div>    
</template>

<script>
export default {
    props:['hostflag'],
    name: "docsBlacklistManage",
    data(){
        return{
            flag:true
        }
    },
    watch:{
        hostflag: {
            handler(val) {
                // if(val){
                    this.flag = val
                // }
                
            },
            deep: true, // 深度监听
            immediate: true, // 初次监听即执行
        },
    }
}
</script>

<style scoped>
.title {
    font-size: 1.6rem;
}
.title1{
    font-size:16px;
}
.mar-b {
    font-size:14px;
    margin-bottom: 5px;
}
.analysis {
    margin-top: 10px;
    margin-bottom: 20px;
    background: #F6F6F6;
    padding: 16px 20px;
    border: 1px solid #d9d9db;
    border-radius: 5px;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    font-size: 15px;
}
.subtitle {
    font-size: 16px;
    line-height: 42px;
    font-weight: 400;
}
.mar-w{
    height:34px;
    line-height: 34px;
    font-weight: bold;
     font-size:15px;
}
.f-red {
    font-size:16px;
}
</style>