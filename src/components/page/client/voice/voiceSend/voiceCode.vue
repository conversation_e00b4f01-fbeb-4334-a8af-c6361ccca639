<template>
    <div class="bag">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 语音验证码</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="fillet shortChain-box" style="background:#fff">
            <el-form :model="formData" :rules="formRule" ref="formRef" label-width="95px" style="padding:20px 8px  0 8px;">
                <el-form-item label="语音类型：" prop="isTiming">
                    <span>验证码</span>
                </el-form-item>
                <el-form-item label="验证码：" prop="content">
                    <el-input v-model="formData.content" style="width:500px" placeholder="请输入4-6位数字" type="textarea"></el-input>
                </el-form-item>
                <el-form-item label="手机号：" prop="mobile" :rules="filter_rules({required:true,type:'fiveMobiles',message:'请输入手机号码'})">
                    <el-input v-model="formData.mobile" style="width:500px"  placeholder="仅能输入一个手机号码" type="textarea"></el-input>
                </el-form-item>
                <el-form-item label="" prop="">
                    <el-button type="primary" @click="send">测试发送</el-button>
                </el-form-item>
            </el-form>
        </div>
        <el-dialog
            title="实名认证"
            :visible.sync="dialogSmrzFlag"
            width="30%"
            center>
            <span>尊敬的客户，根据《中华人民共和国网络安全法》及相关法律的规定，请您尽快完成实名认证。如需帮助，请联系在线售后客服。</span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="goSmrz">前往实名认证</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
export default {
    name: "voiceCode",
    data () {
        var content=(rule,value,callback)=>{
            if(!/^\d{4,6}$/.test(value)){
                callback(new Error('请输入4-6位的验证码'))
            }else{
                callback()
            } 
        }
        return {
            dialogSmrzFlag :false,
            formData:{
                content:'',
                mobile:'',
            },
            formRule:{
                content:[
                    { required: true, validator: content, trigger: 'blur' },
                ],
            }
        }
    },
    created(){
        this.$api.get(
        this.API.cpus + "consumerclientinfo/getClientInfo",
        null,
      (res) => {
        // console.log(res.data);
        if(res.data.certificate == 0){
            this.dialogSmrzFlag = true
        }
        // this.certificate = res.data.certificate;

      }
    );
    },
    // activated(){
    //     this.$api.get(
    //     this.API.cpus + "consumerclientinfo/getClientInfo",
    //     null,
    //   (res) => {
    //     // console.log(res.data);
    //     if(res.data.certificate == 0){
    //         this.dialogSmrzFlag = true
    //     }
    //     // this.certificate = res.data.certificate;

    //   }
    // );
    // },
    methods: {
        goSmrz(){
            this.dialogSmrzFlag = false
            this.$router.push("/authentication")
        },
        send(){
            this.$refs['formRef'].validate((valid,value) => {
                if (valid) {
                     this.$confirms.confirmation('post','确认发送验证码？',this.API.cpus+'v1/consumervoice/captcha/send',this.formData,res =>{
                         if(res.code==200){
                             this.$refs['formRef'].resetFields();
                         }
                    });
                }
            })
        }
    },
    mounted(){
        
    },
    watch:{
        
    },
}
</script>
<style scoped>
.shortChain-box{
    padding:20px;
}
</style>