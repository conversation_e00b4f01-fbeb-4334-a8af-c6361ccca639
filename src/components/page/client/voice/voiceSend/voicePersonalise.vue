<template>
  <div class="bag">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><i class="el-icon-lx-emoji"></i> 个性化通知</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div class="fillet shortChain-box" style="background: #fff">
      <el-form
        :model="formData"
        ref="formRef"
        label-width="95px"
        style="padding: 20px 8px 0 8px"
      >
        <el-form-item label="语音类型：" prop="isTiming">
          <span>语音个性化-通知</span>
          <a
            href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/d3ccbf40077f10ad131af7fc0a8aa330"
            style="margin-left: 10px; color: #0066ff; cursor: pointer"
            >模板下载</a
          >
        </el-form-item>
        <el-form-item label="语音内容：" prop="">
          <file-upload
            style="display: inline-block"
            :action="this.API.cpus + 'v3/file/upload'"
            :limit="1"
            :showfileList="true"
            :fileStyle="fileStyle"
            :del="del1"
            :istip="false"
            :tip="tip"
            @fileup="fileup"
            @fileupres="fileupres"
            >选择上传文件</file-upload
          >
        </el-form-item>
        <el-form-item label="发送时间" prop="timingSend">
          <el-radio-group v-model="formData.timingSend">
            <el-radio label="0">立即发送</el-radio>
            <el-radio label="1">定时发送</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="formData.timingSend == 1" prop="sendTime">
          <span slot="label" style="color: #a4a9ab"> 时间日期 </span>
          <el-date-picker
            v-model="formData.sendTime"
            type="datetime"
            class="lableW"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期时间"
            align="right"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="" prop="">
          <el-button @click="submissionItem('formRef')" type="primary"
            >立即发送</el-button
          >
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>
<script>
import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传
export default {
  name: "voicePersonalise",
  components: {
    FileUpload,
  },
  data() {
    return {
      fileFlag: true,
      message: "",
      formData: {
        sendTime: "",
        timingSend: "0",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        },
      },
      fileStyle: {
        size: 245678943234,
        style: ["xlsx", "xls", "txt"],
      },
      del1: true, //关闭弹框时清空图片
      tip: "仅支持.xlsx .xls.txt 等格式",
    };
  },
  methods: {
    submissionItem(formName) {
      this.$refs[formName].validate((valid, val) => {
        if (valid) {
          if (!this.fileFlag) {
            this.$message({
              message: this.message,
              type: "error",
            });
            return;
          }
          this.$confirms.confirmation(
            "post",
            "确认发送个性化通知？",
            this.API.cpus + "v1/consumervoice/customize/send",
            this.formData,
            (res) => {
              if (res.code == 200) {
                this.formData = {
                  sendTime: "",
                  timingSend: "0",
                };
                this.RowNum = null;
                this.textareaShow = true;
                this.del1 = false;
                this.$router.push("/VoiceWebTask");
              }
            }
          );
        }
      });
    },
    //移除文件
    fileup(val) {
      this.RowNum = null;
      this.formData.group = "";
      this.formData.path = "";
      this.formData.fileName = "";
      this.formData.files = "";
      this.textareaShow = true;
      this.formData.mobile = "";
      this.limit = 0; //号码限制
    },
    //文件上传成功
    fileupres(val, val2) {
      if (val.code == 200) {
        this.fileFlag = true;
        if (val.data.total) {
          this.RowNum = val.data.total;
        } else {
          this.RowNum = null;
        }
        this.formData.fileName = val.data.fileName;
        this.formData.group = val.data.group;
        this.formData.path = val.data.path;
        this.formData.files = val2[0].name;
        this.formData.mobile = "";
        this.textareaShow = false;
        this.del1 = true;
        this.$message({
          message: val.msg,
          type: "success",
        });
      } else {
        this.fileFlag = false;
        this.message = val.msg;
        this.$message({
          message: val.msg,
          type: "error",
        });
      }
    },
    formWorkDownload() {
      console.log(111);
      this.$File.export(
        this.API.cpus + "v3/consumersms/templateZipDownload",
        {},
        `自定义发送模板.zip`
      );
    },
  },
  mounted() {},
  watch: {},
};
</script>
<style scoped>
.el-textarea {
  width: 50%;
}
.shortChain-box {
  padding: 20px;
}
.sendMsg-box {
  padding: 20px;
}
.sendMsg-title {
  margin: 10px 0;
  padding-left: 5px;
  color: #16a589;
}
.sendMsg-table {
  margin-top: 12px;
}
.sendMsg-list-header {
  padding-top: 30px;
  font-weight: bold;
}
.el-steps--simple {
  background: #fff;
  border-radius: 0px;
  border-bottom: 1px solid #f3efef;
}
.send-mobel-box {
  width: 260px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}
.send-mobel-box img {
  width: 255px;
}
.el-select {
  width: 50%;
}
.send-upload-tips {
  padding-left: 96px;
  padding-top: 5px;
  font-size: 12px;
}
.send-upload-tips span {
  display: inline-block;
  padding-left: 8px;
  color: #0066ff;
}
.sms-content-exhibition {
  width: 170px;
  height: 32%;
  border-radius: 10px;
  position: absolute;
  padding: 8px 9px;
  background: #e2e2e2;
  top: 146px;
  left: 31px;
  font-size: 12px;
  line-height: 18px;
  color: #000;
  overflow: hidden;
}
/* .sms-first-steps-btns{
    width:517px;
    position: absolute;
    text-align: center;
    bottom: 10px;
} */
.sms-seconnd-steps-box {
  padding: 30px 20px 30px 55px;
}
.sms-seconnd-steps-btns {
  text-align: right;
}
.goTiming {
  padding-right: 14px;
  cursor: pointer;
  color: #16a589;
}
.goTiming:hover {
  color: #03886e;
}
.shortChain-matter {
  border: 1px solid #66ccff;
  padding: 10px 14px;
}

.short-title {
  font-weight: bolder;
  padding-bottom: 5px;
}
.font-sizes {
  padding-top: 2px;
  font-size: 12px;
  color: rgb(163, 163, 163);
}
.font-sizes1 {
  margin-top: 10px;
}
.input-w-3 {
  width: 320px !important;
}
</style>
<style>
.sendMsg-box .el-dialog__body {
  padding: 10px 20px;
}
.sendMsg-box .el-form-item__label {
  text-align: left;
}
.sendMsg-box .el-step__head.is-process {
  color: #989898;
}
.sendMsg-box .el-step__title.is-process {
  color: #989898;
}
.sms-content-exhibition .el-scrollbar__wrap {
  overflow-x: hidden;
  /* overflow-y: scroll !important;  */
  margin-right: -27px !important;
}
.el-picker-panel .el-button--text {
  display: none;
}
.textareas textarea {
  height: 100px;
  resize: none;
}
.textareas textarea::-webkit-scrollbar {
  display: none;
}
.box-textareas {
  width: 380px;
  height: 125px;
  border-radius: 5px;
  border: 1px solid rgb(162, 219, 208);
}
.box-textareas textarea {
  border: none;
}
</style>