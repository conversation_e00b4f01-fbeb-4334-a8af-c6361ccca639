<template>
  <div class="bag">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><i class="el-icon-lx-emoji"></i> 语音群通知</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div class="fillet shortChain-box" style="background: #fff">
      <el-form
        :model="formData"
        :rules="formRule"
        ref="formRef"
        label-width="95px"
        style="padding: 20px 8px 0 8px"
      >
        <el-form-item label="语音类型：" prop="">
          <span>语音群通知</span>
        </el-form-item>
        <el-form-item label="语音内容：" prop="content">
          <el-input
            type="textarea"
            placeholder="请输入语音内容"
            v-model="formData.content"
            class="textareas"
            maxlength="500"
            show-word-limit
            style="width: 380px"
          ></el-input>
        </el-form-item>
        <div
          class="send-upload-tips"
          style="font-size: 12px; margin-bottom: 10px"
        >
          最多 500 字（含签名）,85字计一条
        </div>
        <el-form-item label="发送方式">
              <el-radio-group v-model="formData.sendType" @change="handelSend">
                <el-radio label="0">号码发送</el-radio>
                <el-radio label="1">文件发送</el-radio>
              </el-radio-group>
            </el-form-item>
        <el-form-item v-if="formData.sendType=='1'"  label="发送对象：" prop="">
          <el-upload
              class="upload-demo"
              drag
              style="display: inline-block"
              :action="this.API.cpus + 'v3/file/upload'"
              :headers="header"
              :limit="1"
              :on-remove="fileup"
              :on-success="fileupres"
              :before-upload="beforeAvatarUpload"
              :file-list="fileList"
              multiple
            >
              <i class="el-icon-upload active"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
              <!-- <div class="el-upload__tip" slot="tip">
                只能上传jpg/png文件，且不超过500kb
              </div> -->
            </el-upload>
          <!-- <file-upload
            style="display: inline-block"
            :action="this.API.cpus + 'v3/file/upload'"
            :limit="1"
            :showfileList="true"
            :fileStyle="fileStyle"
            :del="del1"
            :istip="false"
            :tip="tip"
            @fileup="fileup"
            @fileupres="fileupres"
            >选择上传文件</file-upload
          > -->
          <span v-if="RowNum" style="font-size: 12px; color: #aaa"
            >本次共上传 {{ RowNum }} 行</span
          >
        </el-form-item>
        <div v-if="formData.sendType=='1'"  class="send-upload-tips">
          格式要求：支持.xlsx .xls.txt 等格式,文件大小不超过300M<span
            style="color: rgb(210, 7, 7)"
            >(如上传文件将会自动清除下面手动填写的手机号码)</span
          >
        </div>
        <div v-if="formData.sendType=='1'"  class="send-upload-tips" style="margin-bottom: 20px">
          TXT格式：只支持一行一个号码。
        </div>
        <el-form-item label="" prop="mobile" v-if="formData.sendType=='0'" >
          <div class="box-textareas">
            <el-input
              type="textarea"
              v-model="formData.mobile"
              @blur="FilterNumber"
              class="textareas"
              @input="textChange"
              style="width: 380px"
              placeholder="手动最多输入200个手机号码，号码之间用英文逗号隔开"
            ></el-input>
          </div>
          <div
            style="
              width: 695px;
              text-align: center;
              position: absolute;
              bottom: 30px;
              height: 0;
            "
            v-show="limit <= 200"
          >
            <span>{{ limit }}/200</span>
          </div>
          <div
            style="
              width: 695px;
              text-align: center;
              position: absolute;
              bottom: 30px;
              height: 0;
            "
            v-show="limit > 200"
          >
            <span style="color: red">{{ limit }}</span
            ><span>/200</span>
          </div>
        </el-form-item>
        <el-form-item label="发送时间" prop="timingSend">
          <el-radio-group v-model="formData.timingSend">
            <el-radio label="0">立即发送</el-radio>
            <el-radio label="1">定时发送</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="formData.timingSend == 1" prop="sendTime">
          <span slot="label" style="color: #a4a9ab"> 时间日期 </span>
          <el-date-picker
            v-model="formData.sendTime"
            type="datetime"
            class="lableW"
            value-format="yyyy-MM-dd HH:mm:ss"
            placeholder="选择日期时间"
            align="right"
            :picker-options="pickerOptions"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item label="" prop="">
          <el-button @click="submissionItem('formRef')" type="primary"
            >立即发送</el-button
          >
        </el-form-item>
      </el-form>
    </div>
    <el-dialog
      title="实名认证"
      :visible.sync="dialogSmrzFlag"
      width="30%"
      center
    >
      <span
        >尊敬的客户，根据《中华人民共和国网络安全法》及相关法律的规定，请您尽快完成实名认证。如需帮助，请联系在线售后客服。</span
      >
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="goSmrz">前往实名认证</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传
export default {
  name: "voiceNotice",
  components: {
    FileUpload,
  },
  data() {
    var mobile = (rule, value, callback) => {
      if (value || this.RowNum) {
        if (this.limit > 200) {
          callback("已超过填写返回");
        } else {
          callback();
        }
      } else {
        callback("请上传文件或输入手机号");
      }
    };
    return {
      header:{},
      endingName:"",
      fileList:[],
      dialogSmrzFlag: false,
      fileFlag: true,
      message: "",
      formData: {
        content: "",
        mobile: "",
        sendType:"0",
        sendTime: "",
        timingSend: "0",
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 8.64e7;
        },
      },
      formRule: {
        //验证规则
        content: [
          {
            required: true,
            min: 1,
            max: 500,
            message: "长度在 1 到 500 个字符",
            trigger: "blur",
          },
        ],
        mobile: [{ required: true, validator: mobile, trigger: "blur" }],
      },
      fileStyle: {
        size: 245678943234,
        style: ["xlsx", "xls", "txt"],
      },
      del1: true, //关闭弹框时清空图片
      tip: "仅支持.xlsx .xls.txt 等格式",
      RowNum: null, //上传行数
      limit: 0, //号码限制
      textareaShow: true,
    };
  },
  created() {
    this.header = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
    };
    this.$api.get(
      this.API.cpus + "consumerclientinfo/getClientInfo",
      null,
      (res) => {
        // console.log(res.data);
        if (res.data.certificate == 0) {
          this.dialogSmrzFlag = true;
        }
        // this.certificate = res.data.certificate;
      }
    );
  },
  // activated(){
  //     this.$api.get(
  //     this.API.cpus + "consumerclientinfo/getClientInfo",
  //     null,
  //   (res) => {
  //     // console.log(res.data);
  //     if(res.data.certificate == 0){
  //         this.dialogSmrzFlag = true
  //     }
  //     // this.certificate = res.data.certificate;

  //   }
  // );
  // },
  methods: {
    goSmrz() {
      this.dialogSmrzFlag = false;
      this.$router.push("/authentication");
    },
    submissionItem(formName) {
      this.$refs[formName].validate((valid, val) => {
        if (valid) {
          if (!this.fileFlag) {
            this.$message({
              message: this.message,
              type: "error",
            });
            return;
          }
          this.$confirms.confirmation(
            "post",
            "确认发送语音通知？",
            this.API.cpus + "v1/consumervoice/notice/send",
            this.formData,
            (res) => {
              if (res.code == 200) {
                this.formData = {
                  content: "",
                  mobile: "",
                  timingSend: "0",
                  sendTime: "",
                };
                this.RowNum = null;
                this.textareaShow = true;
                this.del1 = false;
                this.$router.push("/VoiceWebTask");
              }
            }
          );
        }
      });
    },
    //移除文件
    fileup(val) {
      this.RowNum = null;
      this.formData.group = "";
      this.formData.path = "";
      this.formData.fileName = "";
      this.formData.files = "";
      this.textareaShow = true;
      this.formData.mobile = "";
      this.limit = 0; //号码限制
      this.fileList = []
    },
    handelSend(val){
      if(val == 0){
        this.fileup()
      }else{
        this.formData.mobile = ''
      }
    },
    //限制用户上传文件格式和大小
        beforeAvatarUpload(file) { 
            let endingCode = file.name;//结尾字符
            this.endingName = endingCode.slice(endingCode.lastIndexOf('.')+1,endingCode.length);
            let isStyle = false; //文件格式
            const isSize = file.size / 1024 / 1024 < this.fileStyle.size; //文件大小
            console.log(isSize)
            for(let i=0;i<this.fileStyle.style.length;i++){
                if(this.endingName === this.fileStyle.style[i]){
                    isStyle = true;
                    break; 
                }
            }
            //不能重复上传文件
            let fileArr=this.fileList;
            let fileNames=[];
            if(fileArr.length>0){
                    for(let k=0;k<fileArr.length;k++){
                    fileNames.push(fileArr[k].name)
                } 
            }
            if(fileNames.indexOf(endingCode)!==-1){
                this.$message.error('不能重复上传文件');
                return false;
            }else if (!isStyle) { //文件格式判断
                this.$message.error(this.tip);
                return false;
            }else{
                //文件大小判断
                if (!isSize) {
                    this.$message.error('上传文件大小不能超过'+this.fileStyle.size);
                    return false;
                }
            }
        },
    //文件上传成功
    fileupres(val, val2) {
      if (val.code == 200) {
        this.fileFlag = true;
        if (val.data.total) {
          this.RowNum = val.data.total;
        } else {
          this.RowNum = null;
        }
        this.formData.fileName = val.data.fileName;
        this.formData.group = val.data.group;
        this.formData.path = val.data.path;
        this.formData.files = val.data.fileName;
        this.formData.mobile = "";
        this.textareaShow = false;
        this.del1 = true;
        this.$message({
          message: val.msg,
          type: "success",
        });
      } else {
        this.fileFlag = false;
        this.message = val.msg;
        this.$message({
          message: val.msg,
          type: "error",
        });
      }
    },
    // 过滤号码
    FilterNumber() {
      this.formData.mobile = this.formData.mobile.replace(/\D/g, ",");
      let NumberFilter = this.formData.mobile.split(",");
      let arrNumber = [];
      let hash = [];
      let reg = /^(?:\+?86)?1\d{10}$/;
      for (var i = 0; i < NumberFilter.length; i++) {
        for (var j = i + 1; j < NumberFilter.length; j++) {
          if (NumberFilter[i] === NumberFilter[j]) {
            ++i;
          }
        }
        arrNumber.push(NumberFilter[i]);
      }
      for (var i = 0; i < arrNumber.length; i++) {
        if (reg.test(arrNumber[i])) {
          hash.push(arrNumber[i]);
        }
      }
      this.formData.mobile = hash.join(",");
      this.filter = NumberFilter.length - arrNumber.length; //过滤
      if (arrNumber[0] == "") {
        this.invalid == 0;
      } else {
        this.invalid = arrNumber.length - hash.length; //无效
      }
      this.limit = hash.length;
    },
    textChange() {
      // this.formData.mobile=this.formData.mobile.replace(/[^\ \d\,]/g,"")
      // this.formData.mobile=this.formData.mobile.replace(/\s+/g,",")
      if (this.formData.mobile[this.formData.mobile.length - 1] == ",") {
        this.limit = this.formData.mobile.split(",").length - 1;
      } else {
        this.limit = this.formData.mobile.split(",").length;
      }
      if (
        this.formData.mobile.split(",").length == 1 &&
        this.formData.mobile.split(",")[0] == ""
      ) {
        this.limit = 0;
      }
    },
  },
  mounted() {},
  watch: {},
};
</script>
<style scoped>
.active{
        font-size: 67px !important; 
    color: #C0C4CC !important;
    margin: 40px 0 16px !important;
    line-height: 50px !important;
}
.el-textarea {
  width: 50%;
}
.shortChain-box {
  padding: 20px;
}
.sendMsg-box {
  padding: 20px;
}
.sendMsg-title {
  margin: 10px 0;
  padding-left: 5px;
  color: #16a589;
}
.sendMsg-table {
  margin-top: 12px;
}
.sendMsg-list-header {
  padding-top: 30px;
  font-weight: bold;
}
.el-steps--simple {
  background: #fff;
  border-radius: 0px;
  border-bottom: 1px solid #f3efef;
}
.send-mobel-box {
  width: 260px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}
.send-mobel-box img {
  width: 255px;
}
.el-select {
  width: 50%;
}
.send-upload-tips {
  padding-left: 96px;
  padding-top: 5px;
  font-size: 12px;
}
.send-upload-tips span {
  display: inline-block;
  padding-left: 8px;
  color: #0066ff;
}
.sms-content-exhibition {
  width: 170px;
  height: 32%;
  border-radius: 10px;
  position: absolute;
  padding: 8px 9px;
  background: #e2e2e2;
  top: 146px;
  left: 31px;
  font-size: 12px;
  line-height: 18px;
  color: #000;
  overflow: hidden;
}
/* .sms-first-steps-btns{
    width:517px;
    position: absolute;
    text-align: center;
    bottom: 10px;
} */
.sms-seconnd-steps-box {
  padding: 30px 20px 30px 55px;
}
.sms-seconnd-steps-btns {
  text-align: right;
}
.goTiming {
  padding-right: 14px;
  cursor: pointer;
  color: #16a589;
}
.goTiming:hover {
  color: #03886e;
}
.shortChain-matter {
  border: 1px solid #66ccff;
  padding: 10px 14px;
}

.short-title {
  font-weight: bolder;
  padding-bottom: 5px;
}
.font-sizes {
  padding-top: 2px;
  font-size: 12px;
  color: rgb(163, 163, 163);
}
.font-sizes1 {
  margin-top: 10px;
}
.input-w-3 {
  width: 320px !important;
}
</style>
<style>
@media screen and (min-width: 1200px) {
  .sendMsg-box .el-dialog__body {
    padding: 10px 20px;
  }
  .sendMsg-box .el-form-item__label {
    text-align: left;
  }
  .sendMsg-box .el-step__head.is-process {
    color: #989898;
  }
  .sendMsg-box .el-step__title.is-process {
    color: #989898;
  }
  .sms-content-exhibition .el-scrollbar__wrap {
    overflow-x: hidden;
    /* overflow-y: scroll !important;  */
    margin-right: -27px !important;
  }
  .el-picker-panel .el-button--text {
    display: none;
  }
  .textareas textarea {
    height: 100px;
    resize: none;
  }
  .textareas textarea::-webkit-scrollbar {
    display: none;
  }
  .box-textareas {
    width: 380px;
    height: 125px;
    border-radius: 5px;
    border: 1px solid rgb(162, 219, 208);
  }
  .box-textareas textarea {
    border: none;
  }
}
/* @media screen and (max-width: 1200px){
    .el-picker-pane{
        width: 300px;
        height: 350px;
        overflow: auto;
    }
    .el-date-range-picker__content{
        width: 100%;
    }
} */
</style>