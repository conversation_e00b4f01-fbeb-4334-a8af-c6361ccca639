<template>
    <div id=shortMessage class="bag">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 发送详情</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="fillet  ShortMessageRecording-chart-box" style="background:#fff">
            <div class="short-message-recording-type">
                <el-form ref="SHformList" :model="form" label-width="86px" class="query-frame">
                    <el-form-item label="手机号码" prop="mobile">
                        <el-input v-model="form.mobile" placeholder="请输入手机号" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="发送状态" prop="status" v-if="this.$store.state.isDateState == 1">
                        <el-select v-model="form.status" placeholder="发送状态" class="input-w">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="成功" value="1"></el-option>
                            <el-option label="失败" value="2"></el-option>
                            <el-option label="待返回" value="3"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="发送类型" prop="type">
                        <el-select v-model="form.type" placeholder="发送类型" class="input-w">
                            <el-option label="全部" value=""></el-option>
                            <el-option label="语音验证码" value="1"></el-option>
                            <el-option label="语音通知" value="2"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="语音内容" prop="content">
                        <el-input v-model="form.content" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="发送时间" prop="time1">
                        <el-date-picker class="input-time" v-model="form.time1" value-format="yyyy-MM-dd" type="daterange"
                            range-separator="-" :picker-options="pickerOptions" :clearable="false"
                            @change="getTimeOperating" start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                    <div style="margin-bottom: 18px;">
                        <el-button type="primary" plain @click="querySending()">查 询</el-button>
                        <el-button type="primary" plain @click="resetSending('SHformList')">重 置</el-button>
                        <el-button type="primary" @click="exportNums1()">导出数据</el-button>
                    </div>
                </el-form>
                <div style="padding-bottom:40px;">
                    <table-tem :tableDataObj="tableDataObj" @handPhone="handPhone"></table-tem>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
                        style="background:#fff;padding:10px 0;text-align:right;">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange"
                            @current-change="handleCurrentChange" :current-page="1" :page-size="formData.pageSize"
                            :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
                            :total="tableDataObj.totalRow">
                        </el-pagination>
                    </el-col>
                </div>
            </div>
            <!-- <el-dialog
            title="手机验证"
            :visible.sync="exportFlag"
            width="40%"
            top="30vh"
        >  
            <DownLoadExport ref="ExportChild" :formData1='formData' productType='6' :isDownload='formData.isDownload' :phoneList="phoneList"/>
        </el-dialog> -->
            <el-dialog title="导出数据" :visible.sync="exportShow" width="30%" :before-close="handleClose">
                <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                    <el-form-item label="导出类型" prop="decode">
                        <el-radio-group v-model="ruleForm.decode">
                            <el-radio label="0">掩码</el-radio>
                            <el-radio label="1">明码</el-radio>
                        </el-radio-group>
                    </el-form-item>
                    <div style="margin-left: 28px;color: #F56C6C;" v-if="ruleForm.decode == '1'">
                        tips: 明码文件将会发送您的{{ emailInfo.email }}邮箱，请注意查收。
                    </div>
                </el-form>
                <span slot="footer" class="dialog-footer">
                    <el-button @click="exportShow = false">取 消</el-button>
                    <el-button type="primary" @click="submitExport">{{ ruleForm.decode == '0' ? '下载' : '发送' }}</el-button>
                </span>
            </el-dialog>
            <ResetNumberVue v-if="resetVideo" ref="resetNumber" :infoData="infoData" :visible="resetVideo"></ResetNumberVue>
        </div>
    </div>
</template>

<script>

import TableTem from '@/components/publicComponents/TableTem'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import DownLoadExport from "@/components/publicComponents/downLodExport"
import moment from 'moment'
import ResetNumberVue from "@/components/publicComponents/ResetNumber.vue";
import bus from "../../../../common/bus"
import common from "../../../../../assets/js/common";
export default {
    name: "voiceSendDetails",
    components: {
        TableTem,
        DatePlugin,
        DownLoadExport,
        ResetNumberVue
    },
    data() {
        return {
            pickerOptions: {
                disabledDate(time) {
                    return time.getTime() < Date.now() - 30 * 24 * 3600 * 1000 || time.getTime() > Date.now();
                }
            },
            phoneList: [],
            exportFlag: false,
            //复选框的值
            selectId: '',
            // 单条存值
            AddBlackVal: '',
            // 备注
            AddBlackform: {
                remark: ''
            },
            // AddBlacks:false,
            AddBlacksStatus: false,
            isDateState: '',
            //发送查询的值
            form: {
                mobile: '',
                status: '',
                type: "",
                signature: '',
                content: '',
                temId: '',
                area: '',
                beginTime: moment().subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss'),
                endTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
                time1: [moment().subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss') + "", moment(Date.now()).format('YYYY-MM-DD HH:mm:ss')],
                currentPage: 1,
                pageSize: 10,
                time: '',
                isDownload: 2
            },
            //复制发送查询的值
            formData: {
                mobile: '',
                status: '',
                type: "",
                signature: '',
                content: '',
                temId: '',
                area: '',
                beginTime: moment().subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss'),
                endTime: moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
                time1: [moment().subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss') + "", moment(Date.now()).format('YYYY-MM-DD HH:mm:ss')],
                // beginTime:new Date().toLocaleDateString().split('/').join('-')+' 00:00:00',
                // endTime:new Date().toLocaleDateString().split('/').join('-')+' 23:59:59',
                // time1:[new Date().toLocaleDateString().split('/').join('-')+' 00:00:00',new Date().toLocaleDateString().split('/').join('-')+' 23:59:59'],
                currentPage: 1,
                pageSize: 10,
                isDownload: 2
            },
            rules:{},
            tableDataObj: { //表格数据
                loading2: false,
                totalRow: 0,
                tableData: [],
                tableLabel: [  //列表表头
                    { prop: "mobile", showName: '手机号码', width: 140, fixed: false },
                    { prop: "content", showName: '短信内容', width: 650, fixed: false },
                    { prop: "playNum", showName: '播报次数', width: 70, fixed: false },
                    { prop: "chargeNum", showName: '计费条数', width: 70, fixed: false },
                    { prop: "msgid", showName: '消息ID', fixed: false, width: 200, copy: true },
                    { prop: "sendTime", showName: '发送时间', width: 170, fixed: false },
                    {
                        prop: "type", showName: '发送类型', width: 140, fixed: false, formatData(val) {
                            if (val == 1) {
                                return '语音验证码'
                            } else {
                                return '语音通知'
                            }
                        }
                    },
                    { prop: "reportTime", showName: '状态上报时间', width: 170, fixed: false },
                ],
                tableStyle: {
                    isSelection: false,//是否复选框
                    isExpand: false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width: "100%"
                    },
                    optionWidth: '180',//操作栏宽度
                    border: true,//是否边框
                    stripe: false,//是否有条纹
                }
            },
            resetVideo: false,
            infoData: {},
            emailInfo: {
                email: "",
                username: ""
            },
            exportShow: false,
            ruleForm: {
                decode: "0",
            },
        }
    },
    methods: {
        //发送请求
        sendReport() {
            let data = Object.assign({}, this.formData);
            data.flag = 5;
            this.$api.post(this.API.cpus + 'consumervoicemessage/messages', data, res => {
                this.tableDataObj.tableData = res.data.records;
                this.tableDataObj.totalRow = res.data.total;
                this.tableDataObj.loading2 = false;
            })
        },
        // 发送时间
        getTimeOperating(val) {
            if (val) {
                this.form.beginTime = val[0] + " 00:00:00"
                this.form.endTime = val[1] + " 23:59:59"
            } else {
                this.form.beginTime = ''
                this.form.endTime = ''
            }
        },
        //查询 （发送）
        querySending() {
            Object.assign(this.formData, this.form);
            this.sendReport();
        },
        //重置 （发送）
        resetSending(formName) {
            this.$refs[formName].resetFields();
            this.form.beginTime = moment().subtract(1, 'months').format('YYYY-MM-DD HH:mm:ss'),
                this.form.endTime = moment(Date.now()).format('YYYY-MM-DD HH:mm:ss'),
                // this.form.beginTime=new Date().toLocaleDateString().split('/').join('-')+' 00:00:00',
                // this.form.endTime=new Date().toLocaleDateString().split('/').join('-')+' 23:59:59',
                Object.assign(this.formData, this.form);
            this.sendReport();
        },
        //获取分页的每页数量
        handleSizeChange(size) {
            this.formData.pageSize = size;
            this.sendReport();
        },
        //获取分页的第几页
        handleCurrentChange(currentPage) {
            this.formData.currentPage = currentPage;
            this.sendReport();
        },
        handPhone(row, index) {
            this.$api.post(this.API.upms + '/generatekey/decryptMobile', {
                keyId: row.keyId,
                smsInfoId: row.decryptMobile,
                cipherMobile: row.cipherMobile
            }, res => {
                if (res.code == 200) {
                    this.tableDataObj.tableData[index].mobile = res.data;
                } else if (res.code == 4004002) {
                    common.fetchData().then((res) => {
                        if (res.code == 200) {
                            if (res.data.isAdmin == 1) {
                                this.resetVideo = true;
                                this.infoData = res.data;
                            } else {
                                this.$message({
                                    message: '您今日解密次数已超限，如需重置解密次数，请联系管理员！',
                                    type: "warning",
                                });
                            }
                        } else {
                            this.$message({
                                message: res.msg,
                                type: "error",
                            });
                        }

                    })
                } else {
                    this.$message({
                        message: res.msg,
                        type: 'warning'
                    });
                }
                //    this.tableDataObj.tableData[index].mobile = res.data;
            })
        },
        handleClose() {
            this.exportFlag = false
        },
        getLoginPhone() {
            this.$api.post(
                this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
                {},
                (res) => {
                    if (res.code == 200) {
                        this.phoneList = res.data.data;
                    }
                }
            );
        },
        exportFn(obj) {
            this.$api.post(this.API.cpus + "statistics/export", obj, (res) => {
                if (res.code == 200) {
                    this.exportShow = false
                    this.$message({
                        type: "success",
                        duration: "2000",
                        message: "已加入到文件下载中心!",
                    });
                    this.$router.push('/FileExport');
                } else {
                    this.$message({
                        type: "error",
                        duration: "2000",
                        message: res.msg,
                    });
                }
            });
        },
        exportNums1() {
            // this.exportFlag = true
            // this.getLoginPhone()
            if (this.tableDataObj.tableData.length == 0) {
                this.$message({
                    message: "列表无数据，不可导出！",
                    type: "warning",
                });
            } else {
                this.$api.get(this.API.cpus + "statistics/exportDecode/check", {}, (res) => {
                    if (res.code == 200) {
                        this.emailInfo.email = res.data.email;
                        this.emailInfo.username = res.data.username;
                        if (res.data.decode) {
                            this.exportShow = true
                        } else {
                            let data = Object.assign({}, this.formData);
                            data.productType = 6
                            this.exportFn(data)
                        }
                    }
                })
            }
        },
        submitExport() {
            let data = Object.assign({}, this.formData);
            data.productType = 6
            data.decode = this.ruleForm.decode == 0 ? false : true;
            this.exportFn(data)
        },
    },
    created() {
        bus.$on("closeVideo", (msg) => {
            this.resetVideo = msg;
        });
    },
    mounted() {
        this.$api.get(this.API.cpus + 'consumerclientinfo/getClientInfo', null, res => {
            this.isDateState = res.data.isDateState
            if (res.data.isDateState == 1) {
                this.tableDataObj.tableLabel.push(
                    {
                        prop: "status", showName: '发送状态', formatData: function (val) {
                            if (val == '1') {
                                return val = '成功'
                            } else if (val == '2') {
                                return val = '失败'
                            } else {
                                return val = '待返回'
                            }
                        }, width: '100', fixed: false
                    },
                    { prop: "originalCode", showName: '备注', fixed: false }
                )
            }
        })
        this.sendReport();
    },
    // activated(){
    //     this.sendReport();
    // },
}
</script>

<style scoped>
.ShortMessageRecording-chart-box {
    margin: 10px 0;
    padding: 20px 20px 20px 20px;
}

.ShortMessageRecording-chart-title {
    display: flex;
}

.ShortMessageRecording-chart {
    height: 360px;
}

.ShortMessageRecording-title {
    padding-top: 40px;
    font-weight: bold;
}

.look-at-more {
    color: #16a589;
}

.ShortMessageRecording-select {
    position: relative;
    margin-top: 10px;
}

.query-frame {
    margin-top: 20px;
}

.query-frame .el-form-item {
    display: inline-block;
    margin-bottom: 12px;
}
</style>
<style>
/* .threeDay .el-radio-button__inner{
    border-right: 0 ;
} */
.ShortMessageRecording-chart-box .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 0;
}

.ShortMessageRecording-chart-box .el-range-editor.el-input__inner {
    border-radius: 0px 4px 4px 0;
}

.ShortMessageRecording-chart-box .el-table--small td,
.ShortMessageRecording-chart-box .el-table--small th {
    padding-top: 8px;
    padding-bottom: 8px;
}
</style>
