<template>
  <div class="bag">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 文件下载中心</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="OuterFrame fillet">
      <div class="sensitive-fun">
        <span class="sensitive-list-header">文件下载列表<span style="
              margin: 10px 0;
              padding-left: 5px;
              color: #16a085;
              font-size: 12px;
            ">（文件有效期3天，超过3天将不能下载，请重新进行导出）</span><span style="font-size: 12px; color: red">请刷新查看最新状态</span></span>
      </div>
      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)" ref="multipleTable"
          border :data="tableDataObj.tableData" style="width: 100%" @selection-change="handelSelection">
          <!-- <el-table-column type="selection" width="46"></el-table-column> -->
          <el-table-column prop="name" label="文件名称"></el-table-column>
          <el-table-column prop="createTime" label="创建时间">
            <template slot-scope="scope">
              <span>{{
                new Date(scope.row.createTime).getFullYear() +
                "-" +
                (new Date(scope.row.createTime).getMonth() + 1) +
                "-" +
                new Date(scope.row.createTime).getDate() +
                " " +
                new Date(scope.row.createTime).getHours() +
                ":" +
                new Date(scope.row.createTime).getMinutes() +
                ":" +
                new Date(scope.row.createTime).getSeconds()
                }}</span>
              <!-- <span>{{scope.row.createTime}}</span> -->
            </template>
          </el-table-column>
          <el-table-column prop="type" label="文件类型">
            <template slot-scope="scope">
              <!-- <span>{{new Date(scope.row.createTime).getFullYear()+'-'+(new Date(scope.row.createTime).getMonth()+1)+'-'+new Date(scope.row.createTime).getDate()+' '+new Date(scope.row.createTime).getHours()+ ':' +new Date(scope.row.createTime).getMinutes() + ':' + new Date(scope.row.createTime).getSeconds()}}</span> -->
              <!-- <span>{{scope.row.type=='1'?'短信明细':"黑名单"}}</span> -->
              <span v-if="scope.row.type == '1'">短信明细</span>
              <span v-else-if="scope.row.type == '2'">黑名单</span>
              <span v-else-if="scope.row.type == '3'">彩信明细</span>
              <span v-else-if="scope.row.type == '4'">视频明细</span>
              <span v-else-if="scope.row.type == '5'">国际明细</span>
              <span v-else-if="scope.row.type == '6'">语音明细</span>
              <span v-else-if="scope.row.type == '7'">短信回复</span>
              <span v-else-if="scope.row.type == '9'">视频回复</span>
              <span v-else-if="scope.row.type == '11'">短链查看明细</span>
              <span v-else-if="scope.row.type == '12'">模板管理</span>
              <span v-else-if="scope.row.type == '13'">视频短信web发送任务</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <div v-if="scope.row.decode === true">
                <span style="color: #67C23A" v-if="scope.row.status == 2">已发送</span>
                <span v-else-if="scope.row.status == -1" style="color: red">文件异常</span>
                <span v-else-if="scope.row.status == 4" style="color: #e2e2e2">文件已过期</span>
                <el-button v-else type="text"><i class="el-icon-loading"></i>&nbsp;未生成（请等待）</el-button>
              </div>
              <div v-else>
                <el-button v-if="scope.row.status == 2" type="text" @click="download(scope.row, scope.$index)"><i
                    class="el-icon-success"></i>&nbsp;下载</el-button>
                <span v-else-if="scope.row.status == -1" style="color: red">文件异常</span>
                <span v-else-if="scope.row.status == 4" style="color: #e2e2e2">文件已过期</span>
                <el-button v-else type="text"><i class="el-icon-loading"></i>&nbsp;未生成（请等待）</el-button>
              </div>
              <!-- <el-button type="text" style="color:orange"><i class="el-icon-error"></i>&nbsp;取消</el-button> -->
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
          style="background: #fff; padding: 10px 0; text-align: right">
          <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="sensitiveConditions.pagesize" :page-size="sensitiveConditions.pagesize"
            :page-sizes="[20, 50, 100, 200]" layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.tablecurrent.totalRow">
          </el-pagination>
        </el-col>
      </div>
    </div>
    <el-dialog title="手机验证" :visible.sync="exportFlag" width="40%" top="30vh">
      <div>
        <el-table :data="phoneList" class="Login-c-p-getPhone" border style="width: 100%">
          <el-table-column align="center" prop="consumerName" label="编号" width="120">
          </el-table-column>
          <el-table-column prop="mobile" align="center" label="手机号">
          </el-table-column>
          <el-table-column align="center" width="80" label="选择">
            <template slot-scope="scope">
              <el-radio v-if="!flag" @change.native="getCurrentRow(scope.$index)" :label="scope.$index" v-model="radio"
                class="textRadio">&nbsp;</el-radio>
              <el-radio v-else :disabled="true" @change.native="getCurrentRow(scope.$index)" :label="scope.$index"
                v-model="radio" class="textRadio">&nbsp;</el-radio>
            </template>
          </el-table-column>
        </el-table>
        <el-form :model="setphoneFrom.ruleForm1" :rules="setphoneFrom.rules1" ref="ruleForm1" class="demo-ruleForm"
          label-width="120px">
          <el-form-item label="手机验证码" prop="verCode" style="margin: 40px auto">
            <el-input v-model="setphoneFrom.ruleForm1.verCode" style="display: inline-block; width: 180px"></el-input>
            <el-button type="primary" plain style="width: 124px; padding: 9px 0px" @click="CountdownCode"
              v-if="nmb == 120">获取验证码</el-button>
            <el-button type="primary" plain style="width: 124px; padding: 9px 0px" disabled v-else>重新获取({{ nmb
              }})</el-button>
          </el-form-item>
          <el-form-item style="">
            <el-button @click="handleClose()" style="width: 100px; padding: 9px 0">取消</el-button>
            <el-button type="primary" @click="submitForm('ruleForm1')"
              style="width: 100px; padding: 9px 0">确定</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-dialog>
  </div>
</template>

<script>
var axios = require("axios");
import getNoce from "../../../../plugins/getNoce";
export default {
  name: "FileExport",
  data() {
    var code = (rule, value, callback) => {
      if (!this.phoneData) {
        return callback(new Error("请选中手机号"));
      } else if (value == "") {
        return callback(new Error("请输入验证码"));
      } else {
        callback();
      }
    };
    return {
      name: "FileExport",
      exportFlag: false,
      tableDataObj: {
        //列表数据
        loading2: false,
        tablecurrent: {
          //分页参数
          totalRow: 0,
        },
        tableData: [],
      },
      sensitiveConditions: {
        //赋值查询条件的值
        currentPage: 1,
        pageSize: 20,
      },
      selectId: "",
      phoneList: [],
      validateObj: {},
      flag: false,
      radio: "",
      phoneData: "",
      nmb: 120,
      fileIndex: null,
      timer: null,
      setphoneFrom: {
        ruleForm1: {
          verCode: "",
        },
        rules1: {
          verCode: [
            { required: true, validator: code, trigger: "blur" },
            { min: 6, max: 6, message: "请输入6位数字验证码" },
          ],
        },
      },
    };
  },
  created() {
    this.getLoginPhone();
  },
  methods: {
    GettableDtate() {
      //获取列表数据
      this.tableDataObj.loading2 = true;
      this.$api.get(
        this.API.cpus + "v3/export/page",
        this.sensitiveConditions,
        (res) => {
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.tablecurrent.totalRow = res.data.total;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    getCurrentRow(val) {
      // console.log(val,'ll');
      // this.count = val
      // if(this.count == val){
      //     this.flag = true
      // }else{
      //     this.flag = false
      // }
      this.phoneData = this.phoneList[val].mobile; //赋值手机号
    },
    CountdownCode() {
      if (this.phoneData) {
        --this.nmb;
        this.timer = setInterval((res) => {
          --this.nmb;
          if (this.nmb < 1) {
            this.nmb = 120;
            this.flag = false;
            clearInterval(this.timer);
          } else {
            this.flag = true;
          }
        }, 1000);
        this.$api.get(
          this.API.cpus +
          "code/sendVerificationCode?phone=" +
          this.phoneData +
          "&flag=4",
          {},
          (res) => {
            if (res.code == 200) {
              this.flag = true;
              this.$message({
                type: "success",
                duration: "2000",
                message: "验证码已发送至手机!",
              });
            } else {
              this.flag = true;
              this.$message({
                type: "warning",
                message: "验证码未失效，需失效后重新获取!",
              });
            }
          }
        );
      } else {
        this.$message({
          message: "请先选中手机号码",
          type: "warning",
        });
      }
    },
    getLoginPhone() {
      this.$api.post(
        this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
        {},
        (res) => {
          if (res.code == 200) {
            this.phoneList = res.data.data;
          }
        }
      );
    },
    handleClose() {
      this.exportFlag = false;
    },
    async download(val, index) {
      const nonce = await getNoce.useNonce();
      //   this.exportFlag = true;
      //   this.validateObj = val;
      //   this.fileIndex = index
      Date.prototype.format = function (format) {
        var args = {
          "M+": this.getMonth() + 1,
          "d+": this.getDate(),
          "h+": this.getHours(),
          "m+": this.getMinutes(),
          "s+": this.getSeconds(),
          "q+": Math.floor((this.getMonth() + 3) / 3), //quarter
          S: this.getMilliseconds(),
        };
        if (/(y+)/.test(format))
          format = format.replace(
            RegExp.$1,
            (this.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        for (var i in args) {
          var n = args[i];
          if (new RegExp("(" + i + ")").test(format))
            format = format.replace(
              RegExp.$1,
              RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length)
            );
        }
        return format;
      };
      var that = this;
      filedownload();
      function filedownload() {
        axios({
          method: "get",
          url:
            that.API.cpus +
            "v3/file/downloadFile?fileName=" +
            val.name +
            "&group=" +
            val.group +
            "&path=" +
            val.path,
          data: {},
          headers: {
            "Content-Type": "application/json",
            Authorization:
              "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
            'Once': nonce,
          },
          responseType: 'blob',
        })
          .then(function (response) {
            let blobUrl = window.URL.createObjectURL(response.data);
            download(blobUrl);
            that.exportFlag = false;
          })
        //    +
        //     "&phone=" + that.phoneData
        //   "&smsCode=" +
        //     that.setphoneFrom.ruleForm1.verCode +
        // fetch(
        //   that.API.cpus +
        //     "v3/file/download?fileName=" +
        //     val.name +
        //     "&group=" +
        //     val.group +
        //     "&path=" +
        //     val.path +
        //     "&smsCode=" +
        //     that.setphoneFrom.ruleForm1.verCode,
        //   {
        //     method: "get",
        //     headers: {
        //       "Content-Type": "application/json",
        //       Authorization:
        //         "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
        //     },
        //     // body: JSON.stringify({
        //     //     batchNo: val.row.batchNo,
        //     // })
        //   }
        // )
        //   .then((res) => {
        //     console.log(res,'res');
        //     res.blob()
        //   })
        //   .then((data) => {
        //     console.log(data,'data');
        //     let blobUrl = window.URL.createObjectURL(data);
        //     download(blobUrl);
        //     that.exportFlag = true;
        //   })
      }
      function download(blobUrl) {
        var a = document.createElement("a");
        a.style.display = "none";
        a.download =
          "(" +
          new Date().format("yyyy-MM-dd hh:mm:ss") +
          ") " +
          val.name +
          ".zip";
        a.href = blobUrl;
        a.click();
      }
    },
    async submitForm(form) {
      const nonce = await getNoce.useNonce();
      this.$refs[form].validate((valid) => {
        if (valid) {
          // 时间过滤
          Date.prototype.format = function (format) {
            var args = {
              "M+": this.getMonth() + 1,
              "d+": this.getDate(),
              "h+": this.getHours(),
              "m+": this.getMinutes(),
              "s+": this.getSeconds(),
              "q+": Math.floor((this.getMonth() + 3) / 3), //quarter
              S: this.getMilliseconds(),
            };
            if (/(y+)/.test(format))
              format = format.replace(
                RegExp.$1,
                (this.getFullYear() + "").substr(4 - RegExp.$1.length)
              );
            for (var i in args) {
              var n = args[i];
              if (new RegExp("(" + i + ")").test(format))
                format = format.replace(
                  RegExp.$1,
                  RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length)
                );
            }
            return format;
          };
          var that = this;
          filedownload();
          function filedownload() {
            axios({
              method: "get",
              url:
                that.API.cpus +
                "v3/file/downloadFile?fileName=" +
                that.validateObj.name +
                "&group=" +
                that.validateObj.group +
                "&path=" +
                that.validateObj.path +
                "&smsCode=" +
                that.setphoneFrom.ruleForm1.verCode +
                "&phone=" + that.phoneData,
              data: {},
              headers: {
                "Content-Type": "application/json",
                Authorization:
                  "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
                'Once': nonce,
              },
              responseType: 'blob',
            })
              .then(function (response) {
                let blobUrl = window.URL.createObjectURL(response.data);
                download(blobUrl);
                that.exportFlag = false;
              })
            // fetch(
            //   that.API.cpus +
            //     "v3/file/download?fileName=" +
            //     that.validateObj.name +
            //     "&group=" +
            //     that.validateObj.group +
            //     "&path=" +
            //     that.validateObj.path +
            //     "&smsCode=" +
            //     that.setphoneFrom.ruleForm1.verCode,
            //   {
            //     method: "get",
            //     headers: {
            //       "Content-Type": "application/json",
            //       Authorization:
            //         "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
            //     },
            //     // body: JSON.stringify({
            //     //     batchNo: that.validateObj.row.batchNo,
            //     // })
            //   }
            // )
            //   .then((res) => {
            //     console.log(res,'res');
            //     res.blob()
            //   })
            //   .then((data) => {
            //     console.log(data,'data');
            //     let blobUrl = window.URL.createObjectURL(data);
            //     download(blobUrl);
            //     that.exportFlag = true;
            //   })
          }
          function download(blobUrl) {
            var a = document.createElement("a");
            a.style.display = "none";
            a.download =
              "(" +
              new Date().format("yyyy-MM-dd hh:mm:ss") +
              ") " +
              that.validateObj.name +
              ".zip";
            a.href = blobUrl;
            a.click();
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = [];
      let timingStatus = [];
      let sendTimess = [];
      for (let i = 0; i < val.length; i++) {
        // selectId.push(val[i].timingSmsId);
        selectId.push(val[i].id);
      }
      this.selectId = selectId.join(","); //批量操作选中消息id
    },
    handleSizeChange(size) {
      this.sensitiveConditions.pageSize = size;
    },
    handleCurrentChange: function (currentPage) {
      console.log(currentPage);
      this.sensitiveConditions.currentPage = currentPage;
    },
  },
  // mounted(){
  //     this.GettableDtate()
  // },
  // activated(){
  //     this.GettableDtate()
  // },
  watch: {
    sensitiveConditions: {
      handler(val) {
        this.GettableDtate();
      },
      deep: true,
      immediate: true,
    },
    exportFlag(val) {
      if (!val) {
        this.$refs.ruleForm1.resetFields();
      }
    },
    fileIndex(val) {
      if (val) {
        this.nmb = 120;
        this.flag = false;
        clearInterval(this.timer);
      }
    }
  },
};
</script>

<style scoped>
.Timing-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px 14px;
  border-radius: 5px;
  font-size: 12px;
  margin-bottom: 20px;
}

.Timing-matter>p {
  padding: 5px 0px;
}

.OuterFrame {
  padding: 20px;
}

.sensitive-table {
  padding-bottom: 40px;
}

.demo-ruleForm {
  width: 500px;
  margin: 0 auto;
}
</style>
