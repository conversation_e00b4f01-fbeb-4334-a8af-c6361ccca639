<template>
  <div style="background: #fff">
    <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 充值记录</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
     <div class="rechargerecord fillet">
   
        
        <el-form ref="chaxunObj" :model="chaxunObj" label-width="80px">
           <el-form-item label="充值日期" style="display:inline-block" prop="userName">
              <date-plugin class="search-date"  :datePluginValueList="datePluginValueList"  @handledatepluginVal="handledatepluginVal" ></date-plugin>
          </el-form-item>
          <el-form-item label="充值类型" style="display:inline-block">
            <el-select v-model="chaxunObj.productId" placeholder="请选择类型">
              <el-option label="全部" value=""></el-option>
              <el-option
                v-for="item in toptions"
                :key="item.id"
                :label="item.name"
                :value="item.id">
              </el-option>
            </el-select>
          </el-form-item>
          <el-button type="primary" plain @click="query()">查 询</el-button>
          <el-button type="primary" plain style="" @click="reset()">重 置</el-button>
        </el-form>
      <div style="padding-bottom:40px;">
        <!-- <table-tem :tableDataObj="tableDataObj" ></table-tem> -->
          <el-table
            v-loading="tableDataObj.loading2 "
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :stripe="true"
            :data="tableDataObj.tableData"
            style="width: 100%"
           >
            <el-table-column label="日期" width="160">
                <template slot-scope="scope">
                    <span >{{ scope.row.rechargeTime }}</span>
                </template>
            </el-table-column>
            <el-table-column label="充值类型">
                <template slot-scope="scope">
                <span >{{scope.row.productName}}</span>
                </template>
            </el-table-column>
            <el-table-column label="充值条数">
                <template slot-scope="scope">
                    <span >{{ scope.row.rechargeNum+" ("+scope.row.unit+")"}}</span>
                </template>
            </el-table-column>
            <el-table-column label="订单号">
                <template slot-scope="scope">
                    <span >{{ scope.row.orderNumber }}</span>
                </template>
            </el-table-column>
            <el-table-column label="备注" >
                <template slot-scope="scope">
                    <span >{{ scope.row.rechargeNote }}</span>
                </template>
            </el-table-column>
        </el-table>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page pageStyle" slot="pagination" >
            <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="chaxunObj.currentPage"  :page-size='chaxunObj.pageSize' :page-sizes="[10, 20, 50, 100,300]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total">
            </el-pagination>
        </el-col>
      </div>
    </div>
  </div>
 
</template>

<script>
import TableTem from '../../../publicComponents/TableTem'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import { mapState, mapMutations,mapActions } from "vuex";
export default {
  name: "RechargeRecord",
  components: {TableTem,DatePlugin},
  computed:{
      ...mapState({  //比如'movies/hotMovies
          roleId:state=>state.userId,
        })
  },
  data() {
    return {
      datePluginValueList: { //日期选择器
          type:"daterange",
          start:"",
          end:'',
          range:'-',
          
          clearable:true,
          pickerOptions:{
                disabledDate: (time) => {
                    return time.getTime() > Date.now() ;
                }
              },
          datePluginValue: ''
      },
      toptions:[],
      formInline:{
        currentPage:1,
        pageSize:10,
        beginTime:'',
        endTime:'',
        productId:''
      },
      chaxunObj:{
        currentPage:1,
        pageSize:10,
        beginTime:'',
        endTime:'',
        productId:''
      },
      tableDataObj:{
        total:0,
        tableData: [],
      },
    };
  },
  methods: {
    reset(){//重置   
      this.datePluginValueList.datePluginValue ='';
      this.formInline.beginTime='';
      this.formInline.endTime='';
      this.formInline.productId='';
      this.formInline.pageSize=10;
      this.formInline.currentPage=1;
      Object.assign(this.chaxunObj,this.formInline);
      this.getDate();
    },
    // 查询
    query(){
      Object.assign(this.formInline,this.chaxunObj);
      this.getDate();
    },
    getDate(){
      this.tableDataObj.loading2 = true;
        this.$api.get(this.API.recharge+'client/recharge/page?'+'currentPage='+this.chaxunObj.currentPage+'&pageSize='+this.chaxunObj.pageSize+'&beginTime='+this.chaxunObj.beginTime+'&endTime='+this.chaxunObj.endTime+'&productId='+this.chaxunObj.productId,{},res=>{
          this.tableDataObj.loading2 = false;
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.total = res.data.total;
        })
    },
    handleSizeChange(size) { //分页一页的size
      this.chaxunObj.pageSize = size
      this.getDate();
    },
    handleCurrentChange: function(currentPage){//分页第几页
      this.chaxunObj.currentPage = currentPage;
      this.getDate();
    },
    handledatepluginVal: function(val1,val2){
      if(val1){
          this.chaxunObj.beginTime = val1;
          this.chaxunObj.endTime = val2;
      }else{
        this.chaxunObj.beginTime = '';
          this.chaxunObj.endTime = '';
      }
    },
    getList(){
        this.$api.get(
        this.API.recharge + "products",
        {},
        (res) => {
          console.log(res);
          this.toptions = res.data
        }
      );
    },
  },
  created(){
    this.getDate();
    this.getList()
  },
  // activated(){
  //   this.getDate();
  //   this.getList()
  // },
  // watch:{
  //   chaxunObj:{
  //     handler(){
  //         this.getDate();
  //     },
  //     deep:true,
  //     immediate:true
  //   }
  // }
}
</script>

<style scoped>
.rechargerecord{
  padding:20px;
}
.statistical-title{
  font-weight: bold;
  padding-bottom: 10px;
}
.detailsList{
  height:42px;
  line-height: 26px;
  padding-left:30px;
  font-size:12px;
}
.detailsList-title{
  display: inline-block;
  width:80px;
}
.detailsList-content{
  display: inline-block;
  width:340px;
  color:#848484;
  padding-left:10px;
  border-bottom: 1px solid #eee;
}
 .look-at-more{
    color:#16a589;
    padding-top: 12px;
}
</style>
<style>
.rechargerecord .el-dialog__footer{
  text-align:center;
}
.el-dialog__title{
  font-size:16px;
}
@media screen and (max-width: 1200px){
    .el-picker-panel{
        width: 370px;
        height: 350px;
        overflow: auto;
    }
    .el-date-range-picker__content{
        width: 100%;
    }
}
</style>


