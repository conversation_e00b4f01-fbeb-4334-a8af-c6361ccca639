<template>
  <div class="modern-signature-page">
    <!-- 现代化页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">
            <i class="el-icon-message"></i>
            {{ SmSid ? "发送短信编辑" : "发送短信" }}
          </h1>
        </div>
        <div class="header-right">
          <el-tag :type="SmSid ? 'warning' : 'success'" size="medium">
            {{ SmSid ? '编辑模式' : '新建发送' }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container">
        <el-row class="send-form-layout">
          <el-col :span="15" class="form-section">
            <!-- 短信配置表单卡片 -->
            <el-card shadow="hover" class="form-card sms-config-card">
              <div slot="header" class="card-header">
                <span class="card-title">
                  <i class="el-icon-setting"></i>
                  短信配置
                </span>
              </div>

              <el-form
                :model="configurationItem.formData"
                :rules="configurationItem.formRule"
                ref="configurations"
                label-width="95px"
                class="modern-form"
              >
                <el-form-item label="标签字段" prop="label" class="form-item-modern">
                  <input-tag v-on:childLabelEvent="handChildLabel" class="modern-input" />
                </el-form-item>

                <el-form-item label="任务名称" prop="taskName" class="form-item-modern">
                  <el-input
                    v-model="configurationItem.formData.taskName"
                    placeholder="请输入任务名称"
                    size="large"
                    class="modern-input"
                  >
                    <i slot="prefix" class="el-icon-edit-outline"></i>
                  </el-input>
                </el-form-item>

                <el-form-item label="短信类型" prop="tempVal" class="form-item-modern">
                  <div class="sms-type-selector">
                    <el-select
                      v-model="configurationItem.formData.tempVal"
                      placeholder="请选择短信类型"
                      size="large"
                      class="modern-select"
                      @change="handelSelect"
                    >
                      <el-option label="自定义发送" value="-1">
                        <span style="float: left">自定义发送</span>
                        <span style="float: right; color: #8cc5ff; font-size: 13px">灵活自定义</span>
                      </el-option>
                      <el-option label="模板发送" value="2">
                        <span style="float: left">模板发送</span>
                        <span style="float: right; color: #8cc5ff; font-size: 13px">快速发送</span>
                      </el-option>
                    </el-select>
                    <div v-if="configurationItem.formData.tempVal == '-1'" class="variable-tip">
                      <el-alert
                        title="变量说明"
                        type="info"
                        :closable="false"
                        show-icon
                      >
                        <template slot="default">
                          支持自定义变量，变量名必须以英文字母开头，英文字母和数字和英文下划线的组合。例如：{name}
                        </template>
                      </el-alert>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item
                  label="选择模板"
                  v-show="configurationItem.formData.tempVal != '-1'"
                  class="form-item-modern"
                >
                  <div class="template-selector">
                    <el-button
                      type="primary"
                      icon="el-icon-document"
                      @click="templateDialog = true"
                      size="large"
                      class="select-template-btn"
                    >
                      选择模板
                    </el-button>
                    <span class="template-tip">从模板库中选择已审核通过的短信模板</span>
                  </div>
                </el-form-item>

                <el-form-item
                  label="选择签名"
                  v-show="!(TemplateContent.indexOf('】') > 0 && configurationItem.formData.tempVal != '-1')"
                  class="form-item-modern"
                >
                  <div class="signature-selector">
                    <el-select
                      v-model="configurationItem.formData.signatureId"
                      clearable
                      filterable
                      @change="changeLocationValue"
                      placeholder="请选择短信签名"
                      size="large"
                      class="modern-select signature-select"
                    >
                      <el-option
                        v-for="item in sigOptions"
                        :key="item.signatureId"
                        :label="item.signature"
                        :value="item.signatureId"
                      >
                        <span style="float: left">{{ item.signature }}</span>
                        <!-- <span style="float: right; color: #8cc5ff; font-size: 13px">
                          {{ item.auditStatus === 1 ? '已审核' : '待审核' }}
                        </span> -->
                      </el-option>
                    </el-select>
                    <router-link :to="'CreateSign?i=1'">
                      <el-button
                        type="success"
                        icon="el-icon-plus"
                        size="large"
                        class="add-signature-btn"
                        @click="logUrl('SignatureManagement')"
                      >
                        添加签名
                      </el-button>
                    </router-link>
                  </div>
                </el-form-item>
                <el-form-item
                  label="短信内容"
                  prop="content"
                  v-if="configurationItem.formData.tempVal == '-1'"
                  class="form-item-modern"
                >
                  <div class="sms-content-editor">
                    <el-input
                      type="textarea"
                      placeholder="【短信签名】请在此处输入短信内容"
                      v-model="configurationItem.formData.content"
                      maxlength="1500"
                      :rows="6"
                      show-word-limit
                      class="modern-textarea content-textarea"
                      @change="handelChange"
                    />

                    <div class="content-options">
                      <el-checkbox
                        v-model="checked"
                        @change="handleQuite"
                        class="reject-option"
                      >
                        拒收请回复R
                      </el-checkbox>

                      <div class="content-actions">
                        <el-button
                          type="info"
                          @click="saveDraft"
                          size="small"
                          icon="el-icon-document"
                          plain
                        >
                          保存至草稿箱
                        </el-button>
                        <el-button
                          type="info"
                          @click="DraftBox"
                          size="small"
                          icon="el-icon-folder-opened"
                          plain
                        >
                          草稿箱
                        </el-button>
                        <el-button
                          type="warning"
                          v-show="configurationItem.formData.tempVal == '-1' && !SmSid"
                          icon="el-icon-link"
                          plain
                          @click="shortReset()"
                          size="small"
                        >
                          短链转换
                        </el-button>
                      </div>
                    </div>

                    <div class="content-tips">
                      <el-alert
                        type="info"
                        :closable="false"
                        show-icon
                      >
                        <template slot="default">
                          <div class="tip-item">
                            <i class="el-icon-info"></i>
                            已输入 <span :class="{ 'text-danger': number_zs > 1500 }">{{ number_zs }}</span> 字，最多 1500 字（含签名），70字内（含70字）计一条，超过70字，按67字/条计费
                          </div>
                          <div class="tip-item">
                            <i class="el-icon-warning"></i>
                            短信内容中，将在链接前后生成空格符号，以防止出现手机短信客户端不识别链接的情况
                          </div>
                        </template>
                      </el-alert>
                    </div>
                  </div>
                </el-form-item>
                <el-form-item
                  label="短信内容"
                  prop="content1"
                  v-if="configurationItem.formData.tempVal != '-1'"
                  class="form-item-modern"
                >
                  <div class="template-content-editor">
                    <div class="template-content-wrapper" @mouseover="button_QC = true" @mouseout="button_QC = false">
                      <el-button
                        type="danger"
                        size="small"
                        plain
                        class="clear-content-btn"
                        @click="button_QCNR"
                        v-if="button_QC"
                        icon="el-icon-delete"
                      >
                        清除内容
                      </el-button>
                      <el-input
                        type="textarea"
                        placeholder="请选择您需要发送的短信模板，选择完模板内容将会显示"
                        readonly
                        v-model="configurationItem.formData.content1"
                        :rows="6"
                        class="modern-textarea template-textarea"
                      />
                    </div>

                    <div class="template-content-info">
                      <div class="content-stats">
                        <el-alert
                          type="info"
                          :closable="false"
                          show-icon
                        >
                          <template slot="default">
                            <div class="stats-item">
                              <i class="el-icon-info"></i>
                              已输入 <span :class="{ 'text-danger': number_zs > 800 }">{{ number_zs }}</span> 字，最多 800 字（含签名），70字内（含70字）计一条，超过70字，按67字/条计费
                            </div>
                            <div class="stats-item" v-if="configurationItem.formData.content">
                              <el-button
                                type="text"
                                @click="Variabledownload"
                                class="download-variables-btn"
                              >
                                <i class="el-icon-download"></i>
                                模板变量下载
                              </el-button>
                            </div>
                          </template>
                        </el-alert>
                      </div>
                    </div>
                  </div>
                </el-form-item>
            <!-- <el-form-item label="扩展号" prop="extType">
              <el-radio-group @change="extChange" v-model="configurationItem.formData.extType">
                <el-radio label="1">固定码号</el-radio>
              </el-radio-group>
              <span v-if="configurationItem.formData.extType == '1'" style="margin-left:10px;cursor: pointer;"
                @click="cencal">
                <i class="el-icon-error"></i>取消
              </span>
            </el-form-item>
            <el-form-item v-if="configurationItem.formData.extType == '1'" label="码号" prop="ext">
              <el-input maxlength="4" placeholder="最大支持4位" v-model="configurationItem.formData.ext" class="select_s"
                @input="extflag"></el-input>
              <div style="color: red; font-size: 12px" v-if="exthFlag">
                {{ text1 }}
              </div>
            </el-form-item>
            <el-form-item v-if="configurationItem.formData.extType == '2'" label="位数" prop="extLength">
              <el-radio-group v-model="configurationItem.formData.extLength">
                <el-radio label="1">1位</el-radio>
                <el-radio label="2">2位</el-radio>
                <el-radio label="3">3位</el-radio>
                <el-radio label="4">4位</el-radio>
              </el-radio-group>
            </el-form-item> -->
                <el-form-item label="发送时间" class="form-item-modern">
                  <div class="send-timing-section">
                    <el-radio-group v-model="configurationItem.formData.isTiming" class="timing-radio-group">
                      <div class="timing-option">
                        <el-radio label="0" class="timing-radio">
                          <div class="timing-content">
                            <div class="timing-header">
                              <i class="el-icon-position timing-icon"></i>
                              <span class="timing-title">立即发送</span>
                            </div>
                            <div class="timing-description">短信将立即发送到目标手机号码</div>
                          </div>
                        </el-radio>
                      </div>
                      <div class="timing-option">
                        <el-radio label="1" class="timing-radio">
                          <div class="timing-content">
                            <div class="timing-header">
                              <i class="el-icon-time timing-icon"></i>
                              <span class="timing-title">定时发送</span>
                            </div>
                            <div class="timing-description">设置指定时间发送短信</div>
                          </div>
                        </el-radio>
                      </div>
                    </el-radio-group>

                    <div v-if="configurationItem.formData.isTiming == 1" class="timing-picker-section">
                      <div class="timing-picker-wrapper">
                        <label class="timing-picker-label">定时时间：</label>
                        <date-plugin
                          class="modern-date-picker"
                          :datePluginValueList="datePluginValueList"
                          @handledatepluginVal="handledatepluginVal"
                        />
                      </div>
                    </div>
                  </div>
                </el-form-item>

                <el-form-item label="发送方式" class="form-item-modern">
                  <div class="send-method-section">
                    <el-radio-group v-model="configurationItem.formData.type" @change="handelSend" class="method-radio-group">
                      <div class="method-option" v-if="tempShow">
                        <el-radio label="0" class="method-radio">
                          <div class="method-content">
                            <div class="method-header">
                              <i class="el-icon-edit-outline method-icon"></i>
                              <span class="method-title">号码发送</span>
                            </div>
                            <div class="method-description">手动输入或从通讯录选择手机号码</div>
                          </div>
                        </el-radio>
                      </div>
                      <div class="method-option">
                        <el-radio label="1" class="method-radio">
                          <div class="method-content">
                            <div class="method-header">
                              <i class="el-icon-document method-icon"></i>
                              <span class="method-title">文件发送</span>
                            </div>
                            <div class="method-description">上传Excel、TXT等文件批量发送</div>
                          </div>
                        </el-radio>
                      </div>
                    </el-radio-group>
                  </div>
                  <div v-if="configurationItem.formData.type == 0" class="mobile-input-tips">
                  <el-alert
                    title="号码提取说明"
                    type="info"
                    :closable="false"
                    show-icon
                  >
                    <template slot="default">
                      该文本框仅支持提取Excel中的前10000个号码
                    </template>
                  </el-alert>
                </div>
                </el-form-item>
                <!-- 文件上传区域 -->
                <div v-if="configurationItem.formData.type == 1" class="file-upload-section">
                  <el-form-item label="发送对象" class="form-item-modern">
                    <div class="file-upload-wrapper">
                      <el-upload
                        class="modern-upload-drag"
                        drag
                        v-if="!SmSid"
                        :action="this.API.cpus + 'v3/file/upload'"
                        :headers='header'
                        :limit='1'
                        :on-remove="fileup"
                        :on-success="fileupres"
                        :before-upload="beforeAvatarUpload"
                        :file-list="fileList"
                        multiple
                      >
                        <i class="el-icon-upload upload-icon"></i>
                        <div class="upload-text">将文件拖到此处，或<em>点击上传</em></div>
                        <div class="upload-hint">支持 .xlsx .xls .txt 等格式，文件大小不超过300M</div>
                      </el-upload>

                      <div v-if="RowNum" class="upload-result">
                        <el-alert
                          :title="`本次共上传 ${RowNum} 行数据`"
                          type="success"
                          :closable="false"
                          show-icon
                        />
                      </div>
                    </div>
                    <div class="upload-tips-section">
                    <el-alert
                      title="文件上传说明"
                      type="warning"
                      :closable="false"
                      show-icon
                    >
                      <template slot="default">
                        <div class="tip-item">• 格式要求：支持 .xlsx .xls .txt 等格式，文件大小不超过300M</div>
                        <div class="tip-item">• TXT格式：只支持一行一个号码</div>
                        <div class="tip-item text-danger">• 如上传文件将会自动清除下面手动填写的手机号码</div>
                      </template>
                    </el-alert>
                  </div>
                  </el-form-item>

                  
                </div>

                <!-- 手机号码输入提示 -->
                
                <!-- 手机号码输入区域 -->
                <el-form-item
                  label="手机号码"
                  prop="mobile"
                  v-if="configurationItem.formData.type == 0"
                  class="form-item-modern"
                >
                  <div class="mobile-input-section">
                    <div class="mobile-input-wrapper">
                      <div class="mobile-textarea-container">
                        <el-input
                          type="textarea"
                          @blur="FilterNumber"
                          @input="textChange"
                          placeholder="手动最多输入10000个手机号码，号码之间用英文逗号隔开"
                          v-model="configurationItem.formData.mobile"
                          :rows="6"
                          class="modern-textarea mobile-textarea"
                        />
                        <div class="mobile-count">
                          <span :class="{ 'count-danger': limit > 10000 }">{{ limit }}</span>/10000
                        </div>
                      </div>

                      <div class="mobile-actions">
                        <el-upload
                          ref="uploadNum"
                          :action="this.API.cpus + 'v3/file/discernMobile'"
                          :headers="header"
                          :show-file-list="false"
                          :on-success="fileupresM"
                          :on-remove="fileupM"
                          :limit="1"
                          :file-list="fileList"
                          style="display: none;"
                        />
                        <el-button
                          type="primary"
                          @click="triggerUpload"
                          icon="el-icon-upload2"
                          size="large"
                          class="extract-btn"
                        >
                          提取Excel号码
                        </el-button>
                      </div>
                    </div>
                  </div>
                </el-form-item>
            <!-- <el-form-item label="" prop="" v-if="textareaShow==true&&downloadtem!='1'&&variableTp" style="margin-top:-10px">
                            <span style="margin-left: 10px;font-size:12px;">成功提交<i style="font-style: normal;color: #16A589;font-size:12px;"> {{SuccessfullySubmitted}} </i>个</span><span style="margin-left: 10px;font-size:12px;">已过滤<i style="font-style: normal;color: red;font-size:12px;"> {{filter}} </i>个重复</span><span style="margin-left: 10px;font-size:12px;">无效号码<i style="font-style: normal;color: red;"> {{invalid}} </i>个</span>
                        </el-form-item> -->
                <!-- 操作按钮区域 -->
                <div class="action-buttons">
                  <div class="button-group">
                    <el-button
                      type="primary"
                      v-if="sendLoading == true"
                      @click="submissionItem('configurations')"
                      size="large"
                      class="submit-btn"
                    >
                      <i class="el-icon-s-promotion"></i>
                      发送短信
                    </el-button>
                    <el-button
                      type="primary"
                      v-if="sendLoading == false"
                      :loading="true"
                      size="large"
                      class="loading-btn"
                    >
                      提交数据中
                    </el-button>
                  </div>
                </div>
              </el-form>
            </el-card>
          </el-col>

          <!-- 右侧预览区域 -->
          <el-col v-if="isShowBar != 0" :span="9" class="preview-section">
            <!-- 短信预览卡片 -->
            <el-card shadow="hover" class="form-card preview-card">
              <div slot="header" class="card-header">
                <span class="card-title">
                  <i class="el-icon-mobile-phone"></i>
                  短信预览
                </span>
                <div class="preview-actions">
                  <el-tooltip content="刷新预览" placement="top">
                    <el-button type="text" @click="refreshPreview" class="refresh-btn">
                      <i class="el-icon-refresh"></i>
                    </el-button>
                  </el-tooltip>
                </div>
              </div>

              <div class="phone-preview">
                <!-- 手机外观容器 -->
                <div class="phone-mockup">
                  <div class="phone-frame">
                    <div class="phone-screen">
                      <!-- 手机状态栏 -->
                      <div class="status-bar">
                        <div class="status-left">
                          <span class="time">{{ currentTime }}</span>
                        </div>
                        <div class="status-right">
                          <i class="signal-icon"></i>
                          <i class="wifi-icon"></i>
                          <span class="battery">100%</span>
                        </div>
                      </div>

                      <!-- 短信应用界面 -->
                      <div class="sms-app">
                        <div class="sms-header">
                          <div class="contact-info">
                            <div class="contact-avatar">
                              <i class="el-icon-message"></i>
                            </div>
                            <div class="contact-details">
                              <div class="contact-name">短信预览</div>
                              <div class="contact-number">{{ previewPhoneNumber }}</div>
                            </div>
                          </div>
                        </div>

                        <!-- 短信内容区域 -->
                        <div class="sms-conversation">
                          <div class="message-container">
                            <div class="message-bubble received">
                              <div class="message-content">
                                <div v-if="!smsPreviewContent" class="placeholder-text">
                                  请输入短信内容或选择模板...
                                </div>
                                <div v-else class="sms-text">
                                  {{ smsPreviewContent }}
                                </div>
                              </div>
                              <div class="message-time">{{ messageTime }}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <!-- 手机按钮 -->
                    <div class="phone-button"></div>
                  </div>
                </div>

                <!-- 预览统计信息 -->
                <div class="preview-stats">
                  <div class="stats-grid">
                    <div class="stat-item">
                      <div class="stat-label">字数统计</div>
                      <div class="stat-value">
                        <span class="stat-number" :class="{ 'over-limit': SMScount.smswordNum > 1500 }">
                          {{ SMScount.smswordNum }}
                        </span>
                        <span class="stat-unit">字</span>
                      </div>
                    </div>

                    <div class="stat-item">
                      <div class="stat-label">预计条数</div>
                      <div class="stat-value">
                        <span class="stat-number">{{ SMScount.smssTrip }}</span>
                        <span class="stat-unit">条</span>
                      </div>
                    </div>

                    <!-- <div class="stat-item">
                      <div class="stat-label">预计费用</div>
                      <div class="stat-value">
                        <span class="stat-number">{{ estimatedCost }}</span>
                        <span class="stat-unit">元</span>
                      </div>
                    </div> -->
                  </div>

                  <div class="stats-note">
                    <el-alert
                      type="info"
                      :closable="false"
                      show-icon
                    >
                      <template slot="default">
                        <div class="note-content">
                          <div class="note-item">
                            • 70字内（含70字）计1条，超过70字按67字/条计费
                          </div>
                          <div class="note-item" v-if="hasVariables">
                            • 实际发送时，模板变量会影响计费条数，请注意关注
                          </div>
                        </div>
                      </template>
                    </el-alert>
                  </div>
                </div>
              </div>
            </el-card>

            <!-- 发送规则卡片 -->
            <el-card shadow="hover" class="form-card rules-card">
              <div slot="header" class="card-header">
                <span class="card-title">
                  <i class="el-icon-warning-outline"></i>
                  发送规则
                </span>
              </div>
              <div class="rules-content">
                <el-alert
                  title="重要规则"
                  type="warning"
                  :closable="false"
                  show-icon
                >
                  <template slot="default">
                    <div class="rules-list">
                      <div class="rule-item">• 从模板库中导入的短信可实时发送</div>
                      <div class="rule-item">• 您在本页面上输入的短信内容，需进入人工审核，待审核完毕后将自动发送</div>
                      <div class="rule-item">• 发送会员营销短信，不能包含变量，且需进入人工审核，待审核完毕后将自动发送</div>
                      <div class="rule-item">• 发送行业通知短信，选择模板发送不需要人工审核直接发送；输入内容最少需添加1个参数，最多可添加16个参数</div>
                      <div class="rule-item">• 计费规则：<span class="highlight">70字内（含70字）</span>计一条，超过70字，按<span class="highlight">67字/条</span>计费</div>
                    </div>
                  </template>
                </el-alert>
              </div>
            </el-card>

            <!-- 内容规范卡片 -->
            <el-card shadow="hover" class="form-card content-rules-card">
              <div slot="header" class="card-header">
                <span class="card-title">
                  <i class="el-icon-document-checked"></i>
                  内容规范
                </span>
              </div>
              <div class="content-rules">
                <el-alert
                  title="内容规范"
                  type="info"
                  :closable="false"
                  show-icon
                >
                  <template slot="default">
                    <div class="rules-list">
                      <div class="rule-item">• 签名内容为：公司或品牌名称，字数要求<span class="highlight">2-12</span>个字符</div>
                      <div class="rule-item">• <span class="highlight">邀请注册、邀请成为会员、邀请加微信、加QQ群</span>的商业性信息不能发送</div>
                      <div class="rule-item">• <span class="highlight">黄、赌、毒</span>犯法等国家法律法规严格禁止的内容不能发送</div>
                      <div class="rule-item">• 包含"股票加群、购物加群、集资贷款、改分、代办大额信用卡、信用卡提额"等疑似诈骗或类似的信息不能发送</div>
                      <div class="rule-item">• 超链接地址请写在短信内容中，便于核实，部分安卓系统存在超链接识别问题，需在超链接前后添加空格</div>
                      <div class="rule-item">• 变量模板中的变量有长度和个数限制，具体请咨询</div>
                    </div>
                  </template>
                </el-alert>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>
    </div>
    <!-- 模板列表 -->
    <el-dialog title="短信模板选择" :visible.sync="templateDialog" :modal-append-to-body="false" :closeOnClickModal="false"
      width="70%">
      <div style="margin-bottom: 10px">
        <el-input style="width: 200px" v-model="tabelAlllist.param" placeholder="请输入内容"></el-input>
        <el-button type="primary" plain style="" @click="getTableData()">查询</el-button>
        <router-link :to="'CreateTemplate?i=1'">
          <el-button type="primary" plain style="" @click="logUrl('TemplateManagement')">添加模版</el-button>
        </router-link>
      </div>
      <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中" element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)" ref="multipleTable" border :data="tableDataObj.tableData"
        style="width: 100%">
        <el-table-column prop="temId" label="ID" width="60"></el-table-column>
        <el-table-column label="模板类型" width="90">
          <template slot-scope="scope">
            <span v-if="scope.row.temType == '1'">验证码</span>
            <span v-else-if="scope.row.temType == '2'">通知</span>
            <span v-else-if="scope.row.temType == '3'">营销推广</span>
          </template>
        </el-table-column>
        <el-table-column label="模板名称" width="90">
          <template slot-scope="scope">
            <span>{{ scope.row.temName }}</span>
          </template>
        </el-table-column>
        <!-- <el-table-column label="申请时间" width="170">
                        <template slot-scope="scope">{{ scope.row.createTime}}</template>
                </el-table-column>
                <el-table-column label="模板名称" width="170">
                        <template slot-scope="scope">{{ scope.row.temName}}</template>
                </el-table-column> -->
        <el-table-column label="内容">
          <template slot-scope="scope">
            <span>{{ scope.row.temContent }}</span>
            <!-- <span v-if="scope.row.checkReason == null " ></span> -->
            <!-- <span v-else-if="scope.row.checkReason == ''" ></span> -->
            <!-- <span v-else style="color:#F56C6C">( 驳回原因：{{ scope.row.checkReason }} )</span>                          -->
          </template>
        </el-table-column>
        <el-table-column label="字数" width="90">
          <template slot-scope="scope">
            <span>{{ scope.row.temContent.length }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="80">
          <template slot-scope="scope">
            <el-button type="text" @click="delTem(scope.row.temContent, scope.row.temId, scope.row)"><i
                class="el-icon-circle-plus"></i>&nbsp;选择</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <div style="height: 20px; padding: 20px">
        <el-pagination style="float: right" class="page_bottom" @size-change="handleSizeChange"
          @current-change="handleCurrentChange" :current-page="tabelAlllist.currentPage"
          :page-size="tabelAlllist.pageSize" :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
        </el-pagination>
      </div>
    </el-dialog>
    <!-- 短链转换 -->
    <el-dialog title="短链转换" :visible.sync="shortVisible" :closeOnClickModal="false" width="70%">
      <div class="short-box">
        <p class="short-title" style="padding-top: 10px">长网址链接</p>
        <el-input placeholder="请输入长链接" v-model="originalUrl" class="width-l">
          <el-button slot="append" type="primary" icon="el-icon-refresh" @click="transformation()">转换</el-button>
        </el-input>
        <div class="font-sizes font-sizes1">
          <span style="color: red">* </span>我们可以帮您把长链接压缩，让您可以输入更多的内容。
        </div>
        <div class="font-sizes">
          <span style="color: red">* </span>插入短信内容中时，将在链接前后生成空格符号，以防止出现手机短信客户端不识别链接的情况。
        </div>
      </div>
      <div class="short-box">
        <p class="short-title" style="padding-top: 20px">短网址链接</p>
        <el-input v-model="shortConUrl" class="width-l" :disabled="true">
          <el-button slot="append" type="primary" @click="handlePreview()" icon="el-icon-share">预览</el-button>
        </el-input>
      </div>
      <div style="text-align: right; margin-top: 16px">
        <el-button @click="HandelCencals()">取 消</el-button>
        <el-button type="primary" @click="shortConDetermine()">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 二次确认弹出 -->
    <el-dialog title="确认发送" :visible.sync="ConfirmSend" width="520px">
      <div style="padding-bottom: 5px">
        您本次提交号码数 <span style="color: #16a589">{{ sendNumber }}</span> 个
      </div>
      <div style="padding-top: 10px">
        发送内容：
        <div style="color: #999; padding-top: 6px; word-wrap: break-word">
          {{ sendContent }}
        </div>
      </div>
      <div class="sms-seconnd-steps-btns">
        <el-button type="primary" @click="ConfirmSending()" style="padding: 10px 20px"
          v-if="fullscreenLoading == true">确定发送</el-button>
        <el-button @click="ConfirmSends">取 消</el-button>
      </div>
    </el-dialog>
    <!-- 草稿箱 -->
    <el-dialog title="草稿箱" :visible.sync="DraftBoxflag" width="70%">
      <el-table :data="tableDraftBox.tableData" class="Login-c-p-getPhone" border style="width: 100%">
        <el-table-column align="center" width="80" label="选择">
          <template slot-scope="scope">
            <el-radio @change.native="getCurrentRow(scope.$index)" :label="scope.$index" v-model="radio"
              class="textRadio">&nbsp;</el-radio>
          </template>
        </el-table-column>
        <el-table-column label="内容">
          <template slot-scope="scope">
            <div @click="FzRadio(scope.$index)" style="cursor: pointer">
              <span>{{ scope.row.content }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作" width="120">
          <template slot-scope="scope">
            <el-button type="text" @click="draftModify(scope.row)"><i class="el-icon-edit mr-2"></i>修改</el-button>
            <el-button type="text" style="color: #f56c6c" @click="draftDelete(scope.row)"><i
                class="el-icon-delete mr-2"></i>删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <!--分页-->
      <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                <el-pagination class="page_bottom" @size-change="handleSizeChangedraft" @current-change="handleCurrentChangedraft" :current-page="tableObj.currentPage" :page-size="tableObj.pagesize" :page-sizes="[20, 50]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDraftBox.tableData.total">
                </el-pagination>
            </el-col> -->
      <!--分页-->
      <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" style="margin: 5px 0">
        <el-pagination style="float: right" class="page_bottom" @size-change="handleSizeChangedraft"
          @current-change="handleCurrentChangedraft" :current-page="tableObj.currentPage" :page-size="tableObj.pageSize"
          :page-sizes="[10, 20]" layout="total, sizes, prev, pager, next, jumper" :total="tableDraftBox.total">
        </el-pagination>
      </el-col>
      <!-- 表格和分页结束 -->
      <div class="sms-seconnd-steps-btns" style="margin-top: 15px; padding-right: 10px">
        <el-button type="primary" @click="ConfirmUse()" style="padding: 10px 20px">使用草稿</el-button>
        <el-button @click="DraftBoxflag = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="修改内容" width="360px" :visible.sync="DraftBoxModify">
      <el-input type="textarea" class="input-w-3" :rows="4" resize="none" placeholder="请输入内容" v-model="textarea">
      </el-input>
      <div class="sms-seconnd-steps-btns" style="margin-top: 15px; padding-right: 10px">
        <el-button type="primary" @click="determine()" style="padding: 10px 20px">确 定</el-button>
        <el-button @click="DraftBoxModify = false">取 消</el-button>
      </div>
    </el-dialog>
    <el-dialog title="实名认证" :visible.sync="dialogSmrzFlag" width="30%" center>
      <span>尊敬的客户，根据《中华人民共和国网络安全法》及相关法律的规定，请您尽快完成实名认证。如需帮助，请联系在线售后客服。</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="goSmrz">前往实名认证</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import moment from "moment";
import { mapState, mapMutations, mapActions } from "vuex";
import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传
import DatePlugin from "@/components/publicComponents/DatePlugin"; //日期
import inputTag from "@/components/publicComponents/InputTag";
export default {
  name: "sendDetails",
  components: {
    FileUpload,
    DatePlugin,
    inputTag,
  },
  data() {
    //短信内容的签名格式是否正确，或者是否有签名
    // var content = (rule, value, callback) => {
    //   if (value == "") {
    //     // this.falgJudge = false;
    //     return callback(new Error("请填写短信内容！"));
    //   } else {
    //     let reg =
    //       /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+)\.)+([A-Za-z0-9-~\/])+$/;

    //     let ret = 3;
    //     let beg = value.indexOf("【");
    //     let end = value.indexOf("】");
    //     let lastbeg = value.lastIndexOf("【");
    //     let lastend = value.lastIndexOf("】");
    //     let valLength = value.length;
    //     if (beg > -1 && end > -1 && end > beg && beg == 0) {
    //       if (
    //         beg == 0 &&
    //         end < 50 &&
    //         end > 2 &&
    //         value.split("】").length == 2
    //       ) {
    //         let index = value.split("】").length;
    //         for (let i = 0; i < index; i++) {
    //           if (i != 0) {
    //             let c = value.split("【")[i];
    //             let a = c.substring(0, c.indexOf("】"));
    //             if (
    //               !/^[\u4e00-\u9fa5_a-zA-Z0-9]+$/.test(a) ||
    //               a.length < 2 ||
    //               a.length > 20
    //             ) {
    //               return callback(new Error("请填写正确的签名！"));
    //             }
    //           }
    //         }
    //         // if (reg.test(value)) {
    //         //   callback();
    //         // }else{
    //         //   return callback(new Error("内容中包含链接"));
    //         // }
    //         callback();
    //       } else {
    //         return callback(new Error("请填写正确的签名！"));
    //       }
    //     } else if (
    //       lastbeg > -1 &&
    //       lastend > -1 &&
    //       lastend > lastbeg &&
    //       lastend == valLength - 1
    //     ) {
    //       if (
    //         lastend == valLength - 1 &&
    //         lastend - lastbeg < 49 &&
    //         lastend - lastbeg > 2
    //       ) {
    //         // if (reg.test(value)) {
    //         //   console.log(1111);
    //         //   return callback(new Error("内容中包含链接"));
    //         // }else{
    //         //   console.log(2222);
    //         //   callback();
    //         // }
    //         // console.log(1111);
    //         callback();
    //       } else {
    //         return callback(new Error("请填写正确的签名！"));
    //       }
    //     } else {
    //       // this.falgJudge = true;
    //       return callback(new Error("请填写签名！"));
    //     }
    //   }
    // };
    var content = (_, value, callback) => {
      if (value === "") {
        return callback(new Error("请填写短信内容！"));
      }

      let reg = /^([hH][tT]{2}[pP]:\/\/|[hH][tT]{2}[pP][sS]:\/\/)(([A-Za-z0-9-~]+)\.)+([A-Za-z0-9-~\/])+$/;
      let beg = value.indexOf("【");
      let end = value.indexOf("】");
      let lastbeg = value.lastIndexOf("【");
      let lastend = value.lastIndexOf("】");
      let valLength = value.length;

      let isValidSignature = (beg > -1 && end > -1 && end > beg && beg == 0 && end < 50 && end > 2 && value.split("】").length == 2) ||
        (lastbeg > -1 && lastend > -1 && lastend > lastbeg && lastend == valLength - 1 && lastend - lastbeg < 49 && lastend - lastbeg > 2);

      if (isValidSignature) {
        let signatures = value.match(/【.*?】/g);
        if (signatures) {
          for (let i = 0; i < signatures.length; i++) {
            let signature = signatures[i].slice(1, -1); // Remove the enclosing brackets
            if (!/^[\u4e00-\u9fa5_a-zA-Z0-9（）()]+$/.test(signature) || signature.length < 2 || signature.length > 20) {
              return callback(new Error("请填写正确的签名！"));
            }
          }
        }
        if (reg.test(value)) {
          return callback(new Error("内容中包含链接"));
        } else {
          return callback(); // Success
        }
      } else {
        return callback(new Error("请填写签名！"));
      }
    };

    var content1 = (_, value, callback) => {
      if (value == "") {
        this.falgJudge = false;
        return callback(new Error("请填写短信内容！"));
      } else {
        this.falgJudge = true;
        callback();
        // return callback(new Error("请填写签名！"));
      }
    };
    var mobile = (_, value, callback) => {
      if (value == "") {
        callback();
      } else {
        if (this.limit > 10000) {
          callback("已超过填写返回");
        }
      }
    };
    return {
      // 编辑id
      isShowBar: "0",
      // 预览相关数据
      previewPhoneNumber: "10086",
      currentTime: "",
      messageTime: "",
      fileFlag: true,
      message: "",
      text: "",
      text1: "",
      exthFlag: false,
      SmSid: "",
      extFlag: false,
      header: {},
      checked: false,
      limitFlag: true,
      TemplateStatus: false,
      dialogSmrzFlag: false,
      variableTp: true,
      tempIdVal: "",
      TemplateContent: "", //选择模板存储内容用
      number_zs: 0,
      button_QC: false,
      textareaShow: true,
      ConfirmSend: false,
      loadingcomp: false,//发送短信loading
      limit: 0, //号码限制
      SuccessfullySubmitted: 0, //成功提交号
      filter: 0, //过滤
      invalid: 0, //无效
      shortVisible: false, //短链弹框
      originalUrl: "",
      shortConUrl: "", //短链的URL
      sendLoading: true, //第一步短信发送的loading按钮
      fullscreenLoading: true, //第二步短信发送的loading按钮
      sendNumber: "", //短信发送条数
      sendContent: "", //发送短信
      copysigCenten: "", //复制签名内容
      copytemCenten: "", //复制模板内容
      phoneCenten: "", //手机短信内容
      del1: true, //关闭弹框时清空图片
      tip: "仅支持.xlsx .xls.txt 等格式",
      sigOptions: [], //签名列表
      temOptions: [], //模板列表
      templateDialog: false,
      singsShow: true,//模版发送签名提示
      tempConent: "",
      // 模板列表
      tableDataObj: {
        //列表数据
        tableData: [],
      },
      tabelAlllist: {
        //------发送表格请求的对象
        param: "",
        currentPage: 1, //当前页
        pageSize: 10, //每一页条数
      },
      pageTotal: 0, //总共条数
      SMScount: {
        //手机展示短信内容的下方，短信内容的计数
        smswordNum: "0", //短信内容的个数
        smssTrip: "0", //短信可以分为几条
      },
      downloadtem: "2", //模板下载（全文，自定义还是变量）（自定义为2）
      fileStyle: {
        size: 245678943234,
        style: ["xlsx", "xls", "txt"],
      },
      RowNum: null, //上传行数
      fileLists: [],
      fileList: [],
      datePluginValueList: {
        //日期参数配置
        type: "datetime",
        value: "yyyy-MM-dd HH:mm:ss",
        pickerOptions: {
          disabledDate(time) {
            const oneMonthFromNow = Date.now() + 30 * 24 * 60 * 60 * 1000; // 当前时间加上30天
            return time.getTime() < Date.now() - 8.64e7 || time.getTime() > oneMonthFromNow + 8.64e7; // 禁用掉30天前的时间
          },
        },
        defaultTime: "", //默认起始时刻
        datePluginValue: "",
      },
      configurationItem: {
        //发送短信弹出框的值
        formData: {
          tempVal: "-1",
          content: "",
          label: "",
          taskName: "",
          content1: "",
          signatureId: "", //签名id
          signature: "", //签名
          isTiming: "0", //选择立即发送还是定时发送
          type: "0",
          sendTime: "", //发送时间的值
          fileName: "",
          files: "",
          group: "",
          path: "",
          mobile: "",
          tempId: "",
          ext: "",
        },
        formDatass: {
          tempVal: "-1",
          content: "",
          label: "",
          taskName: "",
          signatureId: "",
          signature: "", //签名
          isTiming: "0", //选择立即发送还是定时发送
          type: "0",
          sendTime: "", //发送时间的值
          fileName: "",
          files: "",
          group: "",
          path: "",
          tempId: "",
          ext: "",
        },
        formRule: {
          //验证规则
          content: [
            { required: true, validator: content, trigger: "change" },
            {
              min: 1,
              max: 1500,
              message: "长度在 1 到 1500 个字符",
              trigger: "change",
            },
          ],
          content1: [
            { required: true, validator: content1, trigger: "change" },
          ],
          tempVal: [
            { required: true, message: "请选择模板名称", trigger: "change" },
          ],
          signatureId: [
            { required: true, message: "请选择签名", trigger: "change" },
          ],
          sendTime: [
            { required: true, message: "请填写定时时间", trigger: "change" },
          ],
          isTiming: [
            { required: true, message: "请选择发送时间", trigger: "change" },
          ],
          // mobile:[
          //     {validator: mobile, trigger: 'change'}
          // ]
        },
      },
      flagVal: "1",
      falgJudge: false,
      // 草稿箱
      DraftBoxflag: false,
      DraftBoxModify: false,
      DraftConter: "",
      radio: "",
      textareaID: "",
      textarea: "",
      tableObj: {
        pageSize: 10,
        currentPage: 1,
      },
      tableDraftBox: {
        //列表数据
        total: 0,
        tableData: [],
      },
      endingName: "",
      tempShow: true
    };
  },
  created() {
    this.configurationItem.formData.taskName =
      "任务" + "-" + moment(new Date()).format("YYYYMMDD");
    this.configurationItem.formDatass.taskName =
      "任务" + "-" + moment(new Date()).format("YYYYMMDD");
    this.versions();
    this.$api.get(
      this.API.cpus + "consumerclientinfo/getClientInfo",
      null,
      (res) => {
        // console.log(res.data);
        if (res.data.certificate == 0) {
          this.dialogSmrzFlag = true;
        }
        // this.certificate = res.data.certificate;
      }
    );
  },
  computed: {
    // 短信预览内容
    smsPreviewContent() {
      if (this.configurationItem.formData.tempVal === '-1') {
        return this.configurationItem.formData.content || '';
      } else {
        return this.tempConent || '';
      }
    },

    // 是否包含变量
    hasVariables() {
      const content = this.smsPreviewContent;
      return content && content.includes('{') && content.includes('}');
    },

    // 预计费用
    estimatedCost() {
      const trips = parseInt(this.SMScount.smssTrip) || 0;
      const mobileCount = this.limit || 0;
      const totalMessages = trips * mobileCount;
      // 假设每条短信0.05元，实际费用应该从后端获取
      return (totalMessages * 0.05).toFixed(2);
    }
  },
  methods: {
    // 初始化时间显示
    initTimeDisplay() {
      const now = new Date();
      this.currentTime = now.toTimeString().slice(0, 5); // HH:MM格式
      this.messageTime = now.toTimeString().slice(0, 5);
    },

    // 刷新预览
    refreshPreview() {
      this.initTimeDisplay();
      this.$message.success('预览已刷新');
    },

    versions() {
      //     var u = navigator.userAgent, app = navigator.appVersion;
      //     console.log(u,'u');
      //    var mobile = !!u.match(/AppleWebKit.*Mobile.*/)||!!u.match(/AppleWebKit/)
      //    console.log(mobile,'mobile');
      if (navigator.userAgent.match(/(iPhone|iPod|Android|ios|iPad)/i)) {
        // localStorage.setItem('h5Lg',"0");
        this.isShowBar = 0;
        // window.location.href = "mobile.html";
      } else {
        this.isShowBar = 1;
        //    localStorage.setItem('h5Lg',"1");
        // window.location.href = "pc.html";
      }
    },
    button_QCNR() {
      this.configurationItem.formData.content = "";
      this.TemplateContent = "";
    },
    //返回
    goBack() {
      this.$router.go(-1);
    },
    goSmrz() {
      this.dialogSmrzFlag = false;
      this.$router.push("/authentication");
    },
    textChange() {
      // this.configurationItem.formData.mobile=this.configurationItem.formData.mobile.replace(/[^\ \d\,]/g,"")
      // this.configurationItem.formData.mobile=this.configurationItem.formData.mobile.replace(/\s+/g,",")
      if (
        this.configurationItem.formData.mobile[
        this.configurationItem.formData.mobile.length - 1
        ] == ","
      ) {
        this.limit =
          this.configurationItem.formData.mobile.split(",").length - 1;
      } else {
        this.limit = this.configurationItem.formData.mobile.split(",").length;
      }
      if (
        this.configurationItem.formData.mobile.split(",").length == 1 &&
        this.configurationItem.formData.mobile.split(",")[0] == ""
      ) {
        this.limit = 0;
      }
    },
    //
    // remoteMethod(query) {
    //   if (query !== '') {
    //     this.loadingcomp = true
    //     this.getSignature(query)
    //     this.loadingcomp = false
    //   } else {
    //     this.compNamelist = []
    //     this.getSignature()
    //   }
    // },
    //获得签名列表
    getSignature(query) {
      this.$api.post(
        this.API.cpus + "signature/signatureList",
        {
          // signature: query || '',
          auditStatus: "2",
          currentPage: 1,
          pageSize: 200,
        },
        (res) => {
          this.sigOptions = res.records;
        }
      );
    },
    handChildLabel(data) {
      this.configurationItem.formData.label = data;
      // console.log(this.configurationItem.formData.label,'data');
    },
    handelChange(e) {
      // let list = ['{valid_code}','{ mobile_number}','{other_number}','{amount}','{date}','{chinese}','{others}']
      this.$api.post(
        this.API.cpus + "v3/consumersms/hasVariable",
        {
          string: e
        },
        (res) => {
          if (res.code == 200) {
            if (res.data) {
              this.configurationItem.formData.type = '1'
              this.tempShow = false
            } else {
              this.configurationItem.formData.type = '0'
              this.tempShow = true
            }
          } else {
            this.$message({
              message: res.msg,
              type: "error",
            });
          }

          // this.temOptions = res.records;
        }
      );
    },
    //获得模板名称列表
    getTemplate() {
      this.$api.post(
        this.API.cpus + "v3/consumersmstemplate/selectClientTemplate",
        {
          currentPage: 1,
          pageSize: 200,
        },
        (res) => {
          this.temOptions = res.records;
        }
      );
    },
    //获得模板名称对应的内容
    // getTemplateContent(){
    //     if(this.configurationItem.formData.tempVal){
    //         this.$api.get(this.API.cpus + 'v3/consumersmstemplate/get/'+this.configurationItem.formData.tempVal,{},res=>{
    //             this.downloadtem = res.data.temFormat; //模板下载类型（1变量，2全文）
    //             this.copytemCenten = res.data.temContent;
    //             this.phoneCenten = this.copysigCenten + this.copytemCenten;
    //         })
    //     }
    // },
    // 过滤号码
    FilterNumber() {
      this.configurationItem.formData.mobile =
        this.configurationItem.formData.mobile.replace(/\D/g, ",");
      let NumberFilter = this.configurationItem.formData.mobile.split(",");
      // console.log(NumberFilter,'NumberFilter');
      let arrNumber = [];
      let hash = [];
      let reg = /^(?:\+?86)?1\d{10}$/;
      for (var i = 0; i < NumberFilter.length; i++) {
        for (var j = i + 1; j < NumberFilter.length; j++) {
          if (NumberFilter[i] === NumberFilter[j]) {
            ++i;
          }
        }
        arrNumber.push(NumberFilter[i]);
      }
      for (var i = 0; i < arrNumber.length; i++) {
        if (reg.test(arrNumber[i])) {
          hash.push(arrNumber[i]);
        }
      }
      this.configurationItem.formData.mobile = hash.join(",");
      this.SuccessfullySubmitted = hash.length; //成功提交号
      this.filter = NumberFilter.length - arrNumber.length; //过滤
      if (arrNumber[0] == "") {
        this.invalid == 0;
      } else {
        this.invalid = arrNumber.length - hash.length; //无效
      }
      this.limit = hash.length;
    },
    //获取签名下拉框选择的 签名内容
    changeLocationValue(val) {
      // if (!val) {
      //   this.getSignature()
      // }
      this.singsShow = true
      let obj = {};
      obj = this.sigOptions.find((item) => {
        return item.signatureId === val;
      });
      this.configurationItem.formData.signature = this.copysigCenten =
        obj.signature;
      //短信内容展示
      if (this.configurationItem.formData.tempVal == "-1") {
        let signaIndex =
          this.configurationItem.formData.content.lastIndexOf("】") + 1;
        this.configurationItem.formData.content =
          this.copysigCenten +
          this.configurationItem.formData.content.substring(
            signaIndex,
            this.configurationItem.formData.content.length
          );
      } else {
        this.configurationItem.formData.content =
          this.copysigCenten + this.TemplateContent;
        this.configurationItem.formData.content1 =
          this.copysigCenten + this.TemplateContent;
      }

    },
    // cencal() {
    //   this.configurationItem.formData.extType = ''
    //   this.configurationItem.formData.ext = ''
    //   this.exthFlag = false
    // },
    //移除文件
    fileup(val) {
      this.RowNum = null;
      this.configurationItem.formData.group = "";
      this.configurationItem.formData.path = "";
      this.configurationItem.formData.fileName = "";
      this.configurationItem.formData.files = "";
      this.textareaShow = true;
      this.configurationItem.formData.mobile = "";
      this.limit = 0; //号码限制
      this.SuccessfullySubmitted = 0; //成功提交号
      this.filter = 0; //过滤
      this.invalid = 0; //无效
      this.fileList = []
    },
    //文件上传成功
    fileupres(val, val2) {
      console.log(val, 'val');
      if (val.code == 200) {
        this.fileFlag = true
        this.$message({
          message: val.msg,
          type: "success",
        });
        if (val.data.row1 || val.data.row2) {
          let obj = {
            //表格返回数据转换键值对
          };
          if (val.data.row2) {
            for (let index = 0; index < val.data.row1.length; index++) {
              for (let j = 0; j < val.data.row2.length; j++) {
                obj[val.data.row1[index]] = val.data.row2[index];
              }
            }
          }
          //字符串同时替换多个
          var str = this.configurationItem.formData.content;
          Array.prototype.each = function (trans) {
            for (var i = 0; i < this.length; i++)
              this[i] = trans(this[i], i, this);
            return this;
          };
          Array.prototype.map = function (trans) {
            return [].concat(this).each(trans);
          };
          RegExp.escape = function (str) {
            return new String(str).replace(
              /([.*+?^=!:${}()|[\]\/\\])/g,
              "\\$1"
            );
          };
          function properties(obj) {
            var props = [];
            for (var p in obj) props.push(p);
            return props;
          }
          var regex = new RegExp(
            properties(obj).map(RegExp.escape).join("|"),
            "g"
          );
          var regex3 = /{(.+?)}/g; // {} 花括号，大括号
          str = str.replace(regex3, function ($0, param) {
            return obj[param];
          });
          let stre = str;
          this.tempConent = stre.replace(/\{|}/g, "");
        }
        if (val.data.total) {
          this.RowNum = val.data.total;
        } else {
          this.RowNum = null;
        }
        this.configurationItem.formData.fileName = val.data.fileName;
        this.configurationItem.formData.group = val.data.group;
        this.configurationItem.formData.path = val.data.path;
        this.configurationItem.formData.files = val.data.fileName;
        this.configurationItem.formData.mobile = "";
        this.textareaShow = false;
        this.del1 = true;
      } else {
        this.fileFlag = false
        this.message = val.msg
        this.$message({
          message: val.msg,
          type: "error",
        });
      }
    },
    //限制用户上传文件格式和大小
    beforeAvatarUpload(file) {
      // console.log(file,'file');
      let endingCode = file.name;//结尾字符
      this.endingName = endingCode.slice(endingCode.lastIndexOf('.') + 1, endingCode.length);
      let isStyle = false; //文件格式
      const isSize = file.size / 1024 / 1024 < 300
      // const isSize = file.size / 1024 / 1024 < this.fileStyle.size; //文件大小
      // console.log(file.size / 1024 / 1024,'sdfsf')
      for (let i = 0; i < this.fileStyle.style.length; i++) {
        if (this.endingName === this.fileStyle.style[i]) {
          isStyle = true;
          break;
        }
      }
      //不能重复上传文件
      let fileArr = this.fileList;
      let fileNames = [];
      if (fileArr.length > 0) {
        for (let k = 0; k < fileArr.length; k++) {
          fileNames.push(fileArr[k].name)
        }
      }
      if (fileNames.indexOf(endingCode) !== -1) {
        this.$message.error('不能重复上传文件');
        return false;
      } else if (!isStyle) { //文件格式判断
        this.$message.error(this.tip);
        return false;
      } else {
        //文件大小判断
        if (!isSize) {
          this.$message.error('上传文件大小不能超过300MB');
          return false;
        }
      }
    },
    fileupM(val) {
      //   console.log(val);
      this.configurationItem.formData.mobile = "";
      this.limit = 0;
      //   this.limitFlag = true
    },
    triggerUpload() {
      this.$confirm('该功能仅支持提取文件中的前10000个号码，请仔细核对！', '提示', {
        confirmButtonText: '上传',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // console.log(this.$refs.uploadNum.$$el,'this.$refs.upload.$refs');
        // this.$refs.uploadNum.$el.querySelector('.upload-button').click();
        // this.$refs.uploadNum.$refs.input.click(); // 手动触发文件选择对话框
        this.$refs['uploadNum'].$refs['upload-inner'].handleClick()
        // this.$message({
        //   type: 'success',
        //   message: '删除成功!'
        // });
      }).catch((err) => {
        console.log(err, 'err');

        this.$message({
          type: 'info',
          message: '已取消'
        });
      });
    },
    fileupresM(val, val2) {
      if (val.code == 200) {
        this.limit = val.data.length;
        this.configurationItem.formData.mobile = val.data.join(",");
        this.$message({
          message: val.msg,
          type: "success",
        });
      } else {
        this.$message({
          message: val.msg,
          type: "error",
        });
      }
      //   this.limit = val.data.length
      //   if(val.data.length>200){
      //      let data =  val.data.slice(0,200)
      //      this.limit = data.length
      //       this.configurationItem.formData.mobile = data.join(",");
      //     //   console.log(data);
      //   }else{
      //       this.limit = val.data.length
      //       this.configurationItem.formData.mobile = val.data.join(",");
      //   }
      this.del1 = true;
    },
    handelSend(val) {
      if (val == 0) {
        this.fileup()
      } else {
        this.configurationItem.formData.mobile = "";
      }
    },
    //下载模板
    downloadTems() {
      if (this.downloadtem == "2") {
        this.$File.export(
          this.API.cpus + "v3/consumersms/templateZipDownload",
          {},
          `发送文件模板（自定义内容，全文模板可用）.zip`
        );
      } else {
        this.$File.export(
          this.API.cpus + "v3/consumersms/templateZipDownload",
          {},
          `发送文件模板（变量模板可用）.zip`
        );
      }
    },
    Variabledownload() {
      this.$File.export(
        this.API.cpus +
        "v3/consumersmstemplate/download?templateId=" +
        this.configurationItem.formData.tempId,
        {},
        `模板变量.xls`
      );
    },
    //短信发送提交 (第一步的短信发送)
    submissionItem(formName) {
      this.$refs[formName].validate((valid, val) => {
        if (this.configurationItem.formData.content) {
          if (
            this.configurationItem.formData.tempVal == "-1" &&
            Object.getOwnPropertyNames(val).length == 1 &&
            val.signatureId[0].message == "请选择签名"
          ) {
            valid = true;
          } else if (
            this.configurationItem.formData.tempVal != "-1" &&
            Object.getOwnPropertyNames(val).length == 1 &&
            val.content[0].message == "请填写短信内容！"
          ) {
            valid = true;
          }
          if (this.limit > 10000) {
            console.log(this.limit, 'this.limit');
            valid = false;
            this.$message({
              message: "手机号超出填写个数",
              type: "warning",
            });
          }
          if (!this.singsShow) {
            this.$message({
              message: "此模版未带签名请重新报备新模版或选择带签名的模版！",
              type: "warning",
            });
            return
          }
          //   if(!this.limitFlag){
          //       this.$message({
          //       message: "只支持提取200个号码！",
          //       type: "warning",
          //     });
          //   }
          if (valid) {
            let falgs = false;
            let falgss = false;
            //判断是否是 自定义内容
            if (this.configurationItem.formData.tempVal === "-1") {
              this.configurationItem.formData.tempId = "";
            } else {
              if (!this.TemplateContent) {
                this.$message({
                  message: "模版短信请选择模版内容",
                  type: "warning",
                });
                return false;
              }
            }
            this.configurationItem.formDatass = Object.assign(
              {},
              this.configurationItem.formData
            );
            if (this.configurationItem.formDatass.isTiming === "1") {
              //判断发送时间为定时时间
              if (this.configurationItem.formDatass.sendTime) {
                falgss = true;
                //如果是定时时间 ，定时时间范围必填
                let nowTiem = new Date(
                  this.configurationItem.formDatass.sendTime
                ).getTime();
                if (nowTiem < Date.now()) {
                  falgss = false;
                  this.datePluginValueList.datePluginValue = "";
                  this.configurationItem.formData.sendTime = "";
                  this.$message({
                    message: "定时时间应大于当前时间，需重新设置！",
                    type: "warning",
                  });
                } else {
                  falgss = true;
                }
              } else {
                falgss = false;
                this.$message({
                  message: "选择定时时间！",
                  type: "warning",
                });
              }
            } else {
              falgss = true;
            }
            //判断是否上传文件
            if (
              this.configurationItem.formDatass.files != "" ||
              this.configurationItem.formDatass.mobile != ""
            ) {
              falgs = true;
            } else {
              falgs = false;
              if (this.downloadtem == "2") {
                this.$message({
                  message: "上传发送对象文件或手动填写手机号!",
                  type: "warning",
                });
              } else {
                this.$message({
                  message: "上传发送对象文件!",
                  type: "warning",
                });
              }
            }

            // //判断是否是 自定义内容
            //     if(this.configurationItem.formDatass.tempVal === '-1'){
            //         this.configurationItem.formDatass.tempVal = '';
            //     }else{
            //         this.configurationItem.formData.tempId= this.tempIdVal
            //     }
            if (!this.fileFlag) {
              this.$message({
                message: this.message,
                type: "error",
              });
              return
            }
            if (
              falgs == true &&
              falgss == true &&
              this.extFlag == false &&
              this.exthFlag == false
            ) {
              const h = this.$createElement;
              this.$msgbox({
                title: "提示",
                message: h("p", null, [
                  h("span", null, "确认发送短信? "),
                  h(
                    "p",
                    { style: "color: red" },
                    "* 若短信内容中存在链接，请在链接前后加上空格"
                  ),
                ]),
                confirmButtonText: "确定",
                cancelButtonText: "取消",
                type: "warning",
              })
                .then(() => {
                  this.fullscreenLoading = false;
                  this.configurationItem.formDatass = Object.assign(
                    {},
                    this.configurationItem.formData
                  );
                  let flags = false;
                  if (
                    this.configurationItem.formDatass.isTiming === "1" &&
                    this.configurationItem.formDatass.sendTime
                  ) {
                    //判断是否定时时间 ，定时时间范围
                    let nowTiem = new Date(
                      this.configurationItem.formDatass.sendTime
                    ).getTime();
                    if (nowTiem < Date.now()) {
                      flags = false;
                      this.ConfirmSend = false;
                      this.fullscreenLoading = true;
                      this.$message({
                        message: "定时时间应大于当前时间，需重新设置！",
                        type: "warning",
                      });
                      this.datePluginValueList.datePluginValue = "";
                      this.configurationItem.formData.sendTime = "";
                      this.stepsActive = 1;
                    } else {
                      flags = true;
                    }
                  } else {
                    flags = true;
                  }
                  if (flags === true) {
                    //自定义内容发送
                    if (this.configurationItem.formData.tempVal === "-1") {
                      this.configurationItem.formData.tempVal = "";
                      // this.configurationItem.formDatass.signatureId = '';
                      if (this.isShort) {
                        this.configurationItem.formDatass.shortCode =
                          this.shortCode;
                      } else {
                        this.configurationItem.formDatass.shortCode = "";
                      }
                      this.$api.post(
                        this.API.cpus + "v3/consumersms/send/customize",
                        this.configurationItem.formDatass,
                        (res) => {
                          this.fullscreenLoading = true;
                          // this.sendMsgDialogShow = false;
                          this.ConfirmSend = false;
                          this.$refs.configurations.resetFields(); //清空表单
                          this.configurationItem.formData.mobile = "";
                          // this.configurationItem.formDatass.files=""
                          this.textareaShow = true;
                          this.downloadtem = "2";
                          this.limit = 0; //号码限制
                          this.SuccessfullySubmitted = 0; //成功提交号
                          this.filter = 0; //过滤
                          this.invalid = 0; //无效
                          if (res.code == 200) {
                            this.$message({
                              message: "短信发送成功！",
                              type: "success",
                            });
                            this.logUrl("SendTask");
                            this.$router.push({ path: "/SendTask" });
                          } else if (res.code == 406) {
                            this.$message({
                              message: "有效短信为0条！",
                              type: "warning",
                            });
                          } else if (res.code == 400) {
                            this.$message({
                              message: res.msg,
                              type: "warning",
                            });
                          } else {
                            this.$message({
                              message: "短信发送失败！",
                              type: "error",
                            });
                          }
                          this.del1 = false;
                          this.configurationItem.formData.files = "";
                          this.configurationItem.formData.group = "";
                          this.configurationItem.formData.path = "";
                        }
                      );
                    } else {
                      //模板内容发送
                      // this.configurationItem.formDatass.content = '';
                      this.$api.post(
                        this.API.cpus + "v3/consumersms/send/template",
                        this.configurationItem.formDatass,
                        (res) => {
                          this.fullscreenLoading = true;
                          // this.sendMsgDialogShow = false;
                          this.ConfirmSend = false;
                          this.$refs.configurations.resetFields(); //清空表单
                          this.configurationItem.formData.mobile = "";
                          this.textareaShow = true;
                          this.downloadtem = "2";
                          this.limit = 0; //号码限制
                          this.SuccessfullySubmitted = 0; //成功提交号
                          this.filter = 0; //过滤
                          this.invalid = 0; //无效
                          if (res.code == 200) {
                            this.$message({
                              message: "短信发送成功！",
                              type: "success",
                            });
                            this.logUrl("SendTask");
                            this.$router.push({ path: "/SendTask" });
                          } else if (res.code == 406) {
                            this.$message({
                              message: "有效短信为0条！",
                              type: "warning",
                            });
                          } else if (res.code == 400) {
                            this.$message({
                              message: res.msg,
                              type: "warning",
                            });
                          } else {
                            this.$message({
                              message: "短信发送失败！",
                              type: "error",
                            });
                          }
                          this.del1 = false;
                          this.configurationItem.formData.files = "";
                          this.configurationItem.formData.group = "";
                          this.configurationItem.formData.path = "";
                        }
                      );
                    }
                  }
                })
                .catch(() => {
                  this.$message({
                    type: "info",
                    message: "已取消发送",
                  });
                });
              // this.sendLoading = false;
              // this.$api.post(this.API.cpus + 'v3/consumersms/submitSms',this.configurationItem.formDatass,res=>{
              //     this.sendLoading = true;
              //     if(res.code == 200){
              //         if(res.data.mobileNum == 0){
              //             this.$message({
              //                 message: '提交成功条数为0 ，需重新设置！',
              //                 type: 'warning'
              //             });
              //         }else{
              //             this.sendNumber = res.data.mobileNum;
              //             this.sendContent = res.data.content;
              //             this.phoneCenten = res.data.content;
              //             this.ConfirmSend=true
              //         }
              //     }else{
              //         this.$message({
              //             message: res.msg,
              //             type: 'warning'
              //         });
              //     }
              // })
            } else {
              // console.log(this.extFlag);
              if (this.extFlag == true) {
                this.$message({
                  message: "扩展位数输入有误！",
                  type: "warning",
                });
              }
              if (this.exthFlag == true) {
                this.$message({
                  message: "小号输入有误！",
                  type: "warning",
                });
              }
            }
          } else {
            console.log("error submit!!");
            return false;
          }
        }
      });
    },
    handleQuite(val) {
      if (val) {
        this.configurationItem.formData.content =
          this.configurationItem.formData.content + "，拒收请回复R";
      } else {
        var reg = new RegExp("，拒收请回复R");
        var str = this.configurationItem.formData.content;
        str = str.replace(reg, "");
        this.configurationItem.formData.content = str;
      }
    },
    extflag(val) {
      var r = /^[0-9]*[1-9][0-9]*$/;
      if (!r.test(val)) {
        this.exthFlag = true;
        this.text1 = "小号输入有误！";
        if (val == "") {
          this.text1 = "最大支持4位！";
        }
      } else {
        this.exthFlag = false;
        this.text1 = "";
      }
    },
    extNum(val) {
      var r = /^[0-9]*[1-9][0-9]*$/;
      // console.log(r.test(val),'r');
      if (val > 4 || !r.test(val)) {
        this.extFlag = true;
        if (val > 4 || val == "") {
          this.text = "仅支持1-4数值之间任意一位扩展";
        } else {
          this.text = "扩展位数输入有误！";
        }
      } else {
        this.extFlag = false;
        this.text = "";
      }
      // if(r.test(val)){
      //   this.extFlag = false
      //   this.text = ""
      // }else{
      //   this.extFlag = true
      //   this.text = "扩展位数输入错误！"
      //   if(val>4){
      //     this.extFlag = true
      //     this.text = "只支持1-4位扩展位数！"
      //   }else{
      //       this.extFlag = false
      //       this.text = ""
      //   }
      // }
    },
    // extChange(e) {
    //   if (e == 1) {
    //     this.configurationItem.formData.extLength = "";
    //     this.extFlag = false;
    //   } else {
    //     this.configurationItem.formData.ext = "";
    //     this.exthFlag = false;
    //   }
    // },
    //确定发送短信 (第二步的短信发送)
    ConfirmSending() {
      this.fullscreenLoading = false;
      this.configurationItem.formDatass = Object.assign(
        {},
        this.configurationItem.formData
      );
      let flags = false;
      if (
        this.configurationItem.formDatass.isTiming === "1" &&
        this.configurationItem.formDatass.sendTime
      ) {
        //判断是否定时时间 ，定时时间范围
        let nowTiem = new Date(
          this.configurationItem.formDatass.sendTime
        ).getTime();
        if (nowTiem < Date.now()) {
          flags = false;
          this.ConfirmSend = false;
          this.fullscreenLoading = true;
          this.$message({
            message: "定时时间应大于当前时间，需重新设置！",
            type: "warning",
          });
          this.datePluginValueList.datePluginValue = "";
          this.configurationItem.formData.sendTime = "";
          this.stepsActive = 1;
        } else {
          flags = true;
        }
      } else {
        flags = true;
      }
      if (flags === true) {
        //自定义内容发送
        if (this.configurationItem.formData.tempVal === "-1") {
          this.configurationItem.formData.tempVal = "";
          // this.configurationItem.formDatass.signatureId = '';
          if (this.isShort) {
            this.configurationItem.formDatass.shortCode = this.shortCode;
          } else {
            this.configurationItem.formDatass.shortCode = "";
          }
          this.$api.post(
            this.API.cpus + "v3/consumersms/send/customize",
            this.configurationItem.formDatass,
            (res) => {
              this.fullscreenLoading = true;
              // this.sendMsgDialogShow = false;
              this.ConfirmSend = false;
              this.$refs.configurations.resetFields(); //清空表单
              this.configurationItem.formData.mobile = "";
              // this.configurationItem.formDatass.files=""
              this.textareaShow = true;
              this.downloadtem = "2";
              this.limit = 0; //号码限制
              this.SuccessfullySubmitted = 0; //成功提交号
              this.filter = 0; //过滤
              this.invalid = 0; //无效
              if (res.code == 200) {
                this.$message({
                  message: "短信发送成功！",
                  type: "success",
                });
                this.logUrl("SendTask");
                this.$router.push({ path: "/SendTask" });
              } else if (res.code == 406) {
                this.$message({
                  message: "有效短信为0条！",
                  type: "warning",
                });
              } else {
                this.$message({
                  message: "短信发送失败！",
                  type: "error",
                });
              }
              this.del1 = false;
              this.configurationItem.formData.files = "";
              this.configurationItem.formData.group = "";
              this.configurationItem.formData.path = "";
            }
          );
        } else {
          //模板内容发送
          // this.configurationItem.formDatass.content = '';
          this.$api.post(
            this.API.cpus + "v3/consumersms/send/template",
            this.configurationItem.formDatass,
            (res) => {
              this.fullscreenLoading = true;
              // this.sendMsgDialogShow = false;
              this.ConfirmSend = false;
              this.$refs.configurations.resetFields(); //清空表单
              this.configurationItem.formData.mobile = "";
              this.textareaShow = true;
              this.downloadtem = "2";
              this.limit = 0; //号码限制
              this.SuccessfullySubmitted = 0; //成功提交号
              this.filter = 0; //过滤
              this.invalid = 0; //无效
              if (res.code == 200) {
                this.$message({
                  message: "短信发送成功！",
                  type: "success",
                });
                this.logUrl("SendTask");
                this.$router.push({ path: "/SendTask" });
              } else if (res.code == 406) {
                this.$message({
                  message: "有效短信为0条！",
                  type: "warning",
                });
              } else {
                this.$message({
                  message: "短信发送失败！",
                  type: "error",
                });
              }
              this.del1 = false;
              this.configurationItem.formData.files = "";
              this.configurationItem.formData.group = "";
              this.configurationItem.formData.path = "";
            }
          );
        }
      }
    },
    handelSelect(val) {
      this.tempShow = true
      this.configurationItem.formData.signature = ''
      if (val == '-1') {
        this.singsShow = true
      } else {
        this.configurationItem.formData.content = ''
        this.configurationItem.formData.content1 = ''
      }

    },
    // 选择模板
    delTem(val, val2, row) {
      this.configurationItem.formData.signature = ''
      let regexCenter = /\【(.+?)\】/g;
      this.singsShow = val.match(regexCenter);
      console.log(this.singsShow, 'this.singsShow');
      this.TemplateStatus = true;
      this.templateDialog = false;
      if (this.TemplateStatus) {
        this.configurationItem.formData.content = val;
        this.configurationItem.formData.content1 = val;
        this.tempConent = val;
      } else {
        let contentIndex =
          this.configurationItem.formData.content.lastIndexOf("】") + 1;
        this.configurationItem.formData.content =
          this.configurationItem.formData.content.substring(0, contentIndex) +
          val;
        this.configurationItem.formData.content1 =
          this.configurationItem.formData.content.substring(0, contentIndex) +
          val;
        this.tempConent =
          this.configurationItem.formData.content.substring(0, contentIndex) +
          val;
      }
      this.TemplateContent = val;
      this.configurationItem.formData.tempId = val2;
      if (row.temFormat == '1') {
        this.configurationItem.formData.type = '1'
        this.tempShow = false
      } else {
        // this.configurationItem.formData.type = '0'
        this.tempShow = true
      }
    },
    //草稿箱------->
    // 保存草稿
    saveDraft() {
      if (this.configurationItem.formData.content) {
        this.$confirms.confirmation(
          "post",
          "确定当前内容保存至草稿箱？",
          this.API.cpus + "drafts",
          { content: this.configurationItem.formData.content },
          (res) => { }
        );
      } else {
        this.$message({
          message: "内容为空！",
          type: "error",
        });
      }
    },
    getDraft() {
      this.$api.post(
        this.API.cpus + "drafts/draftsAll",
        this.tableObj,
        (res) => {
          this.tableDraftBox.tableData = res.data.records;
          this.tableDraftBox.total = res.data.total;
        }
      );
    },
    DraftBox(val) {
      this.getDraft();
      this.DraftBoxflag = true;
    },
    getCurrentRow(val) {
      this.DraftConter = this.tableDraftBox.tableData[val].content; //赋值草稿内容
    },
    ConfirmUse() {
      if (this.DraftConter) {
        this.configurationItem.formData.content = this.DraftConter;
        this.DraftBoxflag = false;
      } else {
        this.$message({
          message: "请选择要使用的草稿内容",
          type: "error",
        });
      }
    },
    // 点击内容赋值Radio
    FzRadio(val) {
      this.radio = val;
      this.DraftConter = this.tableDraftBox.tableData[val].content; //赋值草稿内容
    },
    // 修改
    draftModify(val) {
      this.textareaID = val.id;
      this.textarea = val.content;
      this.DraftBoxModify = true;
    },
    determine() {
      this.$confirms.confirmation(
        "put",
        "确定修改内容？",
        this.API.cpus + "drafts",
        { id: this.textareaID, content: this.textarea },
        (res) => {
          this.DraftBoxModify = false;
          this.getDraft();
        }
      );
    },
    // 删除
    draftDelete(val) {
      this.$confirms.confirmation(
        "delete",
        "确定删除草稿？",
        this.API.cpus + "drafts/" + val.id,
        {},
        (res) => {
          this.getDraft();
        }
      );
    },
    handleSizeChangedraft(size) {
      this.tableObj.pageSize = size;
      this.getDraft();
    },
    handleCurrentChangedraft: function (currentPage) {
      this.tableObj.currentPage = currentPage;
      this.getDraft();
    },
    //草稿箱<-------
    // 取消发送
    ConfirmSends() {
      this.ConfirmSend = false;
    },
    handledatepluginVal: function (val1, val2) {
      //日期
      // console.log(val1,'ll');
      this.configurationItem.formData.sendTime = val1;
    },
    //短链转换
    shortReset() {
      this.shortVisible = true;
    },
    //转换
    transformation() {
      if (this.originalUrl != "") {
        this.$api.post(
          this.API.slms + "v3/shortLink/add",
          { originalUrl: this.originalUrl },
          (res) => {
            if (res.code == 200) {
              this.shortConUrl = res.data.shortLinkUrl;
              this.shortCode = res.data.shortCode;
              this.$message({
                message: "短链接转换成功！",
                type: "success",
              });
            } else {
              this.originalUrl = "";
              this.$message({
                message: res.msg,
                type: "warning",
              });
            }
          }
        );
      } else {
        this.$message({
          message: "长链接不可为空",
          type: "warning",
        });
      }
    },
    //预览
    handlePreview() {
      if (this.shortConUrl != "") {
        window.open("https://" + this.shortConUrl, "_blank");
      } else {
        this.$message({
          message: "短连接为空，无法预览",
          type: "warning",
        });
      }
    },
    //短连接弹框的确定
    shortConDetermine() {
      if (this.shortConUrl != "") {
        this.configurationItem.formData.content += " " + this.shortConUrl + " ";
        this.isShort = true;
        this.shortVisible = false;
      } else {
        this.$message({
          message: "短链接不可为空",
          type: "warning",
        });
      }
    },
    //短链的取消
    HandelCencals() {
      this.shortVisible = false;
      this.isShort = false;
    },
    getTableData() {
      //获模版取列表数据
      this.tableDataObj.loading2 = true;
      let formDatas = this.tabelAlllist;
      this.$api.post(
        this.API.cpus + "v3/consumersmstemplate/selectClientTemplate",
        formDatas,
        (res) => {
          this.tableDataObj.tableData = res.records;
          this.tableDataObj.loading2 = false;
          this.pageTotal = res.total;
          this.tabelAlllist.currentPage = res.current;
          this.tabelAlllist.pageSize = res.size;
          //                 tabelAlllist:{//------发送表格请求的对象
          //     param:'',
          //     currentPage:1,//当前页
          //     pageSize:10,//每一页条数
          // },
          // pageTotal:0,//总共条数
          // for(var i = 0 ; i<res.records.length ; i++){
          //     if(res.records[i].temFormat==2){
          //         this.tableDataObj.tableData[i].temContent = res.records[i].initialContent
          //     }
          // }
        }
      );
    },
    handleSizeChange(size) {
      //分页每一页的有几条
      this.tabelAlllist.pageSize = size;
      this.getTableData();
    },
    handleCurrentChange: function (currentPage) {
      //分页的第几页
      this.tabelAlllist.currentPage = currentPage;
      this.getTableData();
    },

    // 获取项目绝对路径
    ...mapActions([
      //比如'movies/getHotMovies
      "saveUrl",
    ]),
    logUrl(val) {
      let logUrl = {
        logUrl: val,
      };
      this.templateDialog = false;
      this.saveUrl(logUrl);
      window.sessionStorage.setItem("logUrl", val);
    },
  },
  mounted() {
    this.initTimeDisplay();
    this.getSignature();
    this.getTemplate();
    this.getTableData();
    this.header = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
    };
    this.SmSid = this.$route.query.SmSId;
    if (this.SmSid) {
      this.$api.get(
        this.API.cpus + "v3/queryConsumerWebTaskById/" + this.SmSid,
        {},
        (res) => {
          // if(res.sendType==1){
          //     this.configurationItem.formData.tempVal="2"
          // }else{
          //     this.configurationItem.formData.tempVal="-1"
          // }
          this.configurationItem.formData.content = res.content;
          this.configurationItem.formData.isTiming = res.isTiming + "";
          this.configurationItem.formData.mobile = res.mobile;
          this.configurationItem.formData.sendTime = res.sendTime;
        }
      );
    }
    // const savedData = localStorage.getItem('formData');
    // if (savedData) {
    //   this.configurationItem.formData = JSON.parse(savedData);
    //   this.datePluginValueList.datePluginValue = this.configurationItem.formData.sendTime;
    //   this.fileList = [{ name: this.configurationItem.formData.files, url: this.configurationItem.formData.path }]
    //   this.tempConent = this.configurationItem.formData.content1;
    // }
  },
  //   activated() {
  //     this.$api.get(
  //       this.API.cpus + "consumerclientinfo/getClientInfo",
  //       null,
  //       (res) => {
  //         // console.log(res.data);
  //         if (res.data.certificate == 0) {
  //           this.dialogSmrzFlag = true;
  //         }
  //         // this.certificate = res.data.certificate;
  //       }
  //     );
  //     this.getSignature();
  //     this.getTemplate();
  //     this.getTableData();
  //   },
  watch: {
    templateDialog(val) {
      if (val == false) {
        this.getTableData();
        this.tabelAlllist.param = "";
      }
    },
    downloadtem(val) {
      if (val == "1") {
        this.fileStyle.style = ["xlsx", "xls"];
        this.tip = "仅支持.xlsx .xls 等格式";
        this.configurationItem.formData.mobile = "";
      } else {
        this.fileStyle.style = ["xlsx", "xls", "txt"];
        this.tip = "仅支持.xlsx .xls.txt 等格式";
      }
    },
    //监听自定义短信的内容
    "configurationItem.formData.content"() {
      this.phoneCenten = this.configurationItem.formData.content;
      this.number_zs = this.configurationItem.formData.content.length;
      if (
        this.configurationItem.formData.content.indexOf("{") >= 0 &&
        this.configurationItem.formData.content.indexOf("}") >= 0 &&
        this.configurationItem.formData.tempVal != "-1"
      ) {
        this.configurationItem.formData.mobile = "";
        this.variableTp = false;
      } else {
        this.variableTp = true;
      }
    },
    //监听是否选择了自定义短信
    "configurationItem.formData.tempVal"(val) {
      this.flagVal = val;
      this.TemplateContent = "";
      this.TemplateStatus = false;
      //模板
      this.configurationItem.formData.signatureId = "";
      if (this.configurationItem.formData.tempVal != "-1") {
        // if(this.sendMsgDialogShow == true){
        // this.getTemplateContent();
        // }
        this.configurationItem.formData.content = "";
        this.TemplateContent = "";
      } else {
        //自定义
        this.downloadtem = "2"; //模板下载类型（自定义）
        this.phoneCenten = "";
        this.configurationItem.formData.signatureId = ""; //清除签名id
        this.copysigCenten = ""; //清除签名
        this.configurationItem.formData.signatureId = "";
        this.configurationItem.formData.content = "";
      }
    },
    'configurationItem.formData': {
      handler(newData) {
        localStorage.setItem('formData', JSON.stringify(newData));
      },
      deep: true,
    },
    //监听手机框内容的改变
    phoneCenten(val) {
      //模板
      let d1 =
        /(\{var[1-9]{1,2}\|(d|w|\$|c)[0-9]{1,2}-[0-9]{1,2}\})|(\{var[1-9]{1,2}\|(d|w|\$|c)[0-9]{1,2}\})|(\{var[1-9]{1,2}\|(hh:mm:ss|MM-DD|YYYY-MM-DD|YYYY-MM-DD hh:mm:ss|MM-DD hh:mm:ss)\})/g;
      let a1 = val.match(d1);

      let w1 = val.length;
      let w2 = 0;

      if (a1 == null) {
        this.SMScount.smswordNum = val.length;
      } else {
        let w3 = 0;
        for (let i = 0; i < a1.length; i++) {
          w2 += a1[i].length; //参数物理长度
          if (a1[i].substr(-10) == "|hh:mm:ss}") {
            w3 += 8;
          } else if (a1[i].substr(-7) == "|MM-DD}") {
            w3 += 5;
          } else if (a1[i].substr(-12) == "|YYYY-MM-DD}") {
            w3 += 10;
          } else if (a1[i].substr(-21) == "|YYYY-MM-DD hh:mm:ss}") {
            w3 += 19;
          } else if (a1[i].substr(-16) == "|MM-DD hh:mm:ss}") {
            w3 += 14;
          } else {
            let num = /[0-9]{1,2}/g;
            let mun1 = a1[i].match(num);
            w3 += Number(mun1[mun1.length - 1]);
          }
        }
        this.SMScount.smswordNum = w1 - w2 + w3;
      }
      //字数和短信条数的显示
      if (this.SMScount.smswordNum == 0) {
        this.SMScount.smssTrip = 0;
      } else if (
        parseInt(this.SMScount.smswordNum) <= 70 &&
        parseInt(this.SMScount.smswordNum) > 0
      ) {
        this.SMScount.smssTrip = 1;
      } else {
        this.SMScount.smssTrip = Math.ceil(
          parseInt(this.SMScount.smswordNum) / 67
        );
      }
    },
    //短连接弹框是否关闭
    shortVisible(val) {
      if (val == false) {
        this.originalUrl = ""; //长链接的值
        this.shortConUrl = ""; //短连接的值
      }
    },
  },
};
</script>
<style scoped lang="less">
.active {
  font-size: 67px !important;
  color: #C0C4CC !important;
  margin: 40px 0 16px !important;
  line-height: 50px !important;
}

@media screen and (min-width: 1200px) {
  .select_s {
    width: 420px;
  }

  .el-textarea {
    width: 420px;
  }

  .sms_coment {
    width: 100%;
  }

  .textar {
    width: 100%;
  }

  .shortChain-box {
    padding: 20px;
  }

  .sendMsg-box {
    padding: 20px;
  }

  .sendMsg-title {
    margin: 10px 0;
    padding-left: 5px;
    color: #16a589;
  }

  .sendMsg-table {
    margin-top: 12px;
  }

  .sendMsg-list-header {
    padding-top: 30px;
    font-weight: bold;
  }

  .el-steps--simple {
    background: #fff;
    border-radius: 0px;
    border-bottom: 1px solid #f3efef;
  }

  .send-mobel-box {
    width: 260px;
    overflow: hidden;
    position: relative;
    margin-bottom: 35px;
    left: 28%;
  }

  .send-mobel-box img {
    width: 255px;
  }

  /* .el-select {
    width: 50%;
  } */

  .send-upload-tips {
    padding-left: 96px;
    padding-top: 5px;
    font-size: 12px;
  }

  .send-upload-tips span {
    display: inline-block;
    padding-left: 8px;
    color: #0066ff;
  }

  .sms-content-exhibition {
    width: 170px;
    height: 40%;
    border-radius: 10px;
    position: absolute;
    padding: 8px 9px;
    background: #e2e2e2;
    top: 100px;
    left: 31px;
    font-size: 12px;
    line-height: 18px;
    color: #000;
    overflow: hidden;
  }

  /* .sms-first-steps-btns{
    width:517px;
    position: absolute;
    text-align: center;
    bottom: 10px;
} */
  .sms-seconnd-steps-box {
    padding: 30px 20px 30px 55px;
  }

  .sms-seconnd-steps-btns {
    text-align: right;
  }

  .goTiming {
    padding-right: 14px;
    cursor: pointer;
    color: #16a589;
  }

  .goTiming:hover {
    color: #03886e;
  }

  .shortChain-box {
    padding: 20px;
  }

  .shortChain-matter {
    border: 1px solid #66ccff;
    padding: 10px 14px;
  }

  .short-title {
    font-weight: bolder;
    padding-bottom: 5px;
  }

  .font-sizes {
    padding-top: 2px;
    font-size: 12px;
    color: rgb(163, 163, 163);
  }

  .font-sizes1 {
    margin-top: 10px;
  }

  .input-w-3 {
    width: 320px !important;
  }
}

@media screen and (max-width: 1200px) {

  /* .fillet shortChain-box{

    } */
  /* .select_s {
    width: 200px;
  } */

  .page_bottom {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    flex-shrink: 0;
  }

  .sms_coment {
    width: 200px;
  }

  .textareas {
    width: 200px;
  }

  .text {
    width: 200px;
  }

  .send-upload-tips {
    width: 200px;
    margin-left: 90px;
  }

  .textar {
    width: 200px;
    height: 100px;
  }
}

// .fillet {
//   border-radius: 8px;
//   background-color: #fff;
//   box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
//   transition: all 0.3s ease;
//   animation: fadeIn 0.5s ease;
// }

// @keyframes fadeIn {
//   from {
//     opacity: 0;
//     transform: translateY(10px);
//   }
//   to {
//     opacity: 1;
//     transform: translateY(0);
//   }
// }
</style>

<style>
@media screen and (min-width: 1200px) {
  .sendMsg-box .el-dialog__body {
    padding: 10px 20px;
  }

  .sendMsg-box .el-form-item__label {
    text-align: left;
  }

  .sendMsg-box .el-step__head.is-process {
    color: #989898;
  }

  .sendMsg-box .el-step__title.is-process {
    color: #989898;
  }

  .sms-content-exhibition .el-scrollbar__wrap {
    overflow-x: hidden;
    /* overflow-y: scroll !important;  */
    margin-right: -27px !important;
  }

  .el-picker-panel .el-button--text {
    display: none;
  }

  .textareas textarea {
    width: 100%;
    height: 100px;
    resize: none;
  }

  .textareas textarea::-webkit-scrollbar {
    display: none;
  }

  .box-textareas {
    /* width: 500px; */
    width: 380px;
    height: 125px;
    border-radius: 5px;
    border: 1px solid rgb(162, 219, 208);
    /* display: flex; */
    position: relative;
  }

  .upload-demos {
    /* margin-left: 50px;
    position: absolute;
    bottom: -10%;
    right: -20%; */
    margin-left: 10px;
    margin-top: 60px;
  }

  .box-textareas textarea {
    border: none;
  }

  .el-textarea__inner {
    height: 100px;
  }
}

@media screen and (max-width: 1200px) {
  .el-dialog {
    margin-left: 100px;
  }

  .el-radio-group {
    display: flex;
    padding: 10px;
  }

  .el-textarea__inner {
    height: 100px;
  }

  .el-form-item__content {
    width: 200px;
  }

  .el-message-box {
    width: 200px;
  }
}
</style>

<style lang="less" scoped>
// 引入发送短信通用样式
@import '~@/styles/send-details-common.less';
</style>