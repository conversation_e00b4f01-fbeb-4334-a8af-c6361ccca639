<template>
  <div class="modern-signature-page">
    <!-- 现代化页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button
            type="text"
            @click="goBack()"
            class="back-btn"
          >
            <i class="el-icon-arrow-left"></i>
            返回
          </el-button>
          <el-divider direction="vertical"></el-divider>
          <h1 class="page-title">{{ statusOf }}</h1>
        </div>
        <div class="header-right">
          <el-tag :type="statusOf === '添加签名' ? 'success' : 'warning'" size="medium">
            {{ statusOf === '添加签名' ? '新建签名' : '编辑签名' }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container">
        <!-- 签名配置表单 -->
        <el-form
          :model="signatureFrom.formData"
          :rules="signatureFrom.formRule"
          label-width="120px"
          ref="signatureFrom"
          class="compact-vertical-form"
        >
          <!-- 签名配置卡片 -->
          <el-card shadow="hover" class="form-card signature-config-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-edit-outline"></i>
                签名配置
              </span>
              <div class="header-actions">
                <el-button type="text" @click="showImg" class="example-btn">
                  <i class="el-icon-view"></i>
                  查看示例
                </el-button>
                <el-tooltip content="签名将显示在短信开头，用于标识发送方" placement="top">
                  <i class="el-icon-question help-icon"></i>
                </el-tooltip>
              </div>
            </div>

            <!-- 签名内容 -->
            <el-form-item label="签名内容" prop="signature" class="signature-input-item">
              <div class="signature-input-wrapper">
                <span class="signature-bracket left-bracket">【</span>
                <el-input
                style="width: 95%;margin: 0 8px;"
                  v-model="signatureFrom.formData.signature"
                  placeholder="签名限制中文16个字，英文32个字"
                  class="signature-input"
                  maxlength="32"
                  show-word-limit
                >
                  <i slot="prefix" class="el-icon-edit"></i>
                </el-input>
                <span class="signature-bracket right-bracket">】</span>
              </div>
              <div class="signature-preview">
                <span class="preview-label">预览：</span>
                <span class="preview-text">【{{ signatureFrom.formData.signature || '您的签名' }}】您的短信内容...</span>
              </div>
            </el-form-item>

            <!-- 签名来源 -->
            <el-form-item label="签名来源" prop="signatureType" class="compact-form-item">
              <el-select v-model="signatureFrom.formData.signatureType" placeholder="请选择签名来源" class="full-width-select">
                <el-option :value="1" label="企业名称">
                  <span style="float: left">企业名称</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">推荐全称，简称字数须为全称字数60%以上</span>
                </el-option>
                <el-option :value="2" label="事业单位">
                  <span style="float: left">事业单位</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">如机关、学校、科研单位、街道社区等</span>
                </el-option>
                <el-option :value="3" label="商标">
                  <span style="float: left">商标</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">须提供商标注册证书图片</span>
                </el-option>
                <el-option :value="4" label="App应用">
                  <span style="float: left">App应用</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">须提供备案管理系统截图</span>
                </el-option>
                <el-option :value="5" label="小程序">
                  <span style="float: left">小程序</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">须提供备案管理系统截图</span>
                </el-option>
              </el-select>
            </el-form-item>

            <!-- 签名类型 -->
            <el-form-item label="签名类型" prop="signatureSubType" class="compact-form-item">
              <el-radio-group v-model="signatureFrom.formData.signatureSubType" class="vertical-radio-group">
                <el-radio :label="0" class="radio-item">
                  <span class="radio-content">
                    <span class="radio-title">全称</span>
                    <el-tag type="success" size="mini" class="radio-tag">推荐</el-tag>
                    <span class="radio-desc">使用完整的企业/单位名称，报备速度快，通过率高</span>
                  </span>
                </el-radio>
                <el-radio :label="1" class="radio-item">
                  <span class="radio-content">
                    <span class="radio-title">简称</span>
                    <el-tag type="warning" size="mini" class="radio-tag">需证明</el-tag>
                    <span class="radio-desc">请用企业/单位简称签名在企查查搜索企业唯一的图片</span>
                  </span>
                </el-radio>
              </el-radio-group>
            </el-form-item>

            <!-- 文件上传 -->
            <el-form-item label="上传证明材料" class="compact-form-item">
              <file-upload
                :action="this.API.cpus + 'v3/file/upload'"
                :limit="3"
                listType="picture"
                tip="支持jpg、png等格式，大小不超过2M，最多3张"
                :fileStyle="fileStyle"
                :del="del1"
                :fileListS="fileListS"
                :showfileList="true"
                @fileup="fileup"
                @fileupres="fileupres"
                class="compact-upload"
              >
                选择上传文件
              </file-upload>
            </el-form-item>

            <!-- 短信示例 -->
            <el-form-item label="短信示例" prop="contentExample" class="compact-form-item">
              <el-input
                v-model="signatureFrom.formData.contentExample"
                type="textarea"
                placeholder="例如：【xxx】欢迎使用xxx，祝您使用愉快！"
                maxlength="800"
                show-word-limit
                :rows="3"
                class="compact-textarea"
              />
            </el-form-item>

            <!-- 备注内容 -->
            <el-form-item label="备注内容" prop="remark" class="compact-form-item">
              <el-input
                type="textarea"
                v-model="signatureFrom.formData.remark"
                maxLength="50"
                placeholder="请输入备注内容,50字以内"
                :rows="2"
                show-word-limit
                class="compact-textarea"
              />
            </el-form-item>
          </el-card>

          <!-- 企业信息卡片 -->
          <el-card shadow="hover" class="form-card company-info-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-office-building"></i>
                企业信息
              </span>
              <div class="header-actions">
                <el-button type="text" @click="clearPrincipalInfo" class="clear-btn">
                  <i class="el-icon-delete"></i>
                  清空信息
                </el-button>
              </div>
            </div>

            <!-- 企业名称 -->
            <el-form-item label="企业名称" prop="companyName" class="compact-form-item">
              <el-input
                v-model="signatureFrom.formData.companyName"
                placeholder="请输入企业名称"
                class="compact-input"
              >
                <i slot="prefix" class="el-icon-office-building"></i>
              </el-input>
            </el-form-item>

            <!-- 社会统一信用代码 -->
            <el-form-item label="社会统一信用代码" prop="creditCode" class="compact-form-item">
              <el-input
                v-model="signatureFrom.formData.creditCode"
                placeholder="请输入统一社会信用代码"
                class="compact-input"
              >
                <i slot="prefix" class="el-icon-postcard"></i>
              </el-input>
            </el-form-item>

            <!-- 企业法人 -->
            <el-form-item label="企业法人" prop="legalPerson" class="compact-form-item">
              <el-input
                v-model="signatureFrom.formData.legalPerson"
                placeholder="请输入企业法人姓名"
                class="compact-input"
              >
                <i slot="prefix" class="el-icon-user"></i>
              </el-input>
            </el-form-item>

            <!-- 责任人姓名 -->
            <el-form-item label="责任人姓名" prop="principalName" class="compact-form-item">
              <el-input
                v-model="signatureFrom.formData.principalName"
                placeholder="请输入负责人姓名"
                class="compact-input"
              >
                <i slot="prefix" class="el-icon-user-solid"></i>
              </el-input>
            </el-form-item>

            <!-- 责任人证件号码 -->
            <el-form-item label="责任人证件号码" prop="principalIdCard" class="compact-form-item">
              <el-input
                v-model="signatureFrom.formData.principalIdCard"
                placeholder="请输入负责人身份证号"
                class="compact-input"
              >
                <i slot="prefix" class="el-icon-postcard"></i>
              </el-input>
            </el-form-item>

            <!-- 责任人手机号 -->
            <el-form-item label="责任人手机号" prop="principalMobile" class="compact-form-item">
              <el-input
                v-model="signatureFrom.formData.principalMobile"
                placeholder="请输入责任人手机号"
                class="compact-input"
              >
                <i slot="prefix" class="el-icon-mobile-phone"></i>
              </el-input>
              <div class="input-tip">
                <span class="tip-text">须填写身份证号本人使用的手机号，否则报备失败</span>
              </div>
            </el-form-item>
          </el-card>
        </el-form>

        <!-- 操作按钮区域 -->
        <div class="action-buttons-vertical">
          <el-button
            type="primary"
            size="large"
            @click="signature_add('signatureFrom', 'signatureFrom.title', '1')"
            class="submit-btn"
          >
            <i class="el-icon-check"></i>
            提交审核
          </el-button>
          <el-button
            type="success"
            size="large"
            @click="signature_add('signatureFrom', 'signatureFrom.title', '0')"
            class="save-btn"
          >
            <i class="el-icon-document"></i>
            保存但不提交
          </el-button>
          <el-button
            size="large"
            @click="goBack"
            class="cancel-btn"
          >
            <i class="el-icon-close"></i>
            取消
          </el-button>
        </div>

        <!-- 签名规范说明 -->
        <el-card shadow="hover" class="form-card rules-card-vertical">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-info"></i>
              签名规范说明
            </span>
          </div>
          <div class="rules-content-vertical">
            <el-alert
              title="重要提醒"
              type="warning"
              :closable="false"
              show-icon
            >
              <template slot="default">
                <div class="rules-list-vertical">
                  <div class="rule-item-vertical">
                    <strong>签名内容：</strong>公司名称或产品名称，字数要求中文在<span class="highlight">2-16个字符</span>，英文在<span class="highlight">2-32个字符</span>，不能使用空格和特殊符号" - + = * & % # @ ~等
                  </div>
                  <div class="rule-item-vertical">
                    <strong>签名审核：</strong>签名由客服人工审核，审核通过后可使用
                  </div>
                  <div class="rule-item-vertical">
                    <strong>签名规范：</strong>无须在签名内容前后添加【】、（）、{}，系统会自动添加
                  </div>
                </div>
              </template>
            </el-alert>
          </div>
        </el-card>
      </div>
    </div>
    <!-- 现代化图片示例弹窗 -->
    <el-dialog
      title="证明材料示例"
      :visible.sync="showImgDialogVisible"
      width="1000px"
      :before-close="handleClose"
      :close-on-press-escape="false"
      class="modern-dialog"
    >
      <div class="dialog-content">
        <!-- 温馨提示 -->
        <div class="tips-section">
          <el-alert
            title="温馨提示"
            type="info"
            :closable="false"
            show-icon
          >
            <template slot="default">
              <div class="tips-content">
                <p><strong>中国商标网查询网址：</strong>
                  <a href="https://sbj.cnipa.gov.cn/sbj/index.html" target="_blank" class="link">
                    https://sbj.cnipa.gov.cn/sbj/index.html
                  </a>
                </p>
                <p><strong>工信部备案管理系统：</strong>
                  <a href="https://beian.miit.gov.cn/#/Integrated/recordQuery" target="_blank" class="link">
                    https://beian.miit.gov.cn/#/Integrated/recordQuery
                  </a>
                </p>
              </div>
            </template>
          </el-alert>
        </div>

        <!-- 示例图片展示 -->
        <div class="examples-grid">
          <div class="example-item">
            <div class="example-card">
              <div class="example-header">
                <i class="el-icon-search"></i>
                <span class="example-title">唯一性示例图</span>
              </div>
              <div class="example-image">
                <el-image
                  :src="require('../../../../../../assets/images/qcc.png')"
                  :preview-src-list="[require('../../../../../../assets/images/qcc.png')]"
                  fit="cover"
                  class="demo-image"
                >
                </el-image>
              </div>
            </div>
          </div>

          <div class="example-item">
            <div class="example-card">
              <div class="example-header">
                <i class="el-icon-office-building"></i>
                <span class="example-title">营业执照示例</span>
              </div>
              <div class="example-image">
                <el-image
                  :src="require('../../../../../../assets/images/business.png')"
                  :preview-src-list="[require('../../../../../../assets/images/business.png')]"
                  fit="cover"
                  class="demo-image"
                >
                </el-image>
              </div>
            </div>
          </div>

          <div class="example-item">
            <div class="example-card">
              <div class="example-header">
                <i class="el-icon-medal"></i>
                <span class="example-title">商标示例</span>
              </div>
              <div class="example-image">
                <el-image
                  :src="require('../../../../../../assets/images/shangbiao.png')"
                  :preview-src-list="[require('../../../../../../assets/images/shangbiao.png')]"
                  fit="cover"
                  class="demo-image"
                >
                </el-image>
              </div>
            </div>
          </div>

          <div class="example-item">
            <div class="example-card">
              <div class="example-header">
                <i class="el-icon-mobile-phone"></i>
                <span class="example-title">App/小程序示例</span>
              </div>
              <div class="example-image">
                <el-image
                  :src="require('../../../../../../assets/images/productapp.png')"
                  :preview-src-list="[require('../../../../../../assets/images/productapp.png')]"
                  fit="cover"
                  class="demo-image"
                >
                </el-image>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传
export default {
  components: { FileUpload },
  name: "CreateSign",
  data() {
    return {
      statusOf: "", //是新增还是编辑
      fileListS: "", //回显图片的地址
      signatures: "", //签名
      roleId: "",
      signatureFrom: {
        title: "",
        formData: {
          signature: "",
          signatureType: 1,
          signatureSubType: 0,
          remark: "",
          imgUrl: "",
          contentExample: "",
          signatureId: "",
          direct: 1,
          companyName: "",
          creditCode: "",
          legalPerson: "",
          principalName: "",
          principalIdCard: "",
          principalMobile: "",
        },
        imgUrl: [],
        formRule: {
          //验证规则
          // signature: [
          //   { required: true, message: "该输入项为必填项!", trigger: "blur" },
          //   {
          //     min: 2,
          //     max: 15,
          //     message: "长度在 2 到 15 个字符",
          //     trigger: ["blur", "change"],
          //   },
          //   {
          //     pattern: /^(?!\d+$)([a-zA-Z\u4e00-\u9fa5\s\d]+)$/,
          //     message: "不能使用空格和特殊符号" - + = * & % # @ ~"等",
          //   },
          // ],
          signature: [
            { required: true, message: "该输入项为必填项!", trigger: "blur" },
            {
              validator: (_, value, callback) => {
                const englishPattern = /^[a-zA-Z\s]*$/; // 仅英文字符
                const chinesePattern = /^[\u4e00-\u9fa5\s]*$/; // 仅中文字符
                const mixedPattern = /^(?!\d+$)([a-zA-Z\u4e00-\u9fa5\s\d]+)$/; // 不包含特殊字符（仅限字母和汉字）
                if (value) {
                  // 检查是否为纯英文
                  if (englishPattern.test(value)) {
                    if (value.length > 32) {
                      callback(new Error("英文长度不能超过32个字符"));
                    } else {
                      callback(); // 验证通过
                    }
                  }
                  // 检查是否为纯中文
                  else if (chinesePattern.test(value)) {
                    if (value.length > 16) {
                      callback(new Error("中文长度不能超过16个字符"));
                    } else {
                      callback(); // 验证通过
                    }
                  }
                  // 检查是否为中英组合
                  else if (mixedPattern.test(value)) {
                    if (value.length > 16) {
                      callback(new Error("中英文组合长度不能超过16个字符"));
                    } else {
                      callback(); // 验证通过
                    }
                  } else {
                    callback(new Error("请输入有效的字符（英文或中文）"));
                  }
                } else {
                  callback(new Error("该输入项为必填项!"));
                }
              },
              trigger: ["blur", "change"],
            },
          ],
          signatureType: [
            { required: true, message: "请选择签名来源", trigger: "change" },
          ],
          signatureSubType: [
            { required: true, message: "请选择签名类型", trigger: "change" },
          ],
          imgUrl: [
            // { required: true, message: "请选择上传图片", trigger: "change" },
          ],
          contentExample: [
            { required: true, message: "该输入短信示例", trigger: "blur" },
            {
              min: 1,
              max: 800,
              message: "长度在 1 到 800 个字符",
              trigger: ["blur", "change"],
            },
          ],
          direct: [
            { required: true, message: "请选择主体", trigger: "change" },
          ],
          companyName: [
            { required: true, message: '请输入企业名称', trigger: 'change' },
          ],
          creditCode: [
            { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
          ],
          legalPerson: [
            { required: true, message: '请输入法人姓名', trigger: 'change' },
          ],
          principalIdCard: [
            { required: true, message: '请输入负责人身份证号', trigger: 'change' },
          ],
          principalName: [
            { required: true, message: '请输入负责人姓名', trigger: 'change' },
          ],
          principalMobile: [
            { required: true, message: '请输入责任人手机号', trigger: 'change' },
          ],
        },
        signature: "", //签名
        signatureId: "", //签名id
      },
      //上传文件格式
      fileStyle: {
        size: 5,
        type: "img",
        style: ["jpg", "jpeg", "bmp", "gif", "png"],
      },
      del1: true, //关闭弹框时清空图片
      showImgDialogVisible: false, //查看图片示例弹窗
    };
  },
  created() {
    let userInfo = JSON.parse(localStorage.getItem("userInfo"));
    this.roleId = userInfo.roleId;
    if (this.$route.query.d) {
      this.statusOf = "编辑签名";
      this.handleEdit();
    } else {
      this.statusOf = "添加签名";
      this.getRealNameInfo()
    }
  },
  methods: {
    //获取编辑信息
    //编辑的赋值
    getRealNameInfo() {
      try {
        this.$api.get(
          this.API.cpus + "signature/getRealNameInfo",
          {},
          (res) => {
            if (res.code == 200) {
              if (res.data) {
                this.signatureFrom.formData.companyName = res.data.companyName;
                this.signatureFrom.formData.creditCode = res.data.creditCode;
                this.signatureFrom.formData.legalPerson = res.data.legalPerson;
                this.signatureFrom.formData.principalName = res.data.principalName;
                this.signatureFrom.formData.principalIdCard = res.data.principalIdCard;
              }
            } else {
              this.$message.error(res.msg);
            }
          }
        );
      } catch (error) {
        console.log(error, "err");
      }
    },
    clearPrincipalInfo() {
      this.signatureFrom.formData.companyName = "";
      this.signatureFrom.formData.creditCode = "";
      this.signatureFrom.formData.legalPerson = "";
      this.signatureFrom.formData.principalName = "";
      this.signatureFrom.formData.principalIdCard = "";
      this.signatureFrom.formData.principalMobile = "";
      // if (val == 1) {
      //   this.getRealNameInfo()
      // } else {
      //   this.signatureFrom.formData.companyName = "";
      //   this.signatureFrom.formData.creditCode = "";
      //   this.signatureFrom.formData.legalPerson = "";
      //   this.signatureFrom.formData.principalName = "";
      //   this.signatureFrom.formData.principalIdCard = "";
      // }

    },
    handleEdit() {
      let b = this.$route.query.d;
      //获取上传的图片地址
      this.$api.get(
        this.API.cpus + "signature/findModel?signatureId=" + b,
        {},
        (res) => {
          try {
            let aa = res.data.signature;
            this.signatureFrom.signature = aa.slice(1, aa.length - 1);
            if (res.data.imgUrl) {
              this.fileListS = res.data.imgUrl;
              // console.log(this.fileListS,'fileListS');
              let a = res.data.imgUrl.split(",");
              for (let i = 0; i < a.length; i++) {
                if (a[i] != "") {
                  this.signatureFrom.imgUrl.push(a[i]);
                }
              }
            } else {
              this.fileListS = '';
            }
            //存储签名
            this.signatures = res.data.signature;
            this.signatureFrom.formData.imgUrl =
              this.signatureFrom.imgUrl.join(",");
            Object.assign(this.signatureFrom.formData, res.data);
            this.signatureFrom.formData.signature =
              this.signatureFrom.signature;
            this.signatureFrom.formData.signatureType =
              res.data.signatureType;
          } catch (error) {
            console.log(error, "err");
          }
        }
      );
      // console.log(val);
      // this.$nextTick(()=>{
      //     this.$refs.signatureFrom.resetFields(); //清空表单
      //     Object.assign(this.signatureFrom.formData,val);

      // });
    },
    //返回
    goBack() {
      this.$router.push({ path: "SignatureManagement" });
    },
    //提交表单
    signature_add(formName, _, val) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let str = JSON.stringify(this.signatureFrom.formData);
          let formDates = JSON.parse(str);
          // Object.assign(formDates, this.signatureFrom.formData);
          // delete formDates.direct;
          formDates.signature =
            "【" + this.signatureFrom.formData.signature + "】";
          if (this.statusOf == "添加签名") {
            //新增
            delete formDates.signatureId;
            formDates.auditStatus = val;
            if (!((this.signatureFrom.formData.signatureType == 1 || this.signatureFrom.formData.signatureType == 2) && 
                this.signatureFrom.formData.signatureSubType == 0)) {
              if (this.signatureFrom.formData.imgUrl != "") {
                this.sendRequest(
                  "post",
                  "确定新增签名",
                  this.API.cpus + "signature/saveInfo",
                  formDates
                );
              } else {
                this.$message({
                  message: "请上传图片！",
                  type: "warning",
                });
              }
            } else {
              this.sendRequest(
                "post",
                "确定新增签名",
                this.API.cpus + "signature/saveInfo",
                formDates
              );
            }
          } else {
            //编辑
            //判断是否有图片
            formDates.auditStatus = val; //只要编辑，签名的状态就改为待审核状态
            if (!((this.signatureFrom.formData.signatureType == 1 || this.signatureFrom.formData.signatureType == 2) && 
                this.signatureFrom.formData.signatureSubType == 0)) {
              if (this.signatureFrom.formData.imgUrl != "") {
                this.$confirms.confirmation(
                  "put",
                  "确定修改签名",
                  this.API.cpus + "signature/update",
                  formDates,
                  (res) => {
                    // this.gettableData();
                    // this.SigDialogVisible = false;//隐藏弹窗
                    if (res.code == 200) {
                      this.signatureFrom.formData = {
                        signature: "",
                        signatureType: 1,
                        remark: "",
                        imgUrl: "",
                        signatureId: "",
                      };
                      this.$router.push({ path: "SignatureManagement" });
                    }
                    // this.$router.push({ path: "SignatureManagement" });
                  }
                );
              } else {
                this.$message({
                  message: "请上传图片！",
                  type: "warning",
                });
              }
            } else {
              this.$confirms.confirmation(
                "put",
                "确定修改签名",
                this.API.cpus + "signature/update",
                formDates,
                (res) => {
                  // this.gettableData();
                  // this.SigDialogVisible = false;//隐藏弹窗
                  if (res.code == 200) {
                    this.signatureFrom.formData = {
                      signature: "",
                      signatureType: 1,
                      remark: "",
                      imgUrl: "",
                      signatureId: "",
                    };
                    this.$router.push({ path: "SignatureManagement" });
                  }
                }
              );
            }
            // if (this.signatures == formDates.signature) {

            // }
            // else {
            //   //验证签名是否存在
            //   this.$api.get(
            //     this.API.cpus +
            //       "signature/findModelBySignature?signature=【" +
            //       this.signatureFrom.formData.signature +
            //       "】",
            //     {},
            //     (res) => {
            //       if (res.code == 200 && res.data == "0") {
            //         this.$confirms.confirmation(
            //           "put",
            //           "确定修改签名",
            //           this.API.cpus + "signature/update",
            //           formDates,
            //           (res) => {
            //             this.signatureFrom.formData = {
            //               signature: "",
            //               signatureType: "1",
            //               remark: "",
            //               imgUrl: "",
            //               signatureId: "",
            //             };
            //             this.$router.push({ path: "SignatureManagement" });
            //           }
            //         );
            //       } else {
            //         this.$message({
            //           message: "签名已存在，切勿重复！",
            //           type: "warning",
            //         });
            //       }
            //     }
            //   );
            // }
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //发送编辑和新增的请求
    sendRequest(type, title, action, formDates) {
      //验证签名是否存在
      this.$api.get(
        this.API.cpus +
        "signature/findModelBySignature?signature=【" +
        this.signatureFrom.formData.signature +
        "】",
        {},
        (res) => {
          if (res.code == 200 && res.data == "0") {
            //发送编辑和新增的请求
            this.$confirms.confirmation(
              type,
              title,
              action,
              formDates,
              (ress) => {
                if (ress.code == 200) {
                  this.$router.push({ path: "SignatureManagement" });
                }

              }
            );
          } else {
            this.$message({
              message: "签名已存在，切勿重复！",
              type: "warning",
            });
          }
        }
      );
    },
    //移除文件
    fileup(file, _) {
      if (file.response) {
        this.signatureFrom.imgUrl.splice(this.signatureFrom.imgUrl.indexOf(file.response.data.fullpath), 1);
        this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(",");
      } else {
        this.signatureFrom.imgUrl.splice(this.signatureFrom.imgUrl.indexOf(file.name), 1);
        this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(",");
      }
      // if (val2.length) {
      //   if (
      //     this.signatureFrom.formData.imgUrl.indexOf(
      //       val2[0].response.data.fullpath
      //     ) == -1
      //   ) {
      //     let aa = this.signatureFrom.formData.imgUrl.split(",");
      //     if (val.response) {
      //       aa.splice(aa.indexOf(val.response.fullPath), 1);
      //     } else {
      //       let c = val.url;
      //       let d = c.slice(c.indexOf("group1"));
      //       aa.splice(aa.indexOf(d), 1);
      //     }
      //     this.signatureFrom.imgUrl = aa;
      //     console.log(this.signatureFrom.imgUrl, 'this.signatureFrom.imgUrl');

      //     this.signatureFrom.formData.imgUrl = aa.join(",");
      //   }
      // }else{
      //   this.signatureFrom.formData.imgUrl = "";
      //   this.signatureFrom.imgUrl = [];
      // }

    },
    //文件上传成功
    fileupres(val) {
      if (val && val.data && val.data.fullpath) {
        this.signatureFrom.imgUrl.push(val.data.fullpath);
        this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(",");
      } else {
        console.error('文件上传响应数据格式不正确', val);
        this.$message.error('文件上传失败，请重试');
      }
    },
    handleClose() {
      this.showImgDialogVisible = false;
    },
    showImg() {
      this.showImgDialogVisible = true;
    },
    // showImgDialogVisible() {
    //   this.showImgDialogVisible = false;
    // },
  },
};
</script>
<style lang="less" scoped>
// 引入通用签名样式
@import '~@/styles/signature-common.less';
</style>
