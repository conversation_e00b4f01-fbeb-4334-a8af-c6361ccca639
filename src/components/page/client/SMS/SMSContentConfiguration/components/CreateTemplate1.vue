<template>
  <div class="user-modifi-cation bag">
    <div class="Top_title" style="padding: 10px">
      <span style="
          display: inline-block;
          padding-right: 10px;
          cursor: pointer;
          color: #16a589;
        " @click="goBack()"><i class="el-icon-arrow-left"></i>返回</span>|
      <span> {{ statusOf }}</span>
      <!-- <el-button class="el-icon-sort" style="margin-left: 10px;" size="mini" type="primary"
        @click="handelNewTemp">切换旧版添加模板</el-button> -->
    </div>
    <div class="fillet">
      <div>
        <el-form :model="form" :rules="temFormRules" label-width="100px" style="padding-right: 115px" ref="temForm">
          <el-form-item label="模板类型" prop="resource" style="margin-bottom: 0px">
            <el-radio-group v-model="form.temType" :disabled="editTemDialog_status == 0" @change="changeTemType">
              <el-radio :label="1">验证码</el-radio>
              <el-radio :label="2">行业通知</el-radio>
              <el-radio :label="3">会员营销</el-radio>
            </el-radio-group>
            <template>
              <p v-if="form.temType == '1'" style="font-size: 10px">
                适用于注册、登录、找回密码、身份验证、号码绑定身份验证的短信模板
              </p>
              <p v-if="form.temType == '2'" style="font-size: 10px">
                可发告知类短信，例如：快递通知、消费通知、账单、服务通知；含有优惠折扣、促销、推广、超链接、抢购、购物劵等营销内容，会导致审核失败，请联系客服
              </p>
              <p v-if="form.temType == '3'" style="font-size: 10px">
                可用于会员服务，内容可带链接，建议转换成短链接发送。
              </p>
            </template>
          </el-form-item>
          <el-form-item label="模板名称" prop="temName">
            <el-input v-model="form.temName" :disabled="editTemDialog_status == 0"
              placeholder="请填写模板名称，名称只做标识作用"></el-input>
          </el-form-item>
          <el-form-item label="选择签名" prop="signId">
            <el-select v-model="form.signId" placeholder="请选择签名" class="input-w" clearable filterable @change="changeLocationValue">
              <el-option v-for="(item, index) in labelName" :label="item.signature" :value="item.signature"
                :key="index"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="短信内容" prop="temContent">
            <div style="position: relative;">
              <el-input type="textarea" :placeholder="placeholderText" v-model="form.temContent" @input="handelInput"
                maxlength="800" show-word-limit>
              </el-input>
              <el-tooltip class="item" effect="light" content="变量格式：{name}；变量限制：数字+字母组合或仅字母2种类型" placement="top">
                <i style="position: absolute; top: 10px; left: 100.5%" class="el-icon-warning"></i>
              </el-tooltip>
            </div>
            <div class="tem-be-careful">
              <div class="tem-font" style="display:inline-block;">当前模板 预计发送条数约为<span> {{ numTextMsg2 }} </span>条短信</div>
              <div class="tem-font" style="display:inline-block;color:red">（如果此内容包含变量，以实际下发条数为准）</div>
            </div>
            <div>
              <el-button type="primary" @click="shortReset()" class="template-btn-1" style="padding: 9px 21px"
                v-if="form.temType == '3'">短链转换</el-button>
              <el-checkbox v-if="form.temType != '1'" style="margin-left: 10px" @change="handAutoSwitch"
                v-model="form.autoSwitch">是否自动转短链</el-checkbox>
              <el-checkbox v-if="form.temType == '3'" style="margin-left: 10px" @change="handleQuite"
                v-model="form.checked">拒收请回复R</el-checkbox>
            </div>
            <div v-if="form.autoSwitch" style="display: flex; margin: 10px 0">
              <span style="display: inline_block; width: 45px">长链接</span>
              <el-tooltip class="woring" effect="light" content="请检查长链接是否含有多余字符，确保链接能正常打开！" placement="top">
                <i style="color: red" class="el-icon-warning"></i>
              </el-tooltip>
              <el-input v-model="form.longUrl" placeholder="请输入内容"></el-input>
            </div>
          </el-form-item>
          <div>
            <template v-for="(item, index) in getcode">
              <el-form-item :index="index" :key="index" label="已选变量" class="slectcode">
                <el-input style="width: 280px" v-model="item.value" disabled @input="testchange(item, index)"></el-input>
                <el-select v-model="item.codeName" @change="typeChange($event, item)" style="width: 280px"
                  placeholder="请选择参数内容">
                  <el-option label="验证码" v-if="form.temType == 1" value="valid_code"></el-option>
                  <el-option label="中文汉字" v-if="form.temType != 1" value="chinese"></el-option>
                  <el-option label="电话号码" v-if="form.temType != 1" value="mobile_number"></el-option>
                  <el-option label="金额" v-if="form.temType != 1" value="amount"></el-option>
                  <el-option label="时间" v-if="form.temType != 1" value="date"></el-option>
                  <el-option label="其他号码" v-if="form.temType != 1" value="other_number"></el-option>
                  <el-option label="其他" v-if="form.temType != 1" value="others"></el-option>
                </el-select>
                <el-tooltip effect="dark" content="变量名称允许数字+字母组合或仅字母2种类型" placement="top">
                  <i class="el-icon-question" v-if="index == 0" style="margin-left: 5px;"></i>
                </el-tooltip>
              </el-form-item>
            </template>
          </div>
          <el-form-item label="申请说明" prop="remark">
            <template v-if="form.temType == '1'">
              <el-input rows="4" resize="none" placeholder="用于我公司app用户注册时发送验证码" type="textarea"
                v-model="form.remark"></el-input>
            </template>
            <template v-if="form.temType == '2'">
              <el-input rows="4" resize="none" placeholder="用于行业通知类短信发送" type="textarea" v-model="form.remark"></el-input>
            </template>
            <template v-if="form.temType == '3'">
              <el-input rows="4" resize="none" placeholder="用户会员营销类短信发送" type="textarea" v-model="form.remark"></el-input>
            </template>
            <el-tooltip class="item" effect="light" content="为加快审核速度与模板通过率，请填写申请该模板的原因或说明" placement="top">
              <i style="position: relative; top: -95px; left: 100.5%" class="el-icon-warning"></i>
            </el-tooltip>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer" style="margin-left: 80px">
          <el-button type="primary" @click="addTemOk(statusOf, '1')">提交审核</el-button>
          <el-button type="primary" @click="addTemOk(statusOf, '0')">保存但不提交</el-button>
          <el-button @click="calcelTem()">取 消</el-button>
        </span>
      </div>
      <el-dialog title="短链转换" :visible.sync="temshortVisible" width="520px">
        <div class="short-box">
          <p class="short-title" style="padding-top: 10px">长网址链接</p>
          <el-input placeholder="请输入长链接" v-model="originalUrl" class="width-l">
            <el-button slot="append" type="primary" icon="el-icon-refresh" @click="transformation()">转换</el-button>
          </el-input>
          <div class="font-sizes font-sizes1">
            <span style="color: red">* </span>我们可以帮您把长链接压缩，让您可以输入更多的内容。
          </div>
          <div class="font-sizes">
            <span style="color: red">* </span>插入短信内容中时，将在链接前后生成空格符号，以防止出现手机短信客户端不识别链接的情况。
          </div>
        </div>
        <div class="short-box">
          <p class="short-title" style="padding-top: 20px">短网址链接</p>
          <el-input v-model="shortConUrl" class="width-l" :disabled="true">
            <el-button slot="append" type="primary" @click="handlePreview()" icon="el-icon-share">预览</el-button>
          </el-input>
        </div>
        <div style="text-align: right; margin-top: 16px">
          <el-button @click="handleCancles()">取 消</el-button>
          <el-button type="primary" @click="shortConDetermine()">确 定</el-button>
        </div>
      </el-dialog>
      <div class="Signature-matter">
        <p style="font-weight: bolder; font-size: 14px">内容规范：</p>
        <p>
          • 内容中必须带有<span style="color: #ff0000">【签名】</span>，签名内容可以是公司名称或产品名称，字数要求<span
            style="color: #ff0000">2-16</span>个字；
        </p>
        <p>
          •
          签名只能选择审核通过的签名；如没有签名，请重新添加签名提交审核；内容首位不能添加<span style="color: #ff0000">【】</span>；
        </p>
        <p>
          • 内容合法，不能发送<span style="color: #ff0000">房产、发票、移民、黄、赌、毒</span>犯法等国家法律法规严格禁止的内容；
        </p>
        <p>
          •
          超链接地址请写在短信内容中，便于进行核实，部分安卓系统存在超链接识别问题，需在超链接前后添加空格；
        </p>
        <p style="font-weight: bolder; font-size: 14px">变量类型</p>
        <p>• 验证码：{valid_code}， <span style="color:red">4-6位数字英文混合，支持英文大小写。</span> </p>
        <p>• 电话号码：{mobile_number}， <span style="color:red">1-15位纯数字。</span> </p>
        <p>• 其他号码：{other_number}， <span style="color:red">1-32位字母+数字组合,支持中划线-。</span> </p>
        <p>
          • 金额：{amount}， <span style="color:red">支持数字（含英文小数点.）或数字的中文
            （壹贰叁肆伍陆柒捌玖拾佰仟万亿 圆元整角分厘毫）。</span>
        </p>
        <p>
          • 时间：{date}， <span style="color:red">符合时间的表达方式
            也支持中文：2019年9月3日16时24分35秒。</span>
        </p>
        <p>• 中文汉字：{chinese}， <span style="color:red">1-32中文，支持中文圆括号（）。</span> </p>
        <p>• 其他：{others}， <span style="color:red">1-35个中文数字字母组合,支持中文符号和空格。</span> </p>
        <p style="font-weight: bolder; font-size: 14px">计费规则：</p>
        <p>
          • 短信字数<span style="color: #ff0000"> &lt;=70个字</span>，按照<span
            style="color: #ff0000">70个字一条</span>短信计算，中文、英文、符号统一计算一个字符；
        </p>
        <p>
          • 短信字数<span style="color: #ff0000">>70个字</span>，按照<span style="color: #ff0000">67个字一条</span>短信计算，其中3个字占用为分条字符；
        </p>
        <p style="font-weight: bolder; font-size: 14px">审核规则：</p>
        <p>
          •
          工作日的9点至21点，每30分钟审核一次，休息日（含假期）的每天9-18点，每1小时审核一次；
        </p>
        <p>
          • 如果出现批量短信发送被驳回的情况，可能为<span style="color: #ff0000">"敏感词拦截"</span>进入人工审核，请联系客服处理
        </p>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: "NewCreateTemplate",
  data() {
    // 模板名称校验规则
    const checkName = (rule, value, callback) => {
      if (this.statusOf != "添加模板") {
        callback();
        return;
      }
      
      if (!value || (value && (value.length > 20 || value.length < 1))) {
        return callback(new Error("长度在 1 到 20 个字符"));
      }
      
      const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+$/g;
      if (!reg.test(value)) {
        return callback(new Error("不允许输入空格等特殊符号"));
      }
      
      this.$api.post(
        this.API.cpus + "v3/consumersmstemplate/validateUserTempExist/",
        { temName: value },
        (res) => {
          if (res.data == "0") {
            callback();
          } else {
            return callback(new Error("此模板已存在"));
          }
        }
      );
    };

    // 校验变量格式
    const paramValid = (rule, value, callback) => {
      if (!value) {
        callback();
        return;
      }
      
      const list = value.match(/{(.+?)}/g);
      if (!list || list.length === 0) {
        callback();
        return;
      }
      
      for (let i = 0; i < list.length; i++) {
        const checkItem = list[i].replace("{", "").replace("}", "");
        if (checkItem.indexOf("{") !== -1) {
          callback("变量中不能含有{字符");
          return;
        }
      }
      callback();
    };

    return {
      statusOf: "", // 是新增还是编辑
      rowDatas: "",
      editTemDialog_status: 1, // 模板是否是新增---1为新增，0为编辑
      labelName: [], // 签名列表
      oldTemType: 0, // 保存原模板类型，用于切换回退
      getcode: [], // 变量列表
      getcodes: [],
      temshortVisible: false, // 短链弹框
      originalUrl: "", // 长链接的值
      shortConUrl: "", // 短连接的值
      shortCode: "", // 短链的code码
      isShort: false, // 是否短链转换
      valid_code: "",
      signatureTit: "",
      paramsList: [],
      number: null,
      oldNumber: null,
      loadingcomp: false,
      textArr: [],
      placeholderText: "请输入短信内容，如需要变量例如：{name}",
      // 表单数据
      form: {
        params: {}, // 模板变量
        autoSwitch: false, // 是否自动切换短链 false：否, true：是
        longUrl: "", // 长链接
        submit: "", // 是否提交审核 0：否, 1：是
        temFormat: "1", // 1.变量模板；2是全文模板
        signId: "", // 签名
        temName: "", // 模板名称
        temContent: "", // 模板内容
        temType: 1, // 模板类型
        shortCode: "", // 短链Code
        remark: "", // 申请说明
        checked: false // 是否包含拒收回复R
      },
      // 表单验证规则
      temFormRules: {
        signId: [
          { required: true, message: "请选择签名", trigger: "change" }
        ],
        temName: [
          { required: true, validator: checkName, trigger: ["change", "blur"] }
        ],
        temType: [
          { required: true, message: "请选择模板类型", trigger: "change" }
        ],
        temContent: [
          { required: true, message: "请输入模板内容", trigger: "change" },
          { validator: paramValid, trigger: "change" }
        ],
        remark: [
          {
            min: 1,
            max: 200,
            message: "长度在 1 到 200个字符",
            trigger: "blur"
          }
        ]
      },
      timmer: null, // 防抖定时器
      arrStorage: [] // 存储变量信息
    };
  },
  created() {
    if (this.$route.query.i == "1") {
      this.statusOf = "添加模板";
    } else {
      this.statusOf = "编辑模板";
      this.getTem();
    }
  },
  methods: {
    // 获取模板信息
    getTem() {
      this.$api.post(
        this.API.cpus + "v3/consumersmstemplate/page",
        {
          param: this.$route.query.param,
          currentPage: 1,
          pageSize: 200,
        },
        (res) => {
          if (!res.records || res.records.length === 0) {
            this.$message.error("获取模板信息失败");
            return;
          }
          
          const row = res.records[0];
          this.rowDatas = row;
          
          // 提取签名
          const regexCenter = /\【(.+?)\】/g;
          const sings = row.temContent.match(regexCenter);
          
          if (!sings || sings.length === 0) {
            this.$message.warning("模板内容中未找到签名");
          }
          
          // 解析模板参数
          try {
            this.form.params = JSON.parse(row.text);
          } catch (e) {
            console.error("解析模板参数失败", e);
            this.form.params = {};
          }
          
          // 设置表单数据
          this.form.temName = row.temName;
          this.form.temType = row.temType;
          this.form.signId = sings ? sings[0] : "";
          this.form.temContent = row.temContent;
          this.form.temId = row.temId;
          this.form.remark = row.remark;
          this.form.temFormat = row.temFormat;
          
          // 处理变量列表
          // if (row.temType != 3) {
          //   this.getcode = this.getNewCodeList();
          // } else {
          //   this.getcode = [];
          // }
          this.getcode = this.getNewCodeList();
          // 设置其他可选字段
          if (row.autoSwitch) {
            this.form.autoSwitch = row.autoSwitch;
          }
          if (row.longUrl) {
            this.form.longUrl = row.longUrl;
          }
          if (row.shortCode) {
            this.form.shortCode = row.shortCode;
          }
        }
      );
    },

    // 切换模板类型
    changeTemType(val) {
      if (!val) return;
      
      this.$alert('切换模板会清除刚刚添加的参数以及内容!', '提示', {
        confirmButtonText: '确定',
        callback: action => {
          if (action == 'confirm') {
            // 更新模板格式
            // if (val == '3') {
            //   this.form.temFormat = '2';
            //   this.placeholderText = '请输入短信内容';
            // } else {
            //   this.form.temFormat = '1';
            //   this.placeholderText = '请输入短信内容，如需要变量例如：{name}';
            // }
            // this.form.temFormat = '2';
            this.placeholderText = '请输入短信内容，如需要变量例如：{name}';
            // 重置表单
            this.form = {
              params: {}, // 模板变量
              autoSwitch: false, // 是否自动切换短链 false：否, true：是"
              longUrl: "", // 长链接
              submit: "", // 是否提交审核 0：否, 1：是"
              temFormat: 1, // 1.变量模板；2是全文模板
              signId: "", // 签名
              temName: "", // 模板名称
              temContent: "", // 模板内容
              temType: val, // 模板类型
              shortCode: "", // 短链Code
              remark: "",
              checked: false
            };
            this.getcode = [];
          } else {
            // 恢复原来的模板类型
            this.form.temType = this.oldTemType;
          }
        }
      });
    },

    // 获取新的代码列表
    getNewCodeList() {
      // 构建变量对象数组
      let objNew = [];
      for (let i in this.form.params) {
        objNew.push({ value: i, codeName: this.form.params[i] });
      }
      
      // 匹配内容中的变量
      const compareArr = this.form.temContent.match(/{(.+?)}/g) || [];
      const result = [];
      
      // 提取变量名
      const extractedVariables = compareArr.map(item => 
        item.replace("{", "").replace("}", "")
      );
      
      // 已存在的变量名
      const existingValues = objNew.map(item => item.value);
      
      // 处理每个变量
      extractedVariables.forEach(varName => {
        const existingVarIndex = existingValues.indexOf(varName);
        
        if (existingVarIndex === -1) {
          // 新变量
          result.push({
            value: varName,
            codeName: ""
          });
        } else {
          // 已有变量
          result.push({
            value: varName,
            codeName: objNew[existingVarIndex].codeName
          });
        }
      });
      
      return result;
    },

    // 切换签名
    changeLocationValue(val) {
      this.signatureTit = val;
    },

    // 获取全部用户签名
    getpassLabels() {
      this.$api.post(
        this.API.cpus + "signature/signatureList",
        {
          auditStatus: "2",
          currentPage: 1,
          pageSize: 200,
        },
        (res) => {
          if (res && res.records) {
            this.labelName = res.records;
          }
        }
      );
    },

    // 返回
    goBack() {
      this.$router.push("/TemplateManagement");
    },

    // 切换到旧版模板
    handelNewTemp() {
      const { i, param } = this.$route.query;
      if (i) {
        this.$router.push({ path: '/CreateTemplate', query: { i: "1" } });
      } else {
        this.$router.push({ path: '/CreateTemplate', query: { param } });
      }
    },

    // 处理输入内容
    handelInput(newVal) {
      // 检查"拒收请回复R"
      const reg = new RegExp("拒收请回复R");
      if (this.form.temType == '3' && reg.test(newVal)) {
        this.form.checked = true;
      } else {
        this.form.checked = false;
      }

      // 匹配变量
      const regex3 = /{(.+?)}/g; // 匹配花括号内的内容
      const regex4 = /{(.+?)}{(.+?)}/g; // 匹配连续的变量
      const arr = newVal.match(regex3);

      // 检查变量是否有重复
      const isRepeat = (arr) => {
        const hash = {};
        for (let i in arr) {
          if (hash[arr[i]]) {
            return true;
          }
          hash[arr[i]] = true;
        }
        return false;
      };

      // 验证码模板处理
      if (this.form.temType == 1) {
        // this.form.temFormat = "1";
        
        if (!arr) {
          this.getcode = [];
          return;
        }
        
        if (arr.length > 1) {
          // 验证码类型只能有一个变量
          if (!this.timmer) {
            this.$message({
              type: "warning",
              message: "验证码类型的参数只能添加一个，不能重复添加！"
            });
            this.timmer = setTimeout(() => {
              this.timmer = null;
            }, 2000);
          }
        } else {
          // 保存当前代码设置
          const saveArr = JSON.parse(JSON.stringify(this.getcode));
          
          // 更新变量列表
          this.getcode = arr.map(item => ({
            value: item.replace("{", "").replace("}", ""),
            codeName: ""
          }));
          
          // 恢复之前的变量类型设置
          this.getcode.forEach(gcItem => {
            saveArr.forEach(taItem => {
              if (gcItem.value === taItem.value) {
                gcItem.codeName = taItem.codeName;
              }
            });
          });
        }
      } 
      // 行业通知模板处理
      else {
        // this.form.temFormat = "1";
        
        if (!arr) {
          this.getcode = [];
          return;
        }
        
        if (arr.length > 16) {
          // 变量数量限制
          this.$message({
            type: "warning",
            message: "变量模板参数不能超过16个"
          });
        } else if (isRepeat(arr)) {
          // 变量名不能重复
          this.$message({
            message: "已选变量名称不能重复",
            type: "warning"
          });
        } else if (regex4.test(newVal)) {
          // 变量不能连续
          if (!this.timmer) {
            this.$message({
              type: "warning",
              message: "两个参数不可挨在一起，中间至少有一个字符隔开。"
            });
            this.timmer = setTimeout(() => {
              this.timmer = null;
            }, 2000);
          }
          this.getcode = [];
        } else {
          // 正常处理变量
          if (this.statusOf == "添加模板") {
            // 保存当前代码设置
            const saveArr = JSON.parse(JSON.stringify(this.getcode));
            
            // 更新变量列表
            this.getcode = arr.map(item => ({
              value: item.replace("{", "").replace("}", ""),
              codeName: ""
            }));
            
            // 恢复之前的变量类型设置
            this.getcode.forEach(gcItem => {
              saveArr.forEach(taItem => {
                if (gcItem.value === taItem.value) {
                  gcItem.codeName = taItem.codeName;
                }
              });
            });
          } else {
            // 编辑模板场景
            this.paramsList = arr.map(item => ({
              value: item.replace("{", "").replace("}", ""),
              codeName: ""
            }));
            
            this.number = this.paramsList.length;
            
            // 比较变量列表
            const compareParamsArr = this.paramsList.map(item => item.value);
            const compareCodeArr = [];
            
            this.textArr = [];
            this.getcode.forEach(item => {
              this.textArr.push(item.value);
              compareCodeArr.push(item.value.replace("{", "").replace("}", ""));
            });
            
            // 移除不再存在的变量
            for (let i = compareCodeArr.length - 1; i >= 0; i--) {
              if (compareParamsArr.indexOf(compareCodeArr[i]) === -1) {
                this.getcode.splice(i, 1);
              }
            }
            
            // 更新变量列表
            this.getcode = this.getNewCodeList();
          }
        }
      } 
      // 会员营销模板处理
      // else {
      //   this.form.temFormat = "2";
      // }
    },

    // 变量类型变更
    typeChange(value, item) {
      if (value && item.value) {
        this.arrStorage.forEach(asItem => {
          if (asItem.value === item.value) {
            asItem.codeName = value;
          }
        });
      }
    },

    // 测试变量变更
    testchange(item, index) {
      // 占位方法，目前未使用
    },

    // 打开短链转换对话框
    shortReset() {
      this.temshortVisible = true;
    },

    // 长链转换为短链
    transformation() {
      if (!this.originalUrl) {
        this.$message({
          message: "长链接不可为空",
          type: "warning"
        });
        return;
      }
      
      this.$api.post(
        this.API.slms + "v3/shortLink/add",
        { originalUrl: this.originalUrl },
        (res) => {
          if (res.code == 200) {
            this.shortConUrl = res.data.shortLinkUrl;
            this.shortCode = res.data.shortCode;
            this.$message({
              message: "短链接转换成功！",
              type: "success"
            });
          } else {
            this.originalUrl = "";
            this.$message({
              message: res.msg,
              type: "warning"
            });
          }
        }
      );
    },

    // 预览短链
    handlePreview() {
      if (this.shortConUrl) {
        window.open(this.shortConUrl, "_blank");
      } else {
        this.$message({
          message: "短连接为空，无法预览",
          type: "warning"
        });
      }
    },

    // 确认使用短链
    shortConDetermine() {
      if (this.shortConUrl) {
        this.form.temContent += this.shortConUrl;
        this.isShort = true;
        this.temshortVisible = false;
      } else {
        this.$message({
          message: "短链接不可为空",
          type: "warning"
        });
      }
    },

    // 取消短链转换
    handleCancles() {
      this.temshortVisible = false;
      this.isShort = false;
    },

    // 自动识别链接
    handAutoSwitch(val) {
      if (val) {
        this.$api.post(
          this.API.cpus + "v3/consumersmstemplate/longUrl",
          {
            temContent: this.form.temContent
          },
          (res) => {
            if (res.data) {
              this.form.longUrl = res.data;
            }
          }
        );
      } else {
        this.form.longUrl = "";
      }
    },

    // 处理拒收回复
    handleQuite(val) {
      if (val) {
        if (!this.form.temContent.includes("，拒收请回复R")) {
          this.form.temContent = this.form.temContent + "，拒收请回复R";
        }
      } else {
        const reg = new RegExp("，拒收请回复R");
        this.form.temContent = this.form.temContent.replace(reg, "");
      }
    },

    // 提交模板
    addTemOk(val, val2) {
      this.$refs.temForm.validate((valid) => {
        if (!valid) return;
        
        // 验证签名匹配
        const matches = this.form.temContent.match(/【.*?】/g);
        if (!matches || matches.length === 0) {
          this.$message({
            message: '模板内容中未找到签名！',
            type: 'warning'
          });
          return;
        }
        
        if (this.form.signId !== matches[0]) {
          this.$message({
            message: '选择的签名与模板内容签名不匹配！',
            type: 'warning'
          });
          return;
        }
        
        // 验证营销类短信是否包含拒收回复R
        const reg = new RegExp("拒收请回复R");
        if (this.form.temType == '3' && !reg.test(this.form.temContent)) {
          this.$message({
            message: '请勾选拒收请回复为R!',
            type: 'warning'
          });
          return;
        }
        
        // 准备变量参数
        this.form.params = {};
        for (let i = 0; i < this.getcode.length; i++) {
          const varName = this.getcode[i].value.replace("{", "").replace("}", "");
          this.form.params[varName] = this.getcode[i].codeName;
        }
        
        // 设置提交状态
        this.form.submit = val2;
        
        // 验证变量参数是否都有类型
        let showToast = false;
        this.getcode.forEach(item => {
          if (!item.codeName) {
            showToast = true;
          }
        });
        
        if (showToast) {
          this.$message({
            message: '参数内容不能为空!',
            type: 'warning'
          });
          return;
        }
        if(this.getcode.length == 0) {
          this.form.temFormat = 2;
        }else{
          this.form.temFormat = 1;
        }
        
        // 选择请求路径
        let requestUrl = this.$route.query.param 
          ? "v3/consumersmstemplate/template/update" 
          : "v3/consumersmstemplate/template/add";
          
        if (this.$route.query.param) {
          this.form.temId = this.$route.query.param;
        }
        
        // 提交请求
        this.$confirms.confirmation(
          "post",
          "确认新增模板？",
          this.API.cpus + requestUrl,
          this.form,
          (res) => {
            if (res.code == 200) {
              this.goBack();
            }
          }
        );
      });
    },

    // 取消
    calcelTem() {
      this.$router.push("/TemplateManagement");
    },

    // 计算字符数
    countNum1(val) {
      if (!val) return 0;
      return val.length;
    }
  },
  computed: {
    // 弹窗标题
    editTemDialog_title() {
      return this.editTemDialog_status == "1" ? "新增短信模板" : "编辑短信模板";
    },
    
    // 短信条数
    numTextMsg2() {
      if (this.wordCount2 === 0) return 0;
      
      if (this.wordCount2 <= 70) {
        return 1;
      } else {
        return Math.ceil(this.wordCount2 / 67);
      }
    },
    
    // 字数统计
    wordCount2() {
      if (!this.form.temContent) return 0;
      
      if (this.form.temContent === " ") return 0;
      
      if (this.form.temContent.length === 2 && this.form.temContent.indexOf(" ") !== -1) {
        return 1;
      }
      
      return this.countNum1(this.form.temContent);
    }
  },
  mounted() {
    this.getpassLabels(); // 获取签名
  },
  watch: {
    // 监听签名变化
    "form.signId": {
      handler(newVal, oldVal) {
        // if (!newVal || !oldVal) return;
        
        this.form.temContent = this.signatureTit + this.form.temContent;
        this.form.temContent = this.form.temContent.replace(oldVal, "");
      }
    },
    
    // 保存原模板类型
    'form.temType': {
      handler(newVal, oldVal) {
        this.oldTemType = oldVal;
      },
      immediate: true
    },
    
    // 监听短链弹框关闭
    temshortVisible(val) {
      if (val === false) {
        this.originalUrl = ""; // 清空长链接
        this.shortConUrl = ""; // 清空短链接
      }
    },
    
    // 监听变量列表变化
    getcode: {
      handler(newVal, oldVal) {
        if (!newVal || newVal.length === 0) return;
        
        newVal.forEach(item => {
          let pushFlag = true;
          
          this.arrStorage.forEach(asItem => {
            if (item.value === asItem.value) {
              item.codeName = asItem.codeName;
              pushFlag = false;
            }
          });
          
          if (pushFlag) {
            this.arrStorage.push(item);
          }
        });
      },
      immediate: true,
      deep: true
    }
  }
};
</script>

<style lang="less" scoped>
.user-modifi-cation {
  padding: 10px;
}

.fillet {
  border-radius: 5px;
  background-color: #fff;
  padding: 15px;
}

.Top_title {
  display: flex;
  align-items: center;
  font-weight: bold;
}

.template-btn-1 {
  transition: all 0.3s;
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  }
}

.tem-be-careful {
  margin: 10px 0;
  padding: 8px;
  background-color: #f8f8f8;
  border-radius: 4px;
  
  .tem-font {
    font-size: 12px;
    line-height: 1.5;
    margin-right: 5px;
    
    span {
      font-weight: bold;
      color: #409EFF;
    }
  }
}

.Signature-matter {
  margin-top: 50px;
  border: 1px solid #66ccff;
  margin-left: 80px;
  margin-right: 115px;
  padding: 14px;
  border-radius: 5px;
  font-size: 12px;
  line-height: 1.6;
  background-color: #f9fdff;
  
  > p {
    padding: 5px 0;
    margin: 0;
  }
}

.woring {
  margin: 8px;
}

.short-box {
  margin-bottom: 15px;
  
  .short-title {
    font-weight: bold;
    margin-bottom: 8px;
  }
  
  .font-sizes {
    font-size: 12px;
    color: #606266;
    margin-top: 8px;
    line-height: 1.4;
  }
}

.slectcode {
  // display: flex;
  // align-items: center;
}

/deep/ .el-textarea__inner {
  height: 100px;
  resize: vertical;
}

/deep/ .el-radio-group {
  margin-bottom: 8px;
}

/deep/ .el-select {
  width: 100%;
}

/deep/ .width-l {
  width: 100%;
}

/deep/ .el-tooltip {
  cursor: pointer;
}
</style>

<style>
/* 全局样式 */
.el-dialog__body {
  padding: 20px;
}

.el-form-item__label {
  font-weight: 500;
}
</style>