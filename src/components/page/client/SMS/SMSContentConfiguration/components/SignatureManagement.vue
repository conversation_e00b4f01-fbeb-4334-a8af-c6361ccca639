<template>
  <div class="simple-signature-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 简约提醒区域 -->
        <div class="notice-section">
          <h3 class="notice-title">温馨提醒</h3>
          <div class="notice-list">
            <div class="notice-item">
              <span class="notice-label">短信组成：</span>一个完整的短信由短信签名和短信正文内容两部分组成，您可以根据业务需求分别设置不同的短信正文内容模板，然后进行组合形成最终展示。短信签名+短信正文内容=最终显示内容。
            </div>
            <div class="notice-item">
              <span class="notice-label">审核时间：</span>签名提交审核，工作日预计2小时内完成，非工作日预计4小时内完成。审核时间：周一至周日9:30-22:00（法定节假日顺延）。
            </div>
            <div class="notice-item">
              <span class="notice-label">通知设置：</span>您可设置常用手机和邮箱，用于即时接收该应用短信内容审核通知。
            </div>
          </div>
        </div>

        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button @click="clickAddSig" class="action-btn primary" icon="el-icon-plus">
                  创建签名
                </el-button>
                <el-button @click="exportNums1" class="action-btn" icon="el-icon-download">
                  导出
                </el-button>
                <el-button @click="batchQu" class="action-btn" icon="el-icon-upload2">
                  批量导入
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ tableDataObj.total }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">当前第 {{ tableDataObj.tablecurrent.currentPage }} 页</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form :model="tableDataObj.tablecurrent" :inline="true" ref="tablecurrent" class="advanced-search-form">
                <div class="search-row">
                  <el-form-item label="签名内容" prop="signature" class="search-item">
                    <el-input
                      v-model="tableDataObj.tablecurrent.signature"
                      placeholder="请输入签名内容"
                      class="search-input"
                      clearable
                      prefix-icon="el-icon-search"
                    />
                  </el-form-item>

                  <el-form-item label="审核状态" prop="auditStatus" class="search-item">
                    <el-select
                      v-model="tableDataObj.tablecurrent.auditStatus"
                      placeholder="全部状态"
                      class="search-select"
                      clearable
                    >
                      <el-option label="编辑中" value="0"></el-option>
                      <el-option label="待审核" value="1"></el-option>
                      <el-option label="审核通过" value="2"></el-option>
                      <el-option label="审核不通过" value="3"></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="实名信息" prop="isComplete" class="search-item">
                    <el-select
                      v-model="tableDataObj.tablecurrent.isComplete"
                      placeholder="全部状态"
                      class="search-select"
                      clearable
                    >
                      <el-option label="已完善" :value="true"></el-option>
                      <el-option label="未完善" :value="false"></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item class="search-buttons">
                    <el-button type="primary" @click="submitForm('tablecurrent')" class="search-btn primary" icon="el-icon-search">
                      查询
                    </el-button>
                    <el-button @click="resetForm('tablecurrent')" class="search-btn" icon="el-icon-refresh">
                      重置
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </div>
        </div>

        <!-- 签名列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">签名列表</h3>
          </div>

          <div class="table-container">
            <el-table
              v-loading="tableDataObj.loading2"
              element-loading-text="正在加载..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.8)"
              ref="multipleTable"
              border
              :data="tableDataObj.tableData"
              class="enhanced-table"
              stripe
              :header-cell-style="{ background: '#fafafa', color: '#333' }"
              :row-style="{ height: '60px' }"
              empty-text="暂无签名数据"
            >
              <el-table-column prop="signatureId" label="ID" width="90">
                <template slot-scope="scope">
                  {{ scope.row.signatureId }}
                </template>
              </el-table-column>

              <el-table-column label="签名" width="200">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span>{{ scope.row.signature }}</span>
                    <span v-if="scope.row.auditReason == null"></span>
                    <span v-else-if="scope.row.auditReason == ''"></span>
                    <span v-else-if="scope.row.auditReason == '审核通过'"></span>
                    <span v-else-if="scope.row.auditStatus == '3'" style="color: #f56c6c">( 驳回原因：{{ scope.row.auditReason }} )</span>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="短信示例" width="300">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <Tooltip v-if="scope.row.contentExample" :content="scope.row.contentExample" className="wrapper-text" effect="light">
                    </Tooltip>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="审核状态" width="120">
                <template slot-scope="scope">
                  <el-tag effect="light" type="info" v-if="scope.row.auditStatus == '0'">编辑中</el-tag>
                  <el-tag effect="light" type="warning" v-else-if="scope.row.auditStatus == '1'">待审核</el-tag>
                  <el-tag effect="light" type="success" v-else-if="scope.row.auditStatus == '2'">审核通过</el-tag>
                  <el-tag effect="light" type="danger" v-else-if="scope.row.auditStatus == '3'">审核不通过</el-tag>
                </template>
              </el-table-column>

              <el-table-column label="是否已完善实名信息" width="140">
                <template slot-scope="scope">
                  <el-tag effect="light" type="success" v-if="scope.row.reportId">已完善</el-tag>
                  <el-tag effect="light" type="warning" v-else>未完善</el-tag>
                </template>
              </el-table-column>

              <el-table-column label="实名状态" width="140">
                <template slot-scope="scope">
                  <div style="display: flex">
                    <div>
                      <el-tooltip v-if="scope.row.ydRealNameStatus == 0" class="item" effect="dark" content="未实名" placement="top-start">
                        <i class="iconfont icon-yidong" style="font-size: 20px"></i>
                      </el-tooltip>
                      <el-tooltip v-if="scope.row.ydRealNameStatus == 1" class="item" effect="dark" content="已实名" placement="top-start">
                        <i class="iconfont icon-yidong" style="color: #409eff; font-size: 20px"></i>
                      </el-tooltip>
                    </div>
                    <div style="margin: 0 10px">
                      <el-tooltip v-if="scope.row.ltRealNameStatus == 0" class="item" effect="dark" content="未实名" placement="top-start">
                        <i class="iconfont icon-liantong" style="font-size: 20px"></i>
                      </el-tooltip>
                      <el-tooltip v-if="scope.row.ltRealNameStatus == 1" class="item" effect="dark" content="已实名" placement="top-start">
                        <i class="iconfont icon-liantong" style="color: #409eff; font-size: 20px"></i>
                      </el-tooltip>
                    </div>
                    <div>
                      <el-tooltip v-if="scope.row.dxRealNameStatus == 0" class="item" effect="dark" content="未实名" placement="top-start">
                        <i class="iconfont icon-dianxin" style="font-size: 20px"></i>
                      </el-tooltip>
                      <el-tooltip v-if="scope.row.dxRealNameStatus == 1" class="item" effect="dark" content="已实名" placement="top-start">
                        <i class="iconfont icon-dianxin" style="color: #409eff; font-size: 20px"></i>
                      </el-tooltip>
                    </div>
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="实名状态原因" width="140">
                <template slot-scope="scope">
                  <div v-if="scope.row.ydReason && (scope.row.auditStatus == '2' || scope.row.auditStatus == '3')">
                    {{ scope.row.ydReason }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="申请时间" width="180">
                <template slot-scope="scope">
                  {{ scope.row.createTime | fmtDate }}
                </template>
              </el-table-column>

              <el-table-column label="首次通过时间" width="180">
                <template slot-scope="scope">
                  <div v-if="scope.row.firstAuditTime">
                    {{ scope.row.firstAuditTime | fmtDate }}
                  </div>
                </template>
              </el-table-column>

              <el-table-column label="操作" width="220" fixed="right">
                <template slot-scope="scope">
                  <div class="table-actions">
                    <el-tooltip content="编辑签名" placement="top" v-if="scope.row.auditStatus == '0' || scope.row.auditStatus == '3'">
                      <el-button
                        type="text"
                        @click="details(scope.$index, scope.row)"
                        class="action-btn-small edit"
                        icon="el-icon-edit"
                      >
                        编辑
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="实名信息更改" placement="top" v-if="scope.row.auditStatus == '2'">
                      <el-button
                        type="text"
                        @click="getRealNameInfo(scope.row)"
                        class="action-btn-small info"
                        icon="el-icon-user"
                      >
                        实名认证
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="删除签名" placement="top">
                      <el-popconfirm
                        title="确定删除这个签名吗？"
                        @confirm="dele(scope.$index, scope.row)"
                        icon="el-icon-info"
                        iconColor="red"
                      >
                        <el-button
                          slot="reference"
                          type="text"
                          class="action-btn-small delete"
                          icon="el-icon-delete"
                        >
                          删除
                        </el-button>
                      </el-popconfirm>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 简约分页 -->
          <div class="pagination-section">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="tableDataObj.tablecurrent.currentPage"
              :page-size="tableDataObj.tablecurrent.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.total"
              class="simple-pagination"
            />
          </div>
        </div>
      </div>
    </div>
      <!-- 编辑弹框 -->
      <el-dialog :title="signatureFrom.title" :visible.sync="SigDialogVisible" width="680px"
        :close-on-click-modal="false">
        <el-form :model="signatureFrom.formData" :rules="signatureFrom.formRule" label-width="80px"
          style="padding-right: 14px" ref="signatureFrom">
          <el-form-item label="签名内容" prop="signature" style="position: relative">
            <el-input v-model="signatureFrom.formData.signature" :disabled="signatureFrom.title == '编辑短信签名'"
              placeholder="签名限制20个字"></el-input>
            <span style="position: absolute; top: 2px; left: 0px">【</span>
            <span style="position: absolute; top: 2px; right: 0px">】</span>
          </el-form-item>
          <el-form-item label="签名类型" class="sig-type" prop="signatureType">
            <el-radio-group v-model="signatureFrom.formData.signatureType" :disabled="signatureFrom.title == '编辑短信签名'">
              <el-radio label="1" style="padding-bottom: 6px"><span
                  class="sig-type-title-tips">公司名全称或简称：</span>须提供营业执照截图</el-radio>
              <el-radio label="2" style="padding-bottom: 6px"><span
                  class="sig-type-title-tips">APP名全称或简称：</span>须提供任一应用商店的下载链接与该应用商店的后台管理截图</el-radio>
              <el-radio label="3" style="padding-bottom: 6px"><span
                  class="sig-type-title-tips">工信部备案的网站名全称或简称：</span>提供域名备案服务商的后台备案截图</el-radio>
              <el-radio label="4" style="padding-bottom: 6px"><span
                  class="sig-type-title-tips">公众号或小程序名全称或简称：</span>须提供公众号（小程序）微信开放平台截图</el-radio>
              <el-radio label="5" style="padding-bottom: 6px"><span
                  class="sig-type-title-tips">商标全称或简称：</span>须提供商标注册证书截图</el-radio>
              <el-radio label="6" style="padding-bottom: 6px"><span class="sig-type-title-tips">其他</span></el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- <span class="audit-criteria"><i class="el-icon-document"></i> 查看审核标准</span> -->
          <el-form-item label="上传图片" v-if="signatureFrom.formData.signatureType != 6">
            <file-upload style="display: inline-block" :action="this.API.cpus + 'file/upload'" :limit="3"
              listType="picture" tip="格式要求：支持.jpg .jpeg .bmp .gif .png等格式照片，大小不超过2M" :fileStyle="fileStyle" :del="del1"
              :fileListS="fileListS" :showfileList="true" @fileup="fileup" @fileupres="fileupres">选择上传文件</file-upload>
          </el-form-item>
          <el-form-item label="备注内容" prop="remark">
            <el-input type="textarea" v-model="signatureFrom.formData.remark" maxLength="50"
              placeholder="请输入备注内容,50字以内"></el-input>
          </el-form-item>
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="signature_add('signatureFrom', 'signatureFrom.title')">确 定</el-button>
          <el-button @click="SigDialogVisible = false">取 消</el-button>
        </span>
      </el-dialog>
      <el-dialog title="批量导入" :visible.sync="batchShow" width="520px" :close-on-press-escape="false">
        <div>
          <div style="margin: 8px 0;">
            <span style="font-weight: bold;margin-right: 10px;">历史签名是否覆盖</span>
            <el-radio-group v-model="coverType">
              <el-radio :label="false">
                <span>否</span>
              </el-radio>
              <el-radio :label="true">
                <span>是</span>
              </el-radio>
            </el-radio-group>
          </div>
          <!-- <span>批量导入</span> -->
          <el-upload class="upload-demo" :headers="header" :action="this.API.cpus + 'signature/batchUploadAttachment'"
            :limit="1" :file-list="zipfileList" :on-success="handleSuccessZip" :on-remove="handleRemoveZip"
            :before-upload="beforeAvatarUploadc">
            <el-button size="small" type="primary">上传图片</el-button>
            <div slot="tip" class="el-upload__tip">
              <!-- <a style="color: #409eff;" href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/99c7bdd6bf23bba6182ab18007cd38d4" rel="noopener noreferrer">模版下载</a> -->
            </div>
          </el-upload>
          <div style="background-color: #eee;padding: 10px;border-radius: 5px;margin: 10px 0;">
            <span style="font-size: 14px;color: red;">提示仅允许导入”.zip”格式文件！请将同一个签名的附件，
              放在同一个文件夹下，并且文件夹要以签名命名。文件夹结构如下：</span>
            <el-image style="width: 300px; height: 100px;margin-top:10px"
              :src="require('../../../../../../assets/images/StorageFormat.png')"
              :preview-src-list="[require('../../../../../../assets/images/StorageFormat.png')]">
            </el-image>
          </div>
          <el-upload class="upload-demo" :headers="header" v-bind:data="{
            FoldPath: '上传目录',
            SecretKey: '安全验证',
            cover: coverType,
          }" :action="this.API.cpus + 'signature/upload'" :file-list="fileListBatch" :on-remove="handleRemoveBatch"
            :on-success="handleSuccessBatch" :limit="1">
            <el-button style="margin-left: 10px" type="primary">批量导入</el-button>
            <div slot="tip" class="el-upload__tip">
              <a style="margin: 10px; color: #409eff"
                href="https://doc.zthysms.com/server/index.php?s=/api/attachment/visitFile/sign/8081069d0b27c941a74af744a4e0b18c"
                rel="noopener noreferrer">模版下载</a>
            </div>
          </el-upload>
        </div>
        <!-- <div style="text-align: right; margin-top: 16px">
          <el-button @click="batchShow = false">取 消</el-button>
          <el-button type="primary" @click="batchQuery()">确 定</el-button>
        </div> -->
      </el-dialog>
      <!-- 编辑弹框 -->
      <el-dialog style="margin-left: 160px;" title="实名信息补充" :visible.sync="realNameDialogVisible" width="750px"
        class="LoginCellPhoneDialog" :close-on-click-modal="false" :before-close="handelClose">
        <div>
          <el-form :inline="false" :model="realNameInfo" :rules="formRules" ref="realNameInfo" class="demo-realNameInfo"
            label-width="140px">
            <el-form-item label="企业名称" prop="companyName">
              <el-input class="input-w" placeholder="请输入企业名称" v-model="realNameInfo.companyName"></el-input>
            </el-form-item>
            <el-form-item label="社会统一信用代码" prop="creditCode">
              <el-input class="input-w" placeholder="请输入统一社会信用代码" v-model="realNameInfo.creditCode"></el-input>
            </el-form-item>
            <el-form-item label="企业法人" prop="legalPerson">
              <el-input class="input-w" placeholder="请输入企业法人姓名" v-model="realNameInfo.legalPerson"></el-input>
            </el-form-item>
            <el-form-item label="责任人姓名" prop="principalName">
              <el-input class="input-w" placeholder="请输入负责人姓名" v-model="realNameInfo.principalName"></el-input>
            </el-form-item>
            <el-form-item label="责任人证件号码" prop="principalIdCard">
              <el-input class="input-w" placeholder="请输入负责人身份证号" v-model="realNameInfo.principalIdCard"></el-input>
            </el-form-item>
            <el-form-item label="责任人手机号" prop="principalMobile" key="principalMobile">
              <el-input class="input-w" placeholder="请输入责任人手机号" v-model="realNameInfo.principalMobile"></el-input>
              <el-tooltip class="item" effect="dark" content="须填写身份证号本人使用的手机号，否则报备失败" placement="top">
                <i style="margin-left: 10px;color: #409EFF;font-size: 15px;cursor: pointer;" class="el-icon-info"></i>
              </el-tooltip>
            </el-form-item>
            <el-form-item label="签名来源" prop="signatureType">
              <el-radio-group style="display: flex;flex-direction: column;align-items: self-start;"
                v-model="realNameInfo.signatureType">
                <el-radio style="margin-top: 10px;" :label="1">
                  <span class="sig-type-title-tips">企业名称</span>
                  <span style="color: #999;font-size: 12px;margin-left: 8px;">tips:推荐全称，简称字数须为全称字数60%以上，尽量连续，不可改字</span>
                  <!-- （须提供营业执照图片） -->
                </el-radio>
                <el-radio style="margin-top: 10px;" :label="2">
                  <span class="sig-type-title-tips">事业单位：如机关，学校，科研单位，街道社区等</span>
                </el-radio>
                <el-radio style="margin-top: 10px;" :label="3">
                  <span class="sig-type-title-tips">商标</span>
                  （须提供商标注册证书图片或在在中国商标网的商标查询截图）
                </el-radio>
                <el-radio style="margin-top: 10px;" :label="4">
                  <span class="sig-type-title-tips">App </span>
                  （须提供app在ICP/IP/域名备案管理系统的截图）
                </el-radio>
                <el-radio style="margin-top: 10px;" :label="5">
                  <span class="sig-type-title-tips">小程序</span>
                  （须提供小程序在ICP/IP/域名备案管理系统的截图）
                </el-radio>
                <!-- <el-radio style="margin-top: 10px;" :label="6">
                  <span class="sig-type-title-tips">公众号</span>
                  （须提供小程序与主体公司存在关联关系的证明材图片）
                </el-radio> -->
                <!-- <el-radio :label="7">
                  <span class="sig-type-title-tips">网站</span>
                  （须提网站在ICP/IP/域名备案管理系统截图，仅限事业单位的网站）
                </el-radio> -->
              </el-radio-group>
            </el-form-item>
            <el-form-item label="签名类型" prop="signatureSubType">
              <el-radio-group v-model="realNameInfo.signatureSubType"
                style="display: flex;flex-direction: column;align-items: self-start;">
                <el-radio style="margin-top: 10px;" :label="0">
                  <span>全称</span>
                  <span style="color: #999;font-size: 12px;margin-left: 8px;">tips:推荐,报备快</span>
                </el-radio>
                <el-radio style="margin-top: 10px;" :label="1">
                  <span>简称</span>
                  <span
                    style="color: #999;font-size: 12px;margin-left: 8px;">tips:请用企业/单位简称签名在企查查搜索企业唯一的图片，并按参照示例图提供</span>
                </el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="文件上传"
              :prop="(realNameInfo.signatureType == 1 || realNameInfo.signatureType == 2) && realNameInfo.signatureSubType == 0 ? '' : 'imgUrl'"
              key="imgUrl">
              <div>
                <el-upload class="upload-demo" :headers="header" :action="this.API.cpus + 'v3/file/upload'" :limit="3"
                  :file-list="fileList" list-type="picture-card" :on-preview="handlePictureCardPreview"
                  :on-success="handleSuccess" :on-remove="handleRemove1">
                  <div>
                    <i class="el-icon-plus"></i>
                  </div>
                </el-upload>
                <div class="el-upload__tip">支持上传jpg, jpeg, png文件，且不超过2M</div>
              </div>
            </el-form-item>
          </el-form>
          <el-button @click="realNameDialogVisible = false"
            style="width: 100px; padding: 9px 0; margin-left: 160px">取消</el-button>
          <el-button type="primary" @click="submitFormRealNameInfo('realNameInfo')"
            style="width: 100px; padding: 9px 0">提交</el-button>
        </div>
      </el-dialog>
      <el-dialog v-model="dialogVisible">
        <img w-full :src="dialogImageUrl" alt="Preview Image" />
      </el-dialog>
    </div>
  </div>
</template>

<script>
import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传
import TableTem from "@/components/publicComponents/TableTem";
import Tooltip from "@/components/publicComponents/tooltip";
import { formatDate } from "@/assets/js/date.js";
import getNoce from '../../../../../../plugins/getNoce';
import Vue from "vue";
export default {
  name: "SignatureManagement",
  components: { TableTem, FileUpload, Tooltip },
  data() {
    return {
      name: "SignatureManagement",
      SigDialogVisible: false, //弹出框显示隐藏
      fileListS: "", //回显图片的地址
      // realNameInfo: {
      //   signature: "",
      // },
      header: {},
      zipfileList: [],
      fileListBatch: [],
      batchShow: false,
      fileList: [],
      flieULR: [],
      copyUrl: "",
      copyUrlList: [],
      coverType: false,
      dialogVisible: false,
      dialogImageUrl: "",
      signatureFrom: {
        title: "",
        formData: {
          signature: "",
          signatureType: "1",
          remark: "",
          imgUrl: "",
          signatureId: "",
        },
        imgUrl: [],
        formRule: {
          //验证规则
          signature: [
            { required: true, message: "该输入项为必填项!", trigger: "blur" },
            {
              min: 1,
              max: 20,
              message: "长度在 1 到 20 个字符",
              trigger: "blur",
            },
          ],
          signatureType: [
            { required: true, message: "请选择签名类型", trigger: "change" },
          ],
          imgUrl: [
            { required: true, message: "请选择上传图片", trigger: "change" },
          ],
        },
        signature: "", //签名
        signatureId: "", //签名id
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        tablecurrent: {
          //分页参数
          signature: "",
          auditStatus: "",
          isComplete: null,
          currentPage: 1,
          pageSize: 10,
        },
        total: 0,
        tableData: [],
        // tableLabel:[{
        //         prop:"signatureId",
        //         showName:'ID',
        //         width:'60px',
        //         fixed:false
        //     },{
        //         prop:"createTime",
        //         showName:'申请时间',
        //         width:'170px',
        //         fixed:false
        //     },{
        //         prop:"signature",
        //         showName:'内容',
        //         fixed:false
        //     },{
        //         prop:"auditStatus",
        //         showName:'状态',
        //         width:'120px',
        //         fixed:false,
        //         formatData: function(val) {
        //                 if(val == 1){
        //                     return '待审核'
        //                 }else if(val == 2){
        //                     return '审核通过'
        //                 }else if(val == 3){
        //                     return '审核不通过'
        //                 }
        //             }
        //     }
        // ],
        // tableStyle:{
        //     isSelection:false,//是否复选框
        //     // height:250,//是否固定表头
        //     isExpand:false,//是否是折叠的
        //     style: {//表格样式,表格宽度
        //         width:"100%"
        //         },
        //     optionWidth:'170',//操作栏宽度
        //     border:true,//是否边框
        //     stripe:false,//是否有条纹
        // },
        // conditionOption:[
        //         {
        //             contactCondition:'auditStatus',//关联的表格属性
        //             contactData:'1',//关联的表格属性-值
        //             optionName:'编辑',//按钮的显示文字
        //             optionMethod:'details',//按钮的方法
        //             icon:'el-icon-success',//按钮图标
        //             optionButtonColor:'#16a589'//按钮颜色
        //         },
        //          {
        //             contactCondition:'auditStatus',//关联的表格属性
        //             contactData:'3',//关联的表格属性-值
        //             optionName:'编辑',//按钮的显示文字
        //             optionMethod:'details',//按钮的方法
        //             icon:'el-icon-success',//按钮图标
        //             optionButtonColor:'#16a589'//按钮颜色
        //         }
        // ],
        // tableOptions:[
        // {
        //     optionName:'删除',
        //     type:'',
        //     size:'mini',
        //     optionMethod:'dele',
        //     icon:'el-icon-error',
        //     color:'#F56C6C'
        // }
        // ]
      },
      //上传文件格式
      fileStyle: {
        size: 5,
        type: "img",
        style: ["jpg", "jpeg", "bmp", "gif", "png"],
      },
      del1: true, //关闭弹框时清空图片
      realNameDialogVisible: false, //实名信息弹框显示隐藏
      realNameInfo: {
        companyName: "",//公司名称
        creditCode: "",//统一社会信用代码
        legalPerson: "",//法人姓名
        principalIdCard: "",//负责人身份证号
        principalName: "",//负责人姓名
        principalMobile: "",//责任人手机号
        signatureId: "",//签名id
        imgUrl: "",
        signatureType: "",
        signatureSubType: 0,
      }, //实名信息
      // fileList: [],
      formRules: {
        companyName: [
          { required: true, message: '请输入企业名称', trigger: 'change' },
        ],
        creditCode: [
          { required: true, message: '请输入统一社会信用代码', trigger: 'change' },
        ],
        legalPerson: [
          { required: true, message: '请输入法人姓名', trigger: 'change' },
        ],
        principalIdCard: [
          { required: true, message: '请输入负责人身份证号', trigger: 'change' },
        ],
        principalName: [
          { required: true, message: '请输入负责人姓名', trigger: 'change' },
        ],
        principalMobile: [
          { required: true, message: '请输入责任人手机号', trigger: 'change' },
        ],
        imgUrl: [
          { required: true, message: '请上传文件', trigger: 'change' },
        ],
        signatureType: [
          { required: true, message: '请选择签名来源', trigger: 'change' },
        ],
        signatureSubType: [
          { required: true, message: '请选择签名类型', trigger: 'change' },
        ],
      },
      // headers: {
      //   Authorization: "Bearer" + window.common.getCookie("ZTADMIN_TOKEN"),
      // },
    };
  },
  methods: {
    /* --------------- 列表展示 ------------------*/
    gettableData() {
      //获取列表数据
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.cpus + "signature/signatureList",
        this.tableDataObj.tablecurrent,
        (res) => {
          this.tableDataObj.tableData = res.records;
          this.tableDataObj.total = res.total;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    submitForm() {
      this.tableDataObj.tablecurrent.currentPage = 1;
      this.gettableData();
    },
    //重置
    resetForm() {
      this.$refs.tablecurrent.resetFields();
      this.tableDataObj.tablecurrent.currentPage = 1;
      this.tableDataObj.tablecurrent.pageSize = 10;
      this.tableDataObj.tablecurrent.signature = "";
      this.gettableData();
    },
    handleSizeChange(size) {
      //分页一页的size
      this.tableDataObj.tablecurrent.pageSize = size;
      this.gettableData();
    },
    handleCurrentChange: function (currentPage) {
      //分页第几页
      this.tableDataObj.tablecurrent.currentPage = currentPage;
      this.gettableData();
    },
    /* --------------- 列表展示 -----------------------*/

    /* ---- 列表展示操作功能：编辑、删除 、创建（新增）-------*/
    establishSig() {
      //创建签名功能
      this.$router.push({ path: "/CreateSign" });
    },
    clickAddSig() {
      //创建签名功能
      this.$router.push({ path: "/CreateSign" });
    },

    // 导出功能
    exportNums1() {
      // 导出签名列表
      this.$message.info('导出功能开发中...');
    },

    // 获取状态类型（参照tempv1.vue）
    getStatusType(status) {
      const statusMap = {
        '0': 'info',     // 编辑中
        '1': 'warning',  // 待审核
        '2': 'success',  // 已通过
        '3': 'danger'    // 驳回
      };
      return statusMap[status] || 'info';
    },

    // 获取状态文本（参照tempv1.vue）
    getStatusText(status) {
      const textMap = {
        '0': '编辑中',
        '1': '待审核',
        '2': '已通过',
        '3': '驳回'
      };
      return textMap[status] || '未知';
    },
    handleSuccessBatch(res) {
      // console.log(res)
      if (res.code == 200) {
        this.$message({
          type: "success",
          duration: "2000",
          message: "导入成功",
        });
        this.batchShow = false;
        this.fileListBatch = [];
        this.gettableData();
      } else {
        this.$message({
          type: "error",
          duration: "2000",
          message: res.msg,
        });
      }
    },
    handleRemoveBatch() {
      this.fileListBatch = [];
    },
    batchQu() {
      this.batchShow = true;
      // const nonce = await getNoce.useNonce();
      // this.header = {
      //   Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
      //   'Once': nonce,
      // };
    },
    //编辑
    details(index, val) {
      this.$router.push({ path: "/CreateSign", query: { d: val.signatureId } });

      // this.signatureFrom.title="编辑短信签名";
      //     this.del1 = true;
      //     let a = val.signature;
      //     this.signatureFrom.signature = a.slice(1,a.length-1);

      //     //获取上传的图片地址
      //     this.$api.get(this.API.cpus+'signature/findModel?signatureId='+val.signatureId,{},res=>{
      //         this.fileListS = res.data.imgUrl;
      //         let a = res.data.imgUrl.split(',');
      //         for(let i =0; i<a.length; i++){
      //             if(a[i] != ''){
      //                 this.signatureFrom.imgUrl.push(a[i]);
      //             }
      //         }
      //         this.signatureFrom.formData.imgUrl=this.signatureFrom.imgUrl.join(',');
      //     })
      //     this.SigDialogVisible = true;
      //     this.$nextTick(()=>{
      //         this.$refs.signatureFrom.resetFields(); //清空表单
      //         Object.assign(this.signatureFrom.formData,val);
      //         this.signatureFrom.formData.signature = a.slice(1,a.length-1);
      //     });
    },
    //删除
    dele(index, val) {
      this.$confirms.confirmation(
        "delete",
        "此操作将永久删除该数据, 是否继续？",
        this.API.cpus + "signature/delete?signatureId=" + val.signatureId,
        {},
        (res) => {
          this.gettableData();
        }
      );
    },
    //提交表单
    signature_add(formName, title) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          let formDates = {};
          Object.assign(formDates, this.signatureFrom.formData);
          formDates.signature =
            "【" + this.signatureFrom.formData.signature + "】";
          if (this.signatureFrom.title == "创建短信签名") {
            //新增
            delete formDates.signatureId;
            if (this.signatureFrom.formData.signatureType != 6) {
              if (this.signatureFrom.formData.imgUrl != "") {
                this.sendRequest(
                  "post",
                  "确定新增签名",
                  this.API.cpus + "signature/saveInfo",
                  formDates
                );
              } else {
                this.$message({
                  message: "请上传图片！",
                  type: "warning",
                });
              }
            } else {
              this.sendRequest(
                "post",
                "确定新增签名",
                this.API.cpus + "signature/saveInfo",
                formDates
              );
            }
          } else {
            //编辑
            //判断是否有图片
            formDates.auditStatus = "1"; //只要编辑，签名的状态就改为待审核状态
            if (this.signatureFrom.formData.signatureType != 6) {
              if (this.signatureFrom.formData.imgUrl != "") {
                this.$confirms.confirmation(
                  "put",
                  "确定修改签名",
                  this.API.cpus + "signature/update",
                  formDates,
                  (res) => {
                    this.gettableData();
                    this.SigDialogVisible = false; //隐藏弹窗
                    this.signatureFrom.formData = {
                      signature: "",
                      signatureType: "1",
                      remark: "",
                      imgUrl: "",
                      signatureId: "",
                    };
                  }
                );
              } else {
                this.$message({
                  message: "请上传图片！",
                  type: "warning",
                });
              }
            } else {
              this.$confirms.confirmation(
                "put",
                "确定修改签名",
                this.API.cpus + "signature/update",
                formDates,
                (res) => {
                  this.gettableData();
                  this.SigDialogVisible = false; //隐藏弹窗
                  this.signatureFrom.formData = {
                    signature: "",
                    signatureType: "1",
                    remark: "",
                    imgUrl: "",
                    signatureId: "",
                  };
                }
              );
            }
          }
        } else {
          console.log("error submit!!");
          return false;
        }
      });
    },
    //发送编辑和新增的请求
    sendRequest(type, title, action, formDates) {
      //验证签名是否存在
      this.$api.get(
        this.API.cpus +
        "signature/findModelBySignature?signature=【" +
        this.signatureFrom.formData.signature +
        "】",
        {},
        (res) => {
          if (res.code == 200 && res.data == "0") {
            //发送编辑和新增的请求
            this.$confirms.confirmation(
              type,
              title,
              action,
              formDates,
              (res) => {
                //重载列表
                this.gettableData();
                //隐藏弹窗
                this.SigDialogVisible = false;
              }
            );
          } else {
            this.$message({
              message: "签名已存在，切勿重复！",
              type: "warning",
            });
          }
        }
      );
    },
    //移除文件
    fileup(val) {
      let aa = this.signatureFrom.formData.imgUrl.split(",");
      if (val.response) {
        aa.splice(aa.indexOf(val.response.fullPath), 1);
      } else {
        let c = val.url;
        let d = c.slice(c.indexOf("group1"));
        aa.splice(aa.indexOf(d), 1);
      }
      this.signatureFrom.imgUrl = aa;
      this.signatureFrom.formData.imgUrl = aa.join(",");
    },
    //文件上传成功
    fileupres(val) {
      this.signatureFrom.imgUrl.push(val.fullPath);
      this.signatureFrom.formData.imgUrl = this.signatureFrom.imgUrl.join(",");
    },
    getRealNameInfo(row) {
      this.realNameDialogVisible = true; //打开实名信息弹出框
      this.realNameInfo.signatureId = row.signatureId;
      this.$api.get(this.API.cpus + 'signature/findModel/realName?signatureId=' + row.signatureId, {}, res => {
        if (res.code == 200) {
          this.realNameInfo.companyName = res.data.companyName;
          this.realNameInfo.creditCode = res.data.creditCode;
          this.realNameInfo.legalPerson = res.data.legalPerson;
          this.realNameInfo.principalIdCard = res.data.principalIdCard;
          this.realNameInfo.principalMobile = res.data.principalMobile;
          this.realNameInfo.principalName = res.data.principalName;
          this.realNameInfo.signatureType = res.data.signatureType;
          this.realNameInfo.signatureSubType = res.data.signatureSubType;
          if (res.data.imgUrl) {
            this.realNameInfo.imgUrl = res.data.imgUrl;
            this.copyUrl = res.data.imgUrl;
            this.flieULR = res.data.imgUrl.split(",").filter(item => item.trim() !== '');
            this.fileList = this.flieULR.map((item) => {
              return {
                name: item,
                url: this.API.imgU + item,
              };
            });
            let imgUrlList = JSON.stringify(this.fileList)
            this.copyUrlList = JSON.parse(imgUrlList)

          } else {
            this.fileList = [];
          }
        }
      })
    },
    editContentExample(row) {
      this.$router.push({ path: "/contentExample", query: { signature: row.signature } });
    },
    changeSignatureType(val) {
      if (val == '2') {
        this.realNameInfo.imgUrl = "";
        this.fileList = [];
      } else {
        this.fileList = this.copyUrlList;
        this.realNameInfo.imgUrl = this.copyUrl;
      }
    },
    submitFormRealNameInfo(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {

          this.$confirms.confirmation("post", "确认修改实名信息？", this.API.cpus + "signature/realName/edit ", this.realNameInfo, res => {
            if (res.code == 200) {
              this.realNameDialogVisible = false; //关闭实名信息弹出框
              this.gettableData();
            }
          })
        }
      })
    },
    handelClose() {
      this.realNameDialogVisible = false; //关闭实名信息弹出框
    },
    handleSuccess(res) {
      if (res.code == 200) {
        this.flieULR.push(res.data.fullpath);
        this.realNameInfo.imgUrl = this.flieULR.join(",");
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    handleRemove1(file, fileList) {
      if (file.response) {
        this.flieULR.splice(this.flieULR.indexOf(file.response.data.fullpath), 1);
        this.realNameInfo.imgUrl = this.flieULR.join(",");
      } else {
        this.flieULR.splice(this.flieULR.indexOf(file.name), 1);
        this.realNameInfo.imgUrl = this.flieULR.join(",");
      }

    },
    handleSuccessZip(res) {
      console.log(res);
      if (res.code == 200) {
        let timeStamp = new Date().getTime();
        const link = document.createElement('a');
        link.style.display = 'none';
        link.href = 'data:application/octet-stream;base64,' + res.data
        link.download = timeStamp + '.xlsx'//下载后文件名
        link.click()
      }

    },
    handleRemoveZip(file, fileList) {
      this.fileListZip = [];
    },
    handlePictureCardPreview(file) {
      this.dialogImageUrl = file.url
      this.dialogVisible = true
    },
    beforeAvatarUploadc(file) {
      const siJPGGIF = file.name.split(".")[file.name.split(".").length - 1];
      const fileType = [
        "zip",
      ];
      const isLt5M = file.size / 1024 / 1024 < 20;
      if (fileType.indexOf(siJPGGIF) == -1) {
        this.$message.warning(
          "上传文件只能是zip格式!"
        );
        return false;
      }
      if (!isLt5M) {
        this.$message.warning("上传缩略图大小不能超过20M！");
        return false;
      }
    },

  },
  watch: {
    SigDialogVisible(val) {
      //监听弹框是否关闭
      if (val == false) {
        this.signatureFrom.imgUrl = [];
        this.signatureFrom.formData.imgUrl = "";
        this.del1 = false;
        this.$refs.signatureFrom.resetFields(); //清空表单
        this.fileListS = "";
      }
    },
    batchShow(val) {
      if (!val) {
        this.coverType = false;
        this.fileList = [];
        this.fileListZip = [];
        this.fileListBatch = [];
      }
    },
    realNameDialogVisible: function (val) {
      if (!val) {
        this.realNameInfo.companyName = "";
        this.realNameInfo.creditCode = "";
        this.realNameInfo.legalPerson = "";
        this.realNameInfo.principalIdCard = "";
        this.realNameInfo.principalName = "";
        this.realNameInfo.signatureId = "";
        this.flieULR = [];
        this.fileList = [];
        this.copyUrl = "";
        this.copyUrlList = [];
        this.realNameInfo.imgUrl = "";
        this.realNameInfo.signatureType = "";
        this.realNameInfo.signatureSubType = "";
        // this.$refs.formRealNameInfo.resetFields();
      }
    },
  },
  mounted() {
    this.header = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
    };
    this.gettableData();
  },
  // activated(){
  //     this.gettableData();
  // }
};
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* SignatureManagement 特有样式 */
.search-input {
  width: 200px; // 覆盖通用样式的宽度
}

/* 保留原有的一些必要样式 */
.input-w {
  width: 300px;
}

.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.sig-type .el-radio-group {
  padding-top: 8px;
}

.sig-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 10px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
</style>
<style>
.el-table--small th {
  background: #f5f5f5;
}
</style>
