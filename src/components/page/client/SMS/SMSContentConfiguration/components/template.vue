<template>
  <div class="modern-template-page">
    <!-- 现代化页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <el-button
            type="text"
            @click="goBack()"
            class="back-btn"
          >
            <i class="el-icon-arrow-left"></i>
            返回
          </el-button>
          <el-divider direction="vertical"></el-divider>
          <h1 class="page-title">{{ statusOf }}</h1>
        </div>
        <div class="header-right">
          <el-tag :type="getTemplateTypeTag(form.temType)" size="medium">
            {{ getTemplateTypeName(form.temType) }}
          </el-tag>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container">
        <!-- 模版配置表单 -->
        <el-form
          :model="form"
          :rules="temFormRules"
          label-width="120px"
          ref="temForm"
          class="modern-form"
        >
          <!-- 模版类型选择卡片 -->
          <el-card shadow="hover" class="form-card template-type-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-s-grid"></i>
                模版类型
              </span>
              <el-tooltip content="选择适合您业务场景的模版类型" placement="top">
                <i class="el-icon-question help-icon"></i>
              </el-tooltip>
            </div>

            <el-form-item prop="temType" class="template-type-form-item">
              <div class="template-type-selector">
                <div
                  v-for="typeOption in templateTypeOptions"
                  :key="typeOption.value"
                  class="type-option-card"
                  :class="{
                    'active': form.temType === typeOption.value,
                    'disabled': editTemDialog_status == 0
                  }"
                  @click="selectTemplateType(typeOption.value)"
                >
                  <div class="type-icon">
                    <i :class="typeOption.icon"></i>
                  </div>
                  <div class="type-content">
                    <h4 class="type-title">{{ typeOption.label }}</h4>
                    <p class="type-description">{{ typeOption.description }}</p>
                  </div>
                  <div class="type-check" v-if="form.temType === typeOption.value">
                    <i class="el-icon-check"></i>
                  </div>
                </div>
              </div>
            </el-form-item>
          </el-card>

          <!-- 基本信息卡片 -->
          <el-card shadow="hover" class="form-card basic-info-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-edit-outline"></i>
                基本信息
              </span>
            </div>
            <div class="form-row">
              <el-form-item label="模版名称" prop="temName" class="form-item-modern">
                <el-input
                  v-model="form.temName"
                  :disabled="editTemDialog_status == 0"
                  placeholder="请填写模版名称，名称只做标识作用"
                  size="large"
                  class="modern-input"
                >
                  <i slot="prefix" class="el-icon-edit"></i>
                </el-input>
              </el-form-item>
            </div>

            <div class="form-row">
              <el-form-item label="选择签名" prop="signId" class="form-item-modern">
                <el-select
                  v-model="form.signId"
                  placeholder="请选择已审核通过的签名"
                  class="modern-select"
                  size="large"
                  clearable
                  filterable
                  @change="changeLocationValue"
                >
                  <el-option
                    v-for="(item, index) in labelName"
                    :label="item.signature"
                    :value="item.signature"
                    :key="index"
                  >
                    <span class="signature-option">
                      <span class="signature-text">{{ item.signature }}</span>
                      <el-tag size="mini" type="success">已审核</el-tag>
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </el-card>
          <!-- 短信内容编辑卡片 -->
          <el-card shadow="hover" class="form-card content-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-chat-line-square"></i>
                短信内容编辑
              </span>
              <div class="header-actions">
                <el-tooltip content="当前模版预计发送条数" placement="top">
                  <el-tag :type="numTextMsg2 > 1 ? 'warning' : 'success'" size="small">
                    {{ numTextMsg2 }} 条短信
                  </el-tag>
                </el-tooltip>
                <el-tooltip :content="getVariableCountTooltip()" placement="top">
                  <el-tag :type="getVariableCountTagType()" size="small">
                    {{ getVariableCountText() }}
                  </el-tag>
                </el-tooltip>
                <el-tooltip v-if="form.temType == 1" content="验证码类型限制" placement="top">
                  <el-tag type="info" size="small">
                    仅限1个变量
                  </el-tag>
                </el-tooltip>
              </div>
            </div>

            <el-form-item prop="temContent" class="content-form-item">
              <!-- 智能识别区域 -->
              <div class="smart-recognition-section">
                <el-card shadow="never" class="smart-card" :class="{ 'expanded': showSmartMode }">
                  <div slot="header" class="smart-header">
                    <span class="smart-title">
                      <i class="el-icon-magic-stick"></i>
                      智能模版生成
                    </span>
                    <el-button
                      type="text"
                      @click="toggleSmartMode"
                      class="toggle-btn"
                    >
                      <i :class="showSmartMode ? 'el-icon-arrow-up' : 'el-icon-arrow-down'"></i>
                      {{ showSmartMode ? '收起' : '展开' }}
                    </el-button>
                  </div>
                  <transition name="slide-fade">
                    <div v-show="showSmartMode" class="smart-content">
                      <div class="smart-description">
                        <el-alert
                          title="智能识别说明"
                          type="info"
                          :closable="false"
                          show-icon
                        >
                          <template slot="default">
                            请输入包含具体内容的短信示例，系统将自动识别变量并生成模版
                          </template>
                        </el-alert>
                      </div>

                      <div class="smart-input-section">
                        <el-input
                          type="textarea"
                          placeholder="例如：【某某公司】您的验证码是123456，请在5分钟内使用，如非本人操作请忽略。"
                          v-model="smartExample"
                          :rows="4"
                          maxlength="800"
                          show-word-limit
                          class="smart-textarea"
                        />

                        <div class="smart-actions">
                          <el-button @click="clearSmartExample" icon="el-icon-delete">
                            清空
                          </el-button>
                          <el-button
                            type="primary"
                            @click="analyzeExample"
                            :loading="analyzing"
                            icon="el-icon-magic-stick"
                          >
                            {{ analyzing ? '识别中...' : '智能识别' }}
                          </el-button>
                        </div>
                      </div>
                      <!-- 识别结果展示 -->
                      <transition name="fade">
                        <div v-if="recognitionResults.length > 0" class="recognition-results">
                          <el-divider content-position="left">
                            <span class="results-title">识别结果</span>
                          </el-divider>

                          <div class="generated-template">
                            <label class="result-label">生成的模版：</label>
                            <div class="template-preview">{{ generatedTemplate }}</div>
                          </div>

                          <div class="recognized-variables">
                            <label class="result-label">识别到的变量（点击可修改）：</label>
                            <div class="variables-tags">
                              <el-tag
                                v-for="(result, index) in recognitionResults"
                                :key="index"
                                :type="result.confirmed ? 'success' : 'info'"
                                closable
                                @close="removeRecognitionResult(index)"
                                @click="editVariableName(index)"
                                class="variable-tag-item"
                              >
                                <span class="original-value">{{ result.originalValue }}</span>
                                <i class="el-icon-arrow-right"></i>
                                <span class="variable-name">{{ '{' + result.variableName + '}' }}</span>
                                <span class="variable-type">({{ getVariableTypeLabel(result.suggestedType) }})</span>
                              </el-tag>
                            </div>
                          </div>

                          <div class="recognition-actions">
                            <el-button type="success" @click="applyTemplate" icon="el-icon-check">
                              应用模版
                            </el-button>
                          </div>
                        </div>
                      </transition>
                    </div>
                  </transition>
                </el-card>
              </div>

              <!-- 主要内容编辑区域 -->
              <div class="main-content-editor">
                <div class="editor-header">
                  <label class="editor-label">模版内容</label>
                  <el-tooltip content="变量格式：{name}；变量限制：数字+字母组合或仅字母2种类型" placement="top">
                    <i class="el-icon-question help-icon"></i>
                  </el-tooltip>
                </div>

                <el-input
                  type="textarea"
                  :placeholder="placeholderText"
                  v-model="form.temContent"
                  @input="handelInput"
                  maxlength="800"
                  show-word-limit
                  :rows="6"
                  class="content-textarea"
                />
              </div>
              <!-- 短信内容统计和选项 -->
              <div class="content-stats-options">
                <div class="content-stats">
                  <div class="stats-item">
                    <span class="stats-label">预计发送条数：</span>
                    <span class="stats-value">{{ numTextMsg2 }} 条短信</span>
                    <span class="stats-note">（如果此内容包含变量，以实际下发条数为准）</span>
                  </div>
                </div>

                <div class="content-options">
                  <div class="option-group" v-if="form.temType == '3'">
                    <el-button
                      type="primary"
                      @click="shortReset()"
                      icon="el-icon-link"
                      class="short-link-btn"
                    >
                      短链转换
                    </el-button>
                  </div>

                  <div class="option-group">
                    <el-checkbox
                      v-if="form.temType != '1'"
                      v-model="form.autoSwitch"
                      @change="handAutoSwitch"
                      class="modern-checkbox"
                    >
                      是否自动转短链
                    </el-checkbox>

                    <el-checkbox
                      v-if="form.temType == '3'"
                      v-model="form.checked"
                      @change="handleQuite"
                      class="modern-checkbox"
                    >
                      拒收请回复R
                    </el-checkbox>
                  </div>
                </div>

                <!-- 长链接输入 -->
                <transition name="slide-fade">
                  <div v-if="form.autoSwitch" class="long-url-input">
                    <div class="url-input-header">
                      <span class="url-label">长链接</span>
                      <el-tooltip content="请检查长链接是否含有多余字符，确保链接能正常打开！" placement="top">
                        <i class="el-icon-warning-outline help-icon"></i>
                      </el-tooltip>
                    </div>
                    <el-input
                      v-model="form.longUrl"
                      placeholder="请输入长链接地址"
                      class="url-input"
                    />
                  </div>
                </transition>
              </div>
            </el-form-item>
            <!-- 增强的模版变量管理区域 -->
            <div class="template-variables-section" v-if="extractedVariables.length > 0">
              <el-card shadow="hover" class="variables-card">
                <div slot="header" class="variables-header">
                  <span class="variables-title">
                    <i class="el-icon-setting"></i> 模版变量管理
                  </span>
                  <el-badge :value="extractedVariables.length" class="variables-count">
                    <span class="variables-count-text">已识别变量</span>
                  </el-badge>
                </div>

                <div class="variables-content">
                  <div class="variables-description">
                    <el-alert
                      title="变量配置说明"
                      type="info"
                      :closable="false"
                      show-icon>
                      <template slot="default">
                        <p>系统已自动识别模版中的变量，请为每个变量配置正确的类型。变量类型决定了该变量在实际使用时可以接受的数据格式。</p>
                      </template>
                    </el-alert>
                  </div>

                  <div class="variables-list">
                    <div
                      v-for="(variable, index) in extractedVariables"
                      :key="variable.name"
                      class="variable-item"
                      :class="{ 'variable-item-error': !variable.type }"
                    >
                      <div class="variable-card">
                        <div class="variable-header">
                          <div class="variable-name-section">
                            <el-tag
                              :type="variable.type ? 'success' : 'warning'"
                              size="medium"
                              class="variable-tag"
                            >
                              {{ '{' + variable.name + '}' }}
                            </el-tag>
                            <span class="variable-description">变量名称</span>
                          </div>
                          <div class="variable-actions">
                            <el-button
                              type="text"
                              size="mini"
                              @click="editVariableNameInList(index)"
                              class="edit-btn"
                            >
                              <i class="el-icon-edit"></i> 重命名
                            </el-button>
                            <el-button
                              type="text"
                              size="mini"
                              @click="removeVariable(index)"
                              class="remove-btn"
                            >
                              <i class="el-icon-delete"></i> 删除
                            </el-button>
                          </div>
                        </div>

                        <div class="variable-body">
                          <div class="variable-type-section">
                            <label class="variable-label">变量类型：</label>
                            <el-select
                              v-model="variable.type"
                              placeholder="请选择变量类型"
                              class="variable-type-select"
                              @change="onVariableTypeChange(variable, $event)"
                            >
                              <el-option
                                v-for="type in getAvailableTypes()"
                                :key="type.value"
                                :label="type.label"
                                :value="type.value"
                              >
                                <div class="type-option">
                                  <span class="type-name">{{ type.label }}</span>
                                  <span class="type-format">{{ type.format }}</span>
                                </div>
                              </el-option>
                            </el-select>
                          </div>

                          <div class="variable-info" v-if="variable.type">
                            <div class="variable-rule">
                              <i class="el-icon-info"></i>
                              <span>{{ getVariableRule(variable.type) }}</span>
                            </div>
                            <div class="variable-examples" v-if="getVariableExamples(variable.type)">
                              <span class="examples-label">示例：</span>
                              <el-tag
                                v-for="example in getVariableExamples(variable.type)"
                                :key="example"
                                size="mini"
                                type="info"
                                class="example-tag"
                              >
                                {{ example }}
                              </el-tag>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div class="variables-summary" v-if="extractedVariables.length > 0">
                    <el-divider content-position="left">
                      <span class="summary-title">配置摘要</span>
                    </el-divider>
                    <div class="summary-content">
                      <div class="summary-stats">
                        <div class="stat-item">
                          <span class="stat-number">{{ extractedVariables.length }}</span>
                          <span class="stat-label">总变量数</span>
                        </div>
                        <div class="stat-item">
                          <span class="stat-number">{{ configuredVariablesCount }}</span>
                          <span class="stat-label">已配置</span>
                        </div>
                        <div class="stat-item">
                          <span class="stat-number">{{ unconfiguredVariablesCount }}</span>
                          <span class="stat-label">未配置</span>
                        </div>
                      </div>
                      <div class="summary-actions">
                        <el-button
                          type="primary"
                          size="small"
                          @click="autoConfigureVariables"
                          :disabled="configuredVariablesCount === extractedVariables.length"
                        >
                          <i class="el-icon-magic-stick"></i> 智能配置
                        </el-button>
                        <el-button
                          type="default"
                          size="small"
                          @click="resetVariableTypes"
                        >
                          <i class="el-icon-refresh"></i> 重置配置
                        </el-button>
                      </div>
                    </div>
                  </div>
                </div>
              </el-card>
            </div>
          </el-card>

          <!-- 变量管理卡片 -->
          <el-card v-if="getcode.length > 0" shadow="hover" class="form-card variable-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-setting"></i>
                变量管理
              </span>
              <div class="header-actions">
                <el-tooltip content="已配置变量数量" placement="top">
                  <el-tag :type="getConfiguredVariablesTagType()" size="small">
                    {{ getConfiguredVariablesCount() }}/{{ getcode.length }} 已配置
                  </el-tag>
                </el-tooltip>
              </div>
            </div>

            <div class="variables-management">
              <div class="variables-grid">
                <div
                  v-for="(variable, index) in getcode"
                  :key="index"
                  class="variable-item"
                  :class="{ 'configured': variable.codeName, 'unconfigured': !variable.codeName }"
                >
                  <div class="variable-header">
                    <span class="variable-name">{{ '{' + variable.value + '}' }}</span>
                    <el-tag
                      v-if="variable.codeName"
                      :type="getVariableTypeTagType(variable.codeName)"
                      size="mini"
                    >
                      {{ getVariableTypeLabel(variable.codeName) }}
                    </el-tag>
                    <el-tag v-else type="info" size="mini">未配置</el-tag>
                  </div>

                  <div class="variable-config">
                    <el-select
                      v-model="variable.codeName"
                      placeholder="选择变量类型"
                      @change="typeChange($event, variable)"
                      size="small"
                      class="variable-type-select"
                    >
                      <el-option
                        v-for="type in variableTypes"
                        :key="type.value"
                        :label="type.label"
                        :value="type.value"
                      >
                        <div class="variable-option">
                          <span class="option-label">{{ type.label }}</span>
                          <span class="option-rule">{{ type.rule }}</span>
                        </div>
                      </el-option>
                    </el-select>
                  </div>

                  <div v-if="variable.codeName" class="variable-info">
                    <div class="variable-examples">
                      <span class="info-label">示例：</span>
                      <span class="examples-text">
                        {{ getVariableExamples(variable.codeName) }}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 批量操作 -->
              <div v-if="getcode.length > 1" class="batch-actions">
                <el-button
                  @click="autoConfigureVariables"
                  icon="el-icon-magic-stick"
                  size="small"
                  type="primary"
                  :disabled="form.temType == 1"
                >
                  智能配置
                </el-button>
                <el-button
                  @click="resetVariableTypes"
                  icon="el-icon-refresh"
                  size="small"
                >
                  重置配置
                </el-button>
              </div>
            </div>
          </el-card>

          <!-- 申请说明卡片 -->
          <el-card shadow="hover" class="form-card remark-card">
            <div slot="header" class="card-header">
              <span class="card-title">
                <i class="el-icon-document"></i>
                申请说明
              </span>
              <el-tooltip content="为加快审核速度与模版通过率，请填写申请该模版的原因或说明" placement="top">
                <i class="el-icon-question help-icon"></i>
              </el-tooltip>
            </div>

            <el-form-item prop="remark" class="remark-form-item">
              <el-input
                type="textarea"
                :rows="4"
                resize="none"
                :placeholder="getRemarkPlaceholder()"
                v-model="form.remark"
                maxlength="200"
                show-word-limit
                class="remark-textarea"
              />
            </el-form-item>
          </el-card>
        </el-form>

        <!-- 操作按钮区域 -->
        <div class="action-buttons">
          <div class="button-group">
            <el-button
              type="primary"
              size="large"
              @click="addTemOk(statusOf, '1')"
              icon="el-icon-check"
              class="submit-btn"
            >
              提交审核
            </el-button>
            <el-button
              type="success"
              size="large"
              @click="addTemOk(statusOf, '0')"
              icon="el-icon-document-add"
              class="save-btn"
            >
              保存草稿
            </el-button>
            <el-button
              size="large"
              @click="calcelTem()"
              icon="el-icon-close"
              class="cancel-btn"
            >
              取消
            </el-button>
          </div>
        </div>

        <!-- 帮助信息区域 -->
        <el-card shadow="never" class="help-card">
          <div slot="header" class="card-header">
            <span class="card-title">
              <i class="el-icon-info"></i>
              使用说明
            </span>
          </div>

          <div class="help-content">
            <div class="help-section">
              <h4>模版审核说明</h4>
              <ul>
                <li>提交审核后，模版将进入审核流程，审核时间通常为1-2个工作日</li>
                <li>审核通过后，您可以在模版列表中查看并使用该模版</li>
                <li>如果审核未通过，请根据审核意见修改后重新提交</li>
              </ul>
            </div>

            <div class="help-section">
              <h4>变量使用说明</h4>
              <ul>
                <li>变量格式：<code>{变量名}</code>，变量名只能包含字母、数字和下划线，且必须以字母开头</li>
                <li>变量数量限制：
                  <ul>
                    <li><strong>验证码类型</strong>：只能包含1个变量</li>
                    <li><strong>行业通知类型</strong>：最多可包含16个变量</li>
                    <li><strong>会员营销类型</strong>：最多可包含16个变量</li>
                  </ul>
                </li>
                <li>变量间隔要求：变量之间不能挨着，必须有文字或符号分隔</li>
                <li>验证码类型强校验：当检测到多个变量时，系统将阻止保存并提示修改</li>
                <li>每种变量类型都有对应的格式要求，请根据实际需要选择</li>
                <li>发送短信时，变量将被替换为实际的内容</li>
              </ul>
            </div>

            <div class="help-section">
              <h4>注意事项</h4>
              <ul>
                <li>短信内容必须包含已审核通过的签名</li>
                <li>营销类短信必须包含"拒收请回复R"字样</li>
                <li>请确保短信内容符合相关法律法规要求</li>
              </ul>
            </div>
          </div>
        </el-card>
      </div>
    </div>

    <!-- 短链转换对话框 -->
    <el-dialog title="短链转换" :visible.sync="temshortVisible" width="520px">
      <div class="short-box">
        <p class="short-title" style="padding-top: 10px">长网址链接</p>
        <el-input placeholder="请输入长链接" v-model="originalUrl" class="width-l">
          <el-button slot="append" type="primary" icon="el-icon-refresh" @click="transformation()">转换</el-button>
        </el-input>
        <div class="font-sizes font-sizes1">
          <span style="color: red">* </span>我们可以帮您把长链接压缩，让您可以输入更多的内容。
        </div>
        <div class="font-sizes">
          <span style="color: red">* </span>插入短信内容中时，将在链接前后生成空格符号，以防止出现手机短信客户端不识别链接的情况。
        </div>
      </div>
      <div class="short-box">
        <p class="short-title" style="padding-top: 20px">短网址链接</p>
        <el-input v-model="shortConUrl" class="width-l" :disabled="true">
          <el-button slot="append" type="primary" @click="handlePreview()" icon="el-icon-share">预览</el-button>
        </el-input>
      </div>
      <div style="text-align: right; margin-top: 16px">
        <el-button @click="handleCancles()">取 消</el-button>
        <el-button type="primary" @click="shortConDetermine()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
  
  <script>
  export default {
    name: "NewCreateTemplate",
    data() {
      // 模板名称校验规则
      const checkName = (_rule, value, callback) => {
        if (this.statusOf != "添加模板") {
          callback();
          return;
        }
        
        if (!value || (value && (value.length > 20 || value.length < 1))) {
          return callback(new Error("长度在 1 到 20 个字符"));
        }
        
        const reg = /^[A-Za-z0-9\u4e00-\u9fa5]+$/g;
        if (!reg.test(value)) {
          return callback(new Error("不允许输入空格等特殊符号"));
        }
        
        this.$api.post(
          this.API.cpus + "v3/consumersmstemplate/validateUserTempExist/",
          { temName: value },
          (res) => {
            if (res.data == "0") {
              callback();
            } else {
              return callback(new Error("此模板已存在"));
            }
          }
        );
      };
  
      // 校验变量格式
      const paramValid = (_rule, value, callback) => {
        if (!value) {
          callback();
          return;
        }

        const list = value.match(/{(.+?)}/g);
        if (!list || list.length === 0) {
          callback();
          return;
        }

        // 使用新的验证方法
        const validationResult = this.validateVariables(value, list);
        if (!validationResult.isValid) {
          callback(new Error(validationResult.message));
          return;
        }

        // 原有的基本格式检查
        for (let i = 0; i < list.length; i++) {
          const checkItem = list[i].replace("{", "").replace("}", "");
          if (checkItem.indexOf("{") !== -1) {
            callback(new Error("变量中不能含有{字符"));
            return;
          }
        }

        callback();
      };
  
      return {
        statusOf: "", // 是新增还是编辑
        rowDatas: "",
        editTemDialog_status: 1, // 模板是否是新增---1为新增，0为编辑
        labelName: [], // 签名列表
        oldTemType: 0, // 保存原模板类型，用于切换回退
        getcode: [], // 变量列表
        getcodes: [],
        temshortVisible: false, // 短链弹框
        originalUrl: "", // 长链接的值
        shortConUrl: "", // 短连接的值
        shortCode: "", // 短链的code码
        isShort: false, // 是否短链转换
        valid_code: "",
        signatureTit: "",
        paramsList: [],
        number: null,
        oldNumber: null,
        loadingcomp: false,
        textArr: [],
        placeholderText: "请输入短信内容，如需要变量例如：{name}",
        // 表单数据
        form: {
          params: {}, // 模板变量
          autoSwitch: false, // 是否自动切换短链 false：否, true：是
          longUrl: "", // 长链接
          submit: "", // 是否提交审核 0：否, 1：是
          temFormat: "1", // 1.变量模板；2是全文模板
          signId: "", // 签名
          temName: "", // 模板名称
          temContent: "", // 模板内容
          temType: 1, // 模板类型
          shortCode: "", // 短链Code
          remark: "", // 申请说明
          checked: false // 是否包含拒收回复R
        },
        // 表单验证规则
        temFormRules: {
          signId: [
            { required: true, message: "请选择签名", trigger: "change" }
          ],
          temName: [
            { required: true, validator: checkName, trigger: ["change", "blur"] }
          ],
          temType: [
            { required: true, message: "请选择模板类型", trigger: "change" }
          ],
          temContent: [
            { required: true, message: "请输入模板内容", trigger: "change" },
            { validator: paramValid, trigger: "change" }
          ],
          remark: [
            {
              min: 1,
              max: 200,
              message: "长度在 1 到 200个字符",
              trigger: "blur"
            }
          ]
        },
        timmer: null, // 防抖定时器
        arrStorage: [], // 存储变量信息
  
        // 智能识别相关数据
        showSmartMode: false, // 是否显示智能识别区域
        smartExample: "", // 用户输入的示例文本
        analyzing: false, // 是否正在分析
        recognitionResults: [], // 识别结果
        generatedTemplate: "", // 生成的模版
        editingVariableIndex: -1, // 正在编辑的变量索引

        // 增强的模版变量管理数据
        extractedVariables: [], // 提取的变量列表

        // 模版类型选项
        templateTypeOptions: [
          {
            value: 1,
            label: '验证码',
            icon: 'el-icon-key',
            description: '适用于注册、登录、找回密码、身份验证、号码绑定身份验证的短信模板'
          },
          {
            value: 2,
            label: '行业通知',
            icon: 'el-icon-bell',
            description: '可发告知类短信，例如：快递通知、消费通知、账单、服务通知'
          },
          {
            value: 3,
            label: '会员营销',
            icon: 'el-icon-present',
            description: '可用于会员服务，内容可带链接，建议转换成短链接发送'
          }
        ],

        variableTypes: [ // 变量类型定义
          {
            value: 'valid_code',
            label: '验证码',
            format: '{valid_code}',
            rule: '4-6位数字英文混合，支持英文大小写',
            examples: ['123456', 'A1B2C3', 'abc123']
          },
          {
            value: 'mobile_number',
            label: '电话号码',
            format: '{mobile_number}',
            rule: '1-15位纯数字',
            examples: ['13812345678', '02012345678', '4001234567']
          },
          {
            value: 'other_number',
            label: '其他号码',
            format: '{other_number}',
            rule: '1-32位字母+数字组合，支持中划线-',
            examples: ['ORDER-123456', 'SF1234567890', 'ID-ABC123']
          },
          {
            value: 'amount',
            label: '金额',
            format: '{amount}',
            rule: '支持数字（含英文小数点.）或数字的中文（壹贰叁肆伍陆柒捌玖拾佰仟万亿 圆元整角分厘毫）',
            examples: ['100.50', '99元', '壹佰元整']
          },
          {
            value: 'date',
            label: '时间',
            format: '{date}',
            rule: '符合时间的表达方式，也支持中文：2019年9月3日16时24分35秒',
            examples: ['2023-12-25', '2023年12月25日', '14:30:00']
          },
          {
            value: 'chinese',
            label: '中文汉字',
            format: '{chinese}',
            rule: '1-32中文，支持中文圆括号（）',
            examples: ['张三', '北京市（朝阳区）', '优惠活动']
          },
          {
            value: 'others',
            label: '其他',
            format: '{others}',
            rule: '1-35个中文数字字母组合，支持中文符号和空格',
            examples: ['Hello 世界', '产品名称 V2.0', '特殊符号！@#']
          }
        ]
      };
    },
    created() {
      if (this.$route.query.i == "1") {
        this.statusOf = "添加模板";
        this.editTemDialog_status = 1; // 新增模式
        // 获取签名列表
        this.getpassLabels();
      } else {
        this.statusOf = "编辑模板";
        this.editTemDialog_status = 0; // 编辑模式
        this.getTem();
      }
    },
    methods: {
      // 获取模板信息
      getTem() {
        this.$api.post(
          this.API.cpus + "v3/consumersmstemplate/page",
          {
            param: this.$route.query.param,
            currentPage: 1,
            pageSize: 200,
          },
          (res) => {
            if (!res.records || res.records.length === 0) {
              this.$message.error("获取模板信息失败");
              return;
            }

            const row = res.records[0];
            this.rowDatas = row;



            // 提取签名
            const regexCenter = /\【(.+?)\】/g;
            const sings = row.temContent.match(regexCenter);

            if (!sings || sings.length === 0) {
              this.$message.warning("模板内容中未找到签名");
            }

            // 解析模板参数
            try {
              this.form.params = row.text ? JSON.parse(row.text) : {};

            } catch (e) {
              console.error("解析模板参数失败", e);
              this.form.params = {};
            }

            // 设置表单数据
            this.form.temName = row.temName;
            this.form.temType = parseInt(row.temType); // 确保是数字类型
            this.form.signId = sings ? sings[0] : "";
            this.form.temContent = row.temContent;
            this.form.temId = row.temId;
            this.form.remark = row.remark || "";
            this.form.temFormat = row.temFormat || 1;

            // 设置编辑状态，禁用模版类型选择
            this.editTemDialog_status = 0;

            // 处理变量列表 - 编辑模式下需要正确回显
            this.getcode = this.getNewCodeList();



            // 同步到增强的变量管理系统（但不覆盖已有的变量类型配置）
            this.extractVariablesFromContent(this.form.temContent, true); // 传入 true 表示编辑模式

            // 设置其他可选字段
            if (row.autoSwitch) {
              this.form.autoSwitch = row.autoSwitch;
            }
            if (row.longUrl) {
              this.form.longUrl = row.longUrl;
            }
            if (row.shortCode) {
              this.form.shortCode = row.shortCode;
            }
            if (row.checked !== undefined) {
              this.form.checked = row.checked;
            }

            // 获取签名列表
            this.getpassLabels();
          }
        );
      },
  
      // 切换模板类型
      changeTemType(val) {
        if (!val) return;
        
        this.$alert('切换模板会清除刚刚添加的参数以及内容!', '提示', {
          confirmButtonText: '确定',
          callback: action => {
            if (action == 'confirm') {
              // 更新模板格式
              // if (val == '3') {
              //   this.form.temFormat = '2';
              //   this.placeholderText = '请输入短信内容';
              // } else {
              //   this.form.temFormat = '1';
              //   this.placeholderText = '请输入短信内容，如需要变量例如：{name}';
              // }
              // this.form.temFormat = '2';
              this.placeholderText = '请输入短信内容，如需要变量例如：{name}';
              // 重置表单
              this.form = {
                params: {}, // 模板变量
                autoSwitch: false, // 是否自动切换短链 false：否, true：是"
                longUrl: "", // 长链接
                submit: "", // 是否提交审核 0：否, 1：是"
                temFormat: 1, // 1.变量模板；2是全文模板
                signId: "", // 签名
                temName: "", // 模板名称
                temContent: "", // 模板内容
                temType: val, // 模板类型
                shortCode: "", // 短链Code
                remark: "",
                checked: false
              };
              this.getcode = [];
            } else {
              // 恢复原来的模板类型
              this.form.temType = this.oldTemType;
            }
          }
        });
      },
  
      // 获取新的代码列表
      getNewCodeList() {
        // 构建变量对象数组
        let objNew = [];
        for (let i in this.form.params) {
          objNew.push({ value: i, codeName: this.form.params[i] });
        }

        // 匹配内容中的变量
        const compareArr = this.form.temContent.match(/{(.+?)}/g) || [];
        const result = [];

        // 提取变量名
        const extractedVariables = compareArr.map(item =>
          item.replace("{", "").replace("}", "")
        );

        // 已存在的变量名
        const existingValues = objNew.map(item => item.value);

        // 处理每个变量
        extractedVariables.forEach(varName => {
          const existingVarIndex = existingValues.indexOf(varName);

          if (existingVarIndex === -1) {
            // 新变量
            result.push({
              value: varName,
              codeName: ""
            });
          } else {
            // 已有变量 - 恢复原有的类型配置
            result.push({
              value: varName,
              codeName: objNew[existingVarIndex].codeName
            });
          }
        });

        return result;
      },
  
      // 切换签名
      changeLocationValue(val) {
        this.signatureTit = val;
      },
  
      // 获取全部用户签名
      getpassLabels() {
        this.$api.post(
          this.API.cpus + "signature/signatureList",
          {
            auditStatus: "2",
            currentPage: 1,
            pageSize: 200,
          },
          (res) => {
            if (res && res.records) {
              this.labelName = res.records;
            }
          }
        );
      },
  
      // 返回
      goBack() {
        this.$router.push("/TemplateManagement");
      },
  
      // 切换到旧版模板
      handelNewTemp() {
        const { i, param } = this.$route.query;
        if (i) {
          this.$router.push({ path: '/CreateTemplate', query: { i: "1" } });
        } else {
          this.$router.push({ path: '/CreateTemplate', query: { param } });
        }
      },
  
      // 处理输入内容
      handelInput(newVal) {
        // 检查"拒收请回复R"
        const reg = new RegExp("拒收请回复R");
        if (this.form.temType == '3' && reg.test(newVal)) {
          this.form.checked = true;
        } else {
          this.form.checked = false;
        }

        // 提取变量到新的管理系统
        this.extractVariablesFromContent(newVal);

        // 匹配变量
        const regex3 = /{(.+?)}/g; // 匹配花括号内的内容
        const regex4 = /{(.+?)}{(.+?)}/g; // 匹配连续的变量
        const arr = newVal.match(regex3);

        // 验证变量规则
        const validationResult = this.validateVariables(newVal, arr);
        if (!validationResult.isValid) {
          if (!this.timmer) {
            this.$message({
              type: "warning",
              message: validationResult.message
            });
            this.timmer = setTimeout(() => {
              this.timmer = null;
            }, 2000);
          }
          return;
        }

        // 检查变量是否有重复
        const isRepeat = (arr) => {
          const hash = {};
          for (let i in arr) {
            if (hash[arr[i]]) {
              return true;
            }
            hash[arr[i]] = true;
          }
          return false;
        };
  
        // 验证码模板处理 - 强校验
        if (this.form.temType == 1) {
          if (!arr) {
            this.getcode = [];
            return;
          }

          // 验证码类型严格限制：只能有一个变量
          if (arr.length > 1) {
            // 强校验：阻止第二个变量进入管理
            if (!this.timmer) {
              this.$message({
                type: "error",
                message: `验证码类型模版只能包含一个变量，当前检测到${arr.length}个变量，请删除多余的变量！`,
                duration: 3000
              });
              this.timmer = setTimeout(() => {
                this.timmer = null;
              }, 3000);
            }

            // 不更新变量列表，保持原有状态
            return;
          }

          // 只有一个变量时才允许更新
          // 保存当前代码设置
          const saveArr = JSON.parse(JSON.stringify(this.getcode));

          // 更新变量列表
          this.getcode = arr.map(item => ({
            value: item.replace("{", "").replace("}", ""),
            codeName: ""
          }));

          // 恢复之前的变量类型设置
          this.getcode.forEach(gcItem => {
            saveArr.forEach(taItem => {
              if (gcItem.value === taItem.value) {
                gcItem.codeName = taItem.codeName;
              }
            });
          });
        }
        // 行业通知模板处理
        else {
          // this.form.temFormat = "1";
          
          if (!arr) {
            this.getcode = [];
            return;
          }
          
          if (arr.length > 16) {
            // 变量数量限制
            this.$message({
              type: "warning",
              message: "变量模板参数不能超过16个"
            });
          } else if (isRepeat(arr)) {
            // 变量名不能重复
            this.$message({
              message: "已选变量名称不能重复",
              type: "warning"
            });
          } else if (regex4.test(newVal)) {
            // 变量不能连续
            if (!this.timmer) {
              this.$message({
                type: "warning",
                message: "两个参数不可挨在一起，中间至少有一个字符隔开。"
              });
              this.timmer = setTimeout(() => {
                this.timmer = null;
              }, 2000);
            }
            this.getcode = [];
          } else {
            // 正常处理变量
            if (this.statusOf == "添加模板") {
              // 保存当前代码设置
              const saveArr = JSON.parse(JSON.stringify(this.getcode));
              
              // 更新变量列表
              this.getcode = arr.map(item => ({
                value: item.replace("{", "").replace("}", ""),
                codeName: ""
              }));
              
              // 恢复之前的变量类型设置
              this.getcode.forEach(gcItem => {
                saveArr.forEach(taItem => {
                  if (gcItem.value === taItem.value) {
                    gcItem.codeName = taItem.codeName;
                  }
                });
              });
            } else {
              // 编辑模板场景 - 使用统一的变量处理逻辑
              // 保存当前代码设置
              const saveArr = JSON.parse(JSON.stringify(this.getcode));

              // 更新变量列表
              this.getcode = arr.map(item => ({
                value: item.replace("{", "").replace("}", ""),
                codeName: ""
              }));

              // 恢复之前的变量类型设置
              this.getcode.forEach(gcItem => {
                saveArr.forEach(taItem => {
                  if (gcItem.value === taItem.value) {
                    gcItem.codeName = taItem.codeName;
                  }
                });
              });
            }
          }
        } 
        // 会员营销模板处理
        // else {
        //   this.form.temFormat = "2";
        // }
      },
  
      // 变量类型变更
      typeChange(value, item) {
        if (!value || !item.value) return;

        // 验证码类型特殊验证
        if (this.form.temType == 1 && value === 'valid_code') {
          // 检查是否已经有其他验证码变量
          const existingValidCodeCount = this.getcode.filter(code =>
            code.codeName === 'valid_code' && code.value !== item.value
          ).length;

          if (existingValidCodeCount > 0) {
            this.$message({
              type: "warning",
              message: "验证码类型模版只能包含一个验证码变量"
            });
            return;
          }
        }

        // 更新变量类型
        this.arrStorage.forEach(asItem => {
          if (asItem.value === item.value) {
            asItem.codeName = value;
          }
        });

        // 更新当前变量列表中的类型
        const currentItem = this.getcode.find(code => code.value === item.value);
        if (currentItem) {
          currentItem.codeName = value;
        }

        // 同步到表单参数
        this.updateFormParams();

        // 验证完成后的额外检查
        this.$nextTick(() => {
          this.validateVerificationCodeVariables();
        });
      },

      // 更新表单参数
      updateFormParams() {
        this.form.params = {};

        // 从 getcode 数组更新参数
        this.getcode.forEach(item => {
          if (item.codeName) {
            this.form.params[item.value] = item.codeName;
          }
        });

        // 同时从 extractedVariables 更新参数（如果存在）
        if (this.extractedVariables && this.extractedVariables.length > 0) {
          this.extractedVariables.forEach(variable => {
            if (variable.type) {
              this.form.params[variable.name] = variable.type;
            }
          });

          // 同步更新 getcode 数组（保持兼容性）
          this.getcode = this.extractedVariables.map(variable => ({
            value: variable.name,
            codeName: variable.type
          }));
        }


      },

      // 获取已配置变量数量
      getConfiguredVariablesCount() {
        return this.getcode.filter(item => item.codeName).length;
      },

      // 获取已配置变量标签类型
      getConfiguredVariablesTagType() {
        const total = this.getcode.length;
        const configured = this.getConfiguredVariablesCount();

        if (configured === total) {
          return 'success';
        } else if (configured > 0) {
          return 'warning';
        } else {
          return 'danger';
        }
      },

      // 获取变量类型标签类型
      getVariableTypeTagType(type) {
        const typeTagMap = {
          'valid_code': 'success',
          'mobile_number': 'primary',
          'other_number': 'info',
          'amount': 'warning',
          'date': 'primary',
          'chinese': 'success',
          'others': 'info'
        };
        return typeTagMap[type] || 'info';
      },

      // 获取变量示例
      getVariableExamples(type) {
        const typeObj = this.variableTypes.find(t => t.value === type);
        return typeObj ? typeObj.examples.slice(0, 2).join('、') : '';
      },

      // 智能配置变量类型
      autoConfigureVariables() {
        if (this.form.temType == 1) {
          this.$message({
            type: "info",
            message: "验证码类型模版不支持智能配置"
          });
          return;
        }

        this.getcode.forEach(variable => {
          if (!variable.codeName) {
            // 根据变量名智能推荐类型
            const varName = variable.value.toLowerCase();

            if (varName.includes('code') || varName.includes('验证')) {
              variable.codeName = 'valid_code';
            } else if (varName.includes('phone') || varName.includes('mobile') || varName.includes('手机')) {
              variable.codeName = 'mobile_number';
            } else if (varName.includes('amount') || varName.includes('money') || varName.includes('金额') || varName.includes('价格')) {
              variable.codeName = 'amount';
            } else if (varName.includes('date') || varName.includes('time') || varName.includes('时间') || varName.includes('日期')) {
              variable.codeName = 'date';
            } else if (varName.includes('name') || varName.includes('姓名') || varName.includes('用户')) {
              variable.codeName = 'chinese';
            } else if (varName.includes('order') || varName.includes('订单') || varName.includes('number') || varName.includes('编号')) {
              variable.codeName = 'other_number';
            } else {
              variable.codeName = 'others';
            }
          }
        });

        this.updateFormParams();

        this.$message({
          type: "success",
          message: "智能配置完成！"
        });
      },

      // 重置变量类型
      resetVariableTypes() {
        this.$confirm('确定要重置所有变量的类型配置吗？', '重置确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.getcode.forEach(variable => {
            variable.codeName = '';
          });
          this.updateFormParams();

          this.$message({
            type: "success",
            message: "重置完成！"
          });
        }).catch(() => {
          // 用户取消
        });
      },
  
      // 测试变量变更
      testchange(_item, _index) {
        // 占位方法，目前未使用
      },
  
      // 打开短链转换对话框
      shortReset() {
        this.temshortVisible = true;
      },
  
      // 长链转换为短链
      transformation() {
        if (!this.originalUrl) {
          this.$message({
            message: "长链接不可为空",
            type: "warning"
          });
          return;
        }
        
        this.$api.post(
          this.API.slms + "v3/shortLink/add",
          { originalUrl: this.originalUrl },
          (res) => {
            if (res.code == 200) {
              this.shortConUrl = res.data.shortLinkUrl;
              this.shortCode = res.data.shortCode;
              this.$message({
                message: "短链接转换成功！",
                type: "success"
              });
            } else {
              this.originalUrl = "";
              this.$message({
                message: res.msg,
                type: "warning"
              });
            }
          }
        );
      },
  
      // 预览短链
      handlePreview() {
        if (this.shortConUrl) {
          window.open(this.shortConUrl, "_blank");
        } else {
          this.$message({
            message: "短连接为空，无法预览",
            type: "warning"
          });
        }
      },
  
      // 确认使用短链
      shortConDetermine() {
        if (this.shortConUrl) {
          this.form.temContent += this.shortConUrl;
          this.isShort = true;
          this.temshortVisible = false;
        } else {
          this.$message({
            message: "短链接不可为空",
            type: "warning"
          });
        }
      },
  
      // 取消短链转换
      handleCancles() {
        this.temshortVisible = false;
        this.isShort = false;
      },
  
      // 自动识别链接
      handAutoSwitch(val) {
        if (val) {
          this.$api.post(
            this.API.cpus + "v3/consumersmstemplate/longUrl",
            {
              temContent: this.form.temContent
            },
            (res) => {
              if (res.data) {
                this.form.longUrl = res.data;
              }
            }
          );
        } else {
          this.form.longUrl = "";
        }
      },
  
      // 处理拒收回复
      handleQuite(val) {
        if (val) {
          if (!this.form.temContent.includes("，拒收请回复R")) {
            this.form.temContent = this.form.temContent + "，拒收请回复R";
          }
        } else {
          const reg = new RegExp("，拒收请回复R");
          this.form.temContent = this.form.temContent.replace(reg, "");
        }
      },
  
      // 提交模板
      addTemOk(_val, val2) {
        this.$refs.temForm.validate((valid) => {
          if (!valid) return;

          // 额外的变量验证
          if (!this.validateVerificationCodeVariables()) {
            return;
          }

          // 检查变量配置完整性
          const unconfiguredVariables = this.getcode.filter(item => !item.codeName);
          if (unconfiguredVariables.length > 0) {
            this.$message({
              type: "warning",
              message: `请为所有变量配置类型，还有 ${unconfiguredVariables.length} 个变量未配置`
            });
            return;
          }
          
          // 验证签名匹配
          const matches = this.form.temContent.match(/【.*?】/g);
          if (!matches || matches.length === 0) {
            this.$message({
              message: '模板内容中未找到签名！',
              type: 'warning'
            });
            return;
          }
          
          if (this.form.signId !== matches[0]) {
            this.$message({
              message: '选择的签名与模板内容签名不匹配！',
              type: 'warning'
            });
            return;
          }
          
          // 验证营销类短信是否包含拒收回复R
          const reg = new RegExp("拒收请回复R");
          if (this.form.temType == '3' && !reg.test(this.form.temContent)) {
            this.$message({
              message: '请勾选拒收请回复为R!',
              type: 'warning'
            });
            return;
          }
          
          // 准备变量参数
          this.form.params = {};
          for (let i = 0; i < this.getcode.length; i++) {
            const varName = this.getcode[i].value.replace("{", "").replace("}", "");
            this.form.params[varName] = this.getcode[i].codeName;
          }
          
          // 设置提交状态
          this.form.submit = val2;
          
          // 验证变量参数是否都有类型
          let showToast = false;
          this.getcode.forEach(item => {
            if (!item.codeName) {
              showToast = true;
            }
          });
          
          if (showToast) {
            this.$message({
              message: '参数内容不能为空!',
              type: 'warning'
            });
            return;
          }
          if(this.getcode.length == 0) {
            this.form.temFormat = 2;
          }else{
            this.form.temFormat = 1;
          }
          
          // 选择请求路径
          let requestUrl = this.$route.query.param 
            ? "v3/consumersmstemplate/template/update" 
            : "v3/consumersmstemplate/template/add";
            
          if (this.$route.query.param) {
            this.form.temId = this.$route.query.param;
          }
          
          // 提交请求
          this.$confirms.confirmation(
            "post",
            "确认新增模板？",
            this.API.cpus + requestUrl,
            this.form,
            (res) => {
              if (res.code == 200) {
                this.goBack();
              }
            }
          );
        });
      },
  
      // 取消
      calcelTem() {
        this.$router.push("/TemplateManagement");
      },
  
      // 计算字符数
      countNum1(val) {
        if (!val) return 0;
        return val.length;
      },
  
      // ========== 智能识别相关方法 ==========
  
      // 切换智能模式显示
      toggleSmartMode() {
        this.showSmartMode = !this.showSmartMode;
      },
  
      // 清空智能示例
      clearSmartExample() {
        this.smartExample = "";
        this.recognitionResults = [];
        this.generatedTemplate = "";
      },
  
      // 分析示例文本
      analyzeExample() {
        if (!this.smartExample.trim()) {
          this.$message({
            message: "请输入示例文本",
            type: "warning"
          });
          return;
        }

        this.analyzing = true;

        // 模拟分析过程
        setTimeout(() => {
          this.recognitionResults = this.performTextAnalysis(this.smartExample);

          // 验证识别结果是否符合当前模版类型的规则
          if (this.recognitionResults.length > 0) {
            // 验证码类型特殊检查
            if (this.form.temType == 1 && this.recognitionResults.length > 1) {
              this.$message({
                message: `验证码类型只能有1个变量，识别到${this.recognitionResults.length}个变量，请修改示例文本`,
                type: "warning"
              });
              this.analyzing = false;
              return;
            }

            // 其他类型变量数量检查
            if (this.form.temType != 1 && this.recognitionResults.length > 16) {
              this.$message({
                message: `变量数量不能超过16个，识别到${this.recognitionResults.length}个变量，请简化示例文本`,
                type: "warning"
              });
              this.analyzing = false;
              return;
            }
          }

          this.generatedTemplate = this.generateTemplateFromResults();
          this.analyzing = false;

          if (this.recognitionResults.length === 0) {
            this.$message({
              message: "未识别到可变内容，请检查示例文本",
              type: "info"
            });
          } else {
            this.$message({
              message: `成功识别到 ${this.recognitionResults.length} 个变量`,
              type: "success"
            });
          }
        }, 1000);
      },
  
      // 执行文本分析
      performTextAnalysis(text) {
        const results = [];
  
        // 定义各种模式和对应的变量类型
        const patterns = [
          // 验证码模式 (4-6位数字或字母数字组合)
          {
            regex: /\b\d{4,6}\b/g,
            type: 'valid_code',
            namePrefix: 'code'
          },
          // 手机号模式
          {
            regex: /1[3-9]\d{9}/g,
            type: 'mobile_number',
            namePrefix: 'phone'
          },
          // 金额模式 (包含小数点的数字)
          {
            regex: /\d+\.?\d*元?/g,
            type: 'amount',
            namePrefix: 'amount'
          },
          // 时间模式 (各种时间格式)
          {
            regex: /\d{1,2}[年月日时分秒]/g,
            type: 'date',
            namePrefix: 'time'
          },
          {
            regex: /\d{4}-\d{1,2}-\d{1,2}/g,
            type: 'date',
            namePrefix: 'date'
          },
          {
            regex: /\d{1,2}:\d{1,2}/g,
            type: 'date',
            namePrefix: 'time'
          },
          // 订单号、编号等 (字母数字组合)
          {
            regex: /[A-Z0-9]{6,}/g,
            type: 'other_number',
            namePrefix: 'orderNo'
          },
          // 纯数字 (其他号码)
          {
            regex: /\b\d{3,}\b/g,
            type: 'other_number',
            namePrefix: 'number'
          }
        ];
  
        let usedNames = new Set();
        let nameCounters = {};
  
        patterns.forEach(pattern => {
          let match;
          while ((match = pattern.regex.exec(text)) !== null) {
            const originalValue = match[0];
  
            // 生成唯一的变量名
            let baseName = pattern.namePrefix;
            if (!nameCounters[baseName]) {
              nameCounters[baseName] = 1;
            }
  
            let variableName = baseName;
            if (nameCounters[baseName] > 1) {
              variableName = `${baseName}${nameCounters[baseName]}`;
            }
  
            while (usedNames.has(variableName)) {
              nameCounters[baseName]++;
              variableName = `${baseName}${nameCounters[baseName]}`;
            }
  
            usedNames.add(variableName);
            nameCounters[baseName]++;
  
            // 检查是否已经识别过这个值
            const existingResult = results.find(r => r.originalValue === originalValue);
            if (!existingResult) {
              results.push({
                originalValue,
                variableName,
                suggestedType: pattern.type,
                confirmed: false,
                startIndex: match.index,
                endIndex: match.index + originalValue.length
              });
            }
          }
        });
  
        // 按照在文本中出现的位置排序
        results.sort((a, b) => a.startIndex - b.startIndex);
  
        return results;
      },
  
      // 从识别结果生成模版
      generateTemplateFromResults() {
        if (this.recognitionResults.length === 0) {
          return this.smartExample;
        }
  
        let template = this.smartExample;
  
        // 从后往前替换，避免索引位置变化
        const sortedResults = [...this.recognitionResults].sort((a, b) => b.startIndex - a.startIndex);
  
        sortedResults.forEach(result => {
          const before = template.substring(0, result.startIndex);
          const after = template.substring(result.endIndex);
          template = before + `{${result.variableName}}` + after;
        });
  
        return template;
      },
  
      // 获取变量类型标签
      getVariableTypeLabel(type) {
        const typeLabels = {
          'valid_code': '验证码',
          'mobile_number': '手机号',
          'amount': '金额',
          'date': '时间',
          'other_number': '其他号码',
          'chinese': '中文',
          'others': '其他'
        };
        return typeLabels[type] || '其他';
      },
  
      // 移除识别结果
      removeRecognitionResult(index) {
        this.recognitionResults.splice(index, 1);
        this.generatedTemplate = this.generateTemplateFromResults();
      },
  
      // 编辑变量名
      editVariableName(index) {
        const result = this.recognitionResults[index];
        this.$prompt('请输入新的变量名', '编辑变量名', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: result.variableName,
          inputValidator: (value) => {
            if (!value) {
              return '变量名不能为空';
            }
            if (!/^[a-zA-Z][a-zA-Z0-9]*$/.test(value)) {
              return '变量名只能包含字母和数字，且必须以字母开头';
            }
            // 检查是否与其他变量名重复
            const isDuplicate = this.recognitionResults.some((r, i) =>
              i !== index && r.variableName === value
            );
            if (isDuplicate) {
              return '变量名不能重复';
            }
            return true;
          }
        }).then(({ value }) => {
          this.recognitionResults[index].variableName = value;
          this.generatedTemplate = this.generateTemplateFromResults();
          this.$message({
            type: 'success',
            message: '变量名修改成功'
          });
        }).catch(() => {
          // 用户取消
        });
      },
  
      // 应用模版
      applyTemplate() {
        if (!this.generatedTemplate) {
          this.$message({
            message: "没有可应用的模版",
            type: "warning"
          });
          return;
        }

        // 验证生成的模版是否符合规则
        const variables = this.generatedTemplate.match(/{(.+?)}/g);
        const validationResult = this.validateVariables(this.generatedTemplate, variables);

        if (!validationResult.isValid) {
          this.$message({
            type: "warning",
            message: "生成的模版不符合规则：" + validationResult.message
          });
          return;
        }

        // 应用生成的模版到表单
        this.form.temContent = this.generatedTemplate;

        // 触发输入处理，更新变量列表
        this.handelInput(this.generatedTemplate);
  
        // 自动设置变量类型
        this.recognitionResults.forEach(result => {
          const matchingCode = this.getcode.find(code =>
            code.value === result.variableName
          );
          if (matchingCode) {
            matchingCode.codeName = result.suggestedType;
          }
        });
  
        this.$message({
          type: 'success',
          message: '模版应用成功！'
        });
  
        // 收起智能识别区域
        this.showSmartMode = false;
      },

      // ========== 变量验证相关方法 ==========

      // 验证变量规则
      validateVariables(content, variableArray) {
        if (!variableArray || variableArray.length === 0) {
          return { isValid: true };
        }

        // 1. 验证码类型特殊规则：严格限制只能有一个变量
        if (this.form.temType == 1) {
          if (variableArray.length > 1) {
            return {
              isValid: false,
              message: `验证码类型模版只能包含一个变量，当前检测到${variableArray.length}个变量`
            };
          }
        }
        // 2. 其他类型（行业通知、会员营销）：最多16个变量
        else {
          if (variableArray.length > 16) {
            return {
              isValid: false,
              message: `变量数量不能超过16个，当前有${variableArray.length}个变量`
            };
          }
        }

        // 3. 检查变量是否挨着（连续的变量）
        const adjacentVariablesRegex = /}[\s]*{/g;
        if (adjacentVariablesRegex.test(content)) {
          return {
            isValid: false,
            message: "变量之间不能挨着，请在变量之间添加文字或符号"
          };
        }

        // 4. 检查变量名格式
        for (let i = 0; i < variableArray.length; i++) {
          const variableName = variableArray[i].replace('{', '').replace('}', '');

          // 变量名不能为空
          if (!variableName.trim()) {
            return {
              isValid: false,
              message: "变量名不能为空"
            };
          }

          // 变量名只能包含字母、数字和下划线，且必须以字母开头
          const variableNameRegex = /^[a-zA-Z][a-zA-Z0-9_]*$/;
          if (!variableNameRegex.test(variableName)) {
            return {
              isValid: false,
              message: `变量名"${variableName}"格式不正确，只能包含字母、数字和下划线，且必须以字母开头`
            };
          }

          // 变量名长度限制
          if (variableName.length > 32) {
            return {
              isValid: false,
              message: `变量名"${variableName}"过长，最多32个字符`
            };
          }
        }

        // 5. 检查变量重复
        const variableNames = variableArray.map(v => v.replace('{', '').replace('}', ''));
        const uniqueNames = [...new Set(variableNames)];
        if (variableNames.length !== uniqueNames.length) {
          return {
            isValid: false,
            message: "存在重复的变量名"
          };
        }

        return { isValid: true };
      },

      // 检查验证码类型变量限制
      validateVerificationCodeVariables() {
        if (this.form.temType !== 1) return true;

        const validCodeVariables = this.getcode.filter(item =>
          item.codeName === 'valid_code'
        );

        if (validCodeVariables.length > 1) {
          this.$message({
            type: "warning",
            message: "验证码类型模版只能包含一个验证码变量"
          });
          return false;
        }

        return true;
      },

      // ========== 现代化界面相关方法 ==========

      // 选择模版类型
      selectTemplateType(typeValue) {
        if (this.editTemDialog_status == 0) return; // 编辑状态下不允许修改

        // 如果选择的是当前类型，直接返回
        if (this.form.temType === typeValue) return;

        // 检查当前内容是否符合新类型的规则
        let needConfirm = false;
        let confirmMessage = '切换模版类型将清空当前的模版内容、变量配置和智能识别结果，是否继续？';

        // 如果切换到验证码类型，且当前有多个变量
        if (typeValue === 1 && this.getcode.length > 1) {
          needConfirm = true;
          confirmMessage = `切换到验证码类型只能包含1个变量，当前有${this.getcode.length}个变量。切换将清空所有内容，是否继续？`;
        }
        // 如果有任何内容
        else if (this.form.temContent || this.extractedVariables.length > 0 || this.smartExample) {
          needConfirm = true;
        }

        if (needConfirm) {
          this.$confirm(
            confirmMessage,
            '切换确认',
            {
              confirmButtonText: '确定',
              cancelButtonText: '取消',
              type: 'warning'
            }
          ).then(() => {
            this.clearAllContent();
            this.form.temType = typeValue;
            this.changeTemType(typeValue);
          }).catch(() => {
            // 用户取消，不做任何操作
          });
        } else {
          this.form.temType = typeValue;
          this.changeTemType(typeValue);
        }
      },

      // 清空所有内容
      clearAllContent() {
        // 清空模版内容
        this.form.temContent = '';

        // 清空智能识别相关数据
        this.smartExample = '';
        this.recognitionResults = [];
        this.generatedTemplate = '';
        this.showSmartMode = false;

        // 清空变量相关数据
        this.extractedVariables = [];
        this.getcode = [];
        this.form.params = {};

        // 重置其他相关状态
        this.form.autoSwitch = false;
        this.form.longUrl = '';
        this.form.checked = false;
      },

      // 获取模版类型标签样式
      getTemplateTypeTag(type) {
        const tagMap = {
          1: 'success',  // 验证码 - 绿色
          2: 'primary',  // 行业通知 - 蓝色
          3: 'warning'   // 会员营销 - 橙色
        };
        return tagMap[type] || 'info';
      },

      // 获取模版类型名称
      getTemplateTypeName(type) {
        const nameMap = {
          1: '验证码',
          2: '行业通知',
          3: '会员营销'
        };
        return nameMap[type] || '未知类型';
      },

      // 获取申请说明占位符
      getRemarkPlaceholder() {
        const placeholderMap = {
          1: '用于我公司app用户注册时发送验证码',
          2: '用于行业通知类短信发送',
          3: '用于会员营销类短信发送'
        };
        return placeholderMap[this.form.temType] || '请填写申请该模版的原因或说明';
      },

      // 获取变量数量标签类型
      getVariableCountTagType() {
        const count = this.getcode.length;

        // 验证码类型特殊处理：只能有1个变量
        if (this.form.temType == 1) {
          return count > 1 ? 'danger' : count === 1 ? 'success' : 'info';
        }

        // 其他类型（行业通知、会员营销）：最多16个变量
        if (count > 16) {
          return 'danger';
        } else if (count > 12) {
          return 'warning';
        } else if (count > 0) {
          return 'success';
        } else {
          return 'info';
        }
      },

      // 获取变量数量显示文本
      getVariableCountText() {
        const count = this.getcode.length;

        if (this.form.temType == 1) {
          // 验证码类型：显示 当前数量/1
          return `${count}/1 变量`;
        } else {
          // 其他类型：显示 当前数量/16
          return `${count}/16 变量`;
        }
      },

      // 获取变量数量提示文本
      getVariableCountTooltip() {
        const count = this.getcode.length;

        if (this.form.temType == 1) {
          if (count > 1) {
            return `验证码类型只能有1个变量，当前有${count}个变量，请删除多余变量`;
          } else if (count === 1) {
            return '验证码类型变量数量正常';
          } else {
            return '验证码类型最多可添加1个变量';
          }
        } else {
          if (count > 16) {
            return `变量数量超出限制，最多16个，当前有${count}个变量`;
          } else if (count > 12) {
            return `变量数量较多，建议控制在合理范围内`;
          } else if (count > 0) {
            return `当前变量数量正常，最多可添加16个变量`;
          } else {
            return '当前模版类型最多可添加16个变量';
          }
        }
      },

      // ========== 增强的模版变量管理方法 ==========

      // 从模版内容中提取变量
      extractVariablesFromContent(content, isEditMode = false) {
        if (!content) {
          this.extractedVariables = [];
          return;
        }

        const variableRegex = /{([^}]+)}/g;
        const matches = [];
        let match;

        while ((match = variableRegex.exec(content)) !== null) {
          const variableName = match[1].trim();
          if (variableName && !matches.find(m => m.name === variableName)) {
            matches.push({
              name: variableName,
              type: '', // 初始为空，需要用户选择
              originalValue: match[0]
            });
          }
        }

        // 在编辑模式下，优先从 form.params 和 getcode 中恢复类型配置
        if (isEditMode) {
          matches.forEach(newVar => {
            // 首先从 form.params 中查找
            if (this.form.params && this.form.params[newVar.name]) {
              newVar.type = this.form.params[newVar.name];
            }
            // 然后从 getcode 中查找
            else {
              const existingInGetcode = this.getcode.find(gc => gc.value === newVar.name);
              if (existingInGetcode && existingInGetcode.codeName) {
                newVar.type = existingInGetcode.codeName;
              }
            }
            // 最后从已有的 extractedVariables 中查找
            if (!newVar.type) {
              const existingVar = this.extractedVariables.find(v => v.name === newVar.name);
              if (existingVar) {
                newVar.type = existingVar.type;
              }
            }
          });
        } else {
          // 非编辑模式，保留已有的类型配置
          matches.forEach(newVar => {
            const existingVar = this.extractedVariables.find(v => v.name === newVar.name);
            if (existingVar) {
              newVar.type = existingVar.type;
            }
          });
        }

        this.extractedVariables = matches;

        // 在编辑模式下，不要覆盖 getcode，而是同步更新
        if (!isEditMode) {
          this.syncExtractedVariablesToGetcode();
        }
      },

      // 同步 extractedVariables 到 getcode
      syncExtractedVariablesToGetcode() {
        this.getcode = this.extractedVariables.map(variable => ({
          value: variable.name,
          codeName: variable.type
        }));
      },

      // 获取可用的变量类型（根据模版类型过滤）
      getAvailableTypes() {
        if (this.form.temType === 1) {
          // 验证码类型只能选择验证码
          return this.variableTypes.filter(type => type.value === 'valid_code');
        }
        return this.variableTypes;
      },

      // 获取变量规则说明
      getVariableRule(typeValue) {
        const type = this.variableTypes.find(t => t.value === typeValue);
        return type ? type.rule : '';
      },

      // 获取变量示例
      getVariableExamples(typeValue) {
        const type = this.variableTypes.find(t => t.value === typeValue);
        return type ? type.examples : [];
      },

      // 变量类型变更事件
      onVariableTypeChange(variable, newType) {
        variable.type = newType;
        this.updateFormParams();
      },

      // 编辑变量名
      editVariableNameInList(index) {
        const variable = this.extractedVariables[index];
        this.$prompt('请输入新的变量名', '重命名变量', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValue: variable.name,
          inputValidator: (value) => {
            if (!value) {
              return '变量名不能为空';
            }
            if (!/^[a-zA-Z][a-zA-Z0-9_]*$/.test(value)) {
              return '变量名只能包含字母、数字和下划线，且必须以字母开头';
            }
            // 检查是否与其他变量名重复
            const isDuplicate = this.extractedVariables.some((v, i) =>
              i !== index && v.name === value
            );
            if (isDuplicate) {
              return '变量名不能重复';
            }
            return true;
          }
        }).then(({ value }) => {
          const oldName = variable.name;
          variable.name = value;

          // 更新模版内容中的变量名
          this.form.temContent = this.form.temContent.replace(
            new RegExp(`{${oldName}}`, 'g'),
            `{${value}}`
          );

          this.updateFormParams();

          this.$message({
            type: 'success',
            message: '变量名修改成功'
          });
        }).catch(() => {
          // 用户取消
        });
      },

      // 删除变量
      removeVariable(index) {
        const variable = this.extractedVariables[index];

        this.$confirm(`确定要删除变量 {${variable.name}} 吗？`, '删除确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          // 从模版内容中移除该变量
          this.form.temContent = this.form.temContent.replace(
            new RegExp(`{${variable.name}}`, 'g'),
            `[${variable.name}]` // 替换为方括号，提示用户
          );

          // 从变量列表中移除
          this.extractedVariables.splice(index, 1);
          this.updateFormParams();

          this.$message({
            type: 'success',
            message: '变量删除成功'
          });
        }).catch(() => {
          // 用户取消
        });
      },

      // 智能配置变量类型
      autoConfigureVariables() {
        this.extractedVariables.forEach(variable => {
          if (!variable.type) {
            // 根据变量名智能推荐类型
            const name = variable.name.toLowerCase();

            if (name.includes('code') || name.includes('验证码')) {
              variable.type = 'valid_code';
            } else if (name.includes('phone') || name.includes('mobile') || name.includes('电话')) {
              variable.type = 'mobile_number';
            } else if (name.includes('amount') || name.includes('money') || name.includes('金额') || name.includes('价格')) {
              variable.type = 'amount';
            } else if (name.includes('date') || name.includes('time') || name.includes('时间') || name.includes('日期')) {
              variable.type = 'date';
            } else if (name.includes('order') || name.includes('number') || name.includes('id') || name.includes('编号')) {
              variable.type = 'other_number';
            } else if (name.includes('name') || name.includes('姓名') || name.includes('用户')) {
              variable.type = 'chinese';
            } else {
              variable.type = 'others';
            }
          }
        });

        this.updateFormParams();

        this.$message({
          type: 'success',
          message: '智能配置完成'
        });
      },

      // 重置变量类型
      resetVariableTypes() {
        this.$confirm('确定要重置所有变量的类型配置吗？', '重置确认', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.extractedVariables.forEach(variable => {
            variable.type = '';
          });

          this.updateFormParams();

          this.$message({
            type: 'success',
            message: '变量类型已重置'
          });
        }).catch(() => {
          // 用户取消
        });
      },


    },
    computed: {
      // 弹窗标题
      editTemDialog_title() {
        return this.editTemDialog_status == "1" ? "新增短信模板" : "编辑短信模板";
      },

      // 短信条数
      numTextMsg2() {
        if (this.wordCount2 === 0) return 0;

        if (this.wordCount2 <= 70) {
          return 1;
        } else {
          return Math.ceil(this.wordCount2 / 67);
        }
      },

      // 字数统计
      wordCount2() {
        if (!this.form.temContent) return 0;

        if (this.form.temContent === " ") return 0;

        if (this.form.temContent.length === 2 && this.form.temContent.indexOf(" ") !== -1) {
          return 1;
        }

        return this.countNum1(this.form.temContent);
      },

      // 已配置变量数量
      configuredVariablesCount() {
        return this.extractedVariables.filter(v => v.type).length;
      },

      // 未配置变量数量
      unconfiguredVariablesCount() {
        return this.extractedVariables.filter(v => !v.type).length;
      }
    },
    mounted() {
      this.getpassLabels(); // 获取签名
    },
    watch: {
      // 监听签名变化
      "form.signId": {
        handler(_newVal, oldVal) {
          // if (!newVal || !oldVal) return;

          this.form.temContent = this.signatureTit + this.form.temContent;
          this.form.temContent = this.form.temContent.replace(oldVal, "");
        }
      },
      
      // 保存原模板类型
      'form.temType': {
        handler(_newVal, oldVal) {
          this.oldTemType = oldVal;
        },
        immediate: true
      },
      
      // 监听短链弹框关闭
      temshortVisible(val) {
        if (val === false) {
          this.originalUrl = ""; // 清空长链接
          this.shortConUrl = ""; // 清空短链接
        }
      },
      
      // 监听变量列表变化
      getcode: {
        handler(newVal, _oldVal) {
          if (!newVal || newVal.length === 0) return;
          
          newVal.forEach(item => {
            let pushFlag = true;
            
            this.arrStorage.forEach(asItem => {
              if (item.value === asItem.value) {
                item.codeName = asItem.codeName;
                pushFlag = false;
              }
            });
            
            if (pushFlag) {
              this.arrStorage.push(item);
            }
          });
        },
        immediate: true,
        deep: true
      }
    }
  };
  </script>
  
  <style lang="less" scoped>
// 现代化模版页面样式
.modern-template-page {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);

  // 页面头部样式
  .page-header {
    background: #ffffff;
    border-bottom: 1px solid #e8eaec;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
    position: sticky;
    top: 0;
    z-index: 100;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 16px 24px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .header-left {
        display: flex;
        align-items: center;
        gap: 16px;

        .back-btn {
          color: #1890ff;
          font-size: 14px;
          padding: 8px 16px;
          border-radius: 6px;
          transition: all 0.3s ease;

          &:hover {
            background: #e6f7ff;
            color: #40a9ff;
          }

          i {
            margin-right: 6px;
          }
        }

        .page-title {
          margin: 0;
          font-size: 20px;
          font-weight: 600;
          color: #262626;
        }
      }

      .header-right {
        .el-tag {
          font-size: 12px;
          padding: 4px 12px;
          border-radius: 12px;
          font-weight: 500;
        }
      }
    }
  }

  // 主要内容区域
  .page-content {
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px;
  }

  // 现代化表单样式
  .modern-form {
    .form-card {
      margin-bottom: 24px;
      border-radius: 12px;
      border: 1px solid #e8eaec;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;

      &:hover {
        box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
        transform: translateY(-2px);
      }

      .card-header {
        display: flex;
        justify-content: space-between;
        align-items: center;

        .card-title {
          font-size: 16px;
          font-weight: 600;
          color: #262626;

          i {
            margin-right: 8px;
            color: #1890ff;
            font-size: 18px;
          }
        }

        .help-icon {
          color: #8c8c8c;
          cursor: pointer;
          transition: color 0.3s ease;

          &:hover {
            color: #1890ff;
          }
        }

        .header-actions {
          display: flex;
          align-items: center;
          gap: 12px;
        }
      }
    }

    // 模版类型选择卡片
    .template-type-card {
      .template-type-form-item {
        margin-bottom: 0;

        /deep/ .el-form-item__content {
          margin-left: 0 !important;
        }
      }

      .template-type-selector {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 16px;

        .type-option-card {
          border: 2px solid #e8eaec;
          border-radius: 12px;
          padding: 20px;
          cursor: pointer;
          transition: all 0.3s ease;
          background: #ffffff;
          position: relative;

          &:hover {
            border-color: #40a9ff;
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
            transform: translateY(-2px);
          }

          &.active {
            border-color: #1890ff;
            background: linear-gradient(135deg, #e6f7ff 0%, #f0f9ff 100%);

            .type-check {
              opacity: 1;
            }
          }

          &.disabled {
            cursor: not-allowed;
            opacity: 0.6;

            &:hover {
              border-color: #e8eaec;
              box-shadow: none;
              transform: none;
            }
          }

          .type-icon {
            width: 48px;
            height: 48px;
            border-radius: 12px;
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 16px;

            i {
              font-size: 24px;
              color: #ffffff;
            }
          }

          .type-content {
            .type-title {
              font-size: 16px;
              font-weight: 600;
              color: #262626;
              margin: 0 0 8px 0;
            }

            .type-description {
              font-size: 13px;
              color: #8c8c8c;
              line-height: 1.5;
              margin: 0;
            }
          }

          .type-check {
            position: absolute;
            top: 16px;
            right: 16px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: #52c41a;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;

            i {
              font-size: 14px;
              color: #ffffff;
            }
          }
        }
      }
    }
  }
}

  .user-modifi-cation {
    padding: 10px;
  }
  
  .fillet {
    border-radius: 5px;
    background-color: #fff;
    padding: 15px;
  }
  
  .Top_title {
    display: flex;
    align-items: center;
    font-weight: bold;
  }
  
  .template-btn-1 {
    transition: all 0.3s;
    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }
  }
  
  .tem-be-careful {
    margin: 10px 0;
    padding: 8px;
    background-color: #f8f8f8;
    border-radius: 4px;
    
    .tem-font {
      font-size: 12px;
      line-height: 1.5;
      margin-right: 5px;
      
      span {
        font-weight: bold;
        color: #409EFF;
      }
    }
  }
  
  .Signature-matter {
    margin-top: 50px;
    border: 1px solid #66ccff;
    margin-left: 80px;
    margin-right: 115px;
    padding: 14px;
    border-radius: 5px;
    font-size: 12px;
    line-height: 1.6;
    background-color: #f9fdff;
    
    > p {
      padding: 5px 0;
      margin: 0;
    }
  }
  
  .woring {
    margin: 8px;
  }
  
  .short-box {
    margin-bottom: 15px;
    
    .short-title {
      font-weight: bold;
      margin-bottom: 8px;
    }
    
    .font-sizes {
      font-size: 12px;
      color: #606266;
      margin-top: 8px;
      line-height: 1.4;
    }
  }
  
  // 基本信息和内容编辑样式
  .basic-info-card, .content-card, .remark-card {
    .form-row {
      margin-bottom: 24px;

      &:last-child {
        margin-bottom: 0;
      }
    }

    .form-item-modern {
      /deep/ .el-form-item__label {
        font-weight: 500;
        color: #262626;
      }

      .modern-input {
        /deep/ .el-input__inner {
          border-radius: 8px;
          border: 1px solid #d9d9d9;
          transition: all 0.3s ease;

          &:focus {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }

        /deep/ .el-input__prefix {
          color: #8c8c8c;
        }
      }

      .modern-select {
        width: 100%;

        /deep/ .el-input__inner {
          border-radius: 8px;
          border: 1px solid #d9d9d9;
          transition: all 0.3s ease;

          &:focus {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }
      }
    }

    .signature-option {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      .signature-text {
        flex: 1;
      }
    }
  }

  // 内容编辑区域样式
  .content-card {
    .smart-recognition-section {
      margin-bottom: 24px;

      .smart-card {
        border: 1px dashed #40a9ff;
        background: linear-gradient(135deg, #f0f9ff 0%, #e6f7ff 100%);
        transition: all 0.3s ease;

        &.expanded {
          border-style: solid;
          background: #ffffff;
        }

        .smart-header {
          display: flex;
          justify-content: space-between;
          align-items: center;

          .smart-title {
            font-weight: 600;
            color: #1890ff;

            i {
              margin-right: 8px;
            }
          }

          .toggle-btn {
            color: #1890ff;
            transition: all 0.3s ease;

            &:hover {
              color: #40a9ff;
              background: rgba(24, 144, 255, 0.1);
            }
          }
        }

        .smart-content {
          .smart-description {
            margin-bottom: 16px;
          }

          .smart-input-section {
            .smart-textarea {
              margin-bottom: 16px;

              /deep/ .el-textarea__inner {
                border-radius: 8px;
                border: 1px solid #d9d9d9;
                transition: all 0.3s ease;

                &:focus {
                  border-color: #40a9ff;
                  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                }
              }
            }

            .smart-actions {
              display: flex;
              justify-content: flex-end;
              gap: 12px;
            }
          }

          .recognition-results {
            margin-top: 20px;

            .results-title {
              font-weight: 600;
              color: #262626;
            }

            .generated-template {
              margin-bottom: 16px;

              .result-label {
                display: block;
                font-size: 14px;
                font-weight: 500;
                color: #262626;
                margin-bottom: 8px;
              }

              .template-preview {
                background: #f5f5f5;
                border: 1px solid #d9d9d9;
                border-radius: 6px;
                padding: 12px;
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                font-size: 14px;
                line-height: 1.5;
                color: #262626;
              }
            }

            .recognized-variables {
              margin-bottom: 16px;

              .result-label {
                display: block;
                font-size: 14px;
                font-weight: 500;
                color: #262626;
                margin-bottom: 8px;
              }

              .variables-tags {
                display: flex;
                flex-wrap: wrap;
                gap: 8px;

                .variable-tag-item {
                  cursor: pointer;
                  transition: all 0.3s ease;

                  &:hover {
                    transform: translateY(-1px);
                    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
                  }

                  .original-value {
                    color: #666;
                  }

                  .el-icon-arrow-right {
                    margin: 0 4px;
                  }

                  .variable-name {
                    font-weight: bold;
                  }

                  .variable-type {
                    font-size: 10px;
                    opacity: 0.8;
                    margin-left: 4px;
                  }
                }
              }
            }

            .recognition-actions {
              text-align: center;
            }
          }
        }
      }
    }

    .main-content-editor {
      .editor-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;

        .editor-label {
          font-size: 14px;
          font-weight: 500;
          color: #262626;
        }

        .help-icon {
          color: #8c8c8c;
          cursor: pointer;
          transition: color 0.3s ease;

          &:hover {
            color: #1890ff;
          }
        }
      }

      .content-textarea {
        /deep/ .el-textarea__inner {
          border-radius: 8px;
          border: 1px solid #d9d9d9;
          transition: all 0.3s ease;
          font-size: 14px;
          line-height: 1.6;

          &:focus {
            border-color: #40a9ff;
            box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
          }
        }
      }
    }

    .content-stats-options {
      margin-top: 20px;

      .content-stats {
        margin-bottom: 16px;

        .stats-item {
          display: flex;
          align-items: center;
          gap: 8px;

          .stats-label {
            font-size: 14px;
            color: #666;
          }

          .stats-value {
            font-size: 14px;
            font-weight: 600;
            color: #1890ff;
          }

          .stats-note {
            font-size: 12px;
            color: #ff4d4f;
          }
        }
      }

      .content-options {
        display: flex;
        flex-wrap: wrap;
        gap: 16px;
        margin-bottom: 16px;

        .option-group {
          display: flex;
          align-items: center;
          gap: 12px;

          .short-link-btn {
            border-radius: 6px;
          }

          .modern-checkbox {
            /deep/ .el-checkbox__label {
              font-size: 14px;
              color: #262626;
            }
          }
        }
      }

      .long-url-input {
        .url-input-header {
          display: flex;
          align-items: center;
          gap: 8px;
          margin-bottom: 8px;

          .url-label {
            font-size: 14px;
            font-weight: 500;
            color: #262626;
          }

          .help-icon {
            color: #ff4d4f;
          }
        }

        .url-input {
          /deep/ .el-input__inner {
            border-radius: 6px;
          }
        }
      }
    }
  }

  // 操作按钮区域样式
  .action-buttons {
    margin: 32px 0;
    text-align: center;

    .button-group {
      display: inline-flex;
      gap: 16px;

      .el-button {
        border-radius: 8px;
        font-weight: 500;
        padding: 12px 24px;
        font-size: 14px;
        transition: all 0.3s ease;

        &.submit-btn {
          background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
          border: none;

          &:hover {
            background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
          }
        }

        &.save-btn {
          background: linear-gradient(135deg, #52c41a 0%, #73d13d 100%);
          border: none;

          &:hover {
            background: linear-gradient(135deg, #73d13d 0%, #95de64 100%);
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(82, 196, 26, 0.3);
          }
        }

        &.cancel-btn {
          background: #ffffff;
          border: 1px solid #d9d9d9;
          color: #666;

          &:hover {
            border-color: #40a9ff;
            color: #40a9ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }

  // 变量管理卡片样式
  .variable-card {
    .variables-management {
      .variables-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
        gap: 16px;
        margin-bottom: 20px;

        .variable-item {
          border: 1px solid #e8eaec;
          border-radius: 8px;
          padding: 16px;
          background: #ffffff;
          transition: all 0.3s ease;

          &:hover {
            border-color: #40a9ff;
            box-shadow: 0 2px 8px rgba(24, 144, 255, 0.15);
          }

          &.configured {
            border-color: #52c41a;
            background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);
          }

          &.unconfigured {
            border-color: #ff4d4f;
            background: linear-gradient(135deg, #fff2f0 0%, #fff1f0 100%);
          }

          .variable-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;

            .variable-name {
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              font-size: 14px;
              font-weight: 600;
              color: #1890ff;
              background: #e6f7ff;
              padding: 4px 8px;
              border-radius: 4px;
            }
          }

          .variable-config {
            margin-bottom: 12px;

            .variable-type-select {
              width: 100%;

              /deep/ .el-input__inner {
                border-radius: 6px;
              }
            }
          }

          .variable-info {
            .variable-examples {
              font-size: 12px;
              color: #666;

              .info-label {
                font-weight: 500;
                color: #262626;
              }

              .examples-text {
                font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
                background: #f5f5f5;
                padding: 2px 4px;
                border-radius: 3px;
              }
            }
          }
        }
      }

      .batch-actions {
        text-align: center;
        padding-top: 16px;
        border-top: 1px solid #e8eaec;

        .el-button {
          margin: 0 8px;
        }
      }
    }
  }

  // 变量选项样式
  .variable-option {
    display: flex;
    flex-direction: column;

    .option-label {
      font-weight: 500;
      color: #262626;
    }

    .option-rule {
      font-size: 12px;
      color: #8c8c8c;
      margin-top: 2px;
    }
  }

  // 帮助信息卡片样式
  .help-card {
    border: 1px solid #e8eaec;
    background: linear-gradient(135deg, #fafbfc 0%, #f0f2f5 100%);

    .help-content {
      .help-section {
        margin-bottom: 24px;

        &:last-child {
          margin-bottom: 0;
        }

        h4 {
          font-size: 16px;
          font-weight: 600;
          color: #262626;
          margin: 0 0 12px 0;
          display: flex;
          align-items: center;

          &:before {
            content: '';
            width: 4px;
            height: 16px;
            background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
            border-radius: 2px;
            margin-right: 8px;
          }
        }

        ul {
          margin: 0;
          padding-left: 20px;

          li {
            margin-bottom: 8px;
            line-height: 1.6;
            color: #666;

            code {
              background: #f5f5f5;
              border: 1px solid #d9d9d9;
              border-radius: 3px;
              padding: 2px 6px;
              font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
              font-size: 12px;
              color: #d63384;
            }
          }
        }
      }
    }
  }

  // 动画效果
  .slide-fade-enter-active {
    transition: all 0.3s ease;
  }

  .slide-fade-leave-active {
    transition: all 0.3s cubic-bezier(1.0, 0.5, 0.8, 1.0);
  }

  .slide-fade-enter, .slide-fade-leave-to {
    transform: translateY(-10px);
    opacity: 0;
  }

  .fade-enter-active, .fade-leave-active {
    transition: opacity 0.3s ease;
  }

  .fade-enter, .fade-leave-to {
    opacity: 0;
  }

  .slectcode {
    display: flex;
    align-items: center;
  }

  // 增强的模版变量管理样式
  .template-variables-section {
    margin: 20px 0;

    .variables-card {
      border: 1px solid #e6f7ff;
      background: linear-gradient(135deg, #f6ffed 0%, #f0f9ff 100%);

      &:hover {
        box-shadow: 0 8px 25px rgba(24, 144, 255, 0.12);
        transform: translateY(-2px);
        transition: all 0.3s ease;
      }
    }

    .variables-header {
      display: flex;
      justify-content: space-between;
      align-items: center;

      .variables-title {
        font-size: 16px;
        font-weight: 600;
        color: #1890ff;

        i {
          margin-right: 8px;
          font-size: 18px;
        }
      }

      .variables-count {
        .variables-count-text {
          font-size: 12px;
          color: #666;
          margin-left: 8px;
        }
      }
    }

    .variables-content {
      .variables-description {
        margin-bottom: 20px;

        /deep/ .el-alert {
          border-radius: 8px;
          border: 1px solid #e6f7ff;

          .el-alert__content {
            p {
              margin: 0;
              line-height: 1.6;
              color: #666;
            }
          }
        }
      }

      .variables-list {
        .variable-item {
          margin-bottom: 16px;

          &.variable-item-error {
            .variable-card {
              border-color: #ff7875;
              background: linear-gradient(135deg, #fff2f0 0%, #fff1f0 100%);
            }
          }

          .variable-card {
            border: 1px solid #d9d9d9;
            border-radius: 12px;
            padding: 16px;
            background: #ffffff;
            transition: all 0.3s ease;

            &:hover {
              border-color: #40a9ff;
              box-shadow: 0 4px 12px rgba(24, 144, 255, 0.15);
            }

            .variable-header {
              display: flex;
              justify-content: space-between;
              align-items: center;
              margin-bottom: 12px;

              .variable-name-section {
                display: flex;
                align-items: center;
                gap: 12px;

                .variable-tag {
                  font-size: 14px;
                  font-weight: 600;
                  padding: 6px 12px;
                  border-radius: 6px;
                }

                .variable-description {
                  font-size: 12px;
                  color: #8c8c8c;
                }
              }

              .variable-actions {
                display: flex;
                gap: 8px;

                .edit-btn {
                  color: #1890ff;

                  &:hover {
                    color: #40a9ff;
                    background: #e6f7ff;
                  }
                }

                .remove-btn {
                  color: #ff4d4f;

                  &:hover {
                    color: #ff7875;
                    background: #fff2f0;
                  }
                }
              }
            }

            .variable-body {
              .variable-type-section {
                display: flex;
                align-items: center;
                gap: 12px;
                margin-bottom: 12px;

                .variable-label {
                  font-size: 14px;
                  font-weight: 500;
                  color: #262626;
                  min-width: 80px;
                }

                .variable-type-select {
                  flex: 1;
                  max-width: 300px;

                  /deep/ .el-input__inner {
                    border-radius: 6px;
                    border: 1px solid #d9d9d9;

                    &:focus {
                      border-color: #40a9ff;
                      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
                    }
                  }
                }
              }

              .variable-info {
                background: #fafafa;
                border-radius: 8px;
                padding: 12px;
                border-left: 3px solid #1890ff;

                .variable-rule {
                  display: flex;
                  align-items: flex-start;
                  gap: 8px;
                  margin-bottom: 8px;
                  font-size: 13px;
                  color: #595959;
                  line-height: 1.5;

                  i {
                    color: #1890ff;
                    margin-top: 2px;
                  }
                }

                .variable-examples {
                  display: flex;
                  align-items: center;
                  gap: 8px;
                  flex-wrap: wrap;

                  .examples-label {
                    font-size: 12px;
                    color: #8c8c8c;
                    font-weight: 500;
                  }

                  .example-tag {
                    font-size: 11px;
                    border-radius: 4px;
                    background: #f0f0f0;
                    border: 1px solid #d9d9d9;
                  }
                }
              }
            }
          }
        }
      }

      .variables-summary {
        margin-top: 24px;

        .summary-title {
          font-size: 14px;
          font-weight: 600;
          color: #262626;
        }

        .summary-content {
          display: flex;
          justify-content: space-between;
          align-items: center;
          background: linear-gradient(135deg, #f6ffed 0%, #e6f7ff 100%);
          border-radius: 12px;
          padding: 16px;
          border: 1px solid #b7eb8f;

          .summary-stats {
            display: flex;
            gap: 24px;

            .stat-item {
              text-align: center;

              .stat-number {
                display: block;
                font-size: 24px;
                font-weight: 700;
                color: #1890ff;
                line-height: 1;
              }

              .stat-label {
                display: block;
                font-size: 12px;
                color: #8c8c8c;
                margin-top: 4px;
              }
            }
          }

          .summary-actions {
            display: flex;
            gap: 8px;

            .el-button {
              border-radius: 6px;
              font-weight: 500;

              &.el-button--primary {
                background: linear-gradient(135deg, #1890ff 0%, #40a9ff 100%);
                border: none;

                &:hover {
                  background: linear-gradient(135deg, #40a9ff 0%, #69c0ff 100%);
                }
              }
            }
          }
        }
      }
    }
  }

  // 变量类型选择器样式
  /deep/ .type-option {
    display: flex;
    flex-direction: column;
    gap: 2px;

    .type-name {
      font-weight: 500;
      color: #262626;
    }

    .type-format {
      font-size: 11px;
      color: #8c8c8c;
      font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    }
  }
  
  // 智能识别相关样式
  .smart-recognition-section {
    .recognition-variables {
      max-height: 120px;
      overflow-y: auto;
  
      .el-tag {
        transition: all 0.3s ease;
  
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
      }
    }
  
    .el-card {
      transition: all 0.3s ease;
  
      &:hover {
        box-shadow: 0 4px 12px rgba(64, 158, 255, 0.15);
      }
    }
  }
  
  /deep/ .el-textarea__inner {
    height: 100px;
    resize: vertical;
  }
  
  /deep/ .el-radio-group {
    margin-bottom: 8px;
  }
  
  /deep/ .el-select {
    width: 100%;
  }
  
  /deep/ .width-l {
    width: 100%;
  }
  
  /deep/ .el-tooltip {
    cursor: pointer;
  }
  </style>
  
  <style>
  /* 全局样式 */
  .el-dialog__body {
    padding: 20px;
  }
  
  .el-form-item__label {
    font-weight: 500;
  }
  </style>