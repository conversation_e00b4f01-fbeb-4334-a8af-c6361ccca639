<template>
  <div class="simple-timing-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  v-if="selectId.length != 0"
                  @click="batchCancellation()"
                  class="action-btn primary"
                  icon="el-icon-delete"
                >
                  批量取消 ({{ selectId.length }})
                </el-button>
                <el-button
                  @click="refreshList"
                  class="action-btn"
                  icon="el-icon-refresh"
                >
                  刷新列表
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ tableDataObj.tablecurrent.totalRow }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">当前第 {{ sensitiveConditions.currentPage }} 页</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form :model="sensitiveCondition" :inline="true" ref="sensitiveCondition" class="advanced-search-form">
                <div class="search-row">
                  <el-form-item label="消息ID" prop="msgid" class="search-item">
                    <el-input
                      v-model="sensitiveCondition.msgid"
                      placeholder="请输入消息ID"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="提交状态" prop="timingStatus" class="search-item">
                    <el-select
                      v-model="sensitiveCondition.timingStatus"
                      placeholder="全部状态"
                      class="search-select"
                      clearable
                    >
                      <el-option label="未执行" value="1"></el-option>
                      <el-option label="已取消" value="3"></el-option>
                      <el-option label="执行完成" value="5"></el-option>
                      <el-option label="执行失败" value="6"></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="提交时间" prop="subtime" class="search-item date-item">
                    <el-date-picker
                      v-model="sensitiveCondition.subtime"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      @change="handeleDate1"
                      class="search-date"
                    />
                  </el-form-item>

                  <el-form-item label="定时时间" prop="dsTime" class="search-item date-item">
                    <el-date-picker
                      v-model="sensitiveCondition.dsTime"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      @change="handeleDate2"
                      class="search-date"
                    />
                  </el-form-item>

                  <el-form-item class="search-buttons">
                    <el-button type="primary" @click="sensitiveQuery()" class="search-btn primary" icon="el-icon-search">
                      查询
                    </el-button>
                    <el-button @click="sensitiveReload('sensitiveCondition')" class="search-btn" icon="el-icon-refresh">
                      重置
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </div>
        </div>
        <!-- 定时发送列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">定时发送列表</h3>
          </div>

          <div class="table-container">
            <el-table
              v-loading="tableDataObj.loading2"
              element-loading-text="正在加载定时任务..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.9)"
              ref="multipleTable"
              border
              :data="tableDataObj.tableData"
              class="enhanced-table"
              stripe
              :header-cell-style="{ background: '#fafafa', color: '#333' }"
              :row-style="{ height: '60px' }"
              empty-text="暂无定时发送数据"
              @selection-change="handelSelection"
            >
              <!-- 复选框列 -->
              <el-table-column type="selection" width="55" align="center"></el-table-column>

              <!-- 消息ID -->
              <el-table-column label="消息ID" width="200">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span class="msgid-text">{{ scope.row.msgid }}</span>
                    <el-tooltip content="复制消息ID" placement="top">
                      <i
                        class="el-icon-document-copy copy-icon"
                        @click="handleCopy(scope.row.msgid, $event)"
                      ></i>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>

              <!-- 提交时间 -->
              <el-table-column label="提交时间" width="170" align="center">
                <template slot-scope="scope">
                  {{ scope.row.createTime }}
                </template>
              </el-table-column>

              <!-- 发送类型 -->
              <el-table-column label="发送类型" width="140" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="getSmsTypeTagType(scope.row.smsType)"
                    size="small"
                  >
                    {{ getSmsTypeText(scope.row.smsType) }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 短信内容 -->
              <el-table-column label="短信内容" min-width="300">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <el-tooltip class="item" effect="dark" placement="top-start">
                      <div class="tooltips" slot="content">
                        {{ scope.row.content }}
                      </div>
                      <span class="content-text">{{ scope.row.content }}</span>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>

              <!-- 手机号 -->
              <el-table-column label="手机号" width="250">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <el-tooltip class="items" effect="dark" placement="top-start">
                      <div class="tooltip" slot="content">{{ scope.row.mobile }}</div>
                      <span class="mobile-text">{{ scope.row.mobile }}</span>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>

              <!-- 提交数量 -->
              <el-table-column prop="number" label="提交数量" width="100" align="center">
                <template slot-scope="scope">
                  <span class="number-text">{{ scope.row.number }}</span>
                </template>
              </el-table-column>

              <!-- 定时时间 -->
              <el-table-column prop="sendTime" label="定时时间" width="170" align="center">
                <template slot-scope="scope">
                  {{ scope.row.sendTime }}
                </template>
              </el-table-column>

              <!-- 提交状态 -->
              <el-table-column label="提交状态" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="getTimingStatusTagType(scope.row.timingStatus)"
                    size="small"
                  >
                    {{ getTimingStatusText(scope.row.timingStatus) }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 操作列 -->
              <el-table-column label="操作" width="120" fixed="right">
                <template slot-scope="scope">
                  <div class="table-actions">
                    <el-tooltip content="取消定时发送" placement="top" v-if="scope.row.timingStatus == '1'">
                      <el-button
                        type="text"
                        @click="handleAdopt(scope.$index, scope.row)"
                        class="action-btn-small delete"
                        icon="el-icon-close"
                      >
                        取消
                      </el-button>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 简约分页 -->
          <div class="pagination-section">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="sensitiveConditions.currentPage"
              :page-size="sensitiveConditions.pageSize"
              :page-sizes="[20, 50, 100, 200]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.totalRow"
              class="simple-pagination"
            />
          </div>
        </div>
      </div>
    </div>
    <!-- 报告的弹出框 -->
    <el-dialog
      title="任务报告查看"
      :visible.sync="sendMsgDialogVisible"
      width="860px"
    >
      <template v-if="this.$store.state.isDateState == 1">
        <div
          style="
            display: inline-block;
            width: 49%;
            text-align: center;
            border-right: 1px solid #f7f7f7;
            margin-top: -20px;
          "
        >
          <PieChart
            id="pie2"
            width="390px"
            height="340px"
            :basicOption="basicOption1"
          ></PieChart>
        </div>
        <div
          style="
            display: inline-block;
            width: 49%;
            text-align: center;
            margin-top: -20px;
          "
        >
          <PieChart
            id="pie1"
            width="390px"
            height="340px"
            :basicOption="basicOption2"
          ></PieChart>
        </div>
        <span style="display: block; padding: 0 0 10px 0; color: #333">
          发送明细列表</span
        >
        <table-tem :tableDataObj="tableDataObj1"></table-tem>
      </template>
      <template v-if="this.$store.state.isDateState == 2">
        <div style="text-align: center; margin-top: -20px">
          <PieChart
            id="pie1"
            height="300px"
            :basicOption="basicOption2"
          ></PieChart>
        </div>
      </template>
      <span style="display: block; padding: 20px 0 10px 0; color: #333">
        回执失败代码分析列表</span
      >
      <table-tem :tableDataObj="tableDataObj2"></table-tem>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sendMsgDialogVisible = false"
          >知道了</el-button
        >
      </div>
    </el-dialog>
  </div>
</template>
<script>
import TableTem from "@/components/publicComponents/TableTem";
import PieChart from "@/components/publicComponents/PieChart"; //饼图
import clip from '../../../utils/clipboard'
// 引入时间戳转换
// import { formatDate } from "@/assets/js/date.js";
import moment from "moment";
export default {
  name: "timing",
  components: {
    TableTem,
    PieChart,
  },
  data() {
    return {
      name: "timing",
      sensitiveCondition: {
        //查询条件的值
        smsContent: "",
        msgid: "",
        timingStatus: "1",
        dsTime: [],
        startTime: "", //提交开始时间
        stopTime: "", //提交结束时间
        beginTime: "", //定时开始时间
        endTime: "", //定时结束时间
        currentPage: 1,
        pageSize: 20,
      },
      sensitiveConditions: {
        //赋值查询条件的值
        smsContent: "",
        msgid: "",
        timingStatus: "1",
        dsTime: [],
        startTime: "", //提交开始时间
        stopTime: "", //提交结束时间
        beginTime: "", //定时开始时间
        endTime: "", //定时结束时间
        currentPage: 1,
        pageSize: 20,
      },
      tableDataObj: {
        //列表数据
        loading2: false,
        tablecurrent: {
          //分页参数
          totalRow: 0,
        },
        tableData: [],
      },
      selectId: "", //列表选中项的id
      timingStatus: [], //列表选中项的的状态
      sendTimess: [], //列表选中项的定时时间
      sendMsgDialogVisible: false, //弹出框
      tableDataObj1: {
        tableData: [],
        tableLabel: [
          {
            prop: "mobileNumber",
            showName: "提交号码数",
            fixed: false,
          },
          {
            prop: "mobileChanrgeNum",
            showName: "提交号码计费数",
            fixed: false,
          },
          {
            prop: "successAmount",
            showName: "成功号计费数",
            fixed: false,
          },
          {
            prop: "failureAmount",
            showName: "失败号计费数",
            width: 100,
            fixed: false,
          },
          {
            prop: "waitNumber",
            showName: "待返回号码计费数",
            width: 120,
            fixed: false,
          },
        ],
        tableStyle: {
          //列表配置项
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          isDefaultExpand: false, //是否默认打开
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "120", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
      tableDataObj2: {
        tableData: [],
        tableLabel: [
          {
            //列表表头
            prop: "failureCodeNoteName",
            showName: "失败原因",
            fixed: false,
          },
          {
            prop: "codeNoteNum",
            showName: "数量",
            fixed: false,
          },
          {
            prop: "codeNoteProportion",
            showName: "占比",
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          height: 240, //是否固定表头
          isExpand: false, //是否是折叠的
          isDefaultExpand: false, //是否默认打开
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "120", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
      basicOption1: {
        //发送明细图表
        data: [
          {
            value: 0,
            name: "成功",
          },
          {
            value: 0,
            name: "失败",
          },
          {
            value: 0,
            name: "待返回",
          },
        ],
        ledate: ["成功", "失败", "待返回"],
        bgColor: ["#8996E6", "#98D87D", "#FFD86E"],
        radius: "62%",
        title: {
          textStyle: {
            color: "#999",
            fontSize: 14,
          },
          text: "发送明细图表",
          x: "right",
        },
      },
      basicOption2: {
        //回执失败代码分析图表
        data: [],
        ledate: [],
        bgColor: [
          "#8996E6",
          "#49A9EE",
          "#98D87D",
          "#FFD86E",
          "#F3857C",
          "#8996E6",
          "#49A9EE",
          "#98D87D",
          "#FFD86E",
          "#F3857C",
          "#8996E6",
          "#49A9EE",
          "#98D87D",
          "#FFD86E",
          "#F3857C",
        ],
        radius: "62%",
        title: {
          text: "回执失败代码分析图表",
          textStyle: {
            color: "#999",
            fontSize: 14,
          },
          x: "right",
        },
      },
      msgid: "", //任务报告行的消息ID
    };
  },
  methods: {
    goBack() {
      //返回
      this.$router.go(-1);
    },
    GettableDtate() {
      //获取列表数据
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.cpus + "v3/consumertimingsms/page",
        this.sensitiveConditions,
        (res) => {
          this.tableDataObj.tableData = res.records;
          this.tableDataObj.tablecurrent.totalRow = res.total;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    handleCopy(name,event){
      clip(name, event)
    },
    sensitiveQuery() {
      //查询
      Object.assign(this.sensitiveConditions, this.sensitiveCondition);
      this.GettableDtate();
    },
    sensitiveReload(formName) {
      //重置
      this.$refs[formName].resetFields();
      this.sensitiveCondition.timingStatus = "1";
      this.sensitiveCondition.startTime = ""; //提交开始时间
      this.sensitiveCondition.stopTime = ""; //提交结束时间
      this.sensitiveCondition.beginTime = ""; //定时开始时间
      this.sensitiveCondition.endTime = ""; //定时结束时间
      Object.assign(this.sensitiveConditions, this.sensitiveCondition);
      this.GettableDtate();
    },
    handleSizeChange(size) {
      this.sensitiveConditions.pageSize = size;
      this.GettableDtate();
    },
    handleCurrentChange: function (currentPage) {
      this.sensitiveConditions.currentPage = currentPage;
      this.GettableDtate();
    },

    // 刷新列表
    refreshList() {
      this.GettableDtate();
    },

    // 获取短信类型标签类型
    getSmsTypeTagType(smsType) {
      const typeMap = {
        '1': 'success',   // 自定义内容发送
        '2': 'primary',   // 模板发送
        '3': 'warning'    // 变量模板发送
      };
      return typeMap[smsType] || 'info';
    },

    // 获取短信类型文本
    getSmsTypeText(smsType) {
      const textMap = {
        '1': '自定义内容发送',
        '2': '模板发送',
        '3': '变量模板发送'
      };
      return textMap[smsType] || '未知类型';
    },

    // 获取定时状态标签类型
    getTimingStatusTagType(timingStatus) {
      const statusMap = {
        '1': 'warning',   // 未执行
        '3': 'info',      // 已取消
        '5': 'success',   // 执行完成
        '6': 'danger'     // 执行失败
      };
      return statusMap[timingStatus] || 'info';
    },

    // 获取定时状态文本
    getTimingStatusText(timingStatus) {
      const textMap = {
        '1': '未执行',
        '3': '已取消',
        '5': '执行完成',
        '6': '执行失败'
      };
      return textMap[timingStatus] || '未知状态';
    },
    handeleDate1(val) {
      if (val) {
        this.sensitiveCondition.startTime = val[0]; //提交开始时间
        this.sensitiveCondition.stopTime = val[1]; //提交结束时间
      } else {
        this.sensitiveCondition.startTime = ""; //提交开始时间
        this.sensitiveCondition.stopTime = ""; //提交结束时间
      }
    },
    handeleDate2(val) {
      if (val) {
        this.sensitiveCondition.beginTime = val[0]; //定时开始时间
        this.sensitiveCondition.endTime = val[1]; //定时结束时间
      } else {
        this.sensitiveCondition.beginTime = ""; //定时开始时间
        this.sensitiveCondition.endTime = ""; //定时结束时间
      }
    },
    // //--------------操作框各个按钮功能开始 ----------
    // handelOptionButton: function(val){
    //     this.msgid = val.row.msgid;
    //     if(val.methods=='setLabel'){ //报告
    //         this.getconsunerTaskData();
    //         this.sendMsgDialogVisible = true;

    //     //    this.setLabelDialog=true
    //     }else if (val.methods=='delLabel'){ //取消
    //        let aa =  new Date(val.row.sendTime).getTime(); //定时时间
    //        let bb =  new Date().getTime(); //当前时间
    //         if((aa-bb)>600000) {
    //             this.$confirms.confirmation('delete','此操作将取消该条发送数据, 是否继续?',this.API.cpus + 'consumertimingsms/'+val.row.timingSmsId,{},res =>{
    //                 this.GettableDtate();
    //             })
    //         }else{
    //             this.$message({
    //                 message: '该项的定时时间距离发送时间不到10分钟，不可取消！',
    //                 type: 'warning'
    //             });
    //         }

    //     }
    // },
    //报告
    handleReject(index, row) {
      this.msgid = row.msgid;
      this.getconsunerTaskData();
      this.sendMsgDialogVisible = true;
    },
    //取消
    handleAdopt(index, row) {
      console.log(row.msgid);
      let aa = new Date(row.sendTime).getTime(); //定时时间
      let bb = new Date().getTime(); //当前时间
      if (aa - bb > 600000) {
        this.$confirms.confirmation(
          "get",
          "此操作将取消该条发送数据, 是否继续?",
          this.API.cpus + "v3/consumertimingsms/batchCancel/" + row.msgid,
          {},
          () => {
            this.GettableDtate();
          }
        );
      } else {
        this.$message({
          message: "该项的定时时间距离发送时间不到10分钟，不可取消！",
          type: "warning",
        });
      }
    },
    //批量取消
    batchCancellation() {
      let aas = true;
      let bbs = true;
      for (let i = 0; i < this.timingStatus.length; i++) {
        if (this.timingStatus[i] != 1) {
          aas = false;
          break;
        }
        let cc = new Date().getTime();
        let dd = new Date(this.sendTimess[i]).getTime();
        if (dd - cc < 600000) {
          bbs = false;
          break;
        }
      }
      if (aas == true) {
        if (bbs == true) {
          this.$confirms.confirmation(
            "get",
            "此操作将取消该条发送数据, 是否继续?",
            this.API.cpus + "v3/consumertimingsms/batchCancel/" + this.selectId,
            {},
            () => {
              this.GettableDtate();
            }
          );
        } else {
          this.$message({
            message: "选中项中的定时时间距离发送时间不到10分钟，不可取消！",
            type: "warning",
          });
        }
      } else {
        this.$message({
          message: "选中项中包含不可取消项，需重新选取！",
          type: "warning",
        });
      }
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = [];
      let timingStatus = [];
      let sendTimess = [];
      for (let i = 0; i < val.length; i++) {
        // selectId.push(val[i].timingSmsId);
        selectId.push(val[i].msgid);
        timingStatus.push(val[i].timingStatus);
        sendTimess.push(val[i].sendTime);
      }
      this.selectId = selectId.join(","); //批量操作选中消息id
      this.timingStatus = timingStatus; //批量操作选中项的 状态
      this.sendTimess = sendTimess; //批量操作选中的发送时间
    },
    getconsunerTaskData() {
      //获得短信失败状态报告的图表数据
      this.$api.get(
        this.API.cpus +
          "consumertasksms/selectConsumerFailureCodeNoteStatisticsByTemplateId?msgid=" +
          this.msgid,
        {},
        (res) => {
          this.tableDataObj2.tableData = res;
          this.basicOption2.data = [];
          this.basicOption2.ledate = [];
          for (let i = 0; i < res.length; i++) {
            this.basicOption2.data.push({
              name: res[i].failureCodeNoteName,
              value: res[i].codeNoteNum,
            });
            this.basicOption2.ledate.push(res[i].failureCodeNoteName);
          }
        }
      );
      //获得任务报告图表数据
      this.$api.get(
        this.API.cpus + "consumersmsinfo/selTemplateDetail?msgid=" + this.msgid,
        {},
        (res) => {
          this.tableDataObj1.tableData = [res];
          this.basicOption1.data[0].value = res.successAmount;
          this.basicOption1.data[1].value = res.failureAmount;
          this.basicOption1.data[2].value = res.waitNumber;
        }
      );
    },
  },
  // activated() {
  //   this.GettableDtate();
  // },
  watch: {
    // sensitiveConditions: {
    //   handler() {
    //     this.GettableDtate();
    //   },
    //   deep: true,
    //   immediate: true,
    // },
  },
};
</script>
<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* timing 特有样式 */
.simple-timing-page {
  min-height: 100vh;
  background: #fafafa;
  padding: 0;
}

/* 搜索表单特殊样式 */
.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.search-item {
  margin-bottom: 0;

  &.date-item {
    min-width: 280px;
  }

  /deep/ .el-form-item__label {
    color: #333;
    font-weight: 500;
    font-size: 14px;
    min-width: 80px;
  }
}

.search-date {
  width: 260px;
}

/* 表格内容特殊样式 */
.msgid-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

.copy-icon {
  color: #409eff;
  cursor: pointer;
  font-size: 14px;
  transition: color 0.2s ease;

  &:hover {
    color: #66b1ff;
  }
}

.mobile-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 200px;
  display: block;
}

.number-text {
  font-weight: 500;
  color: #333;
}

/* 保留原有的一些必要样式 */
.span {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  line-height: 1.5;
}

.tooltips {
  width: 300px;
  white-space: pre-wrap;
  line-height: 1.5;
}

.tooltip {
  white-space: pre-wrap;
  line-height: 1.5;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .search-row {
    flex-direction: column;
    align-items: stretch;
  }

  .search-item {
    width: 100%;

    &.date-item {
      min-width: auto;
    }
  }

  .search-date {
    width: 100%;
  }

  .search-buttons {
    margin-left: 0;

    .search-btn {
      flex: 1;
    }
  }
}
</style>

<style>
.el-table--small th {
  background: #f5f5f5;
}
</style>


