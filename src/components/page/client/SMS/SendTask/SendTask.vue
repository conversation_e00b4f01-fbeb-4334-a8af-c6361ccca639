<template>
  <div class="simple-sendtask-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">


        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button
                  @click="refreshList"
                  class="action-btn"
                  icon="el-icon-refresh"
                  :loading="tableDataObj.loading"
                >
                  {{ tableDataObj.loading ? '刷新中...' : '刷新列表' }}
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ tableDataObj.tablecurrent.total }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">当前第 {{ formInlines.currentPage }} 页</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form :model="formInline" :inline="true" ref="formInline" class="advanced-search-form">
                <div class="search-row">
                  <el-form-item label="发送类型" prop="sendType" class="search-item">
                    <el-select v-model="formInline.sendType" placeholder="全部类型" class="search-select" clearable>
                      <el-option label="全部" value="0"></el-option>
                      <el-option label="模板发送" value="1"></el-option>
                      <el-option label="自定义发送" value="2"></el-option>
                      <el-option label="个性化发送" value="3"></el-option>
                      <el-option label="自定义变量发送" value="4"></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="来源" prop="source" class="search-item">
                    <el-input
                      v-model="formInline.source"
                      placeholder="请输入来源"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="任务名称" prop="taskName" class="search-item">
                    <el-input
                      v-model="formInline.taskName"
                      placeholder="请输入任务名称"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="发送状态" prop="status" class="search-item">
                    <el-select v-model="formInline.status" placeholder="全部状态" class="search-select" clearable>
                      <el-option label="待处理" value="0"></el-option>
                      <el-option label="处理中" value="1"></el-option>
                      <el-option label="已完成" value="2"></el-option>
                      <el-option label="取消发送" value="3"></el-option>
                      <el-option label="处理异常" value="-1"></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item label="发送时间" prop="time" class="search-item date-item">
                    <el-date-picker
                      v-model="formInline.time"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      range-separator="-"
                      @change="getTimeOperating"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      class="search-date"
                    />
                  </el-form-item>

                  <el-form-item label="提交时间" prop="time1" class="search-item date-item">
                    <el-date-picker
                      v-model="formInline.time1"
                      value-format="yyyy-MM-dd"
                      type="daterange"
                      range-separator="-"
                      @change="getTimeOperating1"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      class="search-date"
                    />
                  </el-form-item>

                  <el-form-item class="search-buttons">
                    <el-button
                      type="primary"
                      @click="ListSearch"
                      class="search-btn primary"
                      icon="el-icon-search"
                      :loading="tableDataObj.loading"
                    >
                      {{ tableDataObj.loading ? '查询中...' : '查询' }}
                    </el-button>
                    <el-button
                      @click="Reset('formInline')"
                      class="search-btn"
                      icon="el-icon-refresh"
                      :loading="tableDataObj.loading"
                    >
                      {{ tableDataObj.loading ? '重置中...' : '重置' }}
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </div>
        </div>
        <!-- 发送任务列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">发送任务列表</h3>
            <span class="table-subtitle">可刷新页面查看最新发送进度</span>
          </div>

          <div class="table-container">
            <el-table
              v-loading="tableDataObj.loading"
              :element-loading-text="tableDataObj.loadingText"
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.9)"
              ref="multipleTable"
              border
              :data="tableDataObj.tableData"
              class="enhanced-table"
              stripe
              :header-cell-style="{ background: '#fafafa', color: '#333' }"
              :row-style="{ height: '60px' }"
              empty-text="暂无发送任务数据"
            >

              <!-- 发送ID -->
              <el-table-column prop="taskSmsId" label="发送ID" width="80" align="center">
                <template slot-scope="scope">
                  {{ scope.row.taskSmsId }}
                </template>
              </el-table-column>

              <!-- 任务名称 -->
              <el-table-column label="任务名称" width="120">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <Tooltip v-if="scope.row.taskName" :content="scope.row.taskName" className="wrapper-text" effect="light">
                    </Tooltip>
                  </div>
                </template>
              </el-table-column>

              <!-- 消息ID -->
              <el-table-column label="消息ID" width="200">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span class="msgid-text">{{ scope.row.msgid }}</span>
                    <el-tooltip content="复制消息ID" placement="top">
                      <i
                        class="el-icon-document-copy copy-icon"
                        @click="handleCopy(scope.row.msgid, $event)"
                      ></i>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>

              <!-- 模板发送类型 -->
              <el-table-column label="发送类型" width="120" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="getSendTypeTagType(scope.row.sendType)"
                    size="small"
                  >
                    {{ getSendTypeText(scope.row.sendType) }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 提交时间 -->
              <el-table-column label="提交时间" width="170" align="center">
                <template slot-scope="scope">
                  {{ scope.row.createTime }}
                </template>
              </el-table-column>

              <!-- 发送内容 -->
              <el-table-column label="发送内容" min-width="400">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <div class="content-text">{{ scope.row.content }}</div>
                  </div>
                </template>
              </el-table-column>

              <!-- 文件名/手机号 -->
              <el-table-column label="文件名/手机号" width="150">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <el-tooltip :content="scope.row.fileMobile" placement="top">
                      <span class="file-mobile-text">{{ scope.row.fileMobile }}</span>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>

              <!-- 发送时间 -->
              <el-table-column label="发送时间" width="170" align="center">
                <template slot-scope="scope">
                  {{ scope.row.sendTime }}
                </template>
              </el-table-column>

              <!-- 发送类型（定时/立即） -->
              <el-table-column label="发送方式" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="scope.row.isTiming == 0 ? 'success' : 'warning'"
                    size="small"
                  >
                    {{ scope.row.isTiming == 0 ? '立即发送' : '定时发送' }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 提交号码数 -->
              <el-table-column label="提交号码数" width="120" align="center">
                <template slot-scope="scope">
                  <span class="number-text">{{ scope.row.totalNum }}</span>
                </template>
              </el-table-column>

              <!-- 有效号码数 -->
              <el-table-column label="有效号码数" width="120" align="center">
                <template slot-scope="scope">
                  <span class="number-text success">{{ scope.row.effectiveNum }}</span>
                </template>
              </el-table-column>

              <!-- 无效号码数 -->
              <el-table-column label="无效号码数" width="120" align="center">
                <template slot-scope="scope">
                  <span
                    v-if="scope.row.invalidNum > 0 && scope.row.invalidFilePath"
                    class="number-text error clickable"
                    @click="download(scope.row, '2')"
                  >
                    <el-tooltip content="点击下载无效号码文件" placement="top">
                      <span>{{ scope.row.invalidNum }}</span>
                    </el-tooltip>
                  </span>
                  <span v-else class="number-text">{{ scope.row.invalidNum }}</span>
                </template>
              </el-table-column>

              <!-- 发送状态 -->
              <el-table-column label="发送状态" width="100" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="getStatusTagType(scope.row.status)"
                    size="small"
                  >
                    {{ getStatusText(scope.row.status) }}
                  </el-tag>
                  <el-tooltip
                    v-if="scope.row.status == -1 && scope.row.reason"
                    :content="scope.row.reason"
                    placement="top"
                  >
                    <i class="el-icon-warning-outline error-icon"></i>
                  </el-tooltip>
                </template>
              </el-table-column>

              <!-- 来源 -->
              <el-table-column label="来源" width="100" align="center">
                <template slot-scope="scope">
                  {{ scope.row.source }}
                </template>
              </el-table-column>

              <!-- 操作列 -->
              <el-table-column label="操作" width="150" fixed="right">
                <template slot-scope="scope">
                  <div class="table-actions">
                    <el-tooltip content="编辑任务" placement="top" v-if="canEdit(scope.row)">
                      <el-button
                        type="text"
                        @click="edit(scope.row)"
                        class="action-btn-small edit"
                        icon="el-icon-edit"
                      >
                        编辑
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="取消任务" placement="top" v-if="scope.row.status == 0">
                      <el-button
                        type="text"
                        @click="cancel(scope.row)"
                        class="action-btn-small delete"
                        icon="el-icon-close"
                      >
                        取消
                      </el-button>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 简约分页 -->
          <div class="pagination-section">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
              class="simple-pagination"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import DatePlugin from "@/components/publicComponents/DatePlugin.vue";
import TableTem from "@/components/publicComponents/TableTem";
import Tooltip from "@/components/publicComponents/tooltip";
import clip from '../../../utils/clipboard'
import getNoce from "../../../../../plugins/getNoce";
export default {
  name: "SendTask",
  components: {
    DatePlugin,
    TableTem,
    Tooltip
  },
  data() {
    return {
      name: "SendTask",
      // 搜索数据
      formInline: {
        sendType: "0",
        source: "",
        taskName: "",
        beginTime: "",
        endTime: "",
        startTime: "",
        stopTime: "",
        isTiming: "",
        status: "",
        time: [],
        time1: [],
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        sendType: "0",
        source: "",
        taskName: "",
        beginTime: "",
        endTime: "",
        startTime: "",
        stopTime: "",
        isTiming: "",
        status: "",
        time: [],
        time1: [],
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading: false,
        loadingText: '正在加载发送任务...',
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        // tableLabel:[]
        //     {
        //     prop:"taskSmsId",
        //     showName:'发送ID',
        //     width:'60',
        //     fixed:false
        //     },
        //     {
        //     prop:"content",
        //     showName:'发送内容',
        //     fixed:false
        //     },
        //     {
        //     prop:"fileMobile",
        //     showName:'文件名/手机号',
        //     fixed:false
        //     },
        //     {
        //     prop:"sendTime",
        //     showName:'发送时间',
        //     width:'140',
        //     fixed:false
        //     },
        //     {
        //     prop:"isTiming",
        //     showName:'发送类型',
        //     width:'120',
        //     fixed:false,
        //     formatData:function(val){
        //         let type=''
        //         if(val==0){
        //             type='立即发送'
        //         }else{
        //             type='定时发送'
        //         }
        //         return type
        //     }},
        //     {
        //     prop:"totalNum",
        //     showName:'提交号码数(总行数）',
        //     width:'140',
        //     fixed:false
        //     },{
        //     prop:"effectiveNum",
        //     showName:'有效号码（有效行）',
        //     width:'140',
        //     fixed:false
        //     },{
        //     prop:"invalidNum",
        //     showName:'无效号码（无效行）',
        //     width:'140',
        //     fixed:false
        //     },
        //     {
        //     prop:"status",
        //     showName:'发送状态',
        //     fixed:false,
        //     formatData:function(val){
        //         let type=''
        //         if(val==1){
        //             type='发送中'
        //         }else if(val==2){
        //             type='已完成'
        //         }else if(val==-1){
        //             type='异常'
        //         }else if(val==0){
        //             type='待处理'
        //         }
        //         return type
        //     }}
        // ],
        // tableStyle:{
        //     isSelection:true,//是否复选框
        //     isExpand:false,//是否是折叠的
        //     style: {//表格样式,表格宽度
        //         width:"100%"
        //     },
        //     optionWidth:'120',//操作栏宽度
        //     border:true,//是否边框
        //     stripe:false,//是否有条纹
        // },
        // tableOptions:[
        //     {
        //         optionName:'编辑',
        //         type:'',
        //         size:'mini',
        //         optionMethod:'edit',
        //         icon:''
        //     },
        //     {
        //         optionName:'取消',
        //         type:'',
        //         size:'mini',
        //         optionMethod:'cancel',
        //         icon:''
        //     }
        // ],
        //  conditionOption:[
        //     {
        //         contactCondition:'status',//关联的表格属性
        //         contactData:'0',//关联的表格属性-值
        //         optionName:'编辑',//按钮的显示文字
        //         optionMethod:'edit',//按钮的方法
        //         // icon:'el-icon-edit',//按钮图标
        //         optionButtonColor:'',//按钮颜色
        //     },
        //     {
        //         contactCondition:'status',//关联的表格属性
        //         contactData:'0',//关联的表格属性-值
        //         optionName:'取消',//按钮的显示文字
        //         optionMethod:'cancel',//按钮的方法
        //         // icon:'el-icon-edit',//按钮图标
        //         optionButtonColor:'red',//按钮颜色
        //     }
        // ]
      },
    };
  },
  methods: {

    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading = true;
      this.tableDataObj.loadingText = '正在加载发送任务...';

      this.$api.post(
        this.API.cpus + "v3/selectConsumerWebTaskPage",
        this.formInlines,
        (res) => {
          this.tableDataObj.loading = false;
          if (res && res.records) {
            this.tableDataObj.tableData = res.records;
            this.tableDataObj.tablecurrent.total = res.total || 0;

            // 如果是搜索结果，更新加载文案
            if (this.isSearching()) {
              this.tableDataObj.loadingText = '搜索完成';
            } else {
              this.tableDataObj.loadingText = '加载完成';
            }
          } else {
            this.tableDataObj.tableData = [];
            this.tableDataObj.tablecurrent.total = 0;
            this.$message.warning('暂无数据');
          }
        },
        (error) => {
          this.tableDataObj.loading = false;
          this.tableDataObj.loadingText = '加载失败';
          this.tableDataObj.tableData = [];
          this.tableDataObj.tablecurrent.total = 0;
          this.$message.error('加载发送任务失败，请重试');
          console.error('加载发送任务失败:', error);
        }
      );
    },
    // 编辑
    edit(val) {
      if (Date.parse(val.sendTime) - Date.now() < 300000) {
        this.$message({
          message: "定时小于现在5分钟无法编辑",
          type: "warning",
        });
      } else {
        if (val.sendType == 3) {
          this.$router.push({
            path: "/PersonalizedSendEditing?SmSId=" + val.taskSmsId,
          });
        } else {
          this.$router.push({ path: "/SendSMSEdit?SmSId=" + val.taskSmsId });
        }
      }
    },
    // 取消
    cancel(val) {
      this.$confirms.confirmation(
        "post",
        "确定取消定时？",
        this.API.cpus + "v3/web-task/cancel",
        { ids: [val.taskSmsId] },
        (res) => {
          this.InquireList();
        }
      );
    },
    handleCopy(name, event) {
      clip(name, event)
    },
    // 下载
    download(val, tag) {
      // 时间过滤
      Date.prototype.format = function (format) {
        var args = {
          "M+": this.getMonth() + 1,
          "d+": this.getDate(),
          "h+": this.getHours(),
          "m+": this.getMinutes(),
          "s+": this.getSeconds(),
          "q+": Math.floor((this.getMonth() + 3) / 3), //quarter
          S: this.getMilliseconds(),
        };
        if (/(y+)/.test(format))
          format = format.replace(
            RegExp.$1,
            (this.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        for (var i in args) {
          var n = args[i];
          if (new RegExp("(" + i + ")").test(format))
            format = format.replace(
              RegExp.$1,
              RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length)
            );
        }
        return format;
      };
      var that = this;
      filedownload();
      async function filedownload() {
        const nonce = await getNoce.useNonce();
        if (tag == "2") {
          let group = val.invalidFilePath.substring(0, 6);
          let invalidFilePath = val.invalidFilePath.slice(7);
          fetch(
            "/gateway/client-cpus/v3/file/download?fileName=" +
            new Date().getTime() +
            "&group=" +
            group +
            "&path=" +
            invalidFilePath,
            {
              method: "get",
              headers: {
                "Content-Type": "application/json",
                Authorization: "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
                'Once': nonce,
              },
              // body: JSON.stringify({
              //     batchNo: val.row.batchNo,
              // })
            }
          )
            .then((res) => res.blob())
            .then((data) => {
              let blobUrl = window.URL.createObjectURL(data);
              download(blobUrl);
            });
        } else {
          fetch(
            that.API.cpus +
            "v3/file/download?fileName=" +
            val.fileMobile +
            "&group=group1&path=" +
            val.filePath.slice(7),
            {
              method: "get",
              headers: {
                "Content-Type": "application/json",
                Authorization:
                  "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
                'Once': nonce,
              },
              // body: JSON.stringify({
              //     batchNo: val.row.batchNo,
              // })
            }
          )
            .then((res) => res.blob())
            .then((data) => {
              let blobUrl = window.URL.createObjectURL(data);
              download(blobUrl);
            });
        }
      }
      function download(blobUrl) {
        if (tag == "2") {
          var a = document.createElement("a");
          a.style.display = "none";
          a.download =
            "(" +
            new Date().format("yyyy-MM-dd hh:mm:ss") +
            ") " +
            new Date().getTime() +
            ".txt";
          a.href = blobUrl;
          a.click();
        } else {
          var a = document.createElement("a");
          a.style.display = "none";
          a.download =
            "(" +
            new Date().format("yyyy-MM-dd hh:mm:ss") +
            ") " +
            val.fileMobile;
          a.href = blobUrl;
          a.click();
        }
      }
    },
    // 查询
    ListSearch() {
      this.tableDataObj.loadingText = '正在搜索发送任务...';
      this.formInlines.currentPage = 1; // 重置到第一页
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    // 重置
    Reset(formName) {
      this.tableDataObj.loadingText = '正在重置搜索条件...';
      this.$refs[formName].resetFields();
      (this.formInline.time = []), (this.formInline.beginTime = "");
      this.formInline.endTime = "";
      (this.formInline.time1 = []), (this.formInline.startTime = "");
      this.formInline.stopTime = "";
      this.formInline.source = "";
      this.formInlines.currentPage = 1; // 重置到第一页
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    // 定时时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = val[0] + " 00:00:00";
        this.formInline.endTime = val[1] + " 23:59:59";
      } else {
        this.formInline.beginTime = "";
        this.formInline.endTime = "";
      }
    },
    // 提交时间
    getTimeOperating1(val) {
      if (val) {
        this.formInline.startTime = val[0] + " 00:00:00";
        this.formInline.stopTime = val[1] + " 23:59:59";
      } else {
        this.formInline.startTime = "";
        this.formInline.stopTime = "";
      }
    },
    // 分页
    handleSizeChange(size) {
      this.tableDataObj.loadingText = `正在加载每页 ${size} 条数据...`;
      this.formInlines.pageSize = size;
      this.formInlines.currentPage = 1; // 重置到第一页
      this.InquireList();
    },
    handleCurrentChange: function (currentPage) {
      this.tableDataObj.loadingText = `正在加载第 ${currentPage} 页数据...`;
      this.formInlines.currentPage = currentPage;
      this.InquireList();
    },

    // 刷新列表
    refreshList() {
      this.tableDataObj.loadingText = '正在刷新数据...';
      this.InquireList();
    },

    // 判断是否在搜索状态
    isSearching() {
      return this.formInlines.sendType !== "0" ||
             this.formInlines.source ||
             this.formInlines.taskName ||
             this.formInlines.status ||
             this.formInlines.beginTime ||
             this.formInlines.startTime;
    },

    // 获取发送类型标签类型
    getSendTypeTagType(sendType) {
      const typeMap = {
        '1': 'primary',   // 模板发送
        '2': 'success',   // 自定义发送
        '3': 'warning',   // 个性化发送
        '4': 'info'       // 自定义变量发送
      };
      return typeMap[sendType] || 'info';
    },

    // 获取发送类型文本
    getSendTypeText(sendType) {
      const textMap = {
        '1': '模板发送',
        '2': '自定义发送',
        '3': '个性化发送',
        '4': '自定义变量发送'
      };
      return textMap[sendType] || '未知类型';
    },

    // 获取状态标签类型
    getStatusTagType(status) {
      const statusMap = {
        '0': 'info',      // 待处理
        '1': 'warning',   // 处理中
        '2': 'success',   // 已完成
        '3': 'info',      // 取消发送
        '-1': 'danger'    // 处理异常
      };
      return statusMap[status] || 'info';
    },

    // 获取状态文本
    getStatusText(status) {
      const textMap = {
        '0': '待处理',
        '1': '处理中',
        '2': '已完成',
        '3': '取消发送',
        '-1': '处理异常'
      };
      return textMap[status] || '未知状态';
    },

    // 判断是否可以编辑
    canEdit(row) {
      return row.status != -1 &&
             row.status != 3 &&
             row.status != 2 &&
             row.isTiming != 0;
    },
  },
  created() {
    // this.formInline.source = this.$route.query.source
    if (this.$route.query.source) {
      this.formInline.source = this.$route.query.source;
      this.formInlines.source = this.$route.query.source;
    }
    this.InquireList();
  },
  // activated(){
  //     this.InquireList()
  // },
  watch: {
    // 监听搜索/分页数据
    // formInlines:{
    //     handler() {
    //         this.InquireList()
    //     },
    //     deep: true,
    //     immediate: true,
    // },
  },
};
</script>
<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* SendTask 特有样式 */
.simple-sendtask-page {
  min-height: 100vh;
  background: #fafafa;
  padding: 0;
}

.table-subtitle {
  font-size: 12px;
  color: #999;
  font-weight: normal;
  margin-left: 10px;
}

/* 搜索表单特殊样式 */
.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.search-item {
  margin-bottom: 0;

  &.date-item {
    min-width: 280px;
  }

  /deep/ .el-form-item__label {
    color: #333;
    font-weight: 500;
    font-size: 14px;
    min-width: 80px;
  }
}

.search-date {
  width: 260px;
}

/* 表格内容特殊样式 */
.msgid-text {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #666;
  margin-right: 8px;
}

.copy-icon {
  color: #409eff;
  cursor: pointer;
  font-size: 14px;
  transition: color 0.2s ease;

  &:hover {
    color: #66b1ff;
  }
}

.file-mobile-text {
  max-width: 130px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  display: block;
}

.number-text {
  font-weight: 500;

  &.success {
    color: #67c23a;
  }

  &.error {
    color: #f56c6c;
  }

  &.clickable {
    cursor: pointer;
    text-decoration: underline;

    &:hover {
      color: #409eff;
    }
  }
}

.error-icon {
  color: #f56c6c;
  margin-left: 4px;
  cursor: pointer;
}

/* 加载状态优化 */
.enhanced-table {
  /deep/ .el-loading-mask {
    background-color: rgba(255, 255, 255, 0.95);

    .el-loading-spinner {
      .el-icon-loading {
        font-size: 24px;
        color: #409eff;
      }

      .el-loading-text {
        color: #666;
        font-size: 14px;
        margin-top: 10px;
      }
    }
  }
}

.action-btn {
  &.is-loading {
    .el-icon-loading {
      margin-right: 5px;
    }
  }
}

.search-btn {
  &.is-loading {
    .el-icon-loading {
      margin-right: 5px;
    }
  }
}

/* 保留原有的一些必要样式 */
.span {
  white-space: pre-wrap;
  line-height: 1.5;
}

.addC .el-select {
  width: 100%;
}

.addC .el-cascader {
  width: 100%;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .search-row {
    flex-direction: column;
    align-items: stretch;
  }

  .search-item {
    width: 100%;

    &.date-item {
      min-width: auto;
    }
  }

  .search-date {
    width: 100%;
  }

  .search-buttons {
    margin-left: 0;

    .search-btn {
      flex: 1;
    }
  }
}
</style>
