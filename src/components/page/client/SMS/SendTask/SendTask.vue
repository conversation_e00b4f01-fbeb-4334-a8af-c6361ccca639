<template>
  <div class="login_cell_phone bag">
    <div class="crumbs" style="padding：10px">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 发送任务管理</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form :inline="true" ref="formInline" :model="formInline" label-width="100px" class="demo-form-inline">
            <el-form-item label="模板发送类型" style="display: inline-block" prop="sendType">
              <el-select v-model="formInline.sendType" placeholder="请选择类型">
                <el-option label="全部" value="0"></el-option>
                <el-option label="模板发送" value="1"></el-option>
                <el-option label="自定义发送" value="2"></el-option>
                <el-option label="个性化发送" value="3"></el-option>
                <el-option label="自定义变量发送" value="4"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="来源" prop="source">
              <el-input v-model="formInline.source" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="任务名称" prop="taskName">
              <el-input v-model="formInline.taskName" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="发送类型" prop="isTiming">
              <el-select v-model="formInline.isTiming" placeholder="请选择">
                <el-option label="定时发送" value="1">
                </el-option>
                <el-option label="立即发送" value="0">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="发送状态" prop="status">
              <el-select v-model="formInline.status" placeholder="请选择">
                <el-option label="待处理" value="0">
                </el-option>
                <el-option label="处理中" value="1">
                </el-option>
                <el-option label="已完成" value="2">
                </el-option>
                <el-option label="取消发送" value="3">
                </el-option>
                <el-option label="处理异常" value="-1">
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="发送时间" prop="time">
              <el-date-picker class="input-w" v-model="formInline.time" value-format="yyyy-MM-dd" type="daterange"
                range-separator="-" :clearable="false" @change="getTimeOperating" start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item label="提交时间" prop="time">
              <el-date-picker class="input-w" v-model="formInline.time1" value-format="yyyy-MM-dd" type="daterange"
                range-separator="-" :clearable="false" @change="getTimeOperating1" start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" plain style="" @click="ListSearch">查询</el-button>
              <el-button type="primary" plain style="" @click="Reset('formInline')">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <!-- <div class="boderbottom">
          
        </div> -->
        <div>
          <!-- <span class="sensitive-list-header">发送任务列表</span><span style="font-size: 12px;font-weight: 500;">（可刷新页面查看最新发送进度）</span> -->
          <el-button type="primary" style="margin-right: 10px" @click="batchDeletion"
            v-if="selectId.length">批量取消</el-button>
        </div>
        <div class="Mail-table" style="padding-bottom: 40px">
          <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)" ref="multipleTable"
            border :data="tableDataObj.tableData" style="width: 100%">
            <el-table-column width="70" label="发送ID">
              <template slot-scope="scope">{{ scope.row.taskSmsId }}</template>
            </el-table-column>
            <el-table-column width="90" label="任务名称">
              <template slot-scope="scope">
                <Tooltip v-if="scope.row.taskName" :content="scope.row.taskName" className="wrapper-text" effect="light">
                </Tooltip>
                <!-- {{ scope.row.taskName }} -->
              </template>
            </el-table-column>
            <el-table-column label="消息ID" width="200">
              <template slot-scope="scope">
                <span>{{ scope.row.msgid }}</span>
                <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;" class="el-icon-document-copy"
                  @click="handleCopy(scope.row.msgid, $event)"></i>
              </template>
            </el-table-column>
            <el-table-column label="模板发送类型" width="120">
              <template slot-scope="scope">
                <span v-if="scope.row.sendType == 2">自定义发送</span>
                <span v-else-if="scope.row.sendType == 1">模版发送</span>
                <span v-else-if="scope.row.sendType == 3">个性化发送</span>
                <span v-else-if="scope.row.sendType == 4">自定义变量发送</span>
              </template>
            </el-table-column>
            <el-table-column label="提交时间" width="170">
              <template slot-scope="scope">
                <span>{{ scope.row.createTime }}</span>
              </template>
            </el-table-column>
            <el-table-column label="发送内容" min-width="500">
              <template slot-scope="scope">
                <!-- <Tooltip v-if="scope.row.content" :content="scope.row.content" className="wrapper-text" effect="light">
                </Tooltip> -->
                <span class="span">{{ scope.row.content }}</span>
              </template>
            </el-table-column>
            <el-table-column label="文件名/手机号" width="150">
              <template slot-scope="scope">
                <!-- <span
                  v-if="scope.row.filePath"
                  style="cursor: pointer; color: #16a589"
                  @click="download(scope.row)"
                  >{{ scope.row.fileMobile }}</span
                > -->
                <span :title="scope.row.fileMobile" style="
                      max-width: 150px;
                      overflow: hidden;
                      text-overflow: ellipsis;
                      white-space: nowrap;
                      display: block;
                    ">{{ scope.row.fileMobile }}</span>
              </template>
            </el-table-column>
            <el-table-column label="发送时间" width="170">
              <template slot-scope="scope">
                <span>{{ scope.row.sendTime }}</span>
              </template>
            </el-table-column>
            <el-table-column label="发送类型" width="80">
              <template slot-scope="scope">
                <span v-if="scope.row.isTiming == 0">立即发送</span>
                <span v-else>定时发送</span>
              </template>
            </el-table-column>
            <el-table-column label="提交号码数（总行数）" width="150">
              <template slot-scope="scope">
                <span>{{ scope.row.totalNum }}</span>
              </template>
            </el-table-column>
            <el-table-column label="有效号码（有效行数）" width="150">
              <template slot-scope="scope">
                <span>{{ scope.row.effectiveNum }}</span>
              </template>
            </el-table-column>
            <el-table-column label="无效号码（无效行）" width="140">
              <template slot-scope="scope">
                <span v-if="scope.row.invalidNum > 0 && scope.row.invalidFilePath" style="color: #1890ff; cursor: pointer"
                  @click="download(scope.row, '2')">
                  <el-tooltip class="item" effect="dark" content="点击下载" placement="bottom">
                    <span>{{ scope.row.invalidNum }}</span>
                  </el-tooltip>
                </span>
                <span v-else>{{ scope.row.invalidNum }}</span>
              </template>
            </el-table-column>
            <el-table-column label="发送状态" width="90">
              <template slot-scope="scope">
                <span v-if="scope.row.status == 1">处理中</span>
                <span v-else-if="scope.row.status == 2">已完成</span>
                <span v-else-if="scope.row.status == 3">取消发送</span>
                <span v-else-if="scope.row.status == 0">待处理</span>
                <el-tooltip class="item" effect="dark" :content="scope.row.reason" placement="top">
                  <span style="color: red;" v-if="scope.row.status == -1">处理异常</span>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column label="来源" width="90">
              <template slot-scope="scope">
                <span>{{ scope.row.source }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" width="120">
              <template slot-scope="scope">
                <el-button type="text" v-if="scope.row.status != -1 &&
                  scope.row.status != 3 &&
                  scope.row.status != 2 &&
                  scope.row.isTiming != 0
                  " style="color: #16a589; margin-left: 0px" @click="edit(scope.row)"><i class="el-icon-edit"></i>
                  编辑</el-button>
                <el-button type="text" v-if="scope.row.status == 0" style="color: red; margin-left: 0px"
                  @click="cancel(scope.row)"><i class="el-icon-error"></i> 取消</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
            style="background: #fff; padding: 10px 0; text-align: right">
            <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage" :page-size="formInlines.pageSize" :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tablecurrent.total">
            </el-pagination>
          </el-col>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import DatePlugin from "@/components/publicComponents/DatePlugin.vue";
import TableTem from "@/components/publicComponents/TableTem";
import Tooltip from "@/components/publicComponents/tooltip";
import clip from '../../../utils/clipboard'
import getNoce from "../../../../../plugins/getNoce";
export default {
  name: "SendTask",
  components: {
    DatePlugin,
    TableTem,
    Tooltip
  },
  data() {
    return {
      name: "SendTask",
      //复选框值
      selectId: "",
      // 搜索数据
      formInline: {
        sendType: "0",
        source: "",
        taskName: "",
        beginTime: "",
        endTime: "",
        startTime: "",
        stopTime: "",
        isTiming: "",
        status: "",
        time: [],
        time1: [],
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        sendType: "0",
        source: "",
        taskName: "",
        beginTime: "",
        endTime: "",
        startTime: "",
        stopTime: "",
        isTiming: "",
        status: "",
        time: [],
        time1: [],
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        // tableLabel:[]
        //     {
        //     prop:"taskSmsId",
        //     showName:'发送ID',
        //     width:'60',
        //     fixed:false
        //     },
        //     {
        //     prop:"content",
        //     showName:'发送内容',
        //     fixed:false
        //     },
        //     {
        //     prop:"fileMobile",
        //     showName:'文件名/手机号',
        //     fixed:false
        //     },
        //     {
        //     prop:"sendTime",
        //     showName:'发送时间',
        //     width:'140',
        //     fixed:false
        //     },
        //     {
        //     prop:"isTiming",
        //     showName:'发送类型',
        //     width:'120',
        //     fixed:false,
        //     formatData:function(val){
        //         let type=''
        //         if(val==0){
        //             type='立即发送'
        //         }else{
        //             type='定时发送'
        //         }
        //         return type
        //     }},
        //     {
        //     prop:"totalNum",
        //     showName:'提交号码数(总行数）',
        //     width:'140',
        //     fixed:false
        //     },{
        //     prop:"effectiveNum",
        //     showName:'有效号码（有效行）',
        //     width:'140',
        //     fixed:false
        //     },{
        //     prop:"invalidNum",
        //     showName:'无效号码（无效行）',
        //     width:'140',
        //     fixed:false
        //     },
        //     {
        //     prop:"status",
        //     showName:'发送状态',
        //     fixed:false,
        //     formatData:function(val){
        //         let type=''
        //         if(val==1){
        //             type='发送中'
        //         }else if(val==2){
        //             type='已完成'
        //         }else if(val==-1){
        //             type='异常'
        //         }else if(val==0){
        //             type='待处理'
        //         }
        //         return type
        //     }}
        // ],
        // tableStyle:{
        //     isSelection:true,//是否复选框
        //     isExpand:false,//是否是折叠的
        //     style: {//表格样式,表格宽度
        //         width:"100%"
        //     },
        //     optionWidth:'120',//操作栏宽度
        //     border:true,//是否边框
        //     stripe:false,//是否有条纹
        // },
        // tableOptions:[
        //     {
        //         optionName:'编辑',
        //         type:'',
        //         size:'mini',
        //         optionMethod:'edit',
        //         icon:''
        //     },
        //     {
        //         optionName:'取消',
        //         type:'',
        //         size:'mini',
        //         optionMethod:'cancel',
        //         icon:''
        //     }
        // ],
        //  conditionOption:[
        //     {
        //         contactCondition:'status',//关联的表格属性
        //         contactData:'0',//关联的表格属性-值
        //         optionName:'编辑',//按钮的显示文字
        //         optionMethod:'edit',//按钮的方法
        //         // icon:'el-icon-edit',//按钮图标
        //         optionButtonColor:'',//按钮颜色
        //     },
        //     {
        //         contactCondition:'status',//关联的表格属性
        //         contactData:'0',//关联的表格属性-值
        //         optionName:'取消',//按钮的显示文字
        //         optionMethod:'cancel',//按钮的方法
        //         // icon:'el-icon-edit',//按钮图标
        //         optionButtonColor:'red',//按钮颜色
        //     }
        // ]
      },
    };
  },
  methods: {
    //批量取消
    batchDeletion() {
      this.$confirms.confirmation(
        "post",
        "确定取消定时发送？",
        this.API.cpus + "v3/web-task/cancel",
        { ids: this.selectId },
        (res) => {
          this.InquireList();
        }
      );
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = [];
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].taskSmsId);
      }
      this.selectId = selectId; //批量操作选中id
    },
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.cpus + "v3/selectConsumerWebTaskPage",
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false;
          this.tableDataObj.tableData = res.records;
          this.tableDataObj.tablecurrent.total = res.total;
        }
      );
    },
    // 编辑
    edit(val) {
      if (Date.parse(val.sendTime) - Date.now() < 300000) {
        this.$message({
          message: "定时小于现在5分钟无法编辑",
          type: "warning",
        });
      } else {
        if (val.sendType == 3) {
          this.$router.push({
            path: "/PersonalizedSendEditing?SmSId=" + val.taskSmsId,
          });
        } else {
          this.$router.push({ path: "/SendSMSEdit?SmSId=" + val.taskSmsId });
        }
      }
    },
    // 取消
    cancel(val) {
      this.$confirms.confirmation(
        "post",
        "确定取消定时？",
        this.API.cpus + "v3/web-task/cancel",
        { ids: [val.taskSmsId] },
        (res) => {
          this.InquireList();
        }
      );
    },
    handleCopy(name, event) {
      clip(name, event)
    },
    // 下载
    download(val, tag) {
      // 时间过滤
      Date.prototype.format = function (format) {
        var args = {
          "M+": this.getMonth() + 1,
          "d+": this.getDate(),
          "h+": this.getHours(),
          "m+": this.getMinutes(),
          "s+": this.getSeconds(),
          "q+": Math.floor((this.getMonth() + 3) / 3), //quarter
          S: this.getMilliseconds(),
        };
        if (/(y+)/.test(format))
          format = format.replace(
            RegExp.$1,
            (this.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        for (var i in args) {
          var n = args[i];
          if (new RegExp("(" + i + ")").test(format))
            format = format.replace(
              RegExp.$1,
              RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length)
            );
        }
        return format;
      };
      var that = this;
      filedownload();
      async function filedownload() {
        const nonce = await getNoce.useNonce();
        if (tag == "2") {
          let group = val.invalidFilePath.substring(0, 6);
          let invalidFilePath = val.invalidFilePath.slice(7);
          fetch(
            "/gateway/client-cpus/v3/file/download?fileName=" +
            new Date().getTime() +
            "&group=" +
            group +
            "&path=" +
            invalidFilePath,
            {
              method: "get",
              headers: {
                "Content-Type": "application/json",
                Authorization: "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
                'Once': nonce,
              },
              // body: JSON.stringify({
              //     batchNo: val.row.batchNo,
              // })
            }
          )
            .then((res) => res.blob())
            .then((data) => {
              let blobUrl = window.URL.createObjectURL(data);
              download(blobUrl);
            });
        } else {
          fetch(
            that.API.cpus +
            "v3/file/download?fileName=" +
            val.fileMobile +
            "&group=group1&path=" +
            val.filePath.slice(7),
            {
              method: "get",
              headers: {
                "Content-Type": "application/json",
                Authorization:
                  "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
                'Once': nonce,
              },
              // body: JSON.stringify({
              //     batchNo: val.row.batchNo,
              // })
            }
          )
            .then((res) => res.blob())
            .then((data) => {
              let blobUrl = window.URL.createObjectURL(data);
              download(blobUrl);
            });
        }
      }
      function download(blobUrl) {
        if (tag == "2") {
          var a = document.createElement("a");
          a.style.display = "none";
          a.download =
            "(" +
            new Date().format("yyyy-MM-dd hh:mm:ss") +
            ") " +
            new Date().getTime() +
            ".txt";
          a.href = blobUrl;
          a.click();
        } else {
          var a = document.createElement("a");
          a.style.display = "none";
          a.download =
            "(" +
            new Date().format("yyyy-MM-dd hh:mm:ss") +
            ") " +
            val.fileMobile;
          a.href = blobUrl;
          a.click();
        }
      }
    },
    // 查询
    ListSearch() {
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields();
      (this.formInline.time = []), (this.formInline.beginTime = "");
      this.formInline.endTime = "";
      (this.formInline.time1 = []), (this.formInline.startTime = "");
      this.formInline.stopTime = "";
      this.formInline.source = "";
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    // 定时时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = val[0] + " 00:00:00";
        this.formInline.endTime = val[1] + " 23:59:59";
      } else {
        this.formInline.beginTime = "";
        this.formInline.endTime = "";
      }
    },
    // 提交时间
    getTimeOperating1(val) {
      if (val) {
        this.formInline.startTime = val[0] + " 00:00:00";
        this.formInline.stopTime = val[1] + " 23:59:59";
      } else {
        this.formInline.startTime = "";
        this.formInline.stopTime = "";
      }
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size;
      this.InquireList();
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage;
      this.InquireList();
    },
  },
  created() {
    // this.formInline.source = this.$route.query.source
    if (this.$route.query.source) {
      this.formInline.source = this.$route.query.source;
      this.formInlines.source = this.$route.query.source;
    }
    this.InquireList();
  },
  // activated(){
  //     this.InquireList()
  // },
  watch: {
    // 监听搜索/分页数据
    // formInlines:{
    //     handler() {
    //         this.InquireList()
    //     },
    //     deep: true,
    //     immediate: true,
    // },
  },
};
</script>
<style scoped>
.Statistics-box {
  padding: 20px;
}

.addC .el-select {
  width: 100%;
}

.addC .el-cascader {
  width: 100%;
}

.span {
  white-space: pre-wrap;
}
</style>
