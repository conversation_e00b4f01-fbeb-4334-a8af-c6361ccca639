<template>
    <div class="Edit">
        <div class="Top_title">
            <!-- <span style="display:inline-block;padding-right:10px; cursor:pointer;color:#16a589;" @click="goBack()"><i class="el-icon-arrow-left"></i> 返回</span>| -->
            <span style="display:inline-block;padding-left:6px;"><span
                    style="display:inline-block;padding-right:10px; cursor:pointer;color:#16a589;" @click="goBack()"><i
                        class="el-icon-arrow-left"></i>返回</span>|<span> 发送短信编辑</span></span>
        </div>
        <div class="fillet shortChain-box">
            <el-row style="padding-bottom:20px;">
                <el-col :span="15" style="padding-right:40px;">
                    <el-form :model="configurationItem.formData" :rules="configurationItem.formRule" ref="configurations"
                        label-width="95px" style="padding:20px 8px  0 8px;">
                        <el-form-item label="短信类型" prop="tempId">
                            <el-select v-model="configurationItem.formData.tempId" placeholder="请选择" style="width:364px;">
                                <!-- <el-option label="自定义内容" value="-1" v-if="this.$store.state.custom == 1"></el-option> -->
                                <!-- <el-option v-for="item in temOptions" :key="item.tempId" :label="item.temName" :value="item.temId"></el-option> -->
                                <el-option disabled label="自定义发送内容" value="-1"></el-option>
                                <el-option disabled label="模板发送" value="2"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="选择签名"
                            v-if="!(TemplateContent.indexOf('】') > 0 && configurationItem.formData.tempId != '-1') && sendTypes == '2'">
                            <!-- <el-select v-model="configurationItem.formData.signatureId" @change="changeLocationValue"
                                style="width:364px;" clearable filterable remote :remote-method="remoteMethod"
                                :loading="loadingcomp">
                                <el-option v-for="item in sigOptions" :key="item.signatureId" :label="item.signature"
                                    :value="item.signatureId"></el-option>
                            </el-select> -->
                            <el-select v-model="configurationItem.formData.signatureId" @change="changeLocationValue"
                                style="width:364px;" clearable filterable>
                                <el-option v-for="item in sigOptions" :key="item.signatureId" :label="item.signature"
                                    :value="item.signatureId"></el-option>
                            </el-select>
                            <router-link :to="'CreateSign?i=1'"><el-button type="primary" icon="el-icon-refresh" plain
                                    style="margin-left:10px;"
                                    @click="logUrl('/SignatureManagement')">添加签名</el-button></router-link>
                        </el-form-item>
                        <!-- <el-form-item label="选择模板" v-show="configurationItem.formData.tempId != '-1'" placeholder="请选择模板">
                            <el-button type="primary" icon="el-icon-refresh" @click="templateDialog=true" plain>选择模板</el-button>
                        </el-form-item> -->
                        <el-form-item label="短信内容" prop="content" v-if="configurationItem.formData.tempId == '-1'">
                            <div style="width: 560px;">
                                <!-- <span style="font-size: 12px;color: #ddd;position: absolute;left: 280px;bottom: 35px;">回TD退订</span> -->
                                <el-input type="textarea" placeholder="【短信签名】请在此处输入短信内容"
                                    v-model="configurationItem.formData.content" class="textareas"
                                    style="width:364px;"></el-input>
                            </div>
                            <div style="font-size: 12px;"><i class="el-icon-warning"></i> 已输入 <span
                                    v-if="number_zs <= 450">{{ number_zs }}</span> <span v-else
                                    style="color: red;">{{ number_zs }}</span> 字，最多 450 字（含签名）,70字内（含70字）计一条，超过70字，按67字/条计费
                            </div>
                        </el-form-item>
                        <el-form-item label="短信内容" prop="content" v-if="configurationItem.formData.tempId != '-1'">
                            <div style="width: 363px;" @mouseover="button_QC = true" @mouseout="button_QC = false">
                                <!-- <el-button type="primary" size="mini" plain style="position: absolute;top: 40px;left: 140px;" @click="button_QCNR" v-if="button_QC">清除内容</el-button> -->
                                <el-input type="textarea" disabled placeholder="请选择您需要发送的短信模板,选择完模板内容将会显示" readonly
                                    v-model="configurationItem.formData.content" class="textareas"
                                    style="width:364px;"></el-input>
                            </div>
                            <div style="font-size: 12px;"><i class="el-icon-warning"></i> 已输入 <span
                                    v-if="number_zs <= 450">{{ number_zs }}</span> <span v-else
                                    style="color: red;">{{ number_zs }}</span> 字，最多 450 字（含签名）,70字内（含70字）计一条，超过70字，按67字/条计费
                            </div>
                        </el-form-item>
                        <el-button type="primary" v-show="configurationItem.formData.tempId == '-1' && !SmSid"
                            icon="el-icon-refresh" plain @click="shortReset()"
                            style="margin-left:95px;margin-bottom:6px;">短链转换</el-button>
                        <el-form-item label="发送时间">
                            <el-radio-group v-model="configurationItem.formData.isTiming">
                                <el-radio disabled label="0">立即发送</el-radio>
                                <el-radio label="1">定时发送</el-radio>
                            </el-radio-group>
                        </el-form-item>
                        <div v-if="configurationItem.formData.isTiming == 1" style="padding-bottom:18px;"
                            class="send-time-sel" prop="sendTime">
                            <span style="display:inline-block;width:83px;text-align:right;padding-right:10px;">定时时间</span>
                            <date-plugin class="Mail-search-date" :datePluginValueList="datePluginValueList"
                                @handledatepluginVal="handledatepluginVal" style="width:364px;"></date-plugin>
                        </div>
                        <div style="position:relative;">
                            <span
                                style="display:inline-block;width:83px;text-align:right;padding-right:11px;position:absolute;">发送对象</span>
                            <file-upload v-if="!SmSid" style="display: inline-block; margin-left:97px;"
                                :action="this.API.cpus + 'v3/file/upload'" :limit=1 :showfileList=true :fileStyle='fileStyle'
                                :del='del1' :istip='false' :tip='tip' @fileup="fileup"
                                @fileupres="fileupres">选择上传文件</file-upload>
                        </div>
                        <div class="send-upload-tips">格式要求：支持.xlsx .xls.txt 等格式,文件大小不超过300M<span
                                style="color:rgb(210, 7, 7)">(如上传文件将会自动清除下面手动填写的手机号码)</span></div>
                        <div class="send-upload-tips">TXT格式：只支持一行一个号码，且只适用于自定义与普通模板发送，建议单次最大上传10万行，内容格式请先下载模板</div>
                        <div class="send-upload-tips" style="margin-bottom: 20px;">建议单次最大上传10万行，内容格式请参考模板<span
                                style="cursor: pointer;" @click="downloadTems">模板下载</span></div>
                        <el-form-item label="" prop="" v-if="textareaShow == true && downloadtem != '1' && variableTp">
                            <div class="box-textareas">
                                <el-input type="textarea" disabled @blur="FilterNumber" class="textareas"
                                    @input="textChange" style="width:380px;" placeholder="手动最多输入200个手机号码，号码之间用英文逗号隔开"
                                    v-model="configurationItem.formData.mobile"></el-input>
                            </div>
                            <div style="width: 695px;text-align: center;position: absolute;bottom: 30px;height: 0;"
                                v-show="limit <= 200">
                                <span>{{ limit }}/200</span>
                            </div>
                            <div style="width: 695px;text-align: center;position: absolute;bottom: 30px;height: 0;"
                                v-show="limit > 200">
                                <span style="color: red;">{{ limit }}</span><span>/200</span>
                            </div>
                        </el-form-item>
                        <el-form-item label="" prop="" v-if="textareaShow == true && downloadtem != '1' && variableTp"
                            style="margin-top:-10px">
                            <!-- <el-button type="primary" plain  @click="FilterNumber">过滤号码</el-button> -->
                            <span style="margin-left: 10px;font-size:12px;">成功提交<i
                                    style="font-style: normal;color: #16A589;font-size:12px;"> {{ SuccessfullySubmitted }}
                                </i>个</span><span style="margin-left: 10px;font-size:12px;">已过滤<i
                                    style="font-style: normal;color: red;font-size:12px;"> {{ filter }} </i>个重复</span><span
                                style="margin-left: 10px;font-size:12px;">无效号码<i style="font-style: normal;color: red;">
                                    {{ invalid }} </i>个</span>
                        </el-form-item>
                        <div class="sms-first-steps-btns" style="text-align: left;padding-left: 95px;margin-bottom: 30px;">
                            <el-button type="primary" v-if="sendLoading == true" @click="ConfirmSending()"
                                style="padding:10px 28px;">发送短信</el-button>
                            <el-button type="primary" v-if="sendLoading == false" :loading="true"
                                style="padding:10px 40px;">提交数据中</el-button>
                            <!-- <el-button @click="sendMsgDialogShow = false" style="padding:10px 20px;">取 消</el-button> -->
                        </div>
                        <el-form-item label="发送规则" prop="">
                            <ul style="list-style: disc;margin-left: 20px;">
                                <li>从模板库中导入的短信可实时发送。</li>
                                <li>您在本页面上输入的短信内容，需进入人工审核，待审核完毕后将自动发送。</li>
                                <li>发送会员营销短信，不能包含变量，且需进入人工审核，待审核完毕后将自动发送。</li>
                                <li>发送行业通知短信，选择模板发送不需要人工审核直接发送；输入内容最少需添加1个参数，最多可添加16个参数。</li>
                                <li>计费规则：<span style="color: red;">70字内（含70字）</span>计一条，超过70字，按<span
                                        style="color: red;">67字/条</span>计费。</li>
                            </ul>
                        </el-form-item>
                        <el-form-item label="内容规范" prop="">
                            <ul style="list-style: disc;margin-left: 20px;">
                                <li>签名内容为：公司或品牌名称，字数要求<span style="color: red;">1-50</span>个字符。</li>
                                <li><span style="color: red;">邀请注册、邀请成为会员、邀请加微信、加QQ群</span>的商业性信息不能发送。</li>
                                <li><span style="color: red;">黄、赌、毒</span>犯法等国家法律法规严格禁止的内容不能发送。</li>
                                <li>包含“股票加群、购物加群、集资贷款、改分、代办大额信用卡、信用卡提额”等疑似诈骗或类似的信息不能发送。</li>
                                <li>超链接地址请写在短信内容中，便于核实，部分安卓系统存在超链接识别问题，需在超链接前后添加空格。</li>
                                <li>变量模板中的变量有长度和个数限制，具体请咨询。</li>
                            </ul>
                        </el-form-item>
                    </el-form>
                </el-col>
                <el-col :span="9" style="border-left:1px dashed #ccc;padding-top:32px;">
                    <div class="send-mobel-box">
                        <img src="../../../../../assets/images/phone.png" alt="">
                        <p
                            style="width:80px; position: absolute;padding-left: 76px;top:122px;left:40px;font-size:12px;color:#666;">
                            短信</p>
                        <el-scrollbar class="sms-content-exhibition">
                            <div style="width:168px;word-wrap:break-word;">{{ configurationItem.formData.content }}</div>
                        </el-scrollbar>
                        <div style="width:80px; position:absolute;left: 72px;top:277px;">
                            <span
                                style="width: 17px; height: 20px;position: absolute;left: -50px;top: 24px;border-radius: 100% 0px 80% 0px;background: #e2e2e2"></span>
                            <span
                                style="width:17px;height: 20px;position: absolute;left: -58px;top: 24px;border-radius: 100% 0px 100% 0px;background: #fff;"></span>
                        </div>
                    </div>
                    <div style="font-size:12px;text-align:center;">当前发送内容 <span
                            style="color:#d20707;">{{ SMScount.smswordNum }}</span> 个字，预计发送条数约为 <span
                            style="color:#d20707;">{{ SMScount.smssTrip }}</span> 条短信</div>
                    <div style="font-size:12px;text-align:center;color:#d20707;">(实际发送时，如有模板变量会影响计费条数，请注意关注)</div>
                </el-col>
            </el-row>
        </div>
        <!-- 模板列表 -->
        <el-dialog title="短信模板选择" :visible.sync="templateDialog" :closeOnClickModal="false" width="920px">
            <div style="margin-bottom: 10px;">
                <el-input style="width:200px" v-model="tabelAlllist.param" placeholder="请输入内容"></el-input>
                <el-button type="primary" plain style="" @click="getTableData()">查询</el-button>
                <router-link :to="'CreateTemplate?i=1'">
                    <el-button type="primary" plain style="" @click="logUrl('/TemplateManagement')">添加模版</el-button>
                </router-link>
            </div>
            <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
                element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)"
                ref="multipleTable" border :data="tableDataObj.tableData" style="width: 100%">
                <el-table-column prop="temId" label="ID" width="60"></el-table-column>
                <el-table-column label="模板类型" width="90">
                    <template slot-scope="scope">
                        <span v-if="scope.row.temType == '1'">验证码</span>
                        <span v-else-if="scope.row.temType == '2'">通知</span>
                        <span v-else-if="scope.row.temType == '3'">营销推广</span>
                    </template>
                </el-table-column>
                <!-- <el-table-column label="申请时间" width="170">
                        <template slot-scope="scope">{{ scope.row.createTime}}</template>
                </el-table-column>
                <el-table-column label="模板名称" width="170">
                        <template slot-scope="scope">{{ scope.row.temName}}</template>
                </el-table-column> -->
                <el-table-column label="内容">
                    <template slot-scope="scope">
                        <span>{{ scope.row.temContent }}</span>
                        <span v-if="scope.row.checkReason == null"></span>
                        <span v-else-if="scope.row.checkReason == ''"></span>
                        <span v-else style="color:#F56C6C">( 驳回原因：{{ scope.row.checkReason }} )</span>
                    </template>
                </el-table-column>
                <el-table-column label="字数" width="90">
                    <template slot-scope="scope">
                        <span>{{ scope.row.temContent.length }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" width='80'>
                    <template slot-scope="scope">
                        <el-button type="text" @click="delTem(scope.row.temContent, scope.row.temId)"><i
                                class="el-icon-circle-plus"></i>&nbsp;选择</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <!--分页-->
            <div style="height: 20px;padding: 20px;">
                <el-pagination style="float:right" class="page_bottom" @size-change="handleSizeChange"
                    @current-change="handleCurrentChange" :current-page="tabelAlllist.currentPage"
                    :page-size='tabelAlllist.pageSize' :page-sizes="[10, 20, 50, 100]"
                    layout="total, sizes, prev, pager, next, jumper" :total="pageTotal">
                </el-pagination>
            </div>
        </el-dialog>
        <!-- 短链转换 -->
        <el-dialog title="短链转换" :visible.sync="shortVisible" width="520px">
            <div class="short-box">
                <p class="short-title" style="padding-top:10px">长网址链接</p>
                <el-input placeholder="请输入长链接" v-model="originalUrl" class="width-l">
                    <el-button slot="append" type="primary" icon="el-icon-refresh" @click="transformation()">转换</el-button>
                </el-input>
                <div class="font-sizes font-sizes1"><span style="color:red">* </span>我们可以帮您把长链接压缩，让您可以输入更多的内容。</div>
                <div class="font-sizes"><span style="color:red">* </span>插入短信内容中时，将在链接前后生成空格符号，以防止出现手机短信客户端不识别链接的情况。</div>
            </div>
            <div class="short-box">
                <p class="short-title" style="padding-top:20px">短网址链接</p>
                <el-input v-model="shortConUrl" class="width-l" :disabled="true">
                    <el-button slot="append" type="primary" @click="handlePreview()" icon="el-icon-share">预览</el-button>
                </el-input>
            </div>
            <div style="text-align:right;margin-top:16px">
                <el-button @click="HandelCencals()">取 消</el-button>
                <el-button type="primary" @click="shortConDetermine()">确 定</el-button>
            </div>
        </el-dialog>
        <!-- 二次确认弹出 -->
        <el-dialog title="确认发送" :visible.sync="ConfirmSend" width="520px">
            <div style="padding-bottom:5px;">您本次提交号码数 <span style="color:#16A589;">{{ sendNumber }}</span> 个</div>
            <div style="padding-top:10px;">发送内容： <div style="color:#999;padding-top:6px;word-wrap:break-word;">
                    {{ sendContent }}</div>
            </div>
            <div class="sms-seconnd-steps-btns">
                <el-button type="primary" @click="ConfirmSending()" style="padding:10px 20px;"
                    v-if="fullscreenLoading == true">确定发送</el-button>
                <el-button @click="ConfirmSends">取 消</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { mapState, mapMutations, mapActions } from "vuex";
import FileUpload from '@/components/publicComponents/FileUpload' //文件上传
import DatePlugin from '@/components/publicComponents/DatePlugin' //日期
export default {
    name: "SendSMSEdit",
    components: {
        FileUpload,
        DatePlugin
    },
    data() {
        //短信内容的签名格式是否正确，或者是否有签名
        var content = (rule, value, callback) => {
            if (value == "") {
                return callback(new Error('请填写短信内容！'));
            } else {
                let ret = 3;
                let beg = value.indexOf('【');
                let end = value.indexOf('】');
                let lastbeg = value.lastIndexOf('【');
                let lastend = value.lastIndexOf('】');
                let valLength = value.length;
                if (beg > - 1 && end > -1 && end > beg && beg == 0) {
                    if (beg == 0 && end < 50 && end > 2) {
                        let index = value.split('】').length
                        for (let i = 0; i < index; i++) {
                            if (i != 0) {
                                let c = value.split('【')[i]
                                let a = c.substring(0, c.indexOf("】"))
                                if (!/^[\u4e00-\u9fa5_a-zA-Z0-9]+$/.test(a) || a.length < 2 || a.length > 20) {
                                    return callback(new Error('请填写正确的签名！'));
                                }
                            }
                        }
                        callback();
                    } else {
                        return callback(new Error('请填写正确的签名！'));
                    }
                } else if (lastbeg > -1 && lastend > -1 && lastend > lastbeg && lastend == valLength - 1) {
                    if (lastend == (valLength - 1) && lastend - lastbeg < 49 && lastend - lastbeg > 2) {
                        callback();
                    } else {
                        return callback(new Error('请填写正确的签名！'));
                    }
                } else {
                    return callback(new Error('请填写签名！'));
                }
            }
        };
        var mobile = (rule, value, callback) => {
            if (value == "") {
                callback();
            } else {
                if (this.limit > 200) {
                    callback("已超过填写返回");
                }
            };
        }
        return {
            sendTypes: '',
            // 编辑id
            SmSid: '',
            TemplateStatus: false,
            variableTp: true,
            tempIdVal: '',
            TemplateContent: "",//选择模板存储内容用
            number_zs: 0,
            button_QC: false,
            textareaShow: true,
            ConfirmSend: false,
            limit: 0,//号码限制
            SuccessfullySubmitted: 0, //成功提交号
            filter: 0, //过滤
            invalid: 0, //无效 
            shortVisible: false,//短链弹框
            originalUrl: '',
            shortConUrl: '', //短链的URL
            sendLoading: true,//第一步短信发送的loading按钮
            fullscreenLoading: true, //第二步短信发送的loading按钮
            sendNumber: '', //短信发送条数
            sendContent: "", //发送短信
            copysigCenten: "",//复制签名内容
            copytemCenten: "",//复制模板内容
            phoneCenten: '', //手机短信内容
            del1: true,//关闭弹框时清空图片
            tip: '仅支持.xlsx .xls.txt 等格式',
            sigOptions: [], //签名列表
            loadingcomp: false, //文件上传loading
            temOptions: [],//模板列表
            templateDialog: false,
            // 模板列表
            tableDataObj: { //列表数据
                tableData: []
            },
            tabelAlllist: {//------发送表格请求的对象
                param: '',
                currentPage: 1,//当前页
                pageSize: 10,//每一页条数
            },
            pageTotal: 0,//总共条数
            SMScount: { //手机展示短信内容的下方，短信内容的计数
                smswordNum: '0', //短信内容的个数
                smssTrip: '0' //短信可以分为几条
            },
            downloadtem: '2', //模板下载（全文，自定义还是变量）（自定义为2）
            fileStyle: {
                size: 245678943234,
                style: ['xlsx', 'xls', 'txt']
            },
            datePluginValueList: { //日期参数配置
                type: "datetime",
                pickerOptions: {
                    disabledDate(time) {
                        return time.getTime() < Date.now() - 8.64e7;
                    }
                },
                defaultTime: '', //默认起始时刻
                datePluginValue: ''
            },
            configurationItem: { //发送短信弹出框的值
                formData: {
                    tempId: "-1",
                    content: '',
                    signatureId: '',
                    isTiming: "1", //选择立即发送还是定时发送
                    sendTime: '', //发送时间的值
                    files: '',
                    group: '',
                    path: '',
                    mobile: '',
                    templateId: '',
                    taskSmsId: ''
                },
                formDatass: {
                    tempId: "-1",
                    content: '',
                    signatureId: '',
                    isTiming: "1", //选择立即发送还是定时发送
                    sendTime: '', //发送时间的值
                    files: '',
                    group: '',
                    path: '',
                    templateId: '',
                    taskSmsId: ''
                },
                formRule: {//验证规则
                    content: [
                        { required: true, validator: content, trigger: 'change' },
                        { min: 1, max: 450, message: '长度在 1 到 450 个字符', trigger: 'change' }
                    ],
                    tempId: [
                        { required: true, message: '请选择模板名称', trigger: 'change' },
                    ],
                    signatureId: [
                        { required: true, message: '请选择模板', trigger: 'change' },
                    ],
                    sendTime: [
                        { required: true, message: '请填写定时时间', trigger: 'change' },
                    ],
                    isTiming: [
                        { required: true, message: '请选择发送时间', trigger: 'change' }
                    ],
                    // mobile:[
                    //     {validator: mobile, trigger: 'change'}
                    // ]
                }
            },
        }
    },
    methods: {
        button_QCNR() {
            this.configurationItem.formData.content = ""
        },
        //返回
        goBack() {
            this.$router.go(-1);
        },
        textChange() {
            // this.configurationItem.formData.mobile=this.configurationItem.formData.mobile.replace(/[^\ \d\,]/g,"")
            // this.configurationItem.formData.mobile=this.configurationItem.formData.mobile.replace(/\s+/g,",")
            if (this.configurationItem.formData.mobile[this.configurationItem.formData.mobile.length - 1] == ",") {
                this.limit = this.configurationItem.formData.mobile.split(",").length - 1
            } else {
                this.limit = this.configurationItem.formData.mobile.split(",").length
            }
            if (this.configurationItem.formData.mobile.split(",").length == 1 && this.configurationItem.formData.mobile.split(",")[0] == "") {
                this.limit = 0
            }
        },
        // remoteMethod(query) {
        //     if (query !== '') {
        //         this.loadingcomp = true
        //         this.getSignature(query)
        //         this.loadingcomp = false
        //     } else {
        //         this.compNamelist = []
        //         this.getSignature()
        //     }
        // },
        //获得签名列表
        getSignature(query) {
            this.$api.post(this.API.cpus + 'signature/signatureList', {
                // signature: query || '',
                auditStatus: '2',
                currentPage: 1,
                pageSize: 200
            }, res => {
                this.sigOptions = res.records;
            })
        },
        //获得模板名称列表
        getTemplate() {
            this.$api.post(this.API.cpus + 'v3/consumersmstemplate/selectClientTemplate', {
                currentPage: 1,
                pageSize: 200
            }, res => {
                this.temOptions = res.records;
            })
        },
        //获得模板名称对应的内容
        // getTemplateContent(){
        //     if(this.configurationItem.formData.tempId){
        //         this.$api.get(this.API.cpus + 'v3/consumersmstemplate/get/'+this.configurationItem.formData.tempId,{},res=>{
        //             this.downloadtem = res.data.temFormat; //模板下载类型（1变量，2全文）
        //             this.copytemCenten = res.data.temContent;
        //             this.phoneCenten = this.copysigCenten + this.copytemCenten;
        //         })
        //     }
        // },
        // 过滤号码
        FilterNumber() {
            let NumberFilter = this.configurationItem.formData.mobile.split(",")
            let arrNumber = []
            let hash = []
            let reg = /^1\d{10}$/;
            for (var i = 0; i < NumberFilter.length; i++) {
                for (var j = i + 1; j < NumberFilter.length; j++) {
                    if (NumberFilter[i] === NumberFilter[j]) {
                        ++i;
                    }
                }
                arrNumber.push(NumberFilter[i]);
            }
            for (var i = 0; i < arrNumber.length; i++) {
                if (reg.test(arrNumber[i])) {
                    hash.push(arrNumber[i])
                }
            }
            this.configurationItem.formData.mobile = hash.join(",")
            this.SuccessfullySubmitted = hash.length //成功提交号
            this.filter = (NumberFilter.length) - (arrNumber.length) //过滤
            if (arrNumber[0] == "") {
                this.invalid == 0
            } else {
                this.invalid = (arrNumber.length) - (hash.length) //无效 
            }
            this.limit = hash.length
        },
        //获取签名下拉框选择的 签名内容
        changeLocationValue(val) {
            // if(!val){
            //     this.getSignature()
            // }
            let obj = {};
            obj = this.sigOptions.find((item) => {
                return item.signatureId === val;
            });
            this.copysigCenten = obj.signature;
            //短信内容展示
            if (this.configurationItem.formData.tempId == "-1") {
                let signaIndex = this.configurationItem.formData.content.lastIndexOf("】") + 1
                this.configurationItem.formData.content = this.copysigCenten + this.configurationItem.formData.content.substring(signaIndex, this.configurationItem.formData.content.length);
            } else {
                this.configurationItem.formData.content = this.copysigCenten + this.TemplateContent;
            }
        },
        //移除文件
        fileup(val) {
            this.configurationItem.formData.files = '';
            this.textareaShow = true
            this.configurationItem.formData.mobile = '';
            this.limit = 0//号码限制
            this.SuccessfullySubmitted = 0//成功提交号
            this.filter = 0 //过滤
            this.invalid = 0 //无效 
        },
        //文件上传成功
        fileupres(val, val2) {
            this.configurationItem.formData.group = val.data.group;
            this.configurationItem.formData.path = val.data.path;
            this.configurationItem.formData.files = val2[0].name;
            this.configurationItem.formData.mobile = ""
            this.textareaShow = false
            this.del1 = true;
        },
        //下载模板
        downloadTems() {
            if (this.downloadtem == '2') {
                this.$File.export(this.API.cpus + 'consumersmsinfo/templateZipDownload', {}, `发送文件模板（自定义内容，全文模板可用）.zip`)
            } else {
                this.$File.export(this.API.cpus + 'consumersmsinfo/templateZipDownload', {}, `发送文件模板（变量模板可用）.zip`)
            }
        },
        //短信发送提交 (第一步的短信发送)
        submissionItem(formName) {
            this.$refs[formName].validate((valid, val) => {
                if (this.configurationItem.formData.tempId == '-1' && Object.getOwnPropertyNames(val).length == 1 && val.signatureId[0].message == "请选择签名") {
                    valid = true
                } else if (this.configurationItem.formData.tempId != '-1' && Object.getOwnPropertyNames(val).length == 1 && val.content[0].message == "请填写短信内容！") {
                    valid = true
                }
                if (this.limit > 200) {
                    valid = false
                    this.$message({
                        message: '手机号超出填写个数',
                        type: 'warning'
                    });
                }
                if (valid) {
                    let falgs = false;
                    let falgss = false;
                    this.configurationItem.formDatass = Object.assign({}, this.configurationItem.formData);
                    if (this.configurationItem.formDatass.isTiming === '1') { //判断发送时间为定时时间
                        if (this.configurationItem.formDatass.sendTime) {  //如果是定时时间 ，定时时间范围必填
                            let nowTiem = new Date(this.configurationItem.formDatass.sendTime).getTime();
                            if (nowTiem < Date.now() + 1800000) {
                                falgss = false;
                                this.datePluginValueList.datePluginValue = '';
                                this.configurationItem.formData.sendTime = '';
                                this.$message({
                                    message: '定时时间已过期，定时时间应大于当前时间30分钟，需重新设置！',
                                    type: 'warning'
                                });
                            } else {
                                falgss = true;
                            }
                        } else {
                            falgss = false;
                            this.$message({
                                message: '选择定时时间！',
                                type: 'warning'
                            });
                        }
                    } else {
                        falgss = true;
                    }
                    //判断是否上传文件
                    // if(this.configurationItem.formDatass.files != ''||this.configurationItem.formDatass.mobile!=""){
                    falgs = true;
                    // }else{
                    // falgs = false;
                    //     if(this.downloadtem=="2"){
                    //         this.$message({
                    //             message: '上传发送对象文件或手动填写手机号!',
                    //             type: 'warning'
                    //         });
                    //     }else{
                    //         this.$message({
                    //             message: '上传发送对象文件!',
                    //             type: 'warning'
                    //         });
                    //     }
                    // }
                    //判断是否是 自定义内容
                    if (this.configurationItem.formDatass.tempId === '-1') {
                        this.configurationItem.formDatass.tempId = '';
                    } else {
                        this.configurationItem.formDatass.tempId = this.tempIdVal
                    }
                    if (falgs == true && falgss == true) {
                        this.sendLoading = false;
                        this.$api.post(this.API.cpus + 'consumersmsinfo/submitSms', this.configurationItem.formDatass, res => {
                            this.sendLoading = true;
                            if (res.code == 200) {
                                if (res.data.mobileNum == 0) {
                                    this.$message({
                                        message: '提交成功条数为0 ，需重新设置！',
                                        type: 'warning'
                                    });
                                } else {
                                    this.sendNumber = res.data.mobileNum;
                                    this.sendContent = res.data.content;
                                    this.phoneCenten = res.data.content;
                                    this.ConfirmSend = true
                                }
                            } else {
                                this.$message({
                                    message: res.msg,
                                    type: 'warning'
                                });
                            }
                        })
                    }
                } else {
                    console.log('error submit!!');
                    return false;
                }
            })
        },
        //确定发送短信 (第二步的短信发送)
        ConfirmSending() {
            this.fullscreenLoading = false;
            this.configurationItem.formDatass = Object.assign({}, this.configurationItem.formData);
            let flags = false;
            if (this.configurationItem.formDatass.isTiming === '1' && this.configurationItem.formDatass.sendTime) {  //判断是否定时时间 ，定时时间范围
                let nowTiem = new Date(this.configurationItem.formDatass.sendTime).getTime();
                if (nowTiem < Date.now() + 1800000) {
                    flags = false;
                    this.ConfirmSend = false
                    this.fullscreenLoading = true;
                    this.$message({
                        message: "定时时间应大于当前时间，需重新设置！",
                        type: 'warning'
                    });
                    this.datePluginValueList.datePluginValue = '';
                    this.configurationItem.formData.sendTime = '';
                    this.stepsActive = 1;

                } else {
                    flags = true;
                }
            } else {
                flags = true;
            }
            if (flags === true) {
                //自定义内容发送
                if (this.configurationItem.formDatass.tempId === '-1') {
                    this.configurationItem.formDatass.tempId = '';
                    this.configurationItem.formDatass.signatureId = '';
                    if (this.isShort) {
                        this.configurationItem.formDatass.shortCode = this.shortCode;
                    } else {
                        this.configurationItem.formDatass.shortCode = '';
                    }
                    this.$api.post(this.API.cpus + 'v3/updateConsumerWebTask', this.configurationItem.formDatass, res => {
                        if (res.code == 200) {
                            this.$message({
                                message: '短信编辑成功！',
                                type: 'success'
                            });
                            this.$router.go(-1);
                        } else if (res.code == 406) {
                            this.$message({
                                message: '有效短信为0条！',
                                type: 'warning'
                            })
                        } else {
                            this.$message({
                                message: res.msg,
                                type: 'error'
                            });
                        }
                    })
                } else {
                    //模板内容发送
                    this.$api.post(this.API.cpus + 'v3/updateConsumerWebTask', this.configurationItem.formDatass, res => {
                        if (res.code == 200) {
                            this.$message({
                                message: '短信发送成功！',
                                type: 'success'
                            });
                            this.$router.go(-1);
                        } else if (res.code == 406) {
                            this.$message({
                                message: '有效短信为0条！',
                                type: 'warning'
                            })
                        } else {
                            this.$message({
                                message: res.msg,
                                type: 'error'
                            });
                        }
                    })
                }
            }
        },
        // 选择模板
        delTem(val, val2) {
            this.TemplateStatus = true
            this.templateDialog = false
            if (this.TemplateStatus) {
                this.configurationItem.formData.content = val
            } else {
                let contentIndex = this.configurationItem.formData.content.lastIndexOf("】") + 1
                this.configurationItem.formData.content = this.configurationItem.formData.content.substring(0, contentIndex) + val
            }
            this.TemplateContent = val
            this.tempIdVal = val2
        },
        // 取消发送
        ConfirmSends() {
            this.ConfirmSend = false
        },
        handledatepluginVal: function (val1, val2) { //日期
            this.configurationItem.formData.sendTime = val1
        },
        //短链转换
        shortReset() {
            this.shortVisible = true;
        },
        //转换
        transformation() {
            if (this.originalUrl != '') {
                this.$api.post(this.API.cpus + 'shortlink/changeUrl', { originalUrl: this.originalUrl }, res => {
                    if (res.code == 200) {
                        this.shortConUrl = res.data.shortLinkUrl;
                        this.shortCode = res.data.shortCode;
                        this.$message({
                            message: '短链接转换成功！',
                            type: 'success'
                        });
                    } else {
                        this.originalUrl = '';
                        this.$message({
                            message: res.msg,
                            type: 'warning'
                        });
                    }
                })
            } else {
                this.$message({
                    message: '长链接不可为空',
                    type: 'warning'
                });
            }
        },
        //预览
        handlePreview() {
            if (this.shortConUrl != '') {
                window.open('https://' + this.shortConUrl, '_blank');
            } else {
                this.$message({
                    message: '短连接为空，无法预览',
                    type: 'warning'
                });
            }
        },
        //短连接弹框的确定
        shortConDetermine() {
            if (this.shortConUrl != '') {
                this.configurationItem.formData.content += " " + this.shortConUrl + " ";
                this.isShort = true;
                this.shortVisible = false;
            } else {
                this.$message({
                    message: '短链接不可为空',
                    type: 'warning'
                });
            }
        },
        //短链的取消
        HandelCencals() {
            this.shortVisible = false;
            this.isShort = false;
        },
        getTableData() {//获模版取列表数据
            this.tableDataObj.loading2 = true
            let formDatas = this.tabelAlllist;
            this.$api.post(this.API.cpus + 'v3/consumersmstemplate/selectClientTemplate', formDatas, res => {
                this.tableDataObj.tableData = res.records;
                this.tableDataObj.loading2 = false;
                this.pageTotal = res.total;
                this.tabelAlllist.currentPage = res.current;
                this.tabelAlllist.pageSize = res.size
                //                 tabelAlllist:{//------发送表格请求的对象
                //     param:'',
                //     currentPage:1,//当前页
                //     pageSize:10,//每一页条数
                // },
                // pageTotal:0,//总共条数
                // for(var i = 0 ; i<res.records.length ; i++){
                //     if(res.records[i].temFormat==2){
                //         this.tableDataObj.tableData[i].temContent = res.records[i].initialContent
                //     }
                // }
            })
        },
        handleSizeChange(size) { //分页每一页的有几条
            this.tabelAlllist.pageSize = size;
            this.getTableData();
        },
        handleCurrentChange: function (currentPage) {//分页的第几页
            this.tabelAlllist.currentPage = currentPage;
            this.getTableData();
        },
        // 获取项目绝对路径
        ...mapActions([  //比如'movies/getHotMovies
            'saveUrl',
        ]),
        logUrl(val) {
            let logUrl = {
                logUrl: val
            }
            this.saveUrl(logUrl);
            window.sessionStorage.setItem('logUrl', val)
        },
    },
    mounted() {
        this.getSignature();
        this.getTemplate();
        this.getTableData();
        this.SmSid = this.$route.query.SmSId
        this.configurationItem.formData.taskSmsId = this.$route.query.SmSId
        if (this.SmSid) {
            this.$api.get(this.API.cpus + 'v3/queryConsumerWebTaskById/' + this.SmSid, {}, res => {
                this.sendTypes = res.sendType
                if (res.sendType == 1) {
                    this.configurationItem.formData.tempId = "2"
                } else {
                    this.configurationItem.formData.tempId = "-1"
                }
                this.configurationItem.formData.mobile = res.mobile
                this.configurationItem.formData.sendTime = res.sendTime
                this.datePluginValueList.datePluginValue = new Date(Date.parse(res.sendTime))
                this.$api.get(this.API.cpus + 'v3/web-task/info/' + this.SmSid, {}, response => {
                    if(response.code == 200){
                        this.configurationItem.formData.content = response.data.content
                    }else{
                        this.$message({
                            message: res.msg,
                            type: 'warning'
                        })
                    }
                   
                })
            })
        }
    },
    watch: {
        templateDialog(val) {
            if (val == false) {
                this.getTableData()
                this.tabelAlllist.param = ""
            }
        },
        downloadtem(val) {
            if (val == "1") {
                this.fileStyle.style = ['xlsx', 'xls']
                this.tip = '仅支持.xlsx .xls 等格式'
                this.configurationItem.formData.mobile = ""
            } else {
                this.fileStyle.style = ['xlsx', 'xls', 'txt']
                this.tip = '仅支持.xlsx .xls.txt 等格式'
            }
        },
        //监听自定义短信的内容
        'configurationItem.formData.content'() {
            this.phoneCenten = this.configurationItem.formData.content
            this.number_zs = this.configurationItem.formData.content.length
            if (this.configurationItem.formData.content.indexOf('{') >= 0 && this.configurationItem.formData.content.indexOf('}') >= 0 && this.configurationItem.formData.tempId != '-1') {
                this.configurationItem.formData.mobile = ''
                this.variableTp = false
            } else {
                this.variableTp = true
            }
        },
        //监听手机框内容的改变
        phoneCenten(val) {
            //模板
            let d1 = /(\{var[1-9]{1,2}\|(d|w|\$|c)[0-9]{1,2}-[0-9]{1,2}\})|(\{var[1-9]{1,2}\|(d|w|\$|c)[0-9]{1,2}\})|(\{var[1-9]{1,2}\|(hh:mm:ss|MM-DD|YYYY-MM-DD|YYYY-MM-DD hh:mm:ss|MM-DD hh:mm:ss)\})/g;
            let a1 = val.match(d1);

            let w1 = val.length;
            let w2 = 0;

            if (a1 == null) {
                this.SMScount.smswordNum = val.length;
            } else {
                let w3 = 0;
                for (let i = 0; i < a1.length; i++) {
                    w2 += a1[i].length;//参数物理长度
                    if (a1[i].substr(-10) == '|hh:mm:ss}') {
                        w3 += 8;
                    } else if (a1[i].substr(-7) == '|MM-DD}') {
                        w3 += 5;
                    } else if (a1[i].substr(-12) == '|YYYY-MM-DD}') {
                        w3 += 10;
                    } else if (a1[i].substr(-21) == '|YYYY-MM-DD hh:mm:ss}') {
                        w3 += 19;
                    } else if (a1[i].substr(-16) == '|MM-DD hh:mm:ss}') {
                        w3 += 14;
                    } else {
                        let num = /[0-9]{1,2}/g;
                        let mun1 = a1[i].match(num);
                        w3 += Number(mun1[mun1.length - 1]);
                    }
                }
                this.SMScount.smswordNum = w1 - w2 + w3;
            }
            //字数和短信条数的显示
            if (this.SMScount.smswordNum == 0) {
                this.SMScount.smssTrip = 0;
            } else if (parseInt(this.SMScount.smswordNum) <= 70 && parseInt(this.SMScount.smswordNum) > 0) {
                this.SMScount.smssTrip = 1;
            } else {
                this.SMScount.smssTrip = Math.ceil((parseInt(this.SMScount.smswordNum)) / 67)
            }
        },
        //短连接弹框是否关闭
        shortVisible(val) {
            if (val == false) {
                this.originalUrl = '';//长链接的值
                this.shortConUrl = '';//短连接的值
            }
        },
    },
}
</script>
<style scoped>
.el-textarea {
    width: 50%;
}

.shortChain-box {
    padding: 20px;
}

.sendMsg-box {
    padding: 20px;
}

.sendMsg-title {
    margin: 10px 0;
    padding-left: 5px;
    color: #16a589;
}

.sendMsg-table {
    margin-top: 12px;
}

.sendMsg-list-header {
    padding-top: 30px;
    font-weight: bold;
}

.el-steps--simple {
    background: #fff;
    border-radius: 0px;
    border-bottom: 1px solid #f3efef;
}

.send-mobel-box {
    width: 260px;
    overflow: hidden;
    position: relative;
    margin-bottom: 35px;
    left: 28%;
}

.send-mobel-box img {
    width: 255px;
}

.el-select {
    width: 50%;
}

.send-upload-tips {
    padding-left: 96px;
    padding-top: 5px;
    font-size: 12px;
}

.send-upload-tips span {
    display: inline-block;
    padding-left: 8px;
    color: #0066FF;
}

.sms-content-exhibition {
    width: 170px;
    height: 32%;
    border-radius: 10px;
    position: absolute;
    padding: 8px 9px;
    background: #e2e2e2;
    top: 146px;
    left: 31px;
    font-size: 12px;
    line-height: 18px;
    color: #000;
    overflow: hidden;
}

/* .sms-first-steps-btns{
    width:517px;
    position: absolute;
    text-align: center;
    bottom: 10px;
} */
.sms-seconnd-steps-box {
    padding: 30px 20px 30px 55px;
}

.sms-seconnd-steps-btns {
    text-align: right;
}

.goTiming {
    padding-right: 14px;
    cursor: pointer;
    color: #16a589;
}

.goTiming:hover {
    color: #03886e;
}

.shortChain-box {
    padding: 20px;
}

.shortChain-matter {
    border: 1px solid #66CCFF;
    padding: 10px 14px;
}

.short-title {
    font-weight: bolder;
    padding-bottom: 5px;
}

.font-sizes {
    padding-top: 2px;
    font-size: 12px;
    color: rgb(163, 163, 163);
}

.font-sizes1 {
    margin-top: 10px;
}</style>
<style>.sendMsg-box .el-dialog__body {
    padding: 10px 20px;
}

.sendMsg-box .el-form-item__label {
    text-align: left;
}

.sendMsg-box .el-step__head.is-process {
    color: #989898;
}

.sendMsg-box .el-step__title.is-process {
    color: #989898;
}

.sms-content-exhibition .el-scrollbar__wrap {
    overflow-x: hidden;
    /* overflow-y: scroll !important;  */
    margin-right: -27px !important;
}

.el-picker-panel .el-button--text {
    display: none;
}

.textareas textarea {
    height: 100px;
    resize: none;

}

.textareas textarea::-webkit-scrollbar {
    display: none;

}

.Edit .box-textareas {
    width: 380px;
    height: 125px;
    border-radius: 5px;
    border: 1px solid rgb(162, 219, 208);
    background: #f5f7fa;
}

.box-textareas textarea {
    border: none
}
</style>