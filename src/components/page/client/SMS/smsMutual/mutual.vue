<template>
  <div class="simple-mutual-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button
                  type="primary"
                  @click="createUprule"
                  class="action-btn primary"
                  icon="el-icon-plus"
                >
                  创建自动回复规则
                </el-button>
                <el-button
                  @click="refreshList"
                  class="action-btn"
                  icon="el-icon-refresh"
                >
                  刷新列表
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">共 {{ tableDataObj.tablecurrent.total }} 条记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-text">当前第 {{ formInline.currentPage }} 页</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <el-form :model="formInline" :inline="true" ref="formInline" class="advanced-search-form">
                <div class="search-row">
                  <el-form-item label="回复名称" prop="ruleName" class="search-item">
                    <el-input
                      v-model="formInline.ruleName"
                      placeholder="请输入自动回复名称"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="关键字" prop="keywords" class="search-item">
                    <el-input
                      v-model="formInline.keywords"
                      placeholder="请输入关键字"
                      class="search-input"
                      clearable
                    />
                  </el-form-item>

                  <el-form-item label="匹配模式" prop="matchPattern" class="search-item">
                    <el-select
                      v-model="formInline.matchPattern"
                      placeholder="全部模式"
                      class="search-select"
                      clearable
                    >
                      <el-option label="全文匹配" value="1"></el-option>
                      <el-option label="关键字匹配" value="2"></el-option>
                    </el-select>
                  </el-form-item>

                  <el-form-item class="search-buttons">
                    <el-button type="primary" @click="ListSearch" class="search-btn primary" icon="el-icon-search">
                      查询
                    </el-button>
                    <el-button @click="Reset('formInline')" class="search-btn" icon="el-icon-refresh">
                      重置
                    </el-button>
                  </el-form-item>
                </div>
              </el-form>
            </div>
          </div>
        </div>
        <!-- 自动回复规则列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">自动回复规则列表</h3>
          </div>

          <div class="table-container">
            <el-table
              v-loading="tableDataObj.loading2"
              element-loading-text="正在加载自动回复规则..."
              element-loading-spinner="el-icon-loading"
              element-loading-background="rgba(255, 255, 255, 0.9)"
              ref="multipleTable"
              border
              :data="tableDataObj.tableData"
              class="enhanced-table"
              stripe
              :header-cell-style="{ background: '#fafafa', color: '#333' }"
              :row-style="{ height: '60px' }"
              empty-text="暂无自动回复规则数据"
            >
              <!-- 自动回复名称 -->
              <el-table-column label="自动回复名称" min-width="200">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <span class="rule-name">{{ scope.row.ruleName }}</span>
                  </div>
                </template>
              </el-table-column>

              <!-- 关键字 -->
              <el-table-column label="关键字" min-width="180">
                <template slot-scope="scope">
                  <div class="content-cell">
                    <el-tooltip :content="scope.row.keywords" placement="top">
                      <span class="keywords-text">{{ scope.row.keywords }}</span>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>

              <!-- 匹配模式 -->
              <el-table-column label="匹配模式" width="120" align="center">
                <template slot-scope="scope">
                  <el-tag
                    :type="getMatchPatternTagType(scope.row.matchPattern)"
                    size="small"
                  >
                    {{ getMatchPatternText(scope.row.matchPattern) }}
                  </el-tag>
                </template>
              </el-table-column>

              <!-- 模板ID -->
              <el-table-column label="模板ID" width="100" align="center">
                <template slot-scope="scope">
                  <span class="template-id">{{ scope.row.tempId }}</span>
                </template>
              </el-table-column>

              <!-- 生效时间 -->
              <el-table-column label="生效时间" width="170" align="center">
                <template slot-scope="scope">
                  <span v-if="scope.row.beginTime">
                    {{ moment(scope.row.beginTime).format('YYYY-MM-DD HH:mm:ss') }}
                  </span>
                  <span v-else class="no-data">--</span>
                </template>
              </el-table-column>

              <!-- 结束时间 -->
              <el-table-column label="结束时间" width="170" align="center">
                <template slot-scope="scope">
                  <span v-if="scope.row.endTime">
                    {{ moment(scope.row.endTime).format('YYYY-MM-DD HH:mm:ss') }}
                  </span>
                  <span v-else class="no-data">--</span>
                </template>
              </el-table-column>

              <!-- 操作列 -->
              <el-table-column label="操作" width="150" fixed="right">
                <template slot-scope="scope">
                  <div class="table-actions">
                    <el-tooltip content="编辑规则" placement="top">
                      <el-button
                        type="text"
                        @click="edit(scope.row)"
                        class="action-btn-small edit"
                        icon="el-icon-edit"
                      >
                        编辑
                      </el-button>
                    </el-tooltip>

                    <el-tooltip content="删除规则" placement="top">
                      <el-button
                        type="text"
                        @click="cancel(scope.row)"
                        class="action-btn-small delete"
                        icon="el-icon-delete"
                      >
                        删除
                      </el-button>
                    </el-tooltip>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>

          <!-- 简约分页 -->
          <div class="pagination-section">
            <el-pagination
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInline.currentPage"
              :page-size="formInline.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
              class="simple-pagination"
            />
          </div>
        </div>
      </div>
    </div>
        <el-dialog
            :title="title"
            :visible.sync="dialogVisible"
            style="padding: 0 28px 0 20px"
            width="520px"
            :before-close="handleClose">

                <el-form :inline="true" style="width:100%;padding: 0 28px 0 20px" :rules="rules" ref="formInlines" :model="formInlines">
                        <el-form-item label="自动回复名称" label-width="110px"  prop="ruleName">
                            <el-input style="width: 300px" v-model="formInlines.ruleName" placeholder="上行交互名称"></el-input>
                        </el-form-item>
                        <el-form-item label="关键字列表"  label-width="110px" prop="keywords">
                            <el-input style="width: 300px;" type="textarea" v-model="formInlines.keywords" placeholder="多个关键字以换行分割字母不区分大小写"></el-input>
                        </el-form-item>
                        <el-form-item label="匹配模式"  label-width="110px" prop="matchPattern">
                            <el-radio-group v-model="formInlines.matchPattern">
                                <el-radio label="1">全部匹配</el-radio>
                                <el-radio label="2">关键字包含</el-radio>
                            </el-radio-group>
                            <!-- <el-input v-model="formInlines.matchPattern" placeholder="关键字列表"></el-input> -->
                        </el-form-item>
                        <el-form-item label="生效时间" label-width="110px"  prop="value1">
                             <el-date-picker
                                style="width: 300px"
                                v-model="formInlines.value1"
                                type="datetimerange"
                                range-separator="至"
                                @change="time"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="短信模板" label-width="110px"  prop="tempId">
                            <el-select  v-model="formInlines.tempId" placeholder="请选择">
                                <el-option
                                v-for="item in options"
                                :key="item.temId"
                                :label="item.temName"
                                :value="item.temId">
                                </el-option>
                            </el-select>
                            <!-- <el-input v-model="formInlines.tempId" placeholder="短信模板ID"></el-input> -->
                        </el-form-item>
                        
                </el-form>
            
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitForm(formInlines)">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import moment from 'moment'
export default {
    name: "smsMutual",
    data(){
        return{
            title:"",
            addT:"创建自动回复规则",
            editT:"编辑创建自动回复规则",
            id:'',
            dialogVisible:false,
            options:[],
            
            formInline:{
                pageSize:10,
                currentPage:1,
                ruleName:"",
                matchPattern:"",
                keywords:"",
                tempId:"",
                beginTime:"",
                endTime:"",
                value1:[],
            },
            formInlines:{
                ruleName:"",
                matchPattern:"2",
                keywords:"",
                tempId:"",
                beginTime:"",
                endTime:"",
                value1:[],
            },
            tableDataObj:{
                loading2:false,
                tableData:[],
                tablecurrent:{ //分页参数
                    total:0,
                },
            },
            rules:{
                ruleName:[
                    { required: true, message: "请输入交互名称", trigger: "change" }
                ],
                matchPattern:[
                    { required: true, message: "请选择匹配模式", trigger: "change" }
                ],
                keywords:[
                    { required: true, message: "请输入关键字", trigger: "change" }
                ],
                tempId:[
                    { required: true, message: "请选择模板ID", trigger: "change" }
                ],
                value1:[
                    { required: true, message: "请设置生效时间", trigger: "change" }
                ]
            }
        }
    },
    created(){
        this.InquireList()
        this.temp()
    },
    methods:{
        temp(){
            this.$api.post(
                this.API.cpus + "v3/consumersmstemplate/selectClientTemplate",
                {
                    currentPage:1,
                    pageSize:100
                },
                (res) => {
                    // console.log(res);
                    this.options = res.records
                // this.tableDataObj.tableData = res.records;
                // this.tableDataObj.loading2 = false;
                // this.pageTotal = res.total;
                // this.tabelAlllist.currentPage = res.current;
                // this.tabelAlllist.pageSize = res.size;
                //                 tabelAlllist:{//------发送表格请求的对象
                //     param:'',
                //     currentPage:1,//当前页
                //     pageSize:10,//每一页条数
                // },
                // pageTotal:0,//总共条数
                // for(var i = 0 ; i<res.records.length ; i++){
                //     if(res.records[i].temFormat==2){
                //         this.tableDataObj.tableData[i].temContent = res.records[i].initialContent
                //     }
                // }
                }
            );
        },
        InquireList(){
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.cpus + 'consumersmsreplyrule/page',this.formInline,res=>{
                this.tableDataObj.loading2=false;
                this.tableDataObj.tableData=res.data.records
                this.tableDataObj.tablecurrent.total=res.data.total
            })
        },
        ListSearch(){
            this.InquireList()
        },
        Reset(){
                this.formInline.ruleName=''
                this.formInline.matchPattern='',
                this.formInline.keywords=''
                this.formInline.tempId=''
                this.formInline.pageSize = 10,
                this.formInline.currentPage = 1
                this.InquireList()
        },
        createUprule(){
            this.dialogVisible = true
            this.title=this.addT
        },
        handleSizeChange(size){
            this.formInline.pageSize = size
            this.InquireList()
        },
        handleCurrentChange(currentPage){
            this.formInline.currentPage = currentPage
            this.InquireList()
        },

        // 刷新列表
        refreshList() {
            this.InquireList();
        },

        // 获取匹配模式标签类型
        getMatchPatternTagType(matchPattern) {
            const typeMap = {
                '1': 'primary',   // 全文匹配
                '2': 'success'    // 关键字匹配
            };
            return typeMap[matchPattern] || 'info';
        },

        // 获取匹配模式文本
        getMatchPatternText(matchPattern) {
            const textMap = {
                '1': '全文匹配',
                '2': '关键字匹配'
            };
            return textMap[matchPattern] || '未知模式';
        },
        time(val){
            // console.log(val);
            this.formInlines.beginTime = moment(val[0]).format('YYYY-MM-DD HH:MM:ss')
            this.formInlines.endTime = moment(val[1]).format('YYYY-MM-DD HH:MM:ss')
        },
        submitForm(){
            this.$refs.formInlines.validate(valid=>{
                if(valid){
                    if(this.title ==this.addT){
                        this.$api.post(this.API.cpus + 'consumersmsreplyrule',this.formInlines,res=>{
                            if(res.code == 200){
                                this.$message({
                                    message: res.msg,
                                    type: 'success',
                                    duration:'2000'
                                });
                                this.InquireList()
                                this.dialogVisible = false
                            }else{
                                this.$message({
                                    message: res.msg,
                                    type: 'error',
                                    duration:'2000'
                                });
                            }
                            
                        })
                    }else{
                        this.formInlines.id = this.id
                        this.$api.put(this.API.cpus + 'consumersmsreplyrule',this.formInlines,res=>{
                            if(res.code == 200){
                                this.$message({
                                    message: res.msg,
                                    type: 'success',
                                    duration:'2000'
                                });
                                this.InquireList()
                                this.dialogVisible = false
                            }else{
                                this.$message({
                                    message: res.msg,
                                    type: 'error',
                                    duration:'2000'
                                });
                            }
                        })
                    }
                }
            })
        },
        handleClose(){
            this.dialogVisible= false
        },
        edit(val){
            // console.log(val);
            this.id =val.id
            this.title = this.editT
            this.dialogVisible= true
            this.formInlines.ruleName = val.ruleName
            this.formInlines.keywords = val.keywords
            this.formInlines.matchPattern = val.matchPattern+''
            this.formInlines.tempId = val.tempId
            if(val.beginTime){
                this.formInlines.beginTime = moment(val.beginTime).format('YYYY-MM-DD HH:MM:ss')
                // this.formInlines.value1[0] = this.formInlines.beginTime 
            }else{
                this.formInlines.beginTime=""
                // this.formInlines.value1=[]
            }
            if(val.endTime){
                this.formInlines.endTime = moment(val.endTime).format('YYYY-MM-DD HH:MM:ss')
                // this.formInlines.value1[1] = this.formInlines.endTime
            }else{
                this.formInlines.endTime=""
                // this.formInlines.value1=[]
            }
            if(this.formInlines.beginTime=='' && this.formInlines.endTime==''){
                this.formInlines.value1 = []
            }else{
                this.formInlines.value1 = [this.formInlines.beginTime,this.formInlines.endTime]
            }
            
            // console.log(this.formInlines.value1,'ll');
            
            // this.formInlines.value1 = [this.formInlines.beginTime,this.formInlines.endTime ]
        },
        cancel(val){
            console.log(val);
            this.$confirms.confirmation('delete','是否确认删除此规则？',this.API.cpus+'consumersmsreplyrule/'+val.id,{},res =>{
                this.InquireList()   
            });
        },
    },
    watch:{
        dialogVisible(val){
            if(val==false){
                this.$refs.formInlines.resetFields();
                this.formInlines.ruleName=''
                this.formInlines.matchPattern='2',
                this.formInlines.keywords=''
                this.formInlines.tempId=''
                this.formInlines.id = ''
                this.formInlines.beginTime = ''
                this.formInlines.endTime = ''
                this.formInlines.value1 = []
            }
        }
    }
    
}
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* mutual 特有样式 */
.simple-mutual-page {
  min-height: 100vh;
  background: #fafafa;
  padding: 0;
}

/* 搜索表单特殊样式 */
.search-row {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  align-items: center;
}

.search-item {
  margin-bottom: 0;

  /deep/ .el-form-item__label {
    color: #333;
    font-weight: 500;
    font-size: 14px;
    min-width: 80px;
  }
}

/* 表格内容特殊样式 */
.rule-name {
  font-weight: 500;
  color: #333;
}

.keywords-text {
  color: #666;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 150px;
  display: block;
}

.template-id {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: #666;
  font-weight: 500;
}

.no-data {
  color: #c0c4cc;
  font-style: italic;
}

/* 响应式优化 */
@media (max-width: 1200px) {
  .search-row {
    flex-direction: column;
    align-items: stretch;
  }

  .search-item {
    width: 100%;
  }

  .search-buttons {
    margin-left: 0;

    .search-btn {
      flex: 1;
    }
  }
}
</style>

<style>
.el-textarea__inner{
        height: 100px;
}
</style>