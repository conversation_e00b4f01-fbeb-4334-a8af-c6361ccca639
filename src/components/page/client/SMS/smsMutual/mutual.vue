<template>
    <div style="background:#fff">
        <div class="crumbs" style="padding：10px;">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i>
                短信自动回复</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="fillet Statistics-box">
            <div class="OuterFrame fillet" style="height: 100%;padding:20px">
                <div>
                    <el-form :inline="true" ref="formInline" :model="formInline" class="demo-form-inline">
                        <el-form-item label="自动回复名称"   prop="sendType">
                            <el-input v-model="formInline.ruleName" placeholder=""></el-input>
                        </el-form-item>
                        <el-form-item label="关键字"   prop="sendType">
                            <el-input v-model="formInline.keywords" placeholder=""></el-input>
                        </el-form-item>
                        
                        <!-- <el-form-item label="短信模板"   prop="sendType">
                            <el-select v-model="formInline.tempId" placeholder="请选择">
                                <el-option
                                v-for="item in options"
                                :key="item.temId"
                                :label="item.temName"
                                :value="item.temId">
                                </el-option>
                            </el-select>
                        </el-form-item> -->
                        <el-form-item label="匹配模式"   prop="sendType">
                            <el-select v-model="formInline.matchPattern" placeholder="请选择">
                                <el-option
                                label="全文匹配"
                                value="1">
                                </el-option>
                                <el-option
                                label="关键字匹配"
                                value="2">
                                </el-option>
                            </el-select>
                            <!-- <el-input v-model="formInline.matchPattern" placeholder=""></el-input> -->
                        </el-form-item>
                        
                    </el-form>
                </div>
                <div class="boderbottom">
                    <el-button type="primary" plain style="" @click="ListSearch">查询</el-button>
                    <el-button type="primary" plain style="" @click="Reset('formInline')">重置</el-button>
                </div>

                <div class="sensitive-fun" style="margin-top:10px">
                    <!-- <span class="sensitive-list-header">发送任务列表</span><span style="font-size: 12px;font-weight: 500;">（可刷新页面查看最新发送进度）</span> -->
                    <el-button type="primary" style="margin-right:10px;" @click="createUprule" >创建自动回复规则</el-button>
                </div>
                <div class="Mail-table"  style="padding-bottom:40px;">
                    <el-table
                        v-loading="tableDataObj.loading2 "
                        element-loading-text="拼命加载中"
                        element-loading-spinner="el-icon-loading"
                        element-loading-background="rgba(0, 0, 0, 0.6)"
                        ref="multipleTable"
                        border
                        :data="tableDataObj.tableData"
                        style="width: 100%"                    
                        >
                        <!-- <el-table-column width="70" label="用户名">
                            <template slot-scope="scope">{{ scope.row.userName}}</template>
                        </el-table-column> -->
                        <el-table-column  label="自动回复名称">
                            <template slot-scope="scope" >
                                <span >{{ scope.row.ruleName }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column  label="关键字">
                            <template slot-scope="scope" >
                                <span >{{ scope.row.keywords }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="匹配模式">
                            <template slot-scope="scope">
                                <span v-if="scope.row.matchPattern	==1">全文匹配</span>
                                <span v-else-if="scope.row.matchPattern	==2">关键字匹配</span>
                            </template>
                        </el-table-column>
                        <el-table-column  label="模板ID">
                            <template slot-scope="scope" >
                                <span >{{ scope.row.tempId }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="生效时间">
                            <template slot-scope="scope">
                                <span v-if="scope.row.beginTime" >{{ moment(scope.row.beginTime).format('YYYY-MM-DD HH:MM:ss')}}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="结束时间">
                            <template slot-scope="scope">
                                <span v-if="scope.row.endTime" >{{ moment(scope.row.endTime).format('YYYY-MM-DD HH:MM:ss')}}</span>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column label="创建时间">
                            <template slot-scope="scope">
                                <span >{{ moment(scope.row.createTime).format('YYYY-MM-DD HH:MM:ss')}}</span>
                            </template>
                        </el-table-column> -->
                        <el-table-column label="操作" width='120'>
                            <template slot-scope="scope">
                                <el-button type="text" style="color:#16A589;margin-left:0px;" @click="edit(scope.row)"><i class="el-icon-edit"></i> 编辑</el-button>
                                <el-button type="text" style="color:red;margin-left:0px;" @click="cancel(scope.row)"><i class="el-icon-delete"></i> 删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="formInline.currentPage" :page-size="formInline.pageSize" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tablecurrent.total">
                        </el-pagination>
                    </el-col>
                </div>
            </div>
        </div>
        <el-dialog
            :title="title"
            :visible.sync="dialogVisible"
            style="padding: 0 28px 0 20px"
            width="520px"
            :before-close="handleClose">

                <el-form :inline="true" style="width:100%;padding: 0 28px 0 20px" :rules="rules" ref="formInlines" :model="formInlines">
                        <el-form-item label="自动回复名称" label-width="110px"  prop="ruleName">
                            <el-input style="width: 300px" v-model="formInlines.ruleName" placeholder="上行交互名称"></el-input>
                        </el-form-item>
                        <el-form-item label="关键字列表"  label-width="110px" prop="keywords">
                            <el-input style="width: 300px;" type="textarea" v-model="formInlines.keywords" placeholder="多个关键字以换行分割字母不区分大小写"></el-input>
                        </el-form-item>
                        <el-form-item label="匹配模式"  label-width="110px" prop="matchPattern">
                            <el-radio-group v-model="formInlines.matchPattern">
                                <el-radio label="1">全部匹配</el-radio>
                                <el-radio label="2">关键字包含</el-radio>
                            </el-radio-group>
                            <!-- <el-input v-model="formInlines.matchPattern" placeholder="关键字列表"></el-input> -->
                        </el-form-item>
                        <el-form-item label="生效时间" label-width="110px"  prop="value1">
                             <el-date-picker
                                style="width: 300px"
                                v-model="formInlines.value1"
                                type="datetimerange"
                                range-separator="至"
                                @change="time"
                                start-placeholder="开始时间"
                                end-placeholder="结束时间">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item label="短信模板" label-width="110px"  prop="tempId">
                            <el-select  v-model="formInlines.tempId" placeholder="请选择">
                                <el-option
                                v-for="item in options"
                                :key="item.temId"
                                :label="item.temName"
                                :value="item.temId">
                                </el-option>
                            </el-select>
                            <!-- <el-input v-model="formInlines.tempId" placeholder="短信模板ID"></el-input> -->
                        </el-form-item>
                        
                </el-form>
            
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="submitForm(formInlines)">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import moment from 'moment'
export default {
    name: "smsMutual",
    data(){
        return{
            title:"",
            addT:"创建自动回复规则",
            editT:"编辑创建自动回复规则",
            id:'',
            dialogVisible:false,
            options:[],
            
            formInline:{
                pageSize:10,
                currentPage:1,
                ruleName:"",
                matchPattern:"",
                keywords:"",
                tempId:"",
                beginTime:"",
                endTime:"",
                value1:[],
            },
            formInlines:{
                ruleName:"",
                matchPattern:"2",
                keywords:"",
                tempId:"",
                beginTime:"",
                endTime:"",
                value1:[],
            },
            tableDataObj:{
                loading2:false,
                tableData:[],
                tablecurrent:{ //分页参数
                    total:0,
                },
            },
            rules:{
                ruleName:[
                    { required: true, message: "请输入交互名称", trigger: "change" }
                ],
                matchPattern:[
                    { required: true, message: "请选择匹配模式", trigger: "change" }
                ],
                keywords:[
                    { required: true, message: "请输入关键字", trigger: "change" }
                ],
                tempId:[
                    { required: true, message: "请选择模板ID", trigger: "change" }
                ],
                value1:[
                    { required: true, message: "请设置生效时间", trigger: "change" }
                ]
            }
        }
    },
    created(){
        this.InquireList()
        this.temp()
    },
    methods:{
        temp(){
            this.$api.post(
                this.API.cpus + "v3/consumersmstemplate/selectClientTemplate",
                {
                    currentPage:1,
                    pageSize:100
                },
                (res) => {
                    // console.log(res);
                    this.options = res.records
                // this.tableDataObj.tableData = res.records;
                // this.tableDataObj.loading2 = false;
                // this.pageTotal = res.total;
                // this.tabelAlllist.currentPage = res.current;
                // this.tabelAlllist.pageSize = res.size;
                //                 tabelAlllist:{//------发送表格请求的对象
                //     param:'',
                //     currentPage:1,//当前页
                //     pageSize:10,//每一页条数
                // },
                // pageTotal:0,//总共条数
                // for(var i = 0 ; i<res.records.length ; i++){
                //     if(res.records[i].temFormat==2){
                //         this.tableDataObj.tableData[i].temContent = res.records[i].initialContent
                //     }
                // }
                }
            );
        },
        InquireList(){
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.cpus + 'consumersmsreplyrule/page',this.formInline,res=>{
                this.tableDataObj.loading2=false;
                this.tableDataObj.tableData=res.data.records
                this.tableDataObj.tablecurrent.total=res.data.total
            })
        },
        ListSearch(){
            this.InquireList()
        },
        Reset(){
                this.formInline.ruleName=''
                this.formInline.matchPattern='',
                this.formInline.keywords=''
                this.formInline.tempId=''
                this.formInline.pageSize = 10,
                this.formInline.currentPage = 1
                this.InquireList()
        },
        createUprule(){
            this.dialogVisible = true
            this.title=this.addT
        },
        handleSizeChange(size){
            this.formInline.pageSize = size
            this.InquireList()
        },
        handleCurrentChange(currentPage){
            this.formInline.currentPage = currentPage
            this.InquireList()
        },
        time(val){
            // console.log(val);
            this.formInlines.beginTime = moment(val[0]).format('YYYY-MM-DD HH:MM:ss')
            this.formInlines.endTime = moment(val[1]).format('YYYY-MM-DD HH:MM:ss')
        },
        submitForm(){
            this.$refs.formInlines.validate(valid=>{
                if(valid){
                    if(this.title ==this.addT){
                        this.$api.post(this.API.cpus + 'consumersmsreplyrule',this.formInlines,res=>{
                            if(res.code == 200){
                                this.$message({
                                    message: res.msg,
                                    type: 'success',
                                    duration:'2000'
                                });
                                this.InquireList()
                                this.dialogVisible = false
                            }else{
                                this.$message({
                                    message: res.msg,
                                    type: 'error',
                                    duration:'2000'
                                });
                            }
                            
                        })
                    }else{
                        this.formInlines.id = this.id
                        this.$api.put(this.API.cpus + 'consumersmsreplyrule',this.formInlines,res=>{
                            if(res.code == 200){
                                this.$message({
                                    message: res.msg,
                                    type: 'success',
                                    duration:'2000'
                                });
                                this.InquireList()
                                this.dialogVisible = false
                            }else{
                                this.$message({
                                    message: res.msg,
                                    type: 'error',
                                    duration:'2000'
                                });
                            }
                        })
                    }
                }
            })
        },
        handleClose(){
            this.dialogVisible= false
        },
        edit(val){
            // console.log(val);
            this.id =val.id
            this.title = this.editT
            this.dialogVisible= true
            this.formInlines.ruleName = val.ruleName
            this.formInlines.keywords = val.keywords
            this.formInlines.matchPattern = val.matchPattern+''
            this.formInlines.tempId = val.tempId
            if(val.beginTime){
                this.formInlines.beginTime = moment(val.beginTime).format('YYYY-MM-DD HH:MM:ss')
                // this.formInlines.value1[0] = this.formInlines.beginTime 
            }else{
                this.formInlines.beginTime=""
                // this.formInlines.value1=[]
            }
            if(val.endTime){
                this.formInlines.endTime = moment(val.endTime).format('YYYY-MM-DD HH:MM:ss')
                // this.formInlines.value1[1] = this.formInlines.endTime
            }else{
                this.formInlines.endTime=""
                // this.formInlines.value1=[]
            }
            if(this.formInlines.beginTime=='' && this.formInlines.endTime==''){
                this.formInlines.value1 = []
            }else{
                this.formInlines.value1 = [this.formInlines.beginTime,this.formInlines.endTime]
            }
            
            // console.log(this.formInlines.value1,'ll');
            
            // this.formInlines.value1 = [this.formInlines.beginTime,this.formInlines.endTime ]
        },
        cancel(val){
            console.log(val);
            this.$confirms.confirmation('delete','是否确认删除此规则？',this.API.cpus+'consumersmsreplyrule/'+val.id,{},res =>{
                this.InquireList()   
            });
        },
    },
    watch:{
        dialogVisible(val){
            if(val==false){
                this.$refs.formInlines.resetFields();
                this.formInlines.ruleName=''
                this.formInlines.matchPattern='2',
                this.formInlines.keywords=''
                this.formInlines.tempId=''
                this.formInlines.id = ''
                this.formInlines.beginTime = ''
                this.formInlines.endTime = ''
                this.formInlines.value1 = []
            }
        }
    }
    
}
</script>

<style>
.el-textarea__inner{
        height: 100px;
}
</style>