<template>
    <div id="SmsRecord" class="bag">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 回复记录</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="fillet  ShortMessageRecording-chart-box">
            <div class=" ShortMessageRecording-chart-title">
                <slot name="sele"></slot>
                <el-radio-group v-model="specificTime" size="small" text-color="#fff" fill="#16a589"
                    @change="handleChangeTimeOptions">
                    <el-radio-button label="1">今天</el-radio-button>
                    <el-radio-button label="2" class="threeDay">近4天</el-radio-button>
                </el-radio-group>
                <el-date-picker v-model="time1" value-format="yyyy-MM-dd" range-separator="-" :clearable='false'
                    :picker-options="pickerOptions" @change="getTimeOperating" type="daterange" start-placeholder="开始日期"
                    end-placeholder="结束日期">
                </el-date-picker>
                <!-- <date-plugin class="search-date" :datePluginValueList="datePluginValueList"  @handledatepluginVal="handledatepluginVal"></date-plugin> -->
            </div>
            <div class="short-message-recording-type">
                <!-- <el-tabs v-model="activeName2" type="card" @tab-click="handleClick">
                   
                    <el-tab-pane label="回复记录" name="second"> -->
                <el-form ref="SHformList1" :model="form1" label-width="86px" class="query-frame">
                    <el-form-item label="手机号" prop="mobile">
                        <el-input v-model="form1.mobile" placeholder="请输入手机号" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="消息ID" prop="msgid">
                        <el-input v-model="form1.msgid" placeholder="请输入消息ID" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="签名" prop="signature">
                        <el-input v-model="form1.signature" placeholder="请输入签名" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="回复内容" prop="content">
                        <el-input v-model="form1.content" placeholder="请输入内容" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item label="发送内容" prop="smsContent">
                        <el-input v-model="form1.smsContent" placeholder="请输入内容" class="input-w"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" plain @click="queryReply()">查 询</el-button>
                        <el-button type="primary" plain @click="resetReply('SHformList1')">重 置</el-button>
                        <el-button type="primary" @click="exportNums1()">导出数据</el-button>
                        <!-- <el-button type="primary" @click="batchAdd()">批量加入黑名单</el-button> -->
                    </el-form-item>
                </el-form>
                <div style="padding-bottom:40px;">
                    <!-- <table-tem :tableDataObj="tableDataObj1" @handelOptionButton="handelOptionButton"  @handelSelection="handelSelection"></table-tem> -->
                    <el-table v-loading="tableDataObj1.loading2" element-loading-text="拼命加载中"
                        element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)"
                        ref="multipleTable" border :stripe="true" :data="tableDataObj1.tableData" style="width: 100%"
                        @selection-change="handelSelection">
                        <el-table-column type="selection" width="46"></el-table-column>
                        <!-- <el-table-column label="签名ID" width="80">
                        <template slot-scope="scope">{{ scope.row.signatureId }}</template>
                    </el-table-column> -->
                        <!-- <el-table-column label="用户名称" width='120'>
                        <template slot-scope="scope">
                            <span>{{ scope.row.userName }}</span>
                        </template>
                    </el-table-column> -->
                        <el-table-column label="手机号码" width='130'>
                            <template slot-scope="scope">
                                <!-- <div class="spanColor" @click="trueFlogShow(scope.row.content)" v-html="scope.row.content"></div> -->
                                <!-- <div class="spanColor" v-html="scope.row.mobile"></div> -->
                                <div style="color: #16a589; cursor: pointer" @click="handPhone(scope.row, scope.$index)" >{{scope.row.mobile}}</div>
                            </template>
                        </el-table-column>
                        <el-table-column label="回复内容" width="150">
                            <template slot-scope="scope">
                                <span>{{ scope.row.content }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="回复时间" width="180">
                            <template slot-scope="scope">
                                <span>{{ scope.row.createTime }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="发送内容">
                            <template slot-scope="scope">
                                <!-- <el-tooltip class="item" effect="dark" placement="top-start">
                                <div class="tooltip" slot="content">{{scope.row.smsContent}}</div>
                                <span class="span">{{scope.row.smsContent }}</span>
                            </el-tooltip> -->
                                <Tooltip v-if="scope.row.smsContent" :content="scope.row.smsContent"
                                    className="wrapper-text" effect="light">
                                </Tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column label="消息ID" width='220'>
                            <template slot-scope="scope">
                                <!-- <el-tooltip class="item" effect="dark" placement="top-start">
                                <div class="tooltip" slot="content">{{scope.row.msgid}}</div>
                                <span class="span">{{scope.row.msgid }}</span>
                            </el-tooltip> -->
                                <span class="span">{{ scope.row.msgid }}</span>
                                <i style="color:#409eff;cursor: pointer;margin: 0 5px;font-size: 14px;"
                                    class="el-icon-document-copy" @click="handleCopy(scope.row.msgid, $event)"></i>
                            </template>
                        </el-table-column>
                        <!-- <el-table-column label="扩展码" width="140">
                        <template slot-scope="scope" >
                            <span>{{scope.row.ext}}</span>
                        </template>
                    </el-table-column> -->
                        <!-- <el-table-column label="操作" width='180' fixed="right">
                            <template slot-scope="scope">
                            <el-button v-if="scope.row.isBlacknumber ==1" type="text" style="margin-left:0px;" @click="detailsRow(scope.$index, scope.row)"><i></i>&nbsp;</el-button>
                            <el-button v-else type="text" style="margin-left:0px;" @click="detailsRow(scope.$index, scope.row)"><i class="el-icon-success"></i>&nbsp;加入黑名单</el-button>
                        </template>
                    </el-table-column> -->
                    </el-table>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
                        style="background:#fff;padding:10px 0;text-align:right;">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange1"
                            @current-change="handleCurrentChange1" :current-page="1" :page-size="formData1.pageSize"
                            :page-sizes="[10, 20, 50, 100, 500]" layout="total, sizes, prev, pager, next, jumper"
                            :total="tableDataObj1.totalRow"></el-pagination>
                    </el-col>
                </div>
                <!-- </el-tab-pane>
                </el-tabs> -->
            </div>
        </div>
        <el-dialog title="批量加入黑名单" :visible.sync="AddBlacks" width="500px" top="30vh">
            <el-form :model="AddBlackform" ref="AddBlackformRef" label-width="100px" style="padding: 0px 25px 0px 0px;">
                <el-form-item label="备注" prop="remark">
                    <el-input type="textarea" maxlength="70" placeholder="请输入备注内容，不超过70个字"
                        v-model="AddBlackform.remark"></el-input>
                </el-form-item>
            </el-form>
            <div style="text-align: right;">
                <span slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="batchAddblack()">确定</el-button>
                    <el-button @click="AddBlacks = false">取 消</el-button>
                </span>
            </div>
        </el-dialog>
        <el-dialog title="导出数据" :visible.sync="exportShow" width="30%" :before-close="handleClose">
            <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
                <el-form-item label="导出类型" prop="decode">
                    <el-radio-group v-model="ruleForm.decode">
                        <el-radio label="0">掩码</el-radio>
                        <el-radio label="1">明码</el-radio>
                    </el-radio-group>
                </el-form-item>
                <div style="margin-left: 28px;color: #F56C6C;" v-if="ruleForm.decode == '1'">
                    tips: 明码文件将会发送您的{{ this.emailInfo.email }}邮箱，请注意查收。
                </div>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="exportShow = false">取 消</el-button>
                <el-button type="primary" @click="submitExport">{{ ruleForm.decode == '0' ? '下载' : '发送' }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>

import TableTem from '@/components/publicComponents/TableTem'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import clip from '../../../../utils/clipboard'
import Tooltip from "@/components/publicComponents/tooltip";
import common from "../../../../../../assets/js/common";
import moment from 'moment'
export default {
    name: "SmsRecord",
    components: {
        TableTem,
        DatePlugin,
        Tooltip
    },
    data() {
        return {
            name: "SmsRecord",
            //模板名称
            templateName: [],
            time1: [moment().startOf('day').format('YYYY-MM-DD'), moment().endOf('day').format('YYYY-MM-DD')],
            //复选框的值
            selectId: '',
            // 单条存值
            AddBlackVal: '',
            // 备注
            AddBlackform: {
                remark: ''
            },
            AddBlacks: false,
            AddBlacksStatus: false,

            //回复查询的值
            form1: {
                mobile: '',
                msgid: "",
                signature: "",
                content: "",
                smsContent:"",
                currentPage: 1,
                sendBeginTime: moment().startOf('day').format('YYYY-MM-DD'),
                sendEndTime: moment().endOf('day').format('YYYY-MM-DD'),
                pageSize: 10,
                isDownload: 2
            },
            //复制回复查询的值
            formData1: {
                mobile: '',
                msgid: "",
                signature: "",
                content: "",
                smsContent:"",
                currentPage: 1,
                sendBeginTime: moment().startOf('day').format('YYYY-MM-DD'),
                sendEndTime: moment().endOf('day').format('YYYY-MM-DD'),
                pageSize: 10,
                isDownload: 2
            },
            pickerOptions: {
                disabledDate(time) {
                    // return time.getTime() > Date.now();
                    return time.getTime() < Date.now() - 30 * 24 * 3600 * 1000 || time.getTime() > Date.now();
                },
                //  onPick: ({ maxDate, minDate }) => {
                //     this.pickerMinDate = minDate.getTime();
                //     if (maxDate) {
                //         this.pickerMinDate = ''
                //     }
                // },
                // disabledDate: (time) => {
                //     if(this.pickerMinDate&&this.pickerMinDate>Date.now()){
                //         return false
                //     }
                //     if (this.pickerMinDate !=='') {
                //         const day30 = 30 * 24 * 3600 * 1000
                //         let maxTime = this.pickerMinDate + day30
                //         if (maxTime >  Date.now() - 345600000) {
                //             maxTime =  Date.now() - 345600000
                //         }
                //         const minTime = this.pickerMinDate - day30
                //         return time.getTime() < minTime || time.getTime() > maxTime
                //     }
                //     return time.getTime() > Date.now() - 345600000;
                // }
            },
            specificTime: '1', //选择那一天
            datePluginValueList: { //日期选择器
                type: "daterange",
                start: "",
                end: '',
                range: '-',
                clearable: false,
                pickerOptions: {
                    onPick: ({ maxDate, minDate }) => {
                        this.pickerMinDate = minDate.getTime();
                        if (maxDate) {
                            this.pickerMinDate = ''
                        }
                    },
                    disabledDate: (time) => {
                        if (this.pickerMinDate && this.pickerMinDate > Date.now()) {
                            return false
                        }
                        if (this.pickerMinDate !== '') {
                            const day30 = 30 * 24 * 3600 * 1000
                            let maxTime = this.pickerMinDate + day30
                            if (maxTime > Date.now() - 345600000) {
                                maxTime = Date.now() - 345600000
                            }
                            const minTime = this.pickerMinDate - day30
                            return time.getTime() < minTime || time.getTime() > maxTime
                        }
                        return time.getTime() > Date.now() - 345600000;
                    }
                },
                datePluginValue: ''
            },
            tableDataObj: { //表格数据
                loading2: false,
                totalRow: 0,
                tableData: [],
                tableLabelExpand: [//折叠的列表表头
                    { prop: "content", showName: '短信内容:' }
                ],
                tableLabel: [  //列表表头
                    { prop: "mobile", showName: '手机号码', fixed: false },
                    // {prop:"wordCount",showName:'字数',width:'60',fixed:false},
                    { prop: "chargeNum", showName: '计费条数', width: '80', fixed: false },
                    // {prop:"smsStatus",showName:'短信类型',formatData: function(val) { return val == '2' ? '成功' : '失败' },fixed:false},
                    { prop: "sendTime", showName: '发送时间', fixed: false, width: '140' },
                    { prop: "reportTime", showName: '状态上报时间', fixed: false, width: '140' },

                ],
                tableStyle: {
                    isSelection: false,//是否复选框
                    isExpand: true,//是否是折叠的
                    isDefaultExpand: true, //是否默认打开
                    style: {//表格样式,表格宽度
                        width: "100%"
                    },
                    optionWidth: '180',//操作栏宽度
                    border: true,//是否边框
                    stripe: false,//是否有条纹
                }
            },
            tableDataObj1: { //表格数据
                loading2: false,
                totalRow: 0,
                tableData: [],
                tableLabel: [  //列表表头
                    { prop: "mobile", showName: '手机号码', width: '120', fixed: false },
                    { prop: "content", showName: '回复内容', width: '120', fixed: false },
                    { prop: "msgid", showName: 'msgid', width: '250', fixed: false },
                    { prop: "smsContent", showName: '发送内容', fixed: false },
                    { prop: "createTime", showName: '回复时间', width: '180', fixed: false },
                    // {prop:"ext",showName:'扩展码',width:'150',fixed:false}
                ],
                tableStyle: {
                    isSelection: true,//是否复选框
                    isExpand: false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width: "100%"
                    },
                    optionWidth: '120',//操作栏宽度
                    border: true,//是否边框
                    stripe: false,//是否有条纹
                },
                // conditionOption:[
                //     {
                //         contactCondition:'isBlacknumber',//关联的表格属性
                //         contactData:'2',//关联的表格属性-值
                //         optionName:'加入黑名单',//按钮的显示文字
                //         optionMethod:'Addblack',//按钮的方法
                //         icon:'el-icon-edit',//按钮图标
                //         optionButtonColor:'',//按钮颜色
                //     }
                // ]
            },
            exportShow: false, //是否显示导出按钮
            ruleForm: {
                decode: "0",
            },
            rules: {

            },
            emailInfo: {
                email: "",
                username: ""
            }
        }
    },
    created() {
        //判断是否开启数据展示
        if (this.$store.state.isDateState == 1) {
            this.tableDataObj.tableLabel.push({
                prop: "smsStatus", showName: '发送状态', formatData: function (val) {
                    if (val == '1') {
                        return val = '成功'
                    } else if (val == '2') {
                        return val = '失败'
                    } else {
                        return val = '待返回'
                    }
                }, width: '100', fixed: false
            },
                { prop: "originalCode", showName: '备注', fixed: false });
        }
    },
    methods: {
        //获取已通过的模板名称
        getTemplateName() {
            this.$api.post(this.API.cpus + 'v3/consumersmstemplate/selectClientTemplate', {
                currentPage: 1,
                pageSize: 200
            }, res => {
                this.templateName = res.records;
            })
        },
        //发送请求
        sendReport1(flag) {
            let data = Object.assign({}, this.formData1);
            data.flag = flag;
            this.$api.post(this.API.cpus + 'statistics/replyPage', data, res => {
                this.tableDataObj1.tableData = res.records;
                this.tableDataObj1.totalRow = res.total;
                this.tableDataObj1.loading2 = false;
            })
        },
        handleCopy(name, event) {
            clip(name, event)
        },
        //获取回复列表数据
        getdate1() {
            this.tableDataObj1.loading2 = true;
            if (this.specificTime === '') {//选择时间范围
                this.sendReport1('5');
            } else {//选择其他
                this.sendReport1(this.specificTime);
            }
        },
        //重置 （回复）
        resetReply(formName) {
            this.$refs[formName].resetFields();
            // this.form1.sendBeginTime = moment().startOf('day').format('YYYY-MM-DD')
            // this.form1.sendEndTime = moment().endOf('day').format('YYYY-MM-DD')
            this.time1 = [moment().startOf('day').format('YYYY-MM-DD'), moment().endOf('day').format('YYYY-MM-DD')],
                Object.assign(this.formData1, this.form1)
            this.specificTime = '1'
            this.handleChangeTimeOptions()
        },
        handPhone(row, index) {
            this.$api.post(this.API.upms + '/generatekey/decryptMobile', {
                keyId: row.keyId,
                smsInfoId: row.decryptMobile,
                cipherMobile: row.cipherMobile
            }, res => {
                if (res.code == 200) {
                    this.tableDataObj1.tableData[index].mobile = res.data;
                } else if (res.code == 4004002) {
                    common.fetchData().then((res) => {
                        if (res.code == 200) {
                            if (res.data.isAdmin == 1) {
                                this.resetVideo = true;
                                this.infoData = res.data;
                            } else {
                                this.$message({
                                    message: '您今日解密次数已超限，如需重置解密次数，请联系管理员！',
                                    type: "warning",
                                });
                            }
                        } else {
                            this.$message({
                                message: res.msg,
                                type: "error",
                            });
                        }

                    })
                } else {
                    this.$message({
                        message: res.msg,
                        type: 'warning'
                    });
                }
                //    this.tableDataObj.tableData[index].mobile = res.data;
            })
        },
        //查询 （回复）
        queryReply() {
            Object.assign(this.formData1, this.form1);
            let flag = this.specificTime;
            if (flag == '') flag = '5'
            this.sendReport1(flag);
        },
        handleClose() {
            this.exportShow = false
        },
        exportFn(obj) {
            this.$api.post(this.API.cpus + 'statistics/export', obj, res => {
                if (res.code == 200) {
                    // this.exportShow = false
                    this.$message({
                        type: "success",
                        duration: "2000",
                        message: "已加入到文件下载中心!",
                    });
                    this.$router.push('/FileExport');
                } else {
                    this.$message({
                        type: 'error',
                        duration: '2000',
                        message: res.msg
                    });
                }

            })
        },
        //导出（回复）
        exportNums1() {
            if (this.tableDataObj1.tableData.length == 0) {
                this.$message({
                    message: '列表无数据，不可导出！',
                    type: 'warning'
                });
            } else {
                this.$api.get(this.API.cpus + "statistics/exportDecode/check", {}, (res) => {
                    if (res.code == 200) {
                        this.emailInfo.email = res.data.email;
                        this.emailInfo.username = res.data.username;
                        if (res.data.decode) {
                            this.exportShow = true
                        } else {
                            let data = Object.assign({}, this.formData1);
                            data.isDownload = '1';
                            data.productType = '7'
                            if (this.specificTime === '') {//选择时间范围
                                data.flag = '5';
                            } else {//选择其他
                                data.flag = this.specificTime;
                            }
                            this.exportFn(data)
                        }
                    }
                });

            }
        },
        submitExport() {
            let data = {};
            Object.assign(data, this.formData1);
            data.productType = '7';
            data.decode = this.ruleForm.decode == 0 ? false : true;
            if (this.specificTime === "") {
                //选择时间范围
                data.flag = "5";
            } else {
                //选择其他
                data.flag = this.specificTime;
            }
            this.exportFn(data)
        },
        getTimeOperating(val) {
            // console.log(val,'val');
            if (val) {
                this.formData1.sendBeginTime = val[0];
                this.formData1.sendEndTime = val[1];
                this.form1.sendBeginTime = val[0];
                this.form1.sendEndTime = val[1];
                this.specificTime = '';
            } else {
                this.specificTime = '1';
                this.formData1.sendBeginTime = '';
                this.formData1.sendEndTime = '';
                this.form1.sendBeginTime = '';
                this.form1.sendEndTime = '';
            }
        },
        //时间范围选择器
        handledatepluginVal: function (val1, val2) {
            // console.log(val1,'val');
            if (val1) {

                this.formData1.sendBeginTime = val1;
                this.formData1.sendEndTime = val2;
                this.form1.sendBeginTime = val1;
                this.form1.sendEndTime = val2;
                this.specificTime = '';
            } else {
                this.specificTime = '1';
                this.formData1.sendBeginTime = '';
                this.formData1.sendEndTime = '';
                this.form1.sendBeginTime = '';
                this.form1.sendEndTime = '';
            }
        },
        //选择（今日，昨天，近一年时 置空时间范围框的值）
        handleChangeTimeOptions: function () {
            this.datePluginValueList.datePluginValue = '';
            this.formData1.sendBeginTime = '';
            this.formData1.sendEndTime = '';
            this.form1.sendBeginTime = '';
            this.form1.sendEndTime = '';
        },

        //获取分页的每页数量
        handleSizeChange1(size) {
            this.formData1.pageSize = size;
            this.getdate1();
        },
        //获取分页的第几页
        handleCurrentChange1(currentPage) {
            this.formData1.currentPage = currentPage;
            this.getdate1();
        },
        // 点击批量加入
        batchAdd() {
            if (this.selectId) {
                this.AddBlacks = true
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请勾选需要批量的内容"
                });
            }
        },
        //列表复选框的值
        handelSelection(val) {
            let selectId = [];
            for (let i = 0; i < val.length; i++) {
                selectId.push(val[i].smsReplyId)
            }
            this.selectId = selectId.join(); //批量操作选中id
        },
        // 单条加入黑名单
        handelOptionButton: function (val) {
            if (val.methods == 'Addblack') {
                this.AddBlacks = true
                this.AddBlacksStatus = true
                this.AddBlackVal = val.row.smsReplyId
            }
        },
        //批量加入黑名单
        batchAddblack() {
            this.$confirms.confirmation('post', '确定加入黑名单?', this.API.cpus + 'consumerclientblacknumber/blacklistBatch', { idStr: this.AddBlacksStatus == false ? this.selectId : this.AddBlackVal, remark: this.AddBlackform.remark }, res => {
                this.AddBlacks = false
                this.getdate1();
            })
        }
    },
    mounted() {
        this.getTemplateName();
    },
    // activated(){
    //     this.getTemplateName();
    //     this.sendReport1("1")
    // },
    watch: {
        //监听查询框的值是否改变

        formData1: {
            handler() {
                // if(this.tabsactive == 1){
                this.getdate1();
                // }
            },
            deep: true
        },
        //监听(今天，昨天，近一年)的改变
        specificTime: {
            handler(val) {
                // window.Vue.cancel();
                this.getdate1();

            },
            deep: true,
            immediate: true
        },
        AddBlacks(val) {
            if (val == false) {
                this.AddBlacksStatus = false
                this.$refs.AddBlackformRef.resetFields()
            }
        }
    }
}
</script>

<style scoped>
.ShortMessageRecording-chart-box {
    margin: 10px 0;
    padding: 20px 20px 20px 20px;
}

.ShortMessageRecording-chart-title {
    display: flex;
}

.ShortMessageRecording-chart {
    height: 360px;
}

.ShortMessageRecording-title {
    padding-top: 40px;
    font-weight: bold;
}

.look-at-more {
    color: #16a589;
}

.ShortMessageRecording-select {
    position: relative;
    margin-top: 10px;
}

.short-message-recording-type {
    margin-top: 40px;
}

.query-frame {
    margin-top: 20px;
}

.query-frame .el-form-item {
    display: inline-block;
    margin-bottom: 12px;
}

.span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tooltip {
    width: 350px;
}
</style>
<style>
#SmsRecord .threeDay .el-radio-button__inner {
    border-right: 0;
}

.ShortMessageRecording-chart-box .el-radio-button:last-child .el-radio-button__inner {
    border-radius: 0;
}

.ShortMessageRecording-chart-box .el-range-editor.el-input__inner {
    border-radius: 0px 4px 4px 0;
}

.ShortMessageRecording-chart-box .el-table--small td,
.ShortMessageRecording-chart-box .el-table--small th {
    padding-top: 8px;
    padding-bottom: 8px;
}
</style>
