<template>
  <div class="simple-smsrecord-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button
                  @click="refreshList"
                  class="action-btn"
                  icon="el-icon-refresh"
                >
                  刷新列表
                </el-button>
                <el-button
                  type="primary"
                  @click="exportNums1()"
                  class="action-btn"
                  icon="el-icon-download"
                >
                  导出数据
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">回复记录</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-count">共 {{ tableDataObj1.totalRow }} 条记录</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <div class="search-controls">
                <!-- 时间选择 -->
                <div class="time-controls">
                  <span class="control-label">时间范围：</span>
                  <el-radio-group
                    v-model="specificTime"
                    size="small"
                    @change="handleChangeTimeOptions"
                    class="time-radio-group"
                  >
                    <el-radio-button label="1">今天</el-radio-button>
                    <el-radio-button label="2">近4天</el-radio-button>
                  </el-radio-group>
                  <el-date-picker
                    v-model="time1"
                    value-format="yyyy-MM-dd"
                    range-separator="-"
                    :clearable="false"
                    :picker-options="pickerOptions"
                    @change="getTimeOperating"
                    type="daterange"
                    start-placeholder="开始日期"
                    end-placeholder="结束日期"
                    class="date-picker"
                  />
                </div>

                <!-- 筛选条件 -->
                <div class="filter-controls">
                  <div class="filter-item">
                    <span class="control-label">手机号：</span>
                    <el-input
                      v-model="form1.mobile"
                      placeholder="请输入手机号"
                      class="filter-input"
                      clearable
                    />
                  </div>

                  <div class="filter-item">
                    <span class="control-label">消息ID：</span>
                    <el-input
                      v-model="form1.msgid"
                      placeholder="请输入消息ID"
                      class="filter-input"
                      clearable
                    />
                  </div>

                  <div class="filter-item">
                    <span class="control-label">签名：</span>
                    <el-input
                      v-model="form1.signature"
                      placeholder="请输入签名"
                      class="filter-input"
                      clearable
                    />
                  </div>

                  <div class="filter-item">
                    <span class="control-label">回复内容：</span>
                    <el-input
                      v-model="form1.content"
                      placeholder="请输入回复内容"
                      class="filter-input"
                      clearable
                    />
                  </div>

                  <div class="filter-item">
                    <span class="control-label">发送内容：</span>
                    <el-input
                      v-model="form1.smsContent"
                      placeholder="请输入发送内容"
                      class="filter-input"
                      clearable
                    />
                  </div>

                  <div class="filter-actions">
                    <el-button type="primary" @click="queryReply()" class="search-btn">
                      <i class="el-icon-search"></i> 查询
                    </el-button>
                    <el-button @click="resetReply()" class="reset-btn">
                      <i class="el-icon-refresh-left"></i> 重置
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 回复记录列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">回复记录列表</h3>
            <div class="table-subtitle">
              <el-tag type="info" size="small">
                {{ specificTime === '1' ? '今天' : '近4天' }}
              </el-tag>
            </div>
          </div>

          <div class="table-container">
            <div class="table-wrapper">
              <el-table
                v-loading="tableDataObj1.loading2"
                element-loading-text="拼命加载中"
                element-loading-spinner="el-icon-loading"
                element-loading-background="rgba(0, 0, 0, 0.6)"
                ref="multipleTable"
                border
                :stripe="true"
                :data="tableDataObj1.tableData"
                style="width: 100%"
                @selection-change="handelSelection"
                class="enhanced-table"
              >
                <el-table-column type="selection" width="46"></el-table-column>

                <el-table-column label="手机号码" width="130">
                  <template slot-scope="scope">
                    <div
                      class="phone-link"
                      @click="handPhone(scope.row, scope.$index)"
                    >
                      {{ scope.row.mobile }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column label="回复内容" width="150">
                  <template slot-scope="scope">
                    <span>{{ scope.row.content }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="回复时间" width="180">
                  <template slot-scope="scope">
                    <span>{{ scope.row.createTime }}</span>
                  </template>
                </el-table-column>

                <el-table-column label="发送内容">
                  <template slot-scope="scope">
                    <Tooltip
                      v-if="scope.row.smsContent"
                      :content="scope.row.smsContent"
                      className="wrapper-text"
                      effect="light"
                    />
                  </template>
                </el-table-column>

                <el-table-column label="消息ID" width="220">
                  <template slot-scope="scope">
                    <div class="msgid-cell">
                      <span class="msgid-text">{{ scope.row.msgid }}</span>
                      <i
                        class="el-icon-document-copy copy-icon"
                        @click="handleCopy(scope.row.msgid, $event)"
                        title="复制消息ID"
                      />
                    </div>
                  </template>
                </el-table-column>
              </el-table>

              <div class="pagination-section">
                <el-pagination
                  @size-change="handleSizeChange1"
                  @current-change="handleCurrentChange1"
                  :current-page="1"
                  :page-size="formData1.pageSize"
                  :page-sizes="[10, 20, 50, 100, 500]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="tableDataObj1.totalRow"
                  class="simple-pagination"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 批量加入黑名单弹窗 -->
    <el-dialog title="批量加入黑名单" :visible.sync="AddBlacks" width="500px" class="blacklist-dialog">
      <div class="dialog-content">
        <el-form :model="AddBlackform" ref="AddBlackformRef" label-width="100px" class="blacklist-form">
          <el-form-item label="备注" prop="remark">
            <el-input
              type="textarea"
              maxlength="70"
              placeholder="请输入备注内容，不超过70个字"
              v-model="AddBlackform.remark"
              :rows="4"
              show-word-limit
            />
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="AddBlacks = false">取 消</el-button>
        <el-button type="primary" @click="batchAddblack()">确 定</el-button>
      </div>
    </el-dialog>

    <!-- 导出数据弹窗 -->
    <el-dialog title="导出数据" :visible.sync="exportShow" width="480px" :before-close="handleClose" class="export-dialog">
      <div class="dialog-content">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="export-form">
          <el-form-item label="导出类型" prop="decode">
            <el-radio-group v-model="ruleForm.decode" class="export-radio-group">
              <el-radio label="0" class="export-radio">
                <span class="radio-label">掩码</span>
                <span class="radio-desc">手机号码将被部分隐藏</span>
              </el-radio>
              <el-radio label="1" class="export-radio">
                <span class="radio-label">明码</span>
                <span class="radio-desc">显示完整手机号码</span>
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <div class="export-notice" v-if="ruleForm.decode == '1'">
            <i class="el-icon-warning notice-icon"></i>
            <span class="notice-text">
              明码文件将会发送到您的邮箱 {{ this.emailInfo.email }}，请注意查收。
            </span>
          </div>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exportShow = false">取 消</el-button>
        <el-button type="primary" @click="submitExport">
          <i :class="ruleForm.decode == '0' ? 'el-icon-download' : 'el-icon-message'"></i>
          {{ ruleForm.decode == '0' ? '下载' : '发送' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

import TableTem from '@/components/publicComponents/TableTem'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import clip from '../../../../utils/clipboard'
import Tooltip from "@/components/publicComponents/tooltip";
import common from "../../../../../../assets/js/common";
import moment from 'moment'
export default {
    name: "SmsRecord",
    components: {
        TableTem,
        DatePlugin,
        Tooltip
    },
    data() {
        return {
            name: "SmsRecord",
            //模板名称
            templateName: [],
            time1: [moment().startOf('day').format('YYYY-MM-DD'), moment().endOf('day').format('YYYY-MM-DD')],
            //复选框的值
            selectId: '',
            // 单条存值
            AddBlackVal: '',
            // 备注
            AddBlackform: {
                remark: ''
            },
            AddBlacks: false,
            AddBlacksStatus: false,

            //回复查询的值
            form1: {
                mobile: '',
                msgid: "",
                signature: "",
                content: "",
                smsContent:"",
                currentPage: 1,
                sendBeginTime: moment().startOf('day').format('YYYY-MM-DD'),
                sendEndTime: moment().endOf('day').format('YYYY-MM-DD'),
                pageSize: 10,
                isDownload: 2
            },
            //复制回复查询的值
            formData1: {
                mobile: '',
                msgid: "",
                signature: "",
                content: "",
                smsContent:"",
                currentPage: 1,
                sendBeginTime: moment().startOf('day').format('YYYY-MM-DD'),
                sendEndTime: moment().endOf('day').format('YYYY-MM-DD'),
                pageSize: 10,
                isDownload: 2
            },
            pickerOptions: {
                disabledDate(time) {
                    // return time.getTime() > Date.now();
                    return time.getTime() < Date.now() - 30 * 24 * 3600 * 1000 || time.getTime() > Date.now();
                },
                //  onPick: ({ maxDate, minDate }) => {
                //     this.pickerMinDate = minDate.getTime();
                //     if (maxDate) {
                //         this.pickerMinDate = ''
                //     }
                // },
                // disabledDate: (time) => {
                //     if(this.pickerMinDate&&this.pickerMinDate>Date.now()){
                //         return false
                //     }
                //     if (this.pickerMinDate !=='') {
                //         const day30 = 30 * 24 * 3600 * 1000
                //         let maxTime = this.pickerMinDate + day30
                //         if (maxTime >  Date.now() - 345600000) {
                //             maxTime =  Date.now() - 345600000
                //         }
                //         const minTime = this.pickerMinDate - day30
                //         return time.getTime() < minTime || time.getTime() > maxTime
                //     }
                //     return time.getTime() > Date.now() - 345600000;
                // }
            },
            specificTime: '1', //选择那一天
            datePluginValueList: { //日期选择器
                type: "daterange",
                start: "",
                end: '',
                range: '-',
                clearable: false,
                pickerOptions: {
                    onPick: ({ maxDate, minDate }) => {
                        this.pickerMinDate = minDate.getTime();
                        if (maxDate) {
                            this.pickerMinDate = ''
                        }
                    },
                    disabledDate: (time) => {
                        if (this.pickerMinDate && this.pickerMinDate > Date.now()) {
                            return false
                        }
                        if (this.pickerMinDate !== '') {
                            const day30 = 30 * 24 * 3600 * 1000
                            let maxTime = this.pickerMinDate + day30
                            if (maxTime > Date.now() - 345600000) {
                                maxTime = Date.now() - 345600000
                            }
                            const minTime = this.pickerMinDate - day30
                            return time.getTime() < minTime || time.getTime() > maxTime
                        }
                        return time.getTime() > Date.now() - 345600000;
                    }
                },
                datePluginValue: ''
            },
            tableDataObj: { //表格数据
                loading2: false,
                totalRow: 0,
                tableData: [],
                tableLabelExpand: [//折叠的列表表头
                    { prop: "content", showName: '短信内容:' }
                ],
                tableLabel: [  //列表表头
                    { prop: "mobile", showName: '手机号码', fixed: false },
                    // {prop:"wordCount",showName:'字数',width:'60',fixed:false},
                    { prop: "chargeNum", showName: '计费条数', width: '80', fixed: false },
                    // {prop:"smsStatus",showName:'短信类型',formatData: function(val) { return val == '2' ? '成功' : '失败' },fixed:false},
                    { prop: "sendTime", showName: '发送时间', fixed: false, width: '140' },
                    { prop: "reportTime", showName: '状态上报时间', fixed: false, width: '140' },

                ],
                tableStyle: {
                    isSelection: false,//是否复选框
                    isExpand: true,//是否是折叠的
                    isDefaultExpand: true, //是否默认打开
                    style: {//表格样式,表格宽度
                        width: "100%"
                    },
                    optionWidth: '180',//操作栏宽度
                    border: true,//是否边框
                    stripe: false,//是否有条纹
                }
            },
            tableDataObj1: { //表格数据
                loading2: false,
                totalRow: 0,
                tableData: [],
                tableLabel: [  //列表表头
                    { prop: "mobile", showName: '手机号码', width: '120', fixed: false },
                    { prop: "content", showName: '回复内容', width: '120', fixed: false },
                    { prop: "msgid", showName: 'msgid', width: '250', fixed: false },
                    { prop: "smsContent", showName: '发送内容', fixed: false },
                    { prop: "createTime", showName: '回复时间', width: '180', fixed: false },
                    // {prop:"ext",showName:'扩展码',width:'150',fixed:false}
                ],
                tableStyle: {
                    isSelection: true,//是否复选框
                    isExpand: false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width: "100%"
                    },
                    optionWidth: '120',//操作栏宽度
                    border: true,//是否边框
                    stripe: false,//是否有条纹
                },
                // conditionOption:[
                //     {
                //         contactCondition:'isBlacknumber',//关联的表格属性
                //         contactData:'2',//关联的表格属性-值
                //         optionName:'加入黑名单',//按钮的显示文字
                //         optionMethod:'Addblack',//按钮的方法
                //         icon:'el-icon-edit',//按钮图标
                //         optionButtonColor:'',//按钮颜色
                //     }
                // ]
            },
            exportShow: false, //是否显示导出按钮
            ruleForm: {
                decode: "0",
            },
            rules: {

            },
            emailInfo: {
                email: "",
                username: ""
            }
        }
    },
    created() {
        //判断是否开启数据展示
        if (this.$store.state.isDateState == 1) {
            this.tableDataObj.tableLabel.push({
                prop: "smsStatus", showName: '发送状态', formatData: function (val) {
                    if (val == '1') {
                        return val = '成功'
                    } else if (val == '2') {
                        return val = '失败'
                    } else {
                        return val = '待返回'
                    }
                }, width: '100', fixed: false
            },
                { prop: "originalCode", showName: '备注', fixed: false });
        }
    },
    methods: {
        //获取已通过的模板名称
        getTemplateName() {
            this.$api.post(this.API.cpus + 'v3/consumersmstemplate/selectClientTemplate', {
                currentPage: 1,
                pageSize: 200
            }, res => {
                this.templateName = res.records;
            })
        },
        //发送请求
        sendReport1(flag) {
            let data = Object.assign({}, this.formData1);
            data.flag = flag;
            this.$api.post(this.API.cpus + 'statistics/replyPage', data, res => {
                this.tableDataObj1.tableData = res.records;
                this.tableDataObj1.totalRow = res.total;
                this.tableDataObj1.loading2 = false;
            })
        },
        handleCopy(name, event) {
            clip(name, event)
        },
        //获取回复列表数据
        getdate1() {
            this.tableDataObj1.loading2 = true;
            if (this.specificTime === '') {//选择时间范围
                this.sendReport1('5');
            } else {//选择其他
                this.sendReport1(this.specificTime);
            }
        },
        //重置 （回复）
        resetReply() {
            // 手动重置表单字段
            this.form1 = {
                mobile: '',
                msgid: '',
                signature: '',
                content: '',
                smsContent: ''
            };
            // 重置时间
            this.time1 = [moment().startOf('day').format('YYYY-MM-DD'), moment().endOf('day').format('YYYY-MM-DD')];
            // 重置时间选择
            this.specificTime = '1';
            // 更新查询参数
            Object.assign(this.formData1, this.form1);
            // 重新获取数据
            this.specificTime = '1'
            this.handleChangeTimeOptions()
        },
        handPhone(row, index) {
            this.$api.post(this.API.upms + '/generatekey/decryptMobile', {
                keyId: row.keyId,
                smsInfoId: row.decryptMobile,
                cipherMobile: row.cipherMobile
            }, res => {
                if (res.code == 200) {
                    this.tableDataObj1.tableData[index].mobile = res.data;
                } else if (res.code == 4004002) {
                    common.fetchData().then((res) => {
                        if (res.code == 200) {
                            if (res.data.isAdmin == 1) {
                                this.resetVideo = true;
                                this.infoData = res.data;
                            } else {
                                this.$message({
                                    message: '您今日解密次数已超限，如需重置解密次数，请联系管理员！',
                                    type: "warning",
                                });
                            }
                        } else {
                            this.$message({
                                message: res.msg,
                                type: "error",
                            });
                        }

                    })
                } else {
                    this.$message({
                        message: res.msg,
                        type: 'warning'
                    });
                }
                //    this.tableDataObj.tableData[index].mobile = res.data;
            })
        },
        //查询 （回复）
        queryReply() {
            Object.assign(this.formData1, this.form1);
            let flag = this.specificTime;
            if (flag == '') flag = '5'
            this.sendReport1(flag);
        },
        handleClose() {
            this.exportShow = false
        },
        exportFn(obj) {
            this.$api.post(this.API.cpus + 'statistics/export', obj, res => {
                if (res.code == 200) {
                    // this.exportShow = false
                    this.$message({
                        type: "success",
                        duration: "2000",
                        message: "已加入到文件下载中心!",
                    });
                    this.$router.push('/FileExport');
                } else {
                    this.$message({
                        type: 'error',
                        duration: '2000',
                        message: res.msg
                    });
                }

            })
        },
        //导出（回复）
        exportNums1() {
            if (this.tableDataObj1.tableData.length == 0) {
                this.$message({
                    message: '列表无数据，不可导出！',
                    type: 'warning'
                });
            } else {
                this.$api.get(this.API.cpus + "statistics/exportDecode/check", {}, (res) => {
                    if (res.code == 200) {
                        this.emailInfo.email = res.data.email;
                        this.emailInfo.username = res.data.username;
                        if (res.data.decode) {
                            this.exportShow = true
                        } else {
                            let data = Object.assign({}, this.formData1);
                            data.isDownload = '1';
                            data.productType = '7'
                            if (this.specificTime === '') {//选择时间范围
                                data.flag = '5';
                            } else {//选择其他
                                data.flag = this.specificTime;
                            }
                            this.exportFn(data)
                        }
                    }
                });

            }
        },
        submitExport() {
            let data = {};
            Object.assign(data, this.formData1);
            data.productType = '7';
            data.decode = this.ruleForm.decode == 0 ? false : true;
            if (this.specificTime === "") {
                //选择时间范围
                data.flag = "5";
            } else {
                //选择其他
                data.flag = this.specificTime;
            }
            this.exportFn(data)
        },
        getTimeOperating(val) {
            // console.log(val,'val');
            if (val) {
                this.formData1.sendBeginTime = val[0];
                this.formData1.sendEndTime = val[1];
                this.form1.sendBeginTime = val[0];
                this.form1.sendEndTime = val[1];
                this.specificTime = '';
            } else {
                this.specificTime = '1';
                this.formData1.sendBeginTime = '';
                this.formData1.sendEndTime = '';
                this.form1.sendBeginTime = '';
                this.form1.sendEndTime = '';
            }
        },
        //时间范围选择器
        handledatepluginVal: function (val1, val2) {
            // console.log(val1,'val');
            if (val1) {

                this.formData1.sendBeginTime = val1;
                this.formData1.sendEndTime = val2;
                this.form1.sendBeginTime = val1;
                this.form1.sendEndTime = val2;
                this.specificTime = '';
            } else {
                this.specificTime = '1';
                this.formData1.sendBeginTime = '';
                this.formData1.sendEndTime = '';
                this.form1.sendBeginTime = '';
                this.form1.sendEndTime = '';
            }
        },
        //选择（今日，昨天，近一年时 置空时间范围框的值）
        handleChangeTimeOptions: function () {
            this.datePluginValueList.datePluginValue = '';
            this.formData1.sendBeginTime = '';
            this.formData1.sendEndTime = '';
            this.form1.sendBeginTime = '';
            this.form1.sendEndTime = '';
        },

        //获取分页的每页数量
        handleSizeChange1(size) {
            this.formData1.pageSize = size;
            this.getdate1();
        },
        //获取分页的第几页
        handleCurrentChange1(currentPage) {
            this.formData1.currentPage = currentPage;
            this.getdate1();
        },
        // 点击批量加入
        batchAdd() {
            if (this.selectId) {
                this.AddBlacks = true
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请勾选需要批量的内容"
                });
            }
        },
        //列表复选框的值
        handelSelection(val) {
            let selectId = [];
            for (let i = 0; i < val.length; i++) {
                selectId.push(val[i].smsReplyId)
            }
            this.selectId = selectId.join(); //批量操作选中id
        },
        // 单条加入黑名单
        handelOptionButton: function (val) {
            if (val.methods == 'Addblack') {
                this.AddBlacks = true
                this.AddBlacksStatus = true
                this.AddBlackVal = val.row.smsReplyId
            }
        },
        //批量加入黑名单
        batchAddblack() {
            this.$confirms.confirmation('post', '确定加入黑名单?', this.API.cpus + 'consumerclientblacknumber/blacklistBatch', { idStr: this.AddBlacksStatus == false ? this.selectId : this.AddBlackVal, remark: this.AddBlackform.remark }, () => {
                this.AddBlacks = false
                this.getdate1();
            })
        },

        // 刷新列表
        refreshList() {
            this.getdate1();
        }
    },
    mounted() {
        this.getTemplateName();
    },
    // activated(){
    //     this.getTemplateName();
    //     this.sendReport1("1")
    // },
    watch: {
        //监听查询框的值是否改变

        formData1: {
            handler() {
                // if(this.tabsactive == 1){
                this.getdate1();
                // }
            },
            deep: true
        },
        //监听(今天，昨天，近一年)的改变
        specificTime: {
            handler() {
                // window.Vue.cancel();
                this.getdate1();

            },
            deep: true,
            immediate: true
        },
        AddBlacks(val) {
            if (val == false) {
                this.AddBlacksStatus = false
                // 添加安全检查，确保表单引用存在
                this.$nextTick(() => {
                    if (this.$refs.AddBlackformRef && this.$refs.AddBlackformRef.resetFields) {
                        this.$refs.AddBlackformRef.resetFields()
                    } else {
                        // 手动重置表单数据
                        this.AddBlackform.remark = ''
                    }
                })
            }
        }
    }
}
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* SmsRecord 特有样式 */
.simple-smsrecord-page {
  min-height: 100vh;
  background: #fafafa;
  padding: 0;
}

/* 搜索控件样式 */
.search-controls {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.time-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

.time-radio-group {
  /deep/ .el-radio-button__inner {
    border-radius: 6px;
    border: 1px solid #dcdfe6;
    background: #fff;
    color: #606266;

    &:hover {
      color: #409eff;
      border-color: #c6e2ff;
    }
  }

  /deep/ .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    background: #409eff;
    border-color: #409eff;
    color: #fff;
  }
}

.date-picker {
  min-width: 280px;
}

.filter-input {
  min-width: 200px;
}

.filter-actions {
  display: flex;
  gap: 12px;
  margin-left: auto;

  .search-btn {
    background: #409eff;
    border-color: #409eff;

    &:hover {
      background: #66b1ff;
      border-color: #66b1ff;
    }
  }

  .reset-btn {
    &:hover {
      color: #409eff;
      border-color: #c6e2ff;
      background-color: #ecf5ff;
    }
  }
}

.table-subtitle {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 表格特殊样式 */
.phone-link {
  color: #409eff;
  cursor: pointer;
  font-weight: 500;

  &:hover {
    color: #66b1ff;
    text-decoration: underline;
  }
}

.msgid-cell {
  display: flex;
  align-items: center;
  gap: 8px;

  .msgid-text {
    flex: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    max-width: 180px;
  }

  .copy-icon {
    color: #409eff;
    cursor: pointer;
    font-size: 14px;
    flex-shrink: 0;

    &:hover {
      color: #66b1ff;
    }
  }
}

.wrapper-text {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 弹窗样式 */
.blacklist-dialog, .export-dialog {
  /deep/ .el-dialog {
    border-radius: 12px;
  }

  /deep/ .el-dialog__header {
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
  }

  /deep/ .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  /deep/ .el-dialog__body {
    padding: 24px;
  }
}

.dialog-content {
  .blacklist-form, .export-form {
    margin: 0;
  }
}

.export-radio-group {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .export-radio {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    margin-right: 0;

    &:hover {
      border-color: #c6e2ff;
      background-color: #f5f7fa;
    }

    /deep/ .el-radio__input.is-checked + .el-radio__label {
      color: #409eff;
    }

    /deep/ .el-radio__input.is-checked {
      .el-radio__inner {
        border-color: #409eff;
        background: #409eff;
      }
    }

    .radio-label {
      font-weight: 500;
      font-size: 14px;
      margin-bottom: 4px;
    }

    .radio-desc {
      font-size: 12px;
      color: #909399;
      margin-left: 24px;
    }
  }
}

.export-notice {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 6px;
  margin-top: 16px;

  .notice-icon {
    color: #f56c6c;
    margin-top: 2px;
    flex-shrink: 0;
  }

  .notice-text {
    font-size: 13px;
    color: #f56c6c;
    line-height: 1.4;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .time-controls, .filter-controls {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-item {
    width: 100%;
  }

  .filter-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .msgid-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;

    .msgid-text {
      max-width: 100%;
    }
  }

  .export-radio-group {
    .export-radio {
      .radio-desc {
        margin-left: 0;
      }
    }
  }
}
</style>
<style>
/* 全局样式覆盖 */
.simple-smsrecord-page .el-table--small td,
.simple-smsrecord-page .el-table--small th {
  padding-top: 8px;
  padding-bottom: 8px;
}

.simple-smsrecord-page .el-date-editor.el-input {
  width: auto;
}

.simple-smsrecord-page .el-date-editor .el-range__icon {
  font-size: 14px;
  color: #c0c4cc;
}

.simple-smsrecord-page .el-date-editor .el-range-input {
  background-color: transparent;
  border: 0;
  outline: none;
  display: inline-block;
  font-size: 14px;
  margin: 0;
  padding: 0;
  width: 39%;
}
</style>
