<template>
    <div id=shortMessage style="background: #fff">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 号码状态查询</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="fillet  ShortMessageRecording-chart-box">
            <div class="short-message-recording-type">
                        <el-form ref="SHformList" :model="form" label-width="86px" class="query-frame">
                            <el-form-item label="手机号码" prop="mobile">
                                <el-input v-model="form.mobile" placeholder="请输入手机号" class="input-w"></el-input>
                            </el-form-item>
                            <el-form-item>
                                <el-button type="primary" plain @click="search">查 询</el-button>
                                <el-button type="primary" plain @click="resetting">重 置</el-button>
                                <!-- <el-button type="primary" v-if="specificTime==2" @click="exportNums()">导出数据</el-button> -->
                            </el-form-item>
                        </el-form>
                        <div style="padding-bottom:40px;">
                            <el-table
                                v-loading="tableDataObj.loading2"
                                element-loading-text="拼命加载中"
                                element-loading-spinner="el-icon-loading"
                                element-loading-background="rgba(0, 0, 0, 0.6)"
                                ref="multipleTable"
                                border
                                :stripe="true"
                                :data="tableDataObj.tableData"
                                style="width: 100%"
                                >
                                <!-- <el-table-column type="selection" width="46"></el-table-column> -->
                                <el-table-column label="手机号" width="120">
                                    <template slot-scope="scope">
                                    <span>{{ scope.row.mobile }}</span>
                                    </template>
                                    </el-table-column>
                                    <el-table-column label="短信内容" width="500">
                                    <template slot-scope="scope">
                                    <span>{{ scope.row.content }}</span>
                                    </template>
                                    </el-table-column>
                                <el-table-column label="计费条数" width="70">
                                    <template slot-scope="scope">
                                    <span>{{ scope.row.chargeNum }}</span>
                                    </template>
                                </el-table-column>
                                <el-table-column label="消息ID" width="200">
                                    <template slot-scope="scope">
                                    <span>{{ scope.row.msgid }}</span>
                                    <i style="color:#409eff;cursor: pointer;margin: 4px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.msgid,$event )"></i>
                                    </template>
                                </el-table-column>
                                <el-table-column label="发送时间" width="140">
                                    <template slot-scope="scope">
                                        <span>{{ scope.row.sendTime }}</span>
                                    </template>
                                    
                                </el-table-column>
                                <el-table-column label="状态上报时间" width="140">
                                    <template slot-scope="scope">
                                        <span >{{ scope.row.reportTime }}</span>
                                    </template>
                                    </el-table-column>
                                <el-table-column label="发送状态" width="120">
                                    <template slot-scope="scope">
                                        <span v-if="scope.row.smsStatus ==1">成功</span>
                                        <span v-else-if="scope.row.smsStatus ==2">失败</span>
                                        <span v-else>待返回</span>
                                    </template>
                                    </el-table-column>
                                <el-table-column label="备注">
                                    <template slot-scope="scope">
                                        <span >{{ scope.row.originalCode }}</span>
                                    </template>
                                </el-table-column>
                                </el-table>
                            <!-- <table-tem :tableDataObj="tableDataObj"></table-tem> -->
                            <div></div>
                            <el-col  :xs="23" :sm="23" :md="23" :lg="23" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                                <span style="display: inline-block;width:90px;height:30px;line-height:30px;text-align:center;border:1px solid #ccc" >共{{tableDataObj.total}}条</span>
                                <!-- <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="1" :page-size="formData.pageSize" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.totalRow">
                                </el-pagination> -->
                            </el-col>
                        </div>
            </div>
        </div>
    </div>
</template>

<script>

import TableTem from '@/components/publicComponents/TableTem'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import clip from '../../../utils/clipboard'
export default {
    name: "smsPhoneSearch",
    components: {
        TableTem,
        DatePlugin
    },
    data () {
        return {
            name: "smsPhoneSearch",
            //发送查询的值
            form:{
                mobile:'',
            },
            tableDataObj: {
                //列表数据
                //总条数
                total: 0,
                loading2: false,
                tableData: [],
            },
            //复制发送查询的值
            formData:{
                mobile:'',
            },
        }
    },
     created(){
        this.getdate()
    },
    // activated(){
    //     this.getdate()
    // },
    methods: {
        getdate(){
            this.$api.get(this.API.cpus+'statistics/queryMobile',{mobile:this.form.mobile},res=>{
                this.tableDataObj.tableData = res.data
                this.tableDataObj.total = res.data.length
                // console.log(res.data);
        })
    },
    handleCopy(name,event){
        clip(name, event)
      },
    search(){
        this.getdate()
    },
    resetting(){
        this.form.mobile = ""
        this.getdate()
    }
    }
}
</script>

<style scoped>
    .ShortMessageRecording-chart-box{
        margin:10px 0;
        padding:20px 20px 20px 20px;
        
    }
    .ShortMessageRecording-chart-title{
        display: flex;
    }
    .ShortMessageRecording-chart{
        height:360px;
    }
    .ShortMessageRecording-title{
        padding-top:40px;
        font-weight: bold;
    }
    .look-at-more{
        color:#16a589;
    }
    .ShortMessageRecording-select{
        position: relative;
        margin-top:10px;
    }
    .short-message-recording-type{
        margin-top:10px;
    }
    .query-frame{
       margin-top:20px; 
    }
    .query-frame .el-form-item{
        display: inline-block;
        margin-bottom:12px; 
        
    }
</style>
<style>
/* .threeDay .el-radio-button__inner{
    border-right: 0 ;
} */
.ShortMessageRecording-chart-box .el-radio-button:last-child .el-radio-button__inner{
    border-radius: 0;
}
.ShortMessageRecording-chart-box .el-range-editor.el-input__inner{
    border-radius: 0px 4px 4px 0;
}
.ShortMessageRecording-chart-box .el-table--small td,.ShortMessageRecording-chart-box .el-table--small th{
    padding-top:8px;
    padding-bottom:8px;
}
</style>
