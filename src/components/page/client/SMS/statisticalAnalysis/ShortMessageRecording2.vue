<template>
  <div class="simple-senddetails-page">
    <!-- 主要内容区域 -->
    <div class="page-content">
      <div class="content-container enhanced-layout">
        <!-- 固定操作栏 -->
        <div class="fixed-toolbar">
          <div class="toolbar-container">
            <!-- 操作按钮区域 -->
            <div class="action-section">
              <div class="action-buttons">
                <el-button
                  @click="refreshList"
                  class="action-btn"
                  icon="el-icon-refresh"
                >
                  刷新列表
                </el-button>
                <el-button
                  type="primary"
                  v-if="specificTime == 2"
                  @click="exportNums()"
                  class="action-btn"
                  icon="el-icon-download"
                >
                  导出数据
                </el-button>
              </div>

              <!-- 统计信息 -->
              <div class="stats-info">
                <span class="stats-text">发送详情</span>
                <el-divider direction="vertical"></el-divider>
                <span class="stats-count">共 {{ tableDataObj.totalRow }} 条记录</span>
              </div>
            </div>

            <!-- 搜索区域 -->
            <div class="search-section">
              <div class="search-controls">
                <!-- 时间选择 -->
                <div class="time-controls">
                  <span class="control-label">时间范围：</span>
                  <el-radio-group
                    v-model="specificTime"
                    size="small"
                    class="time-radio-group"
                  >
                    <el-radio-button label="1">近1小时</el-radio-button>
                    <el-radio-button label="2">近一个月</el-radio-button>
                  </el-radio-group>
                </div>

                <!-- 筛选条件 -->
                <div class="filter-controls">
                  <div class="filter-item">
                    <span class="control-label">手机号码：</span>
                    <el-input
                      v-model="form.mobile"
                      placeholder="请输入手机号"
                      class="filter-input"
                      clearable
                    />
                  </div>

                  <div class="filter-item" v-if="this.$store.state.isDateState == 1 && specificTime == 2">
                    <span class="control-label">发送状态：</span>
                    <el-select
                      v-model="form.smsStatus"
                      placeholder="发送状态"
                      class="filter-select"
                      clearable
                    >
                      <el-option label="全部" value=""></el-option>
                      <el-option label="成功" value="1"></el-option>
                      <el-option label="失败" value="2"></el-option>
                      <el-option label="待返回" value="3"></el-option>
                    </el-select>
                  </div>

                  <div class="filter-item" v-if="specificTime == 2">
                    <span class="control-label">短信签名：</span>
                    <el-input
                      v-model="form.signature"
                      placeholder="请输入短信签名"
                      class="filter-input"
                      clearable
                    />
                  </div>

                  <div class="filter-item" v-if="specificTime == 2">
                    <span class="control-label">短信内容：</span>
                    <el-input
                      v-model="form.content"
                      placeholder="请输入短信内容"
                      class="filter-input"
                      clearable
                    />
                  </div>

                  <div class="filter-item" v-if="specificTime == 2">
                    <span class="control-label">标签：</span>
                    <el-select
                      v-model="form.source"
                      placeholder="请选择标签"
                      filterable
                      @blur="selectBlur"
                      class="filter-select"
                      clearable
                    >
                      <el-option
                        v-for="item in dynamicTags"
                        :key="item.id"
                        :label="item.label"
                        :value="item.label"
                      />
                    </el-select>
                  </div>

                  <div class="filter-item">
                    <span class="control-label">消息ID：</span>
                    <el-input
                      v-model="form.msgid"
                      placeholder="请输入消息ID"
                      class="filter-input"
                      clearable
                    />
                  </div>

                  <div class="filter-item" v-if="specificTime == 2">
                    <span class="control-label">模板ID：</span>
                    <el-input
                      v-model="form.temId"
                      placeholder="请输入模板ID"
                      class="filter-input"
                      clearable
                    />
                  </div>

                  <div class="filter-item" v-if="specificTime == 2">
                    <span class="control-label">发送时间：</span>
                    <el-date-picker
                      v-model="form.time1"
                      type="datetimerange"
                      range-separator="-"
                      :picker-options="pickerOptions"
                      :clearable="false"
                      @change="getTimeOperating"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      class="date-picker"
                    />
                  </div>

                  <div class="filter-actions">
                    <el-button type="primary" @click="querySending()" class="search-btn">
                      <i class="el-icon-search"></i> 查询
                    </el-button>
                    <el-button @click="resetSending('SHformList')" class="reset-btn">
                      <i class="el-icon-refresh-left"></i> 重置
                    </el-button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <!-- 发送详情列表 -->
        <div class="table-section">
          <div class="table-header">
            <h3 class="table-title">发送详情列表</h3>
            <div class="table-subtitle">
              <el-tag
                :type="specificTime === '1' ? 'warning' : 'primary'"
                size="small"
              >
                {{ specificTime === '1' ? '近1小时' : '近一个月' }}
              </el-tag>
            </div>
          </div>

          <div class="table-container">
            <div class="table-wrapper">
              <table-tem
                :tableDataObj="tableDataObj"
                tips="view"
                @handPhone="handPhone"
                class="enhanced-table"
              />

              <div class="pagination-section">
                <el-pagination
                  @size-change="handleSizeChange"
                  @current-change="handleCurrentChange"
                  :current-page="1"
                  :page-size="formData.pageSize"
                  :page-sizes="[10, 20, 50, 100]"
                  layout="total, sizes, prev, pager, next, jumper"
                  :total="tableDataObj.totalRow"
                  class="simple-pagination"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 导出数据弹窗 -->
    <el-dialog title="导出数据" :visible.sync="exportShow" width="480px" :before-close="handleClose" class="export-dialog">
      <div class="dialog-content">
        <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="export-form">
          <el-form-item label="导出类型" prop="decode">
            <el-radio-group v-model="ruleForm.decode" class="export-radio-group">
              <el-radio label="0" class="export-radio">
                <span class="radio-label">掩码</span>
                <span class="radio-desc">手机号码将被部分隐藏</span>
              </el-radio>
              <el-radio label="1" class="export-radio">
                <span class="radio-label">明码</span>
                <span class="radio-desc">显示完整手机号码</span>
              </el-radio>
            </el-radio-group>
          </el-form-item>

          <div class="export-notice" v-if="ruleForm.decode == '1'">
            <i class="el-icon-warning notice-icon"></i>
            <span class="notice-text">
              明码文件将会发送到您的邮箱 {{ emailInfo.email }}，请注意查收。
            </span>
          </div>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="exportShow = false">取 消</el-button>
        <el-button type="primary" @click="submitExport">
          <i :class="ruleForm.decode == '0' ? 'el-icon-download' : 'el-icon-message'"></i>
          {{ ruleForm.decode == '0' ? '下载' : '发送' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 重置号码弹窗 -->
    <ResetNumberVue v-if="resetVideo" ref="resetNumber" :infoData="infoData" :visible="resetVideo"></ResetNumberVue>
  </div>
</template>

<script>
import TableTem from "@/components/publicComponents/TableTem";
import DatePlugin from "@/components/publicComponents/DatePlugin";
import DownLoadExport from "@/components/publicComponents/downLodExport"
import ResetNumberVue from "@/components/publicComponents/ResetNumber.vue";
import bus from "../../../../common/bus";
import common from "../../../../../assets/js/common";
import moment from "moment";
export default {
  name: "ShortMessageRecording2",
  components: {
    TableTem,
    DatePlugin,
    DownLoadExport,
    ResetNumberVue
  },
  data() {
    return {
      name: "ShortMessageRecording2",
      pickerOptions: {
        disabledDate(time) {
          // 禁用30天前的日期和未来日期
          const now = new Date();
          const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 3600 * 1000);
          const today = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 23, 59, 59);

          return (
            time.getTime() < thirtyDaysAgo.getTime() ||
            time.getTime() > today.getTime()
          );
        },
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              const today = new Date();
              const start = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 0, 0, 0);
              const end = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '昨天',
            onClick(picker) {
              const yesterday = new Date();
              yesterday.setDate(yesterday.getDate() - 1);
              const start = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 0, 0, 0);
              const end = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近7天',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setDate(start.getDate() - 6);
              start.setHours(0, 0, 0, 0);
              end.setHours(23, 59, 59, 999);
              picker.$emit('pick', [start, end]);
            }
          },
          {
            text: '最近30天',
            onClick(picker) {
              const end = new Date();
              const start = new Date();
              start.setDate(start.getDate() - 29);
              start.setHours(0, 0, 0, 0);
              end.setHours(23, 59, 59, 999);
              picker.$emit('pick', [start, end]);
            }
          }
        ]
      },
      phoneList: [],
      infoData:{},
      exportFlag: false,
      //模板名称
      templateName: [],
      //标签
      dynamicTags: [],
      //复选框的值
      selectId: "",
      // 单条存值
      AddBlackVal: "",
      // 备注
      AddBlackform: {
        remark: "",
      },
      AddBlacks: false,
      AddBlacksStatus: false,
      resetVideo: false,
      isDateState: "",
      //发送查询的值
      form: {
        mobile: "",
        msgid: "",
        smsStatus: "",
        signature: "",
        content: "",
        temId: "",
        source: "",
        sendBeginTime: moment()
          .subtract(30, "days")
          .format("YYYY-MM-DD 00:00:00"),
        sendEndTime: moment().format("YYYY-MM-DD 23:59:59"),
        time1: [
          moment().subtract(30, "days").format("YYYY-MM-DD 00:00:00"),
          moment().format("YYYY-MM-DD 23:59:59"),
        ],
        currentPage: 1,
        pageSize: 10,
        time: "",
        isDownload: 2,
      },
      //复制发送查询的值
      formData: {
        mobile: "",
        msgid: "",
        smsStatus: "",
        signature: "",
        content: "",
        temId: "",
        source: "",
        sendBeginTime: moment()
          .subtract(30, "days")
          .format("YYYY-MM-DD 00:00:00"),
        sendEndTime: moment().format("YYYY-MM-DD 23:59:59"),
        time1: [
          moment().subtract(30, "days").format("YYYY-MM-DD 00:00:00"),
          moment().format("YYYY-MM-DD 23:59:59"),
        ],
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
      },
      specificTime: "1", //选择那一天
      tableDataObj: {
        //表格数据
        loading2: false,
        totalRow: 0,
        tableData: [],
        tableLabel: [
          //列表表头
          { prop: "mobile", showName: "手机号码", fixed: false, width: "120" },
          { prop: "msgid", showName: "消息ID", fixed: false, width: "200", copy: true },
          { prop: "temId", showName: "模板ID", fixed: false, width: "180" },
          { prop: "content", showName: "短信内容", width: 600, fixed: false },
          { prop: "chargeNum", showName: "计费条数", width: 100, fixed: false },
          {
            prop: "sendTime",
            showName: "发送时间",
            fixed: false,
            width: "170",
          },
          // {prop:"isReceipt",showName:'是否回执'}
          {
            prop: "reportTime",
            showName: "状态上报时间",
            fixed: false,
            width: "170",
          },
          {
            prop: "smsStatus",
            showName: "发送状态",
            formatData: function (val) {
              if (val == "1") {
                return (val = "成功");
              } else if (val == "2") {
                return (val = "失败");
              } else {
                return (val = "待返回");
              }
            },
            width: "100",
            fixed: false,
          },
          { prop: "originalCode", showName: "备注", fixed: false },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "180", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },

      },
      exportShow: false, //是否显示导出按钮
      ruleForm: {
        decode: "0",
      },
      rules: {

      },
      emailInfo: {
        email: "",
        username: ""
      }
    };
  },
  created() {
    bus.$on("closeVideo", (msg) => {
      this.resetVideo = msg;
    });
    this.$api.get(
      this.API.cpus + "consumerclientinfo/getClientInfo",
      null,
      (res) => {
        // console.log(res.data.isDateState,'111');
        this.isDateState = res.data.isDateState;
        if (this.isDateState == 2) {
          this.tableDataObj.tableLabel.splice(5, 6);
          // this.$refs["SHformList"].resetFields();
          Object.assign(this.formData, this.form);
          this.tableDataObj.tableLabel.push(
            {
              prop: "createTime",
              showName: "提交时间",
              fixed: false,
              width: "170",
            },
            {
              prop: "sendTime",
              showName: "发送时间",
              fixed: false,
              width: "170",
            },
            {
              prop: "reportTime",
              showName: "状态上报时间",
              fixed: false,
              width: "170",
            });
        } else {
          this.tableDataObj.tableLabel.splice(5, 6);
          // this.$refs["SHformList"].resetFields();
          Object.assign(this.formData, this.form);
          
          this.tableDataObj.tableLabel.push(
            {
              prop: "createTime",
              showName: "提交时间",
              fixed: false,
              width: "170",
            },
            {
              prop: "sendTime",
              showName: "发送时间",
              fixed: false,
              width: "170",
            },
            {
              prop: "reportTime",
              showName: "状态上报时间",
              fixed: false,
              width: "170",
            },
            {
              prop: "smsStatus",
              showName: "发送状态",
              formatData: function (val) {
                if (val == "1") {
                  return (val = "成功");
                } else if (val == "2") {
                  return (val = "失败");
                } else {
                  return (val = "待返回");
                }
              },
              width: "100",
              fixed: false,
            },
            { prop: "originalCode", showName: "备注", fixed: false }
          );
        }
      }
    );
    this.getLabel();
  },
  methods: {
    //发送请求
    sendReport(flag) {
      let data = Object.assign({}, this.formData);
      data.flag = flag;
      this.$api.post(this.API.cpus + "statistics/esSmsPage", data, (res) => {
        this.tableDataObj.tableData = res.data.records;
        this.tableDataObj.totalRow = res.data.total;
        this.tableDataObj.loading2 = false;
      });
    },
    //获取发送列表数据
    getdate() {
      this.tableDataObj.loading2 = true;
      if (this.specificTime === "") {
        //选择时间范围
        this.sendReport("5");
      } else {
        //选择其他
        this.sendReport(this.specificTime);
      }
    },
    //获取标签
    getLabel() {
      this.$api.get(this.API.cpus + "consumerlabel/list", {}, (res) => {
        this.dynamicTags = res.data;

      });
    },
    selectBlur(e) {
      this.form.source = e.target.value
    },
    // 发送时间
    getTimeOperating(val) {
      if (val) {
        this.form.sendBeginTime = moment(val[0]).format("YYYY-MM-DD HH:mm:ss");
        this.form.sendEndTime =  moment(val[1]).format("YYYY-MM-DD HH:mm:ss");
      } else {
        this.form.sendBeginTime = "";
        this.form.sendEndTime = "";
      }
    },
    //查询 （发送）
    querySending() {
      Object.assign(this.formData, this.form);
      if (this.specificTime === "") {
        //选择时间范围
        this.sendReport("5");
      } else {
        //选择其他
        this.sendReport(this.specificTime);
      }
    },
    //重置 （发送）
    resetSending(formName) {
      this.$refs[formName].resetFields();
      (this.form.sendBeginTime = moment()
        .subtract(30, "days")
        .format("YYYY-MM-DD 00:00:00")),
        (this.form.sendEndTime = moment().format("YYYY-MM-DD 23:59:59")),
        // this.form.sendBeginTime=''
        // this.form.sendEndTime=''
        Object.assign(this.formData, this.form);
      if (this.specificTime === "") {
        //选择时间范围
        this.sendReport("5");
      } else {
        //选择其他
        this.sendReport(this.specificTime);
      }
    },

    // 刷新列表
    refreshList() {
      this.getdate();
    },
    handleClose() {
      this.exportFlag = false
      this.exportShow = false
    },
    getLoginPhone() {
      this.$api.post(
        this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
        {},
        (res) => {
          if (res.code == 200) {
            this.phoneList = res.data.data;
          }
        }
      );
    },
    // handleClose() {
    //   this.exportShow = false
    // },
    exportFn(obj) {
      this.$api.post(this.API.cpus + "statistics/export", obj, (res) => {
        if (res.code == 200) {
          this.exportShow = false
          this.$message({
            type: "success",
            duration: "2000",
            message: "已加入到文件下载中心!",
          });
          this.$router.push('/FileExport');
        }
      });
    },
    //导出（发送）
    exportNums() {
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: "列表无数据，不可导出！",
          type: "warning",
        });
      } else {
        this.$api.get(this.API.cpus + "statistics/exportDecode/check", {}, (res) => {
          if (res.code == 200) {
            this.emailInfo.email = res.data.email;
            this.emailInfo.username = res.data.username;
            if (res.data.decode) {
              this.exportShow = true
            } else {
              let data = {};
              Object.assign(data, this.form);
              if (this.specificTime === "") {
                //选择时间范围
                data.flag = "5";
              } else {
                //选择其他
                data.flag = this.specificTime;
              }
              this.exportFn(data)
            }
          }
        });
      }
    },
    submitExport() {
      let data = {};
      Object.assign(data, this.form);
      data.decode = this.ruleForm.decode == 0 ? false : true;
      if (this.specificTime === "") {
        //选择时间范围
        data.flag = "5";
      } else {
        //选择其他
        data.flag = this.specificTime;
      }
      this.exportFn(data)
    },
    //获取分页的每页数量
    handleSizeChange(size) {
      this.formData.pageSize = size;
      if (this.specificTime === "") {
        //选择时间范围
        this.sendReport("5");
      } else {
        //选择其他
        this.sendReport(this.specificTime);
      }
    },
    //获取分页的第几页
    handleCurrentChange(currentPage) {
      this.formData.currentPage = currentPage;
      if (this.specificTime === "") {
        //选择时间范围
        this.sendReport("5");
      } else {
        //选择其他
        this.sendReport(this.specificTime);
      }
    },
    handPhone(val, index) {
      // console.log(index);
      // console.log(this.tableDataObj.tableData);
      this.$api.post(
        this.API.upms + "/generatekey/decryptMobile",
        {
          keyId: val.keyId,
          smsInfoId: val.smsInfoId,
          cipherMobile: val.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data;
            // this.resetVideo = true;
          } else if (res.code == 4004002) {
            common.fetchData().then((res) => {
              if (res.code == 200) {
                if (res.data.isAdmin == 1) {
                  this.resetVideo = true;
                  this.infoData = res.data;
                } else {
                  this.$message({
                    message: '您今日解密次数已超限，如需重置解密次数，请联系管理员！',
                    type: "warning",
                  });
                }
              } else {
                this.$message({
                  message: res.msg,
                  type: "error",
                });
              }

            })
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
          // this.tableDataObj.tableData[index].mobile=res.data
        }
      );
      // console.log(val);
    },
  },
  mounted() {
    this.$api.get(
      this.API.cpus + "consumerclientinfo/getClientInfo",
      null,
      (res) => {
        // console.log(res.data.isDateState,'111');
        this.isDateState = res.data.isDateState;
      }
    );
  },
  //     activated(){
  //        this.sendReport('1')
  //    },
  watch: {
    //监听查询框的值是否改变
    // formData:{
    //     handler(val){
    //         this.getdate();
    //     },
    //     deep:true
    // },

    //监听(今天，昨天，近一年)的改变
    specificTime: {
      handler(val) {
        // console.log(1111);
        if (val == 1) {
          this.form.sendBeginTime = "";
          this.form.sendEndTime = "";
          Object.assign(this.formData, this.form);
          this.tableDataObj.tableLabel.splice(8, 1);
        } else {
          //     sendBeginTime:moment().subtract(3, "days").format("YYYY-MM-DD"),
          // sendEndTime:moment().format("YYYY-MM-DD"),
          (this.form.sendBeginTime = moment()
            .subtract(30, "days")
            .format("YYYY-MM-DD 00:00:00")),
            (this.form.sendEndTime = moment().format("YYYY-MM-DD 23:59:59"));
          Object.assign(this.formData, this.form);
        }
        if (val == 1 && this.tableDataObj.tableLabel.length != 6) {
          // this.$refs['SHformList'].resetFields();
          Object.assign(this.formData, this.form);
          // this.tableDataObj.tableLabel.splice(5,8)
          // this.tableDataObj.tableLabel.push({prop:"isReceipt",showName:'是否回执',formatData: function(val) {
          //     if(val == '0'){
          //         return val='未回执'
          //     }else if(val == '1'){
          //         return val='已回执'
          //     }
          // },width:'100',fixed:false});
        } else if (val == 2 || val == 1) {
          if (this.isDateState == 2) {
            this.tableDataObj.tableLabel.splice(5, 6);
            // this.$refs['SHformList'].resetFields();
            Object.assign(this.formData, this.form);
            this.tableDataObj.tableLabel.push(
              {
                prop: "createTime",
                showName: "提交时间",
                fixed: false,
                width: "170",
              },
              {
                prop: "sendTime",
                showName: "发送时间",
                fixed: false,
                width: "170",
              },
              {
                prop: "reportTime",
                showName: "状态上报时间",
                fixed: false,
                width: "170",
              });
          } else {
            this.tableDataObj.tableLabel.splice(5, 6);
            // this.$refs['SHformList'].resetFields();
            Object.assign(this.formData, this.form);
            this.tableDataObj.tableLabel.push(
              {
                prop: "createTime",
                showName: "提交时间",
                fixed: false,
                width: "170",
              },
              {
                prop: "sendTime",
                showName: "发送时间",
                fixed: false,
                width: "170",
              },
              {
                prop: "reportTime",
                showName: "状态上报时间",
                fixed: false,
                width: "170",
              },
              {
                prop: "smsStatus",
                showName: "发送状态",
                formatData: function (val) {
                  if (val == "1") {
                    return (val = "成功");
                  } else if (val == "2") {
                    return (val = "失败");
                  } else {
                    return (val = "待返回");
                  }
                },
                width: "100",
                fixed: false,
              },
              { prop: "originalCode", showName: "备注", fixed: false },
              { prop: "source", showName: "标签", fixed: false, width: "140" },
            );
          }
        }
        this.getdate();
      },
      deep: true,
      immediate: true,
    },
    AddBlacks(val) {
      if (val == false) {
        this.AddBlacksStatus = false;
        // 添加安全检查，确保表单引用存在
        this.$nextTick(() => {
          if (this.$refs.AddBlackformRef && this.$refs.AddBlackformRef.resetFields) {
            this.$refs.AddBlackformRef.resetFields();
          }
        });
      }
    },
  },
};
</script>

<style lang="less" scoped>
// 引入模板管理通用样式
@import '~@/styles/template-common.less';

/* ShortMessageRecording2 特有样式 */
.simple-senddetails-page {
  min-height: 100vh;
  background: #fafafa;
  padding: 0;
}

/* 搜索控件样式 */
.search-controls {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.time-controls {
  display: flex;
  align-items: center;
  gap: 16px;
  flex-wrap: wrap;
}

.filter-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.filter-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.control-label {
  font-size: 14px;
  color: #666;
  font-weight: 500;
  white-space: nowrap;
}

.time-radio-group {
  /deep/ .el-radio-button__inner {
    border-radius: 6px;
    border: 1px solid #dcdfe6;
    background: #fff;
    color: #606266;

    &:hover {
      color: #409eff;
      border-color: #c6e2ff;
    }
  }

  /deep/ .el-radio-button__orig-radio:checked + .el-radio-button__inner {
    background: #409eff;
    border-color: #409eff;
    color: #fff;
  }
}

.date-picker {
  min-width: 320px;
}

.filter-input {
  min-width: 200px;
}

.filter-select {
  min-width: 200px;
}

.filter-actions {
  display: flex;
  gap: 12px;
  margin-left: auto;

  .search-btn {
    background: #409eff;
    border-color: #409eff;

    &:hover {
      background: #66b1ff;
      border-color: #66b1ff;
    }
  }

  .reset-btn {
    &:hover {
      color: #409eff;
      border-color: #c6e2ff;
      background-color: #ecf5ff;
    }
  }
}

.table-subtitle {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* 导出弹窗样式 */
.export-dialog {
  /deep/ .el-dialog {
    border-radius: 12px;
  }

  /deep/ .el-dialog__header {
    background: #f8f9fa;
    border-radius: 12px 12px 0 0;
    padding: 20px 24px;
    border-bottom: 1px solid #f0f0f0;
  }

  /deep/ .el-dialog__title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }

  /deep/ .el-dialog__body {
    padding: 24px;
  }
}

.dialog-content {
  .export-form {
    margin: 0;
  }
}

.export-radio-group {
  display: flex;
  flex-direction: column;
  gap: 16px;

  .export-radio {
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    padding: 12px;
    border: 1px solid #e4e7ed;
    border-radius: 8px;
    margin-right: 0;

    &:hover {
      border-color: #c6e2ff;
      background-color: #f5f7fa;
    }

    /deep/ .el-radio__input.is-checked + .el-radio__label {
      color: #409eff;
    }

    /deep/ .el-radio__input.is-checked {
      .el-radio__inner {
        border-color: #409eff;
        background: #409eff;
      }
    }

    .radio-label {
      font-weight: 500;
      font-size: 14px;
      margin-bottom: 4px;
    }

    .radio-desc {
      font-size: 12px;
      color: #909399;
      margin-left: 24px;
    }
  }
}

.export-notice {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 12px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 6px;
  margin-top: 16px;

  .notice-icon {
    color: #f56c6c;
    margin-top: 2px;
    flex-shrink: 0;
  }

  .notice-text {
    font-size: 13px;
    color: #f56c6c;
    line-height: 1.4;
  }
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .time-controls, .filter-controls {
    flex-direction: column;
    align-items: flex-start;
  }

  .filter-item {
    width: 100%;
  }

  .filter-actions {
    margin-left: 0;
    width: 100%;
    justify-content: flex-start;
  }
}

@media (max-width: 768px) {
  .date-picker {
    min-width: 280px;
  }

  .export-radio-group {
    .export-radio {
      .radio-desc {
        margin-left: 0;
      }
    }
  }
}
</style>
<style>
/* 全局样式覆盖 */
.simple-senddetails-page .el-table--small td,
.simple-senddetails-page .el-table--small th {
  padding-top: 8px;
  padding-bottom: 8px;
}

.simple-senddetails-page .el-date-editor.el-input {
  width: auto;
}

.simple-senddetails-page .el-date-editor .el-range__icon {
  font-size: 14px;
  color: #c0c4cc;
}

.simple-senddetails-page .el-date-editor .el-range-input {
  background-color: transparent;
  border: 0;
  outline: none;
  display: inline-block;
  font-size: 14px;
  margin: 0;
  padding: 0;
  width: 39%;
}
</style>
