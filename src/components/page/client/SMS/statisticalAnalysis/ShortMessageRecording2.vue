<template>
  <div id="shortMessage" class="bag">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 发送详情</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="fillet ShortMessageRecording-chart-box">
      <div class="ShortMessageRecording-chart-title">
        <slot name="sele"></slot>
        <el-radio-group v-model="specificTime" size="small" text-color="#fff" fill="#16a589">
          <el-radio-button label="1">近1小时</el-radio-button>
          <el-radio-button label="2" class="threeDay">近一个月</el-radio-button>
        </el-radio-group>
      </div>
      <div class="short-message-recording-type">
        <el-form ref="SHformList" :model="form" label-width="86px" class="query-frame">
          <el-form-item label="手机号码" prop="mobile">
            <el-input v-model="form.mobile" placeholder="请输入手机号" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="发送状态" prop="smsStatus" v-if="this.$store.state.isDateState == 1 && specificTime == 2">
            <el-select v-model="form.smsStatus" placeholder="发送状态" class="input-w">
              <el-option label="全部" value=""></el-option>
              <el-option label="成功" value="1"></el-option>
              <el-option label="失败" value="2"></el-option>
              <el-option label="待返回" value="3"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item v-if="specificTime == 2" label="短信签名" prop="signature">
            <el-input v-model="form.signature" class="input-w"></el-input>
          </el-form-item>
          <el-form-item v-if="specificTime == 2" label="短信内容" prop="content">
            <el-input v-model="form.content" class="input-w"></el-input>
          </el-form-item>
          <el-form-item v-if="specificTime == 2" label="标签" prop="source">
            <el-select class="input-w" v-model="form.source" placeholder="" filterable @blur="selectBlur">
              <el-option v-for="item in dynamicTags" :key="item.id" :label="item.label" :value="item.label"></el-option>
            </el-select>
            <!-- <el-input v-model="form.source" class="input-w"></el-input> -->
          </el-form-item>
          <el-form-item label="消息ID" prop="msgid">
            <el-input v-model="form.msgid" placeholder="" class="input-w"></el-input>
            <!-- <el-select v-model="form.temId" placeholder="请选择" class="input-w">
                                    <el-option label="全部" value=''></el-option>
                                    <el-option label="自定义内容" value='0'  v-if="this.$store.state.custom == 1"></el-option>
                                    <el-option
                                    v-for="item in templateName"
                                    :key="item.temId"
                                    :label="item.temName"
                                    :value="item.temId">
                                    </el-option>
                                </el-select> -->
          </el-form-item>
          <el-form-item v-if="specificTime == 2" label="模板ID" prop="temId">
            <el-input v-model="form.temId" placeholder="" class="input-w"></el-input>
          </el-form-item>
          <el-form-item v-if="specificTime == 2" label="发送时间" prop="time1">
            <el-date-picker v-model="form.time1" type="datetimerange"
              range-separator="-" :picker-options="pickerOptions" :clearable="false" @change="getTimeOperating"
              start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="querySending()">查 询</el-button>
            <el-button type="primary" plain @click="resetSending('SHformList')">重 置</el-button>
            <el-button type="primary" v-if="specificTime == 2" @click="exportNums()">导出数据</el-button>
          </el-form-item>
        </el-form>
        <div style="padding-bottom: 40px">
          <table-tem :tableDataObj="tableDataObj" tips="view" @handPhone="handPhone"></table-tem>
          <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
            style="background: #fff; padding: 10px 0; text-align: right">
            <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="1" :page-size="formData.pageSize" :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.totalRow">
            </el-pagination>
          </el-col>
        </div>
        <!-- <el-dialog
            title="手机验证"
            :visible.sync="exportFlag"
            width="40%"
            top="30vh"
        >  
            <DownLoadExport ref="ExportChild" :specificTime='specificTime' :formData1='form' productType='1' :isDownload='form.isDownload' :phoneList="phoneList"/>
        </el-dialog> -->
        <el-dialog title="导出数据" :visible.sync="exportShow" width="30%" :before-close="handleClose">
          <el-form :model="ruleForm" :rules="rules" ref="ruleForm" label-width="100px" class="demo-ruleForm">
            <el-form-item label="导出类型" prop="decode">
              <el-radio-group v-model="ruleForm.decode">
                <el-radio label="0">掩码</el-radio>
                <el-radio label="1">明码</el-radio>
              </el-radio-group>
            </el-form-item>
            <div style="margin-left: 28px;color: #F56C6C;" v-if="ruleForm.decode == '1'">
              tips: 明码文件将会发送您的{{ emailInfo.email }}邮箱，请注意查收。
            </div>
          </el-form>
          <span slot="footer" class="dialog-footer">
            <el-button @click="exportShow = false">取 消</el-button>
            <el-button type="primary" @click="submitExport">{{ ruleForm.decode == '0' ? '下载' : '发送' }}</el-button>
          </span>
        </el-dialog>
        <ResetNumberVue v-if="resetVideo" ref="resetNumber" :infoData="infoData" :visible="resetVideo"></ResetNumberVue>
      </div>
    </div>
  </div>
</template>

<script>
import TableTem from "@/components/publicComponents/TableTem";
import DatePlugin from "@/components/publicComponents/DatePlugin";
import DownLoadExport from "@/components/publicComponents/downLodExport"
import ResetNumberVue from "@/components/publicComponents/ResetNumber.vue";
import bus from "../../../../common/bus";
import common from "../../../../../assets/js/common";
import moment from "moment";
export default {
  name: "ShortMessageRecording2",
  components: {
    TableTem,
    DatePlugin,
    DownLoadExport,
    ResetNumberVue
  },
  data() {
    return {
      name: "ShortMessageRecording2",
      pickerOptions: {
        disabledDate(time) {
          return (
            time.getTime() < Date.now() - 30 * 24 * 3600 * 1000 ||
            time.getTime() > Date.now()
          );
        },
      },
      phoneList: [],
      infoData:{},
      exportFlag: false,
      //模板名称
      templateName: [],
      //标签
      dynamicTags: [],
      //复选框的值
      selectId: "",
      // 单条存值
      AddBlackVal: "",
      // 备注
      AddBlackform: {
        remark: "",
      },
      AddBlacks: false,
      AddBlacksStatus: false,
      resetVideo: false,
      isDateState: "",
      //发送查询的值
      form: {
        mobile: "",
        msgid: "",
        smsStatus: "",
        signature: "",
        content: "",
        temId: "",
        source: "",
        sendBeginTime: moment()
          .subtract(30, "days")
          .format("YYYY-MM-DD 00:00:00"),
        sendEndTime: moment().format("YYYY-MM-DD 23:59:59"),
        time1: [
          moment().subtract(30, "days").format("YYYY-MM-DD 00:00:00"),
          moment().format("YYYY-MM-DD 23:59:59"),
        ],
        currentPage: 1,
        pageSize: 10,
        time: "",
        isDownload: 2,
      },
      //复制发送查询的值
      formData: {
        mobile: "",
        msgid: "",
        smsStatus: "",
        signature: "",
        content: "",
        temId: "",
        source: "",
        sendBeginTime: moment()
          .subtract(30, "days")
          .format("YYYY-MM-DD 00:00:00"),
        sendEndTime: moment().format("YYYY-MM-DD 23:59:59"),
        time1: [
          moment().subtract(30, "days").format("YYYY-MM-DD 00:00:00"),
          moment().format("YYYY-MM-DD 23:59:59"),
        ],
        currentPage: 1,
        pageSize: 10,
        isDownload: 2,
      },
      specificTime: "1", //选择那一天
      tableDataObj: {
        //表格数据
        loading2: false,
        totalRow: 0,
        tableData: [],
        tableLabel: [
          //列表表头
          { prop: "mobile", showName: "手机号码", fixed: false, width: "120" },
          { prop: "msgid", showName: "消息ID", fixed: false, width: "200", copy: true },
          { prop: "temId", showName: "模板ID", fixed: false, width: "180" },
          { prop: "content", showName: "短信内容", width: 600, fixed: false },
          { prop: "chargeNum", showName: "计费条数", width: 70, fixed: false },
          {
            prop: "sendTime",
            showName: "发送时间",
            fixed: false,
            width: "170",
          },
          // {prop:"isReceipt",showName:'是否回执'}
          {
            prop: "reportTime",
            showName: "状态上报时间",
            fixed: false,
            width: "170",
          },
          {
            prop: "smsStatus",
            showName: "发送状态",
            formatData: function (val) {
              if (val == "1") {
                return (val = "成功");
              } else if (val == "2") {
                return (val = "失败");
              } else {
                return (val = "待返回");
              }
            },
            width: "100",
            fixed: false,
          },
          { prop: "originalCode", showName: "备注", fixed: false },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "180", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },

      },
      exportShow: false, //是否显示导出按钮
      ruleForm: {
        decode: "0",
      },
      rules: {

      },
      emailInfo: {
        email: "",
        username: ""
      }
    };
  },
  created() {
    bus.$on("closeVideo", (msg) => {
      this.resetVideo = msg;
    });
    this.$api.get(
      this.API.cpus + "consumerclientinfo/getClientInfo",
      null,
      (res) => {
        // console.log(res.data.isDateState,'111');
        this.isDateState = res.data.isDateState;
        if (this.isDateState == 2) {
          this.tableDataObj.tableLabel.splice(5, 6);
          this.$refs["SHformList"].resetFields();
          Object.assign(this.formData, this.form);
          this.tableDataObj.tableLabel.push(
            {
              prop: "createTime",
              showName: "提交时间",
              fixed: false,
              width: "170",
            },
            {
              prop: "sendTime",
              showName: "发送时间",
              fixed: false,
              width: "170",
            },
            {
              prop: "reportTime",
              showName: "状态上报时间",
              fixed: false,
              width: "170",
            });
        } else {
          this.tableDataObj.tableLabel.splice(5, 6);
          this.$refs["SHformList"].resetFields();
          Object.assign(this.formData, this.form);
          this.tableDataObj.tableLabel.push(
            {
              prop: "createTime",
              showName: "提交时间",
              fixed: false,
              width: "170",
            },
            {
              prop: "sendTime",
              showName: "发送时间",
              fixed: false,
              width: "170",
            },
            {
              prop: "reportTime",
              showName: "状态上报时间",
              fixed: false,
              width: "170",
            },
            {
              prop: "smsStatus",
              showName: "发送状态",
              formatData: function (val) {
                if (val == "1") {
                  return (val = "成功");
                } else if (val == "2") {
                  return (val = "失败");
                } else {
                  return (val = "待返回");
                }
              },
              width: "100",
              fixed: false,
            },
            { prop: "originalCode", showName: "备注", fixed: false }
          );
        }
      }
    );
    this.getLabel();
  },
  methods: {
    //发送请求
    sendReport(flag) {
      let data = Object.assign({}, this.formData);
      data.flag = flag;
      this.$api.post(this.API.cpus + "statistics/esSmsPage", data, (res) => {
        this.tableDataObj.tableData = res.data.records;
        this.tableDataObj.totalRow = res.data.total;
        this.tableDataObj.loading2 = false;
      });
    },
    //获取发送列表数据
    getdate() {
      this.tableDataObj.loading2 = true;
      if (this.specificTime === "") {
        //选择时间范围
        this.sendReport("5");
      } else {
        //选择其他
        this.sendReport(this.specificTime);
      }
    },
    //获取标签
    getLabel() {
      this.$api.get(this.API.cpus + "consumerlabel/list", {}, (res) => {
        this.dynamicTags = res.data;

      });
    },
    selectBlur(e) {
      this.form.source = e.target.value
    },
    // 发送时间
    getTimeOperating(val) {
      if (val) {
        this.form.sendBeginTime = moment(val[0]).format("YYYY-MM-DD HH:mm:ss");
        this.form.sendEndTime =  moment(val[1]).format("YYYY-MM-DD HH:mm:ss");
      } else {
        this.form.sendBeginTime = "";
        this.form.sendEndTime = "";
      }
    },
    //查询 （发送）
    querySending() {
      Object.assign(this.formData, this.form);
      if (this.specificTime === "") {
        //选择时间范围
        this.sendReport("5");
      } else {
        //选择其他
        this.sendReport(this.specificTime);
      }
    },
    //重置 （发送）
    resetSending(formName) {
      this.$refs[formName].resetFields();
      (this.form.sendBeginTime = moment()
        .subtract(30, "days")
        .format("YYYY-MM-DD 00:00:00")),
        (this.form.sendEndTime = moment().format("YYYY-MM-DD 23:59:59")),
        // this.form.sendBeginTime=''
        // this.form.sendEndTime=''
        Object.assign(this.formData, this.form);
      if (this.specificTime === "") {
        //选择时间范围
        this.sendReport("5");
      } else {
        //选择其他
        this.sendReport(this.specificTime);
      }
    },
    handleClose() {
      this.exportFlag = false
      this.exportShow = false
    },
    getLoginPhone() {
      this.$api.post(
        this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
        {},
        (res) => {
          if (res.code == 200) {
            this.phoneList = res.data.data;
          }
        }
      );
    },
    // handleClose() {
    //   this.exportShow = false
    // },
    exportFn(obj) {
      this.$api.post(this.API.cpus + "statistics/export", obj, (res) => {
        if (res.code == 200) {
          this.exportShow = false
          this.$message({
            type: "success",
            duration: "2000",
            message: "已加入到文件下载中心!",
          });
          this.$router.push('/FileExport');
        }
      });
    },
    //导出（发送）
    exportNums() {
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: "列表无数据，不可导出！",
          type: "warning",
        });
      } else {
        this.$api.get(this.API.cpus + "statistics/exportDecode/check", {}, (res) => {
          if (res.code == 200) {
            this.emailInfo.email = res.data.email;
            this.emailInfo.username = res.data.username;
            if (res.data.decode) {
              this.exportShow = true
            } else {
              let data = {};
              Object.assign(data, this.form);
              if (this.specificTime === "") {
                //选择时间范围
                data.flag = "5";
              } else {
                //选择其他
                data.flag = this.specificTime;
              }
              this.exportFn(data)
            }
          }
        });
      }
    },
    submitExport() {
      let data = {};
      Object.assign(data, this.form);
      data.decode = this.ruleForm.decode == 0 ? false : true;
      if (this.specificTime === "") {
        //选择时间范围
        data.flag = "5";
      } else {
        //选择其他
        data.flag = this.specificTime;
      }
      this.exportFn(data)
    },
    //获取分页的每页数量
    handleSizeChange(size) {
      this.formData.pageSize = size;
      if (this.specificTime === "") {
        //选择时间范围
        this.sendReport("5");
      } else {
        //选择其他
        this.sendReport(this.specificTime);
      }
    },
    //获取分页的第几页
    handleCurrentChange(currentPage) {
      this.formData.currentPage = currentPage;
      if (this.specificTime === "") {
        //选择时间范围
        this.sendReport("5");
      } else {
        //选择其他
        this.sendReport(this.specificTime);
      }
    },
    handPhone(val, index) {
      // console.log(index);
      // console.log(this.tableDataObj.tableData);
      this.$api.post(
        this.API.upms + "/generatekey/decryptMobile",
        {
          keyId: val.keyId,
          smsInfoId: val.smsInfoId,
          cipherMobile: val.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data;
            // this.resetVideo = true;
          } else if (res.code == 4004002) {
            common.fetchData().then((res) => {
              if (res.code == 200) {
                if (res.data.isAdmin == 1) {
                  this.resetVideo = true;
                  this.infoData = res.data;
                } else {
                  this.$message({
                    message: '您今日解密次数已超限，如需重置解密次数，请联系管理员！',
                    type: "warning",
                  });
                }
              } else {
                this.$message({
                  message: res.msg,
                  type: "error",
                });
              }

            })
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
          // this.tableDataObj.tableData[index].mobile=res.data
        }
      );
      // console.log(val);
    },
  },
  mounted() {
    this.$api.get(
      this.API.cpus + "consumerclientinfo/getClientInfo",
      null,
      (res) => {
        // console.log(res.data.isDateState,'111');
        this.isDateState = res.data.isDateState;
      }
    );
  },
  //     activated(){
  //        this.sendReport('1')
  //    },
  watch: {
    //监听查询框的值是否改变
    // formData:{
    //     handler(val){
    //         this.getdate();
    //     },
    //     deep:true
    // },

    //监听(今天，昨天，近一年)的改变
    specificTime: {
      handler(val) {
        // console.log(1111);
        if (val == 1) {
          this.form.sendBeginTime = "";
          this.form.sendEndTime = "";
          Object.assign(this.formData, this.form);
          this.tableDataObj.tableLabel.splice(8, 1);
        } else {
          //     sendBeginTime:moment().subtract(3, "days").format("YYYY-MM-DD"),
          // sendEndTime:moment().format("YYYY-MM-DD"),
          (this.form.sendBeginTime = moment()
            .subtract(30, "days")
            .format("YYYY-MM-DD 00:00:00")),
            (this.form.sendEndTime = moment().format("YYYY-MM-DD 23:59:59"));
          Object.assign(this.formData, this.form);
        }
        if (val == 1 && this.tableDataObj.tableLabel.length != 6) {
          // this.$refs['SHformList'].resetFields();
          Object.assign(this.formData, this.form);
          // this.tableDataObj.tableLabel.splice(5,8)
          // this.tableDataObj.tableLabel.push({prop:"isReceipt",showName:'是否回执',formatData: function(val) {
          //     if(val == '0'){
          //         return val='未回执'
          //     }else if(val == '1'){
          //         return val='已回执'
          //     }
          // },width:'100',fixed:false});
        } else if (val == 2 || val == 1) {
          if (this.isDateState == 2) {
            this.tableDataObj.tableLabel.splice(5, 6);
            // this.$refs['SHformList'].resetFields();
            Object.assign(this.formData, this.form);
            this.tableDataObj.tableLabel.push(
              {
                prop: "createTime",
                showName: "提交时间",
                fixed: false,
                width: "170",
              },
              {
                prop: "sendTime",
                showName: "发送时间",
                fixed: false,
                width: "170",
              },
              {
                prop: "reportTime",
                showName: "状态上报时间",
                fixed: false,
                width: "170",
              });
          } else {
            this.tableDataObj.tableLabel.splice(5, 6);
            // this.$refs['SHformList'].resetFields();
            Object.assign(this.formData, this.form);
            this.tableDataObj.tableLabel.push(
              {
                prop: "createTime",
                showName: "提交时间",
                fixed: false,
                width: "170",
              },
              {
                prop: "sendTime",
                showName: "发送时间",
                fixed: false,
                width: "170",
              },
              {
                prop: "reportTime",
                showName: "状态上报时间",
                fixed: false,
                width: "170",
              },
              {
                prop: "smsStatus",
                showName: "发送状态",
                formatData: function (val) {
                  if (val == "1") {
                    return (val = "成功");
                  } else if (val == "2") {
                    return (val = "失败");
                  } else {
                    return (val = "待返回");
                  }
                },
                width: "100",
                fixed: false,
              },
              { prop: "originalCode", showName: "备注", fixed: false },
              { prop: "source", showName: "标签", fixed: false, width: "140" },
            );
          }
        }
        this.getdate();
      },
      deep: true,
      immediate: true,
    },
    AddBlacks(val) {
      if (val == false) {
        this.AddBlacksStatus = false;
        this.$refs.AddBlackformRef.resetFields();
      }
    },
  },
};
</script>

<style scoped>
.ShortMessageRecording-chart-box {
  margin: 10px 0;
  padding: 20px 20px 20px 20px;
}

.ShortMessageRecording-chart-title {
  display: flex;
}

.ShortMessageRecording-chart {
  height: 360px;
}

.ShortMessageRecording-title {
  padding-top: 40px;
  font-weight: bold;
}

.look-at-more {
  color: #16a589;
}

.ShortMessageRecording-select {
  position: relative;
  margin-top: 10px;
}

.short-message-recording-type {
  /* margin-top: 40px; */
}

.query-frame {
  margin-top: 20px;
}

.query-frame .el-form-item {
  display: inline-block;
  margin-bottom: 12px;
}
</style>
<style>
/* .threeDay .el-radio-button__inner{
    border-right: 0 ;
} */
.ShortMessageRecording-chart-box .el-radio-button:last-child .el-radio-button__inner {
  border-radius: 0;
}

.ShortMessageRecording-chart-box .el-range-editor.el-input__inner {
  border-radius: 0px 4px 4px 0;
}

.ShortMessageRecording-chart-box .el-table--small td,
.ShortMessageRecording-chart-box .el-table--small th {
  padding-top: 8px;
  padding-bottom: 8px;
}
</style>
