<template>
  <div id="webSend" class="bag">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> web发送记录</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="fillet sendMsg-box">
      <!-- <el-button type="primary" style="margin-right:10px;"  @click="handleSendMsg">发送短信</el-button> -->
      <div class="sendMsg-list-header">
        web发送记录
        <!-- <span style="color: red; margin-left: 20px; font-size: 13px">（短信发送数据量大时，统计会有延迟，请发送一小时后再查看；发送结果以72小时后的记录为准）</span> -->
        <span style="color: red; margin-left: 20px; font-size: 13px">（短信发送数据量大时，数据统计可能存在延迟，请以实际发送明细为准）</span>
      </div>
      <div>
        <div style="display: flex; padding: 10px 0">
          <el-radio-group v-model="getObject.specificTime" size="small" text-color="#fff" fill="#16a589"
            @change="handleChangeTimeOptions">
            <el-radio-button label="1">今天</el-radio-button>
            <el-radio-button label="2">近7天</el-radio-button>
            <el-radio-button label="3" class="threeDay">近一个月</el-radio-button>
            <!-- <el-radio-button label="4" class="threeDay">近一年</el-radio-button> -->
          </el-radio-group>
          <date-plugin class="search-date" :datePluginValueList="datePluginValueLists"
            @handledatepluginVal="handledatepluginVals"></date-plugin>

        </div>
        <div style="display: flex;align-items: center;">
          <div>
            发送类型
            <el-select v-model="getObject.sendRecord" style="margin-left: 18px;" class="input-w">
              <el-option label="模板发送" value="1"></el-option>
              <el-option label="自定义发送" value="2"></el-option>
              <el-option label="个性化发送" value="3"></el-option>
              <el-option label="自定义变量发送" value="4"></el-option>
            </el-select>
          </div>
          <div style=" display: flex;align-items: center;margin-left: 18px;">
            <span>任务名称</span>
            <el-input style="margin-left: 18px;" v-model="getObject.taskName" class="input-w"></el-input>
          </div>
          <div style="display: flex;align-items: center;margin-left: 18px;">
            <span>短信内容</span>
            <el-input style="margin-left: 18px;" v-model="getObject.content" class="input-w"></el-input>
          </div>
        </div>
      </div>

      <!-- 表格和分页 -->
      <div class="sendMsg-table" style="padding-bottom: 40px" v-if="getObject.sendRecord == '1'">
        <table-tem :tableDataObj="tableDataObj" tips="view" @handelOptionButton="handelOptionButton"></table-tem>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
          style="background: #fff; padding: 10px 0; text-align: right">
          <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :current-page="tableDataObj.configure.currentPage" :page-size="tableDataObj.configure.pageSize"
            :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total">
          </el-pagination>
        </el-col>
      </div>
      <div v-else>
        <div class="sendMsg-table" style="padding-bottom: 40px">
          <table-tem :tableDataObj="tableDataObj0" tips="view" @handelOptionButton="handelOptionButton"></table-tem>
          <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
            style="background: #fff; padding: 10px 0; text-align: right">
            <el-pagination class="page_bottom" @size-change="handleSizeChange0" @current-change="handleCurrentChange0"
              :current-page="tableDataObj0.configure.currentPage" :page-size="tableDataObj0.configure.pageSize"
              :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj0.total">
            </el-pagination>
          </el-col>
        </div>
      </div>
    </div>
    <!-- 报告的弹出框 -->
    <el-dialog title="任务报告查看" :visible.sync="sendMsgDialogVisible" width="860px">
      <!-- <template v-if="this.$store.state.isDateState == 1">
                <div style="display:inline-block;width:49%;text-align:center; border-right:1px solid #f7f7f7;margin-top:-20px;">
                    <PieChart id="pie2" width="390px" height="340px" :basicOption="basicOption1" ></PieChart>
                </div>
                <div style="display:inline-block;width:49%;text-align:center;margin-top:-20px;">
                    <PieChart id="pie1" width="390px" height="340px" :basicOption="basicOption2" ></PieChart>
                </div>
                <span style="display:block;padding:0 0 10px 0;color:#333;"> 发送明细列表</span>
                <table-tem :tableDataObj="tableDataObj1" ></table-tem>
            </template>
            <template v-if="this.$store.state.isDateState == 2">
                <div style="text-align:center;margin-top:-20px;">
                    <PieChart id="pie1" height="300px" :basicOption="basicOption2" ></PieChart>
                </div>
            </template> -->
      <template>
        <div style="text-align: center; margin-top: -20px">
          <PieChart id="pie1" height="300px" :basicOption="basicOption2"></PieChart>
        </div>
      </template>
      <span style="display: block; padding: 20px 0 10px 0; color: #333">
        回执失败代码分析列表</span>
      <table-tem :tableDataObj="tableDataObj2"></table-tem>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="sendMsgDialogVisible = false">知道了</el-button>
      </div>
    </el-dialog>
    <!-- 发送短信弹出框 -->
    <el-dialog title="短信发送" :visible.sync="sendMsgDialogShow" width="900px" :close-on-click-modal="false">
      <el-row style="padding-bottom: 20px">
        <el-col :span="15" style="padding-right: 40px">
          <el-steps :active="stepsActive" simple>
            <el-step title="短信内容设置" icon="el-icon-edit"></el-step>
            <el-step title="确认发送" icon="el-icon-upload"></el-step>
          </el-steps>
          <el-form :model="configurationItem.formData" :rules="configurationItem.formRule" ref="configurations"
            v-show="stepsActive == 1" label-width="95px" style="padding: 32px 8px 0 8px">
            <el-form-item label="模板名称" prop="tempId">
              <el-select v-model="configurationItem.formData.tempId" placeholder="请选择">
                <el-option label="自定义内容" value="-1" v-if="this.$store.state.custom == 1"></el-option>
                <!-- <el-option label="自定义内容" value="-1" ></el-option> -->
                <el-option v-for="item in temOptions" :key="item.tempId" :label="item.temName"
                  :value="item.temId"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="自定义内容" v-show="configurationItem.formData.tempId == '-1'" prop="content">
              <el-input type="textarea" v-model="configurationItem.formData.content" class="textareas"></el-input>
            </el-form-item>
            <el-form-item label="签名名称" v-show="configurationItem.formData.tempId != '-1'" placeholder="请选择签名"
              prop="signatureId">
              <el-select v-model="configurationItem.formData.signatureId" filterable @change="changeLocationValue">
                <el-option v-for="item in sigOptions" :key="item.signatureId" :label="item.signature"
                  :value="item.signatureId"></el-option>
              </el-select>
            </el-form-item>
            <el-button type="primary" v-show="configurationItem.formData.tempId == '-1'" icon="el-icon-refresh" plain
              @click="shortReset()" style="margin-left: 95px; margin-bottom: 6px">短链转换</el-button>
            <el-form-item label="发送时间" prop="sendType">
              <el-radio-group v-model="configurationItem.formData.sendType">
                <el-radio label="1">立即发送</el-radio>
                <el-radio label="2">定时发送</el-radio>
              </el-radio-group>
            </el-form-item>
            <div v-if="configurationItem.formData.sendType == 2" style="padding-bottom: 18px" class="send-time-sel"
              prop="sendTime">
              <span style="
                        display: inline-block;
                        width: 83px;
                        text-align: right;
                        padding-right: 10px;
                      ">定时时间</span>
              <date-plugin class="Mail-search-date" :datePluginValueList="datePluginValueList"
                @handledatepluginVal="handledatepluginVal" style="width: 383px"></date-plugin>
            </div>
            <!-- <div style="position:relative;">
                            <span style="display:inline-block;width:83px;text-align:right;padding-right:11px;position:absolute;">发送对象</span>
                            <file-upload style="display: inline-block; margin-left:97px"
                                :action="this.API.cpus+'consumersmsinfo/upload'"
                                :limit= 1
                                :showfileList=true
                                :fileStyle='fileStyle'
                                :del='del1'
                                tip='格式要求：支持.xlsx .xls 等格式'
                                @fileup="fileup"
                                @fileupres="fileupres"
                            >选择上传文件</file-upload>
                        </div> -->
            <!-- <div class="send-upload-tips">建议单次最大上传10万行，内容格式请参考模板<span style="cursor: pointer;" @click="downloadTem">模板下载</span></div> -->
            <div class="sms-first-steps-btns">
              <el-button type="primary" v-if="sendLoading == true" @click="submissionItem('configurations')"
                style="padding: 10px 20px">提 交</el-button>
              <el-button type="primary" v-if="sendLoading == false" :loading="true"
                style="padding: 10px 40px">提交数据中</el-button>
              <el-button @click="sendMsgDialogShow = false" style="padding: 10px 20px">取 消</el-button>
            </div>
          </el-form>
          <div v-show="stepsActive == 2" class="sms-seconnd-steps-box">
            <div style="padding-bottom: 5px">
              您本次提交号码数
              <span style="color: #16a589">{{ sendNumber }}</span> 个
            </div>
            <div style="padding-top: 10px">
              发送内容：
              <div style="color: #999; padding-top: 6px; word-wrap: break-word">
                {{ sendContent }}
              </div>
            </div>
            <div class="sms-seconnd-steps-btns">
              <el-button @click="stepsActive = 1" style="padding: 10px 20px"
                v-if="fullscreenLoading == true">上一步</el-button>
              <el-button type="primary" @click="ConfirmSending()" style="padding: 10px 20px"
                v-if="fullscreenLoading == true">确定发送</el-button>
              <el-button type="primary" :loading="true" style="padding: 10px 40px"
                v-if="fullscreenLoading == false">短信发送中</el-button>
            </div>
          </div>
        </el-col>
        <el-col :span="9" style="border-left: 1px dashed #ccc; position: relative">
          <div class="send-mobel-box">
            <img src="../../../../../assets/images/phone.png" alt="" />
          </div>
          <p style="
                    width: 80px;
                    position: absolute;
                    padding-left: 76px;
                    top: 112px;
                    left: 70px;
                    font-size: 12px;
                    color: #666;
                  ">
            短信
          </p>
          <div style="width: 80px; position: absolute; left: 72px; top: 259px">
            <span style="
                      width: 17px;
                      height: 20px;
                      position: absolute;
                      left: -4px;
                      top: 24px;
                      border-radius: 100% 0px 80% 0px;
                      background: rgb(234, 234, 234);
                    "></span>
            <span style="
                      width: 17px;
                      height: 20px;
                      position: absolute;
                      left: -10px;
                      top: 24px;
                      border-radius: 100% 0px 100% 0px;
                      background: #fff;
                    "></span>
          </div>
          <el-scrollbar class="sms-content-exhibition">
            <div style="width: 154px; word-wrap: break-word">
              {{ phoneCenten }}
            </div>
          </el-scrollbar>
          <div style="font-size: 12px; text-align: center">
            当前发送内容
            <span style="color: #d20707">{{ SMScount.smswordNum }}</span>
            个字，预计发送条数约为
            <span style="color: #d20707">{{ SMScount.smssTrip }}</span> 条短信
          </div>
          <div style="font-size: 12px; text-align: center; color: #d20707">
            (实际发送时，如有模板变量会影响计费条数，请注意关注)
          </div>
        </el-col>
      </el-row>
    </el-dialog>
    <!-- 短链转换 -->
    <el-dialog title="短链转换" :visible.sync="shortVisible" width="520px">
      <div class="short-box">
        <p class="short-title" style="padding-top: 10px">长网址链接</p>
        <el-input placeholder="请输入长链接" v-model="originalUrl" class="width-l">
          <el-button slot="append" type="primary" icon="el-icon-refresh" @click="transformation()">转换</el-button>
        </el-input>
        <div class="font-sizes font-sizes1">
          <span style="color: red">* </span>我们可以帮您把长链接压缩，让您可以输入更多的内容。
        </div>
        <div class="font-sizes">
          <span style="color: red">* </span>插入短信内容中时，将在链接前后生成空格符号，以防止出现手机短信客户端不识别链接的情况。
        </div>
      </div>
      <div class="short-box">
        <p class="short-title" style="padding-top: 20px">短网址链接</p>
        <el-input v-model="shortConUrl" class="width-l" :disabled="true">
          <el-button slot="append" type="primary" @click="handlePreview()" icon="el-icon-share">预览</el-button>
        </el-input>
      </div>
      <div style="text-align: right; margin-top: 16px">
        <el-button @click="HandelCencals()">取 消</el-button>
        <el-button type="primary" @click="shortConDetermine()">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { mapState, mapMutations, mapActions } from "vuex";
import TableTem from "@/components/publicComponents/TableTem"; //列表
import DatePlugin from "@/components/publicComponents/DatePlugin"; //日期
import PieChart from "@/components/publicComponents/PieChart"; //饼图
import FileUpload from "@/components/publicComponents/FileUpload"; //文件上传
export default {
  name: "webSend",
  components: {
    TableTem,
    DatePlugin,
    PieChart,
    FileUpload,
  },
  data() {
    //短信内容的签名格式是否正确，或者是否有签名
    var content = (rule, value, callback) => {
      if (value == "") {
        return callback(new Error("请填写自定义内容！"));
      } else {
        let ret = 3;
        let beg = value.indexOf("【");
        let end = value.indexOf("】");
        let lastbeg = value.lastIndexOf("【");
        let lastend = value.lastIndexOf("】");
        let valLength = value.length;
        if (beg > -1 && end > -1 && end > beg && beg == 0) {
          if (beg == 0 && end < 50 && end > 2) {
            callback();
          } else {
            return callback(new Error("请填写正确的签名！"));
          }
        } else if (
          lastbeg > -1 &&
          lastend > -1 &&
          lastend > lastbeg &&
          lastend == valLength - 1
        ) {
          if (
            lastend == valLength - 1 &&
            lastend - lastbeg < 49 &&
            lastend - lastbeg > 2
          ) {
            callback();
          } else {
            return callback(new Error("请填写正确的签名！"));
          }
        } else {
          return callback(new Error("请填写签名！"));
        }
      }
    };
    return {
      name: "webSend",
      shortVisible: false, //短链弹框
      originalUrl: "",
      shortConUrl: "", //短链的URL
      shortCode: "", //短连接的code
      isShort: false, //是否添加了短链
      sendLoading: true, //第一步短信发送的loading按钮
      fullscreenLoading: true, //第二步短信发送的loading按钮
      // sendRecord:'1',//发送记录的选项
      sendMsgDialogVisible: false, //弹出框

      datePluginValueList: {
        //日期参数配置
        type: "datetime",
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7;
          },
        },
        defaultTime: "", //默认起始时刻
        datePluginValue: "",
      },
      datePluginValueLists: {
        //日期选择器
        type: "daterange",
        start: "",
        end: "",
        range: "-",
        clearable: true, //是否开启关闭按钮
        pickerOptions: {
          onPick: ({ maxDate, minDate }) => {
            this.pickerMinDate = minDate.getTime();
            if (maxDate) {
              this.pickerMinDate = "";
            }
          },
          disabledDate: (time) => {
            if (this.pickerMinDate && this.pickerMinDate > Date.now()) {
              return false;
            }
            if (this.pickerMinDate !== "") {
              const day30 = 30 * 24 * 3600 * 1000;
              let maxTime = this.pickerMinDate + day30;
              if (maxTime > Date.now() - 86400000) {
                maxTime = Date.now() - 86400000;
              }
              const minTime = this.pickerMinDate - day30;
              return time.getTime() < minTime || time.getTime() > maxTime;
            }
            return time.getTime() > Date.now() - 86400000;
          },
        },
        datePluginValue: "",
      },
      getObject: {
        specificTime: "1", //选择那一天
        // beginTime:'',
        // endTime:'',
        sendBeginTime: "",
        sendEndTime: "",
        content: "",
        taskName: "",
        msgid: "",
        currentPage: 1,
        pageSize: 10,
        sendRecord: "2",
        // isDownload:2
      },
      tableDataObj: {
        configure: {
          currentPage: 1,
          pageSize: 10,
        },
        total: 0,
        tableData: [],
        //折叠的列表表头
        // tableLabelExpand:[{ prop:"content",showName:'模板内容'}],
        //列表表头
        tableLabel: [
          {
            prop: "sendTime",
            showName: "发送时间",
            width: "160",
            fixed: false,
          },
          { prop: "tempId", showName: "模板ID", width: "80", fixed: false },
          {
            prop: "sendType",
            showName: "短信类型",
            fixed: false,
            width: "100",
            formatData: function (val) {
              switch (val) {
                case 1:
                  return (val = "模板发送");
                case 2:
                  return (val = "自定义发送");
                case 3:
                  return (val = "个性化发送");
              }
            },
          },
          { prop: "content", showName: "模板内容", width: "700", fixed: false },
          {
            prop: "totalNum",
            showName: "提交号码数",
            width: "100",
            fixed: false,
          },
          //   {
          //     prop: "invalidNum",
          //     showName: "无效号码数",
          //     width: "100",
          //     fixed: false,
          //   },
          // {prop:"failNum",showName:'失败号码数',width:'100',fixed:false},
          {
            prop: "waiteNum",
            showName: "待返回数",
            width: "100",
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          isDefaultExpand: false, //是否默认打开
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          // optionWidth:'100',//操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: "更多统计",
            optionMethod: "PresentationSMS",
            icon: "el-icon-success",
          },
        ],
      },
      tableDataObj0: {
        configure: {
          currentPage: 1,
          pageSize: 10,
        },
        total: 0,
        tableData: [],
        //列表表头
        tableLabel: [
          { prop: "taskName", showName: "任务名称", fixed: false, width: "140" },
          {
            prop: "sendTime",
            showName: "发送时间",
            width: "160",
            fixed: false,
          },
          { prop: "content", showName: "发送内容", width: "700" },
          // { prop:"",showName:'模板名称',width:'90',fixed:false,formatData:function(val) {
          //     return val='自定义内容'
          // }},                   v-if="this.$store.state.isDateState == 1"
          // {prop:"sendTime",showName:'发送时间',width:'150',fixed:false},
          {
            prop: "totalNum",
            showName: "提交号码数",
            width: "100",
            fixed: false,
          },
          //   {
          //     prop: "invalidNum",
          //     showName: "无效号码数",
          //     width: "100",
          //     fixed: false,
          //   },
          {
            prop: "sendSuccessNum",
            showName: "发送成功数",
            width: "100",
            fixed: false,
          },
          {
            prop: "successRate",
            showName: "号码成功率",
            width: "100",
            fixed: false,
          },
          // {prop:"failNum",showName:'失败号码数',width:'100',fixed:false},
          {
            prop: "waiteNum",
            showName: "待返回数",
            width: "100",
            fixed: false,
          },
          // {prop:"",showName:'提交状态',width:'90',fixed:false,formatData:function(val) {
          //     return val='成功';
          // }}
        ],
        tableStyle: {
          //列表配置项
          isSelection: false, //是否复选框
          isExpand: false, //是否是折叠的
          isDefaultExpand: false, //是否默认打开
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth:'120',//操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: "更多统计",
            optionMethod: "PresentationSMS",
            icon: "el-icon-success",
          },
        ],
      },
      tableDataObj1: {
        tableData: [],
        tableLabel: [
          {
            prop: "mobileNumber",
            showName: "提交号码数",
            fixed: false,
          },
          {
            prop: "mobileChanrgeNum",
            showName: "提交号码计费数",
            fixed: false,
          },
          {
            prop: "successAmount",
            showName: "成功号计费数",
            fixed: false,
          },
          {
            prop: "failureAmount",
            showName: "失败号计费数",
            width: 100,
            fixed: false,
          },
          {
            prop: "waitNumber",
            showName: "待返回号码计费数",
            width: 120,
            fixed: false,
          },
        ],
        tableStyle: {
          //列表配置项
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          isDefaultExpand: false, //是否默认打开
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          // optionWidth:'120',//操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
      tableDataObj2: {
        tableData: [],
        tableLabel: [
          {
            //列表表头
            prop: "reason",
            showName: "失败原因",
            fixed: false,
          },
          {
            prop: "num",
            showName: "数量",
            fixed: false,
          },
          {
            prop: "percent",
            showName: "占比",
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          height: 240, //是否固定表头
          isExpand: false, //是否是折叠的
          isDefaultExpand: false, //是否默认打开
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          // optionWidth:'120',//操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
      },
      basicOption1: {
        //发送明细图表
        data: [
          {
            value: "",
            name: "成功",
          },
          {
            value: "",
            name: "失败",
          },
          {
            value: "",
            name: "待返回",
          },
        ],
        ledate: ["成功", "失败", "待返回"],
        bgColor: ["#8996E6", "#98D87D", "#FFD86E"],
        radius: "62%",
        title: {
          textStyle: {
            color: "#999",
            fontSize: 14,
          },
          text: "发送明细图表",
          x: "right",
        },
      },
      basicOption2: {
        //回执失败代码分析图表
        data: [],
        ledate: [],
        bgColor: [
          "#8996E6",
          "#49A9EE",
          "#98D87D",
          "#FFD86E",
          "#F3857C",
          "#8996E6",
          "#49A9EE",
          "#98D87D",
          "#FFD86E",
          "#F3857C",
          "#8996E6",
          "#49A9EE",
          "#98D87D",
          "#FFD86E",
          "#F3857C",
        ],
        radius: "62%",
        title: {
          text: "回执失败代码分析图表",
          textStyle: {
            color: "#999",
            fontSize: 14,
          },
          x: "right",
        },
      },
      sendMsgDialogShow: false, //发送短信弹出框
      configurationItem: {
        //发送短信弹出框的值
        formData: {
          tempId: "",
          content: "",
          signatureId: "",
          sendType: "1", //选择立即发送还是定时发送
          sendTime: "", //发送时间的值
          files: "",
          group: "",
          path: "",
        },
        formDatass: {
          tempId: "",
          content: "",
          signatureId: "",
          sendType: "1", //选择立即发送还是定时发送
          sendTime: "", //发送时间的值
          files: "",
          group: "",
          path: "",
        },
        formRule: {
          //验证规则
          content: [
            { required: true, validator: content, trigger: "change" },
            {
              min: 1,
              max: 450,
              message: "长度在 1 到 450 个字符",
              trigger: "change",
            },
          ],
          tempId: [
            { required: true, message: "请选择模板名称", trigger: "change" },
          ],
          signatureId: [
            { required: true, message: "请选择签名", trigger: "change" },
          ],
          sendTime: [
            { required: true, message: "请填写定时时间", trigger: "change" },
          ],
          sendType: [
            { required: true, message: "请选择发送时间", trigger: "change" },
          ],
        },
      },
      fileStyle: {
        size: 245678943234,
        style: ["xlsx", "xls"],
      },
      del1: true, //关闭弹框时清空图片
      phoneCenten: "", //手机短信内容
      copytemCenten: "", //复制模板内容
      copysigCenten: "", //复制签名内容
      stepsActive: 1,
      sendNumber: "", //短信发送条数
      sendContent: "", //发送短信
      sigOptions: [], //签名列表
      temOptions: [], //模板列表
      SMScount: {
        //手机展示短信内容的下方，短信内容的计数
        smswordNum: "0", //短信内容的个数
        smssTrip: "0", //短信可以分为几条
      },
      renwuObj: {
        msgid: "",
      },
      downloadtem: "2", //模板下载（全文，自定义还是变量）（自定义为2）
    };
  },
  created() {
    if (this.$route.query.taskName) {
      this.getObject.taskName = this.$route.query.taskName
      this.getObject.specificTime = '3'
      this.getObject.sendRecord = '4'
    }

    // console.log(11);
  },
  methods: {
    //时间范围选择
    handledatepluginVals: function (val1, val2) {
      if (val1) {
        this.getObject.specificTime = "4";
        this.getObject.sendBeginTime = val1;
        this.getObject.sendEndTime = val2;
      } else {
        this.getObject.specificTime = "1";
        this.getObject.sendBeginTime = "";
        this.getObject.sendEndTime = "";
      }
    },

    handleChangeTimeOptions: function () {
      this.datePluginValueList.datePluginValue = "";
    },
    //定时短信
    goTiming() {
      this.$router.push("/timing");
    },
    //查看web端提交数据
    goWEB() {
      this.$router.push("/filewebNumber");
    },
    //发送短信
    // handleSendMsg(){
    //     // this.del1 = true;
    //     // this.sendMsgDialogShow = true;
    //     this.$router.push('/sendDetails');
    // },
    //获取发送记录列表信息
    getMessageDate() {
      if (this.getObject.sendRecord == "1") {
        let datas = {
          sendType: this.getObject.sendRecord,
          flag: this.getObject.specificTime,
          content: this.getObject.content,
          currentPage: this.tableDataObj.configure.currentPage,
          pageSize: this.tableDataObj.configure.pageSize,
          sendBeginTime: this.getObject.sendBeginTime,
          sendEndTime: this.getObject.sendEndTime,
          taskName: this.getObject.taskName
        };
        this.$api.post(this.API.cpus + "consumertasksms/page", datas, (res) => {
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.total = res.data.total;
        });
      } else {
        let datas = {
          sendType: this.getObject.sendRecord,
          flag: this.getObject.specificTime,
          content: this.getObject.content,
          currentPage: this.tableDataObj0.configure.currentPage,
          pageSize: this.tableDataObj0.configure.pageSize,
          sendBeginTime: this.getObject.sendBeginTime,
          sendEndTime: this.getObject.sendEndTime,
          taskName: this.getObject.taskName
        };
        this.$api.post(this.API.cpus + "consumertasksms/page", datas, (res) => {
          this.tableDataObj0.tableData = res.data.records;
          this.tableDataObj0.total = res.data.total;
        });
      }
    },
    //获得签名列表
    getSignature() {
      this.$api.post(
        this.API.cpus + "signature/signatureList",
        {
          auditStatus: "2",
          currentPage: 1,
          pageSize: 200,
        },
        (res) => {
          this.sigOptions = res.records;
        }
      );
    },
    //获得模板名称列表
    getTemplate() {
      this.$api.post(
        this.API.cpus + "consumersmstemplate/selectClientTemplate",
        {
          currentPage: 1,
          pageSize: 200,
        },
        (res) => {
          this.temOptions = res.records;
        }
      );
    },
    //获得模板名称对应的内容
    getTemplateContent() {
      this.$api.get(
        this.API.cpus +
        "consumersmstemplate/get/" +
        this.configurationItem.formData.tempId,
        {},
        (res) => {
          this.downloadtem = res.data.temFormat; //模板下载类型（1变量，2全文）
          this.copytemCenten = res.data.temContent;
          this.phoneCenten = this.copysigCenten + this.copytemCenten;
        }
      );
    },
    //获取签名下拉框选择的 签名内容
    changeLocationValue(val) {
      let obj = {};
      obj = this.sigOptions.find((item) => {
        return item.signatureId === val;
      });
      this.copysigCenten = obj.signature;
      //手机短信内容展示
      this.phoneCenten = this.copysigCenten + this.copytemCenten;
    },
    //下载模板
    // downloadTem(){
    //     if(this.downloadtem == '2'){
    //         this.$File.export(this.API.cpus +'consumersmsinfo/templateDownload?flag=2', {},`发送文件模板（自定义内容，全文模板可用）.xlsx`)
    //     }else{
    //         this.$File.export(this.API.cpus +'consumersmsinfo/templateDownload?flag=1', {},`发送文件模板（变量模板可用）.xlsx`)
    //     }
    // },
    //一页数据量
    handleSizeChange(size) {
      this.tableDataObj.configure.pageSize = size;
    },
    handleSizeChange0(size) {
      this.tableDataObj0.configure.pageSize = size;
    },
    //第几页
    handleCurrentChange: function (currentPage) {
      this.tableDataObj.configure.currentPage = currentPage;
    },
    handleCurrentChange0: function (currentPage) {
      this.tableDataObj0.configure.currentPage = currentPage;
    },
    //列表操作按钮(任务报告)
    handelOptionButton: function (val) {
      this.renwuObj.msgid = val.row.msgid;
      if (val.methods == "PresentationSMS") {
        this.getconsunerTaskData();
        this.sendMsgDialogVisible = true;
      }
    },
    handledatepluginVal: function (val1, val2) {
      //日期
      this.configurationItem.formData.sendTime = val1;
    },
    //短信发送提交 (第一步的短信发送)
    // submissionItem(formName){
    //     this.$refs[formName].validate((valid,val) => {
    //         console.log(val)
    //         if(this.configurationItem.formData.tempId == '-1' &&Object.getOwnPropertyNames(val).length==1 && val.signatureId[0].message == "请选择签名" ){
    //             valid = true
    //         }else if(this.configurationItem.formData.tempId != '-1' &&Object.getOwnPropertyNames(val).length==1 && val.content[0].message == "请填写自定义内容！" ){
    //             valid = true
    //         }
    //         if (valid) {
    //             let falgs = false;
    //             let falgss = false;
    //             this.configurationItem.formDatass = Object.assign({},this.configurationItem.formData);
    //             if(this.configurationItem.formDatass.sendType === '2'){ //判断发送时间为定时时间
    //                 if(this.configurationItem.formDatass.sendTime){  //如果是定时时间 ，定时时间范围必填
    //                     let nowTiem = new Date(this.configurationItem.formDatass.sendTime).getTime();
    //                     if (nowTiem < Date.now()+600000) {
    //                         falgss = false;
    //                         this.datePluginValueList.datePluginValue='';
    //                         this.configurationItem.formData.sendTime = '';
    //                         this.$message({
    //                             message: '定时时间已过期，定时时间应大于当前时间10分钟，需重新设置！',
    //                             type: 'warning'
    //                         });
    //                     }else{
    //                         falgss = true;
    //                     }
    //                 }else{
    //                     falgss = false;
    //                     this.$message({
    //                         message: '选择定时时间！',
    //                         type: 'warning'
    //                     });
    //                 }
    //             }else{
    //                 falgss = true;
    //             }
    //             //判断是否上传文件
    //             if(this.configurationItem.formDatass.files != ''){
    //                 falgs = true;
    //             }else{
    //                 falgs = false;
    //                 this.$message({
    //                     message: '上传发送对象文件!',
    //                     type: 'warning'
    //                 });
    //             }
    //             //判断是否是 自定义内容
    //             if(this.configurationItem.formDatass.tempId === '-1'){
    //                 this.configurationItem.formDatass.tempId = '';
    //             }
    //             if(falgs == true && falgss == true ){
    //                 this.sendLoading = false;
    //                 this.$api.post(this.API.cpus + 'consumersmsinfo/submitSms',this.configurationItem.formDatass,res=>{
    //                     this.sendLoading = true;
    //                     if(res.code == 200){
    //                         if(res.data.mobileNum == 0){
    //                             this.$message({
    //                                 message: '提交成功条数为0 ，需重新设置！',
    //                                 type: 'warning'
    //                             });
    //                         }else{
    //                             this.sendNumber = res.data.mobileNum;
    //                             this.sendContent = res.data.content;
    //                             this.phoneCenten = res.data.content;
    //                             this.stepsActive = 2;
    //                         }
    //                     }else{
    //                         this.$message({
    //                             message: res.msg,
    //                             type: 'warning'
    //                         });
    //                     }
    //                 })
    //             }
    //         } else {
    //             console.log('error submit!!');
    //             return false;
    //         }
    //     })
    // },
    //确定发送短信 (第二步的短信发送)
    // ConfirmSending(){
    //     this.fullscreenLoading = false;
    //     this.configurationItem.formDatass = Object.assign({},this.configurationItem.formData);
    //     let flags = false;
    //     if(this.configurationItem.formDatass.sendType === '2' && this.configurationItem.formDatass.sendTime){  //判断是否定时时间 ，定时时间范围
    //         let nowTiem = new Date(this.configurationItem.formDatass.sendTime).getTime();
    //         if (nowTiem < Date.now()+600000) {
    //             flags = false;
    //             this.$message({
    //                 message: '定时时间已过期，定时时间应大于当前时间10分钟，需重新设置！',
    //                 type: 'warning'
    //             });
    //             this.datePluginValueList.datePluginValue='';
    //             this.configurationItem.formData.sendTime = '';
    //             this.stepsActive = 1;

    //         }else{
    //             flags = true;
    //         }
    //     }else{
    //         flags = true;
    //     }
    //     if(flags === true){
    //         //自定义内容发送
    //         if(this.configurationItem.formDatass.tempId === '-1'){
    //             this.configurationItem.formDatass.tempId = '';
    //             this.configurationItem.formDatass.signatureId = '';
    //             if(this.isShort){
    //                 this.configurationItem.formDatass.shortCode = this.shortCode;
    //             }else{
    //                 this.configurationItem.formDatass.shortCode = '';
    //             }
    //             this.$api.post(this.API.cpus + 'consumersmsinfo/customSendSms',this.configurationItem.formDatass ,res=>{
    //                 this.fullscreenLoading = true;
    //                 this.sendMsgDialogShow = false;
    //                 if(res.code == 200){
    //                     this.$message({
    //                         message: '短信发送成功！',
    //                         type: 'success'
    //                     });
    //                 }else if(res.code == 406){
    //                     this.$message({
    //                         message: '有效短信为0条！',
    //                         type: 'warning'
    //                     })
    //                 }else{
    //                     this.$message({
    //                         message: '短信发送失败！',
    //                         type: 'error'
    //                     });
    //                 }
    //                 this.getMessageDate();//更新列表
    //             })
    //         }else{
    //             //模板内容发送
    //             this.configurationItem.formDatass.content = '';
    //             this.$api.post(this.API.cpus + 'consumersmsinfo/templateSendSms',this.configurationItem.formDatass ,res=>{
    //                 this.fullscreenLoading = true;
    //                 this.sendMsgDialogShow = false;
    //                 if(res.code == 200){
    //                     this.$message({
    //                         message: '短信发送成功！',
    //                         type: 'success'
    //                     });
    //                 }else if(res.code == 406){
    //                     this.$message({
    //                         message: '有效短信为0条！',
    //                         type: 'warning'
    //                     })
    //                 }else{
    //                     this.$message({
    //                         message: '短信发送失败！',
    //                         type: 'error'
    //                     });
    //                 }
    //                 this.getMessageDate();//更新列表
    //             })
    //         }
    //     }
    // },
    //移除文件
    fileup(val) {
      this.configurationItem.formData.files = "";
    },
    //文件上传成功
    fileupres(val, val2) {
      this.configurationItem.formData.group = val.data.group;
      this.configurationItem.formData.path = val.data.path;
      this.configurationItem.formData.files = val2[0].name;
    },
    getconsunerTaskData() {
      //获得短信失败状态报告的图表数据
      // this.$api.get(this.API.cpus + 'consumertasksms/selectConsumerFailureCodeNoteStatisticsByTemplateId?msgid='+this.renwuObj.msgid,{},res=>{
      //     this.tableDataObj2.tableData = res;
      //     this.basicOption2.data = [];
      //     this.basicOption2.ledate = [];
      //     for(let i=0;i<res.length;i++){
      //         this.basicOption2.data.push({name:res[i].failureCodeNoteName,value:res[i].codeNoteNum});
      //         this.basicOption2.ledate.push(res[i].failureCodeNoteName);
      //     }
      // })
      this.$api.get(
        this.API.cpus +
        "consumertasksms/errorCodes?msgid=" +
        this.renwuObj.msgid,
        {},
        (res) => {
          this.tableDataObj2.tableData = res.data;
          this.basicOption2.data = [];
          this.basicOption2.ledate = [];
          for (let i = 0; i < res.data.length; i++) {
            this.basicOption2.data.push({
              name: res.data[i].reason,
              value: res.data[i].num,
            });
            this.basicOption2.ledate.push(res.data[i].reason);
          }
        }
      );
      //获得任务报告图表数据
      // this.$api.get(this.API.cpus + 'consumersmsinfo/selTemplateDetail?msgid='+this.renwuObj.msgid,{},res=>{
      //     this.tableDataObj1.tableData = [res];
      //     this.basicOption1.data[0].value = res.successAmount;
      //     this.basicOption1.data[1].value = res.failureAmount;
      //     this.basicOption1.data[2].value = res.waitNumber;
      // })
    },
    //短链转换
    shortReset() {
      this.shortVisible = true;
    },
    //转换
    transformation() {
      if (this.originalUrl != "") {
        this.$api.post(
          this.API.cpus + "shortlink/changeUrl",
          { originalUrl: this.originalUrl },
          (res) => {
            if (res.code == 200) {
              this.shortConUrl = res.data.shortLinkUrl;
              this.shortCode = res.data.shortCode;
              this.$message({
                message: "短链接转换成功！",
                type: "success",
              });
            } else {
              this.originalUrl = "";
              this.$message({
                message: res.msg,
                type: "warning",
              });
            }
          }
        );
      } else {
        this.$message({
          message: "长链接不可为空",
          type: "warning",
        });
      }
    },
    //预览
    handlePreview() {
      if (this.shortConUrl != "") {
        window.open("https://" + this.shortConUrl, "_blank");
      } else {
        this.$message({
          message: "短连接为空，无法预览",
          type: "warning",
        });
      }
    },
    //短连接弹框的确定
    shortConDetermine() {
      if (this.shortConUrl != "") {
        this.configurationItem.formData.content += " " + this.shortConUrl + " ";
        this.isShort = true;
        this.shortVisible = false;
      } else {
        this.$message({
          message: "短链接不可为空",
          type: "warning",
        });
      }
    },
    //短链的取消
    HandelCencals() {
      this.shortVisible = false;
      this.isShort = false;
    },
  },
  mounted() {
    this.$api.get(
      this.API.cpus + "consumerclientinfo/getClientInfo",
      null,
      (res) => {
        if (res.code == 200) {
          
          if (res.data.isDateState == 1) {
            this.tableDataObj.tableLabel = [
              {
                prop: "taskSmsId",
                showName: "发送ID",
                width: "70",
                fixed: false,
              },
              {
                prop: "taskName",
                showName: "任务名称",
                width: "140",
                fixed: false,
              },
              {
                prop: "msgid",
                showName: "消息ID",
                width: "200",
                fixed: false,
                copy:true
              },
              {
                prop: "sendTime",
                showName: "发送时间",
                width: "160",
                fixed: false,
              },
              { prop: "tempId", showName: "模板ID", width: "80", fixed: false },
              {
                prop: "sendType",
                showName: "短信类型",
                fixed: false,
                width: "100",
                formatData: function (val) {
                  switch (val) {
                    case 1:
                      return (val = "模板发送");
                    case 2:
                      return (val = "自定义发送");
                    case 3:
                      return (val = "个性化发送");
                  }
                },
              },
              {
                prop: "content",
                showName: "模板内容",
                fixed: false,
                width: "700",
              },
              {
                prop: "totalNum",
                showName: "提交号码数",
                width: "100",
                fixed: false,
              },
              //   {
              //     prop: "invalidNum",
              //     showName: "无效号码数",
              //     width: "100",
              //     fixed: false,
              //   },
              {
                prop: "sendSuccessNum",
                showName: "发送成功数",
                width: "100",
                fixed: false,
              },
              {
                prop: "successRate",
                showName: "号码成功率",
                width: "100",
                fixed: false,
              },
              {
                prop: "chargeNum",
                showName: "计费数",
                width: "100",
                fixed: false,
              },
              //   {
              //     prop: "chargeFailNum",
              //     showName: "计费失败数",
              //     width: "100",
              //     fixed: false,
              //   },
              {
                prop: "chargeSuccessNum",
                showName: "计费成功数",
                width: "100",
                fixed: false,
              },
              {
                prop: "chargeSuccessRate",
                showName: "计费成功率",
                width: "100",
                fixed: false,
              },
              // {prop:"failNum",showName:'失败号码数',width:'100',fixed:false},
              //   {
              //     prop: "waiteNum",
              //     showName: "待返回数",
              //     width: "100",
              //     fixed: false,
              //   },
            ];
            this.tableDataObj0.tableLabel = [
              // { prop:"taskName",showName:'任务名称',fixed:false},
              {
                prop: "taskSmsId",
                showName: "发送ID",
                width: "70",
                fixed: false,
              },
              {
                prop: "taskName",
                showName: "任务名称",
                width: "140",
                fixed: false,
              },
              {
                prop: "msgid",
                showName: "消息ID",
                width: "200",
                fixed: false,
                copy:true
              },
              {
                prop: "sendTime",
                showName: "发送时间",
                width: "160",
                fixed: false,
              },
              { prop: "content", showName: "发送内容", width: "700" },
              {
                prop: "totalNum",
                showName: "提交号码数",
                width: "100",
                fixed: false,
              },
              //   {
              //     prop: "invalidNum",
              //     showName: "无效号码数",
              //     width: "100",
              //     fixed: false,
              //   },
              {
                prop: "sendSuccessNum",
                showName: "发送成功数",
                width: "100",
                fixed: false,
              },
              {
                prop: "successRate",
                showName: "号码成功率",
                width: "100",
                fixed: false,
              },
              {
                prop: "chargeNum",
                showName: "提交计费数",
                width: "100",
                fixed: false,
              },
              //   {
              //     prop: "chargeFailNum",
              //     showName: "计费失败数",
              //     width: "100",
              //     fixed: false,
              //   },
              {
                prop: "chargeSuccessNum",
                showName: "成功计费数",
                width: "100",
                fixed: false,
              },
              {
                prop: "chargeSuccessRate",
                showName: "计费成功率",
                width: "100",
                fixed: false,
              },
              // {prop:"failNum",showName:'失败号码数',width:'100',fixed:false},
              //   {
              //     prop: "waiteNum",
              //     showName: "待返回数",
              //     width: "100",
              //     fixed: false,
              //   },
            ];
          } else {
            this.tableDataObj.tableLabel = [
              {
                prop: "taskSmsId",
                showName: "发送ID",
                width: "70",
                fixed: false,
              },
              {
                prop: "taskName",
                showName: "任务名称",
                width: "140",
                fixed: false,
              },
              {
                prop: "msgid",
                showName: "消息ID",
                width: "200",
                fixed: false,
                copy:true
              },
              {
                prop: "sendTime",
                showName: "发送时间",
                width: "160",
                fixed: false,
              },
              { prop: "tempId", showName: "模板ID", width: "80", fixed: false },
              {
                prop: "sendType",
                showName: "短信类型",
                fixed: false,
                width: "100",
                formatData: function (val) {
                  switch (val) {
                    case 1:
                      return (val = "模板发送");
                    case 2:
                      return (val = "自定义发送");
                    case 3:
                      return (val = "个性化发送");
                  }
                },
              },
              {
                prop: "content",
                showName: "模板内容",
                fixed: false,
                width: "700",
              },
              {
                prop: "totalNum",
                showName: "提交号码数",
                width: "100",
                fixed: false,
              },
              {
                prop: "invalidNum",
                showName: "无效号码数",
                width: "100",
                fixed: false,
              },
              // {prop:"failNum",showName:'失败号码数',width:'100',fixed:false},
              {
                prop: "waiteNum",
                showName: "待返回数",
                width: "100",
                fixed: false,
              },
            ];
            this.tableDataObj0.tableLabel = [
              {
                prop: "taskSmsId",
                showName: "发送ID",
                width: "70",
                fixed: false,
              },
              {
                prop: "taskName",
                showName: "任务名称",
                width: "140",
                fixed: false,
              },
              {
                prop: "msgid",
                showName: "消息ID",
                width: "200",
                fixed: false,
                copy:true
              },
              {
                prop: "sendTime",
                showName: "发送时间",
                width: "160",
                fixed: false,
              },
              { prop: "content", showName: "发送内容", width: "700" },
              {
                prop: "totalNum",
                showName: "提交号码数",
                width: "100",
                fixed: false,
              },
              {
                prop: "invalidNum",
                showName: "无效号码数",
                width: "100",
                fixed: false,
              },
              // {prop:"failNum",showName:'失败号码数',width:'100',fixed:false},
              {
                prop: "waiteNum",
                showName: "待返回数",
                width: "100",
                fixed: false,
              },
            ];
          }
        }
      }
    );

    this.getMessageDate();
  },

  // activated(){
  //     this.getMessageDate()
  // },
  watch: {
    '$route.query.taskName'(newValue, oldValue) {
      console.log(newValue, 'newValue');
      if (newValue) {
        this.getObject.taskName = newValue
        this.getObject.specificTime = '3'
        this.getObject.sendRecord = '4'
      }

    },
    //监听自定义短信的内容
    "configurationItem.formData.content"() {
      this.phoneCenten = this.configurationItem.formData.content;
    },
    //监听是否选择了自定义短信
    "configurationItem.formData.tempId"() {
      //模板
      if (this.configurationItem.formData.tempId != "-1") {
        if (this.sendMsgDialogShow == true) {
          this.getTemplateContent();
        }
        this.configurationItem.formData.content = "";
      } else {
        //自定义
        this.downloadtem = "2"; //模板下载类型（自定义）
        this.phoneCenten = "";
        this.configurationItem.formData.signatureId = ""; //清除签名id
        this.copysigCenten = ""; //清除签名
      }
    },

    //监听发送短信弹出框是否关闭
    sendMsgDialogShow(val) {
      if (val == false) {
        //重置
        this.$refs.configurations.resetFields(); //清空表单
        this.del1 = false;
        (this.configurationItem.formData.tempId = ""),
          (this.configurationItem.formData.sendTime = ""), //发送时间的值
          (this.configurationItem.formData.content = ""),
          (this.configurationItem.formData.signatureId = ""),
          (this.datePluginValueList.datePluginValue = ""),
          (this.configurationItem.formData.sendType = "1"), //选择立即发送还是定时发送
          (this.configurationItem.formData.files = ""),
          (this.configurationItem.formData.group = ""),
          (this.configurationItem.formData.path = ""),
          (this.phoneCenten = ""),
          (this.copytemCenten = ""),
          (this.copysigCenten = ""),
          (this.stepsActive = 1);
        this.downloadtem = "2";
        this.fullscreenLoading = true;
        this.sendLoading = true;
        this.isShort = false;
        this.shortCode = "";
      } else {
        //获得签名
        this.getSignature();
        this.getTemplate();
      }
    },
    //是否改变分页的值
    "tableDataObj.configure": {
      handler() {
        this.getMessageDate();
      },
      deep: true,
    },
    //是否改变分页的值
    "tableDataObj0.configure": {
      handler() {
        this.getMessageDate();
      },
      deep: true,
    },
    //发送记录分类是否改变（模板还是自定义）
    getObject: {
      handler() {
        this.getMessageDate();
      },
      deep: true,
    },
    //监听手机框内容的改变
    phoneCenten(val) {
      //模板
      let d1 =
        /(\{var[1-9]{1,2}\|(d|w|\$|c)[0-9]{1,2}-[0-9]{1,2}\})|(\{var[1-9]{1,2}\|(d|w|\$|c)[0-9]{1,2}\})|(\{var[1-9]{1,2}\|(hh:mm:ss|MM-DD|YYYY-MM-DD|YYYY-MM-DD hh:mm:ss|MM-DD hh:mm:ss)\})/g;
      let a1 = val.match(d1);

      let w1 = val.length;
      let w2 = 0;

      if (a1 == null) {
        this.SMScount.smswordNum = val.length;
      } else {
        let w3 = 0;
        for (let i = 0; i < a1.length; i++) {
          w2 += a1[i].length; //参数物理长度
          if (a1[i].substr(-10) == "|hh:mm:ss}") {
            w3 += 8;
          } else if (a1[i].substr(-7) == "|MM-DD}") {
            w3 += 5;
          } else if (a1[i].substr(-12) == "|YYYY-MM-DD}") {
            w3 += 10;
          } else if (a1[i].substr(-21) == "|YYYY-MM-DD hh:mm:ss}") {
            w3 += 19;
          } else if (a1[i].substr(-16) == "|MM-DD hh:mm:ss}") {
            w3 += 14;
          } else {
            let num = /[0-9]{1,2}/g;
            let mun1 = a1[i].match(num);
            w3 += Number(mun1[mun1.length - 1]);
          }
        }
        this.SMScount.smswordNum = w1 - w2 + w3;
      }

      //字数和短信条数的显示
      if (this.SMScount.smswordNum == 0) {
        this.SMScount.smssTrip = 0;
      } else if (
        parseInt(this.SMScount.smswordNum) <= 70 &&
        parseInt(this.SMScount.smswordNum) > 0
      ) {
        this.SMScount.smssTrip = 1;
      } else {
        this.SMScount.smssTrip = Math.ceil(
          parseInt(this.SMScount.smswordNum) / 67
        );
      }
    },
    //短连接弹框是否关闭
    shortVisible(val) {
      if (val == false) {
        this.originalUrl = ""; //长链接的值
        this.shortConUrl = ""; //短连接的值
      }
    },
  },
};
</script>

<style scoped>
.sendMsg-box {
  padding: 20px;
}

.sendMsg-title {
  margin: 10px 0;
  padding-left: 5px;
  color: #16a589;
}

.sendMsg-table {
  margin-top: 12px;
}

.sendMsg-list-header {
  padding-top: 10px;
  font-weight: bold;
}

.el-steps--simple {
  background: #fff;
  border-radius: 0px;
  border-bottom: 1px solid #f3efef;
}

.send-mobel-box {
  width: 100%;
  overflow: hidden;
  text-align: center;
  margin-bottom: 35px;
}

.send-mobel-box img {
  width: 70%;
}

.el-select {
  width: 100%;
}

.send-upload-tips {
  padding-left: 96px;
  padding-top: 5px;
  font-size: 12px;
}

.send-upload-tips span {
  display: inline-block;
  padding-left: 8px;
  color: #0066ff;
}

.sms-content-exhibition {
  width: 145px;
  height: 32%;
  border-radius: 6px;
  position: absolute;
  padding: 8px 10px;
  background: #e2e2e2;
  top: 132px;
  left: 78px;
  font-size: 12px;
  line-height: 18px;
  color: #000;
  overflow: hidden;
}

.sms-first-steps-btns {
  width: 517px;
  position: absolute;
  text-align: center;
  bottom: 10px;
}

.sms-seconnd-steps-box {
  padding: 30px 20px 30px 55px;
}

.sms-seconnd-steps-btns {
  width: 390px;
  position: absolute;
  text-align: center;
  bottom: 36px;
}

.goTiming {
  padding-right: 14px;
  cursor: pointer;
  color: #16a589;
}

.goTiming:hover {
  color: #03886e;
}

.shortChain-box {
  padding: 20px;
}

.shortChain-matter {
  border: 1px solid #66ccff;
  padding: 10px 14px;
}

.short-title {
  font-weight: bolder;
  padding-bottom: 5px;
}

.font-sizes {
  padding-top: 2px;
  font-size: 12px;
  color: rgb(163, 163, 163);
}

.font-sizes1 {
  margin-top: 10px;
}
</style>
<style>
#webSend .threeDay .el-radio-button__inner {
  border-right: 0;
}

.sendMsg-box .el-dialog__body {
  padding: 10px 20px;
}

.sendMsg-box .el-form-item__label {
  text-align: left;
}

.sendMsg-box .el-step__head.is-process {
  color: #989898;
}

.sendMsg-box .el-step__title.is-process {
  color: #989898;
}

.sms-content-exhibition .el-scrollbar__wrap {
  overflow: hidden;
  overflow-y: scroll !important;
}

.sms-content-exhibition .el-scrollbar__wrap {
  margin-right: -27px !important;
}

.el-picker-panel .el-button--text {
  display: none;
}

.textareas textarea {
  height: 100px;
  resize: none;
}
</style>
