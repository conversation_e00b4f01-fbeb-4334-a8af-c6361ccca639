<template>
    <div class="bag">
        <div class="busi-cog-title-box">
            <span style="padding:10px" v-bind:class="DataScreening">统计分析</span>
            <!-- <span v-bind:class="ShortMessageRecording"  @click="handleclick('ShortMessageRecording')">短信记录</span> | -->
            <!-- <span v-bind:class="TemplateQuery" @click="handleclick('TemplateQuery')">模板查询</span> | -->
            <!-- <span v-bind:class="reportForm"  @click="handleclick('reportForm')">短链追踪统计</span> -->
        </div>
        <div>
            <component v-bind:is="currentTabComponent"></component>
        </div>
    </div>    
</template>

<script>
import DataScreening from './components/DataScreening.vue'
import ShortMessageRecording from './components/ShortMessageRecording.vue'
import TemplateQuery from './components/TemplateQuery.vue'
import reportForm from './components/reportForm.vue'
export default {
    name: "StatisticalAnalysis",
    components:{
        DataScreening,
        ShortMessageRecording,
        TemplateQuery,
        reportForm
    },
    data(){
        return{
            DataScreening:{
                'busi-cog-title':true,
                "busiColor":true
            },
            ShortMessageRecording:{
                'busi-cog-title':true,
                "busiColor":false
            },
            TemplateQuery:{
                'busi-cog-title':true,
                "busiColor":false
            },
            reportForm:{
                'busi-cog-title':true,
                "busiColor":false
            },
            currentTabComponent:'DataScreening'
        }
    },
    created(){
        if(this.$route.query.a == 4){
            this.handleclick('reportForm');
        }
    },
    methods:{

    }
}
</script>

<style scoped>
.busi-cog-title{
    cursor: pointer;
    display: inline-block;
    margin:0 5px;
}
.busi-cog-title-box{
    margin: 10px 0 10px 0;
}
.router-link-active{
    color:#16a589;
}
.busiColor{
    color:#16a589;
}
</style>