<template>
    <div class="lege">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 资料完善</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div style="padding: 0 20px;">
            <div class="Templat-matter">
                <p>tips：</p>
                <p>1.证件照应清晰可见容易识别，且边框完整</p>
                <p>2.必须真实拍摄，不能使用复印件</p>
                <p>3.大小不超过5M</p>
                <p>4.仅支持.jpg</p>
            </div>
            <el-form :inline="true" :rules="formRule" label-width="100px" ref="formRefs" :model="formInline"
                class="demo-form-inline" key="two">
                <div class="lege_title">营业执照</div>
                <div style="display: flex;">
                    <div>
                        <el-form-item label="营业执照" prop="businessLicense">
                            <div class="lege_upload">
                                <div style="display: flex" @click.capture="fileType = 'fd_L'">
                                    <el-upload :action="action" :headers="token" :limit="1" list-type="picture-card"
                                        :before-upload="beforeAvatarUpload" :on-preview="handlePictureCardPreview"
                                        :on-remove="handleRemove" :on-success="handlewqsSuccess" :file-list="PhotoLege">
                                        <i class="el-icon-plus"></i>
                                        <!-- <div slot="tip" class="el-upload__tip">
                                        <a style="color: #409eff;"
                                            href="https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1721358290932.jpeg"
                                            download="template.png" rel="noopener noreferrer" target="_blank">营业执照示例</a>
                                    </div> -->
                                    </el-upload>
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item label="营业执照示例" prop="">
                            <el-image style="width: 170px; height: 170px"
                                src="https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1721358290932.jpeg"
                                :preview-src-list="['https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1721358290932.jpeg']">
                            </el-image>
                        </el-form-item>
                    </div>
                    <div v-loading="loading3">
                        <div>
                            <el-form-item label="企业名称" label-width="165px" prop="compName">
                                <el-input v-model="formInline.compName" placeholder=""></el-input>
                            </el-form-item>
                        </div>
                        <div>
                            <el-form-item label="企业资质编号" label-width="165px" prop="number">
                                <el-input v-model="formInline.number" placeholder=""></el-input>
                            </el-form-item>
                        </div>
                        <div>
                            <el-form-item label="企业法人姓名" label-width="165px" prop="corporateName">
                                <el-input v-model="formInline.corporateName" placeholder=""></el-input>
                            </el-form-item>
                        </div>

                    </div>
                </div>
                <div class="lege_title">证件照</div>
                <div style="display: flex;">
                    <div>
                        <el-form-item label="身份证正面" prop="idCardFront">
                            <div class="lege_upload">
                                <div style="display: flex" @click.capture="fileType = 'fd_0'">
                                    <el-upload :action="action" :headers="token" :limit="1" list-type="picture-card"
                                        :before-upload="beforeAvatarUpload" :on-preview="handlePictureCardPreview"
                                        :on-remove="handleRemove" :on-success="handlewqsSuccess" :file-list="PhotoFront">
                                        <i class="el-icon-plus"></i>
                                        <!-- <div slot="tip" class="el-upload__tip">
                                        <a style="color: #409eff;"
                                            href="https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1721358330557.jpeg"
                                            download="template.png" rel="noopener noreferrer" target="_blank">身份证照片示例</a>
                                    </div> -->
                                    </el-upload>
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item label="身份证反面" prop="idCardBack">
                            <div class="lege_upload">
                                <div style="display: flex" @click.capture="fileType = 'fd_1'">
                                    <el-upload :action="action" :headers="token" :limit="1" list-type="picture-card"
                                        :before-upload="beforeAvatarUpload" :on-preview="handlePictureCardPreview"
                                        :on-remove="handleRemove" :on-success="handlewqsSuccess" :file-list="PhotoReverse">
                                        <i class="el-icon-plus"></i>
                                    </el-upload>
                                </div>
                            </div>
                        </el-form-item>
                        <el-form-item label="身份证照片示例" label-width="110px" prop="">
                            <el-image style="width: 170px; height: 170px"
                                src="https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1721358330557.jpeg"
                                :preview-src-list="['https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1721358330557.jpeg']">
                            </el-image>
                        </el-form-item>
                    </div>
                    <div v-loading="loading2">
                        <div>
                            <el-form-item label="姓名" label-width="120px" prop="name">
                                <el-input v-model="formInline.name" placeholder=""></el-input>
                            </el-form-item>
                        </div>
                        <div>
                            <el-form-item label="身份证号码" label-width="120px" prop="idCard">
                                <el-input v-model="formInline.idCard" placeholder=""></el-input>
                            </el-form-item>
                        </div>
                    </div>
                </div>
                <div class="lege_title">办公&授权</div>
                <div>
                    <el-form-item label="办公照片" prop="officePhoto">
                        <div class="lege_upload">
                            <div style="display: flex" @click.capture="fileType = 'fd_R'">
                                <!-- :data="{ clarity: true }" -->
                                <el-upload :action="action" :headers="token" :limit="1" list-type="picture-card"
                                    :before-upload="beforeAvatarUpload" :on-preview="handlePictureCardPreview"
                                    :on-remove="handleRemove" :on-success="handlewqsSuccess" :file-list="officeFile">
                                    <i class="el-icon-plus"></i>
                                </el-upload>
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item label="授权书" prop="authFile">
                        <div class="lege_upload">
                            <div style="display: flex" @click.capture="fileType = 'fd_S'">
                                <!-- :data="{ clarity: true }" -->
                                <el-upload :action="action" :headers="token" :limit="1" list-type="picture-card"
                                    :before-upload="beforeAvatarUpload" :on-preview="handlePictureCardPreview"
                                    :on-remove="handleRemove" :on-success="handlewqsSuccess" :file-list="authFront">
                                    <i class="el-icon-plus"></i>
                                    <div slot="tip" class="el-upload__tip">
                                        <a style="color: #409eff;"
                                            href="https://doc.zthysms.com/Public/Uploads/2021-03-24/605ad686a9473.docx"
                                            download="template.png" rel="noopener noreferrer" target="_blank">模版下载</a>
                                    </div>
                                </el-upload>
                            </div>
                        </div>
                    </el-form-item>
                    <el-form-item label="办公照片示例" label-width="110px" prop="">
                        <el-image style="width: 170px; height: 170px"
                            src="https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1721369256360.png"
                            :preview-src-list="['https://ztpublic.oss-cn-shanghai.aliyuncs.com/tmp/1721369256360.png']">
                        </el-image>
                    </el-form-item>
                    <el-form-item label="授权书示例" label-width="110px" prop="">
                        <el-image style="width: 170px; height: 170px"
                            src="https://doc.zthysms.com/server/../Public/Uploads/2021-03-24/605af850c8491.png"
                            :preview-src-list="['https://doc.zthysms.com/server/../Public/Uploads/2021-03-24/605af850c8491.png']">
                        </el-image>
                    </el-form-item>
                </div>
            </el-form>
            <div class="btn_box">
                <el-button style="width: 150px" @click="$router.push('authentication')">返回</el-button>
                <el-button v-if="submitLoading" style="width: 150px" type="primary" :loading="true">提交中</el-button>
                <el-button v-else style="width: 150px" type="primary" @click="submit('formRefs')">提交</el-button>
            </div>
        </div>
        <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="" />
        </el-dialog>
    </div>
</template>

<script>
import axios from 'axios';
export default {
    name:"completeAuthenInfo",
    data() {
        return {
            dialogImageUrl: "",
            src: "https://doc.zthysms.com/Public/Uploads/2021-04-25/6085176a6c0ff.pdf",
            action: this.API.cpus + "v3/file/upload",
            fileType: "",
            PhotoFront: [],
            PhotoReverse: [],
            PhotoLege: [],
            officeFile: [],
            authFront: [],
            loading3: false,
            loading2: false,
            dialogVisible: false,
            submitLoading: false,
            formInline: {
                businessLicense: "", //营业执照
                officePhoto: "", //办公照片
                idCardBack: "", //身份证反面照
                idCardFront: "", //身份证正面照
                authFile: "", //授权书
                id: "",
                compName: "",//企业名称
                number: "",//企业资质编号
                corporateName: "",//企业法人姓名
                name: "",//身份证姓名
                idCard: ""//身份证号码
            },
            formRule: {
                businessLicense: [
                    {
                        required: true,
                        message: "请上传营业执照",
                        trigger: "change",
                    },
                ],
                idCardBack: [
                    {
                        required: true,
                        message: "请上传身份证反面",
                        trigger: "change",
                    },
                ],
                idCardFront: [
                    {
                        required: true,
                        message: "请上传身份证正面",
                        trigger: "change",
                    },
                ],
                officePhoto: [
                    {
                        required: true,
                        message: "请上传办公照片",
                        trigger: "blur",
                    },
                ],
                compName:[
                    {
                        required: true,
                        message: "请输入企业名称",
                        trigger: "blur",
                    },
                ],

                number:[
                    {
                        required: true,
                        message: "请输入企业资质编号",
                        trigger: "blur",
                    },
                ],
                corporateName:[
                    {
                        required: true,
                        message: "请输入企业法人姓名",
                        trigger: "blur",
                    },
                ],
                name: [
                    {
                        required: true,
                        message: "请输入身份证姓名",
                        trigger: "blur",
                    },
                ],
                idCard: [
                    {
                        required: true,
                        message: "请输入身份证号码",
                        trigger: "blur",
                    },
                ],
            },
        };
    },
    methods: {
        getnfo() {
            this.$api.get(
                this.API.cpus + "consumerclientinfo/info",
                {},
                (res) => {
                    if (res.code == 200) {
                        if (res.data) {
                            this.formInline.idCardFront = res.data.idCardFront;
                            this.formInline.idCardBack = res.data.idCardBack;
                            this.formInline.businessLicense = res.data.businessLicense;
                            this.formInline.officePhoto = res.data.officePhoto;
                            this.formInline.authFile = res.data.authFile;
                            this.formInline.compName = res.data.compName;
                            this.formInline.number = res.data.number;
                            this.formInline.corporateName = res.data.corporateName;
                            this.formInline.name = res.data.name;
                            this.formInline.idCard = res.data.idCard;
                            this.formInline.id = res.data.id;
                            if (res.data.idCardFront) {
                                this.PhotoFront = [
                                    {
                                        name: "身份证正面",
                                        url: this.API.imgU + res.data.idCardFront,
                                    },
                                ];
                            }
                            if (res.data.idCardBack) {
                                this.PhotoReverse = [
                                    {
                                        name: "身份证反面",
                                        url: this.API.imgU + res.data.idCardBack,
                                    },
                                ];
                            }
                            if (res.data.businessLicense) {
                                this.PhotoLege = [
                                    {
                                        name: "营业执照",
                                        url: this.API.imgU + res.data.businessLicense,
                                    },
                                ];
                            }
                            if (res.data.officePhoto) {
                                this.officeFile = [
                                    {
                                        name: "办公照片",
                                        url: this.API.imgU + res.data.officePhoto,
                                    },
                                ];
                            }
                            if (res.data.authFile) {
                                this.authFront = [
                                    {
                                        name: "授权书",
                                        url: this.API.imgU + res.data.authFile, //res.data.authFile
                                    },
                                ];
                            }
                        }
                    }
                })
        },

        // async downloadTemplate() {
        //     const imageUrl = 'https://doc.zthysms.com/server/../Public/Uploads/2021-03-24/605af850c8491.png';
        //     try {
        //         const response = await axios.get(imageUrl, { responseType: 'blob' });
        //         const url = window.URL.createObjectURL(response.data);
        //         const link = document.createElement('a');
        //         link.href = url;
        //         link.download = 'template.png';
        //         document.body.appendChild(link);
        //         link.click();
        //         document.body.removeChild(link);
        //         window.URL.revokeObjectURL(url);
        //     } catch (error) {
        //         console.error('下载失败:', error);
        //     }
        // },
        submit() {
            this.$refs["formRefs"].validate((valid, value) => {
                if (valid) {
                    this.submitLoading = true;
                    let data = {
                        businessLicense: this.formInline.businessLicense,
                        idCardBack: this.formInline.idCardBack,
                        idCardFront: this.formInline.idCardFront,
                        officePhoto: this.formInline.officePhoto,
                        authFile: this.formInline.authFile,
                        compName: this.formInline.compName,
                        number: this.formInline.number,
                        corporateName: this.formInline.corporateName,
                        name: this.formInline.name,
                        idCard: this.formInline.idCard,
                        // id: "",
                    };
                    if (this.formInline.id) {
                        data.id = this.formInline.id;
                    }
                    this.$api.post(
                        this.API.cpus + "consumerclientinfo/setting",
                        data,
                        (res) => {
                            if (res.code == 200) {
                                this.$message({
                                    type: "success",
                                    duration: "2000",
                                    message: "提交成功!",
                                });
                                this.submitLoading = false;
                                this.$router.push({ path: "/authentication" });
                            } else {
                                this.$message({
                                    type: "error",
                                    duration: "2000",
                                    message: res.msg,
                                });
                                this.submitLoading = false;
                            }
                        }
                    );
                }else{
                    this.$message({
                        type: "error",
                        message: "请检查信息是否有遗漏！"
                    });
                }
            });
        },
        beforeAvatarUpload(file) {
            const siJPGGIF = file.name.split(".")[1];
            const isLt5M = file.size / 1024 / 1024 < 5; //单位MB
            if (siJPGGIF !== "jpg") {
                this.$message.error("上传图片只能是 jpg格式!");
                return false;
            }
            if (!isLt5M) {
                this.$message.error("上传图片大小不能超过 5MB!");
                return false;
            }
        },
        handlewqsSuccess(res, file, fileList) {
            let _this = this;
            if (res.code == 200) {
                if (_this.fileType == "fd_0") {
                    this.loading2 = true;
                    this.formInline.idCardFront = res.data.fullpath;
                    this.$api.get(this.API.cpus + 'consumerCertificate/idCardInfo', { filePath: res.data.fullpath }, ress => {
                        if (ress.code == 200) {
                            this.formInline.name = ress.data.personName
                            this.formInline.idCard = ress.data.personIdCard
                            this.loading2 = false
                        } else {
                            this.$message({
                                type: "error",
                                duration: "2000",
                                message: ress.msg
                            });
                            this.loading2 = false
                        }

                    })
                } else if (_this.fileType == "fd_1") {
                    this.formInline.idCardBack = res.data.fullpath;
                } else if (_this.fileType == "fd_L") {
                    this.loading3 = true
                    this.formInline.businessLicense = res.data.fullpath;
                    this.$api.get(this.API.cpus + 'consumerCertificate/entLicenseInfo', { filePath: res.data.fullpath }, ress => {
                        if (ress.code == 200) {
                            this.formInline.corporateName = ress.data.corporateName
                            this.formInline.compName = ress.data.entName
                            this.formInline.number = ress.data.entQualificationNum
                            // this.formInline.qualificationType = ress.data.entQualificationType+''
                            this.loading3 = false
                        } else {
                            this.$message({
                                type: "error",
                                duration: "2000",
                                message: ress.msg
                            });
                            this.loading3 = false
                        }
                    })
                } else if (_this.fileType == "fd_R") {
                    this.formInline.officePhoto = res.data.fullpath;
                } else if (_this.fileType == "fd_S") {
                    this.formInline.authFile = res.data.fullpath;
                }
            } else {
                this.$message({
                    type: "error",
                    duration: "2000",
                    message: res.msg,
                });
                this.loading2 = false;
            }
        },
        handleRemove(file, fileList) {
            let _this = this;
            if (_this.fileType == "fd_0") {
                _this.PhotoFront = [];
                this.formInline.idCardFront = "";
                this.loading2 = false;
            } else if (_this.fileType == "fd_1") {
                _this.PhotoReverse = [];
                this.formInline.idCardBack = "";
            } else if (_this.fileType == "fd_L") {
                console.log(_this.fileType, "ll");
                _this.PhotoLege = [];
                this.formInline.businessLicense = "";
                this.loading2 = false;
            } else if (_this.fileType == "fd_R") {
                _this.officeFile = [];
                this.formInline.officePhoto = "";
            } else if (_this.fileType == "fd_S") {
                _this.authFront = [];
                this.formInline.authFile = "";
            }
        },
        handlePictureCardPreview(file) {
            this.dialogImageUrl = file.url;
            this.dialogVisible = true;
        },
    },
    created() {
        // let data = JSON.parse(localStorage.getItem("userInfo"));
        // this.roleId = data.roleId;
        // this.formInline.type = this.$route.query.type;
        // if (this.$route.query.status == "1") {
        //     this.active = 3;
        // }
        this.token = {
            Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
        };
        this.getnfo();
    },
};
</script>

<style scoped>
.lege {
    background: #fff;
    /* height: 100%; */
    /* min-height: 100%;
        padding: 20px; */
}

.lege_upload {
    padding: 20px;
    /* border-bottom: 1px dashed #ccc; */
}

.Templat-matter {
    border: 1px solid #66ccff;
    background: #e5f0ff;
    padding: 10px;
    font-size: 12px;
}

.Templat-matter>p {
    padding: 5px 0;
}

.btn_box {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px 0;
}

.demo-form-inline {
    margin-top: 18px;
}

.lege_title {
    height: 38px;
    line-height: 38px;
    font-size: 13px;
    color: #333;
    margin-bottom: 10px;
    background: #F2F6FC;
    padding: 0 10px;
}
</style>
<style>
/* @media screen and (max-width: 1200px) {
    .el-upload--picture-card {
        width: 100px;
        height: 100px;
        line-height: 100px
    }

    .el-step__title {
        font-size: 12px;
        line-height: 38px;
    }

    .el-form--inline .el-form-item__label {
        float: none;
        display: inline;
    }
} */
</style>