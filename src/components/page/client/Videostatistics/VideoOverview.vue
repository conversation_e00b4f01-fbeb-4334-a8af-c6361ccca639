<template>
    <div id="DataScreening" class="bag">
        <!-- <div class="Top_title">
            <span>统计分析</span>
        </div> -->
        <!-- 顶部各种数据 -->
        <div class="businessOverview-header">
            <el-row class="fillet statistics-amount">
                <template v-if="this.$store.state.isDateState == 1">
                    <div style="text-align: left;margin: 17px 15px;">
                        <p>今日数据<span style="font-size: 12px;color: red;">（更新于：{{updateTime}}）</span></p>
                    </div>
                    <el-col :span="12" class="total-box">
                        <div class="total-data"><span>{{billingNumber}}</span>条</div>
                        <div class="this-month">发送号码数</div>
                    </el-col>
                    <el-col :span="12" class="border-l-r total-box">
                        <div class="total-data "><span>{{sendAmounts}}</span>条</div>
                        <div class="this-month">计费条数</div>
                    </el-col>
                    <!-- <el-col :span="6" class="border-l-r total-box">
                        <div class="total-data "><span>{{successNum}}</span>条</div>
                        <div class="this-month">成功计费数</div>
                    </el-col>
                    <el-col :span="6" class="total-box">
                        <div class="total-data"><span>{{successRates}}</span>%</div>
                        <div class="this-month">发送成功率</div>
                    </el-col> -->
                </template>
                <template v-if="this.$store.state.isDateState == 2">
                    <el-col :span="12" class="total-box">
                        <div class="total-data"><span>{{billingNumber}}</span>条</div>
                        <div class="this-month">今日发送号码数</div>
                    </el-col>
                    <el-col :span="12" class="border-l-r total-box">
                        <div class="total-data "><span>{{sendAmounts}}</span>条</div>
                        <div class="this-month">计费条数</div>
                    </el-col>
                </template>
            </el-row>
            <template>                
                <el-row class="fillet product-amount" >
                    <template v-if="this.$store.state.isDateState == 1">
                        <div style="text-align: left;margin: 17px 15px;">
                            <p>产品余额</p>
                        </div>
                    </template>
                    <el-col :span="12" style="padding-left:50px;">
                    <div class="amount-title amount-titles"><span class="vertical-line">| </span> 产品名称</div>
                    <div class="amount-content amount-contents">视频短信</div>
                    </el-col>
                    <el-col :span="12"  style="padding-left:30px;">
                    <div class="amount-title"><span class="vertical-line">| </span> 余额（条）</div>
                    <div class="amount-content">{{restNumSum}}</div>
                    </el-col>
                </el-row>
            </template>
        </div>
        <div class="fillet dataScreening-chart-box">
                <!-- <div style="margin-top: 10px;margin-bottom:20px;">
                    短信类型：
                    <el-radio-group v-model="MsgNotice" size="medium"  class="mixchange">
                        <el-radio-button label="验证码"  style="margin-right:10px;"></el-radio-button>
                        <el-radio-button label="行业通知"  style="margin-right:10px;border-left: 1px solid #dcdfee;"></el-radio-button>
                        <el-radio-button label="会员营销"  style="margin-right:10px;border-left: 1px solid #dcdfee;"></el-radio-button>
                    </el-radio-group>
                </div> -->
                <!-- 统计图查询条件 -->
            <div class="dataScreening-chart-title">
                <span style="line-height: 32px;font-weight: normal;">发送时间：</span>
                <el-radio-group v-model="getObject.specificTime" size="small" text-color="#fff" fill="#16a589" @change="handleChangeTimeOptions">
                    <el-radio-button label="1" >更新最近十天</el-radio-button>
                    <!-- <el-radio-button label="2">近7天</el-radio-button> -->
                    <!-- <el-radio-button label="3" class="threeDay">近30天</el-radio-button> -->
                    <!-- <el-radio-button label="4" class="threeDay">近一年</el-radio-button> -->
                </el-radio-group>
                <!-- <date-plugin class="search-date" :datePluginValueList="datePluginValueList"  @handledatepluginVal="handledatepluginVal" ></date-plugin> -->
            </div>
            <div class="dataScreening-title">
                <!-- <div class="dataScreening-select">
                    <span>趋势图 </span>
                    <el-select v-model="value" placeholder="请选择" style="margin-right:10px;">
                        <el-option  label="发送量"  value="1"></el-option>
                        <el-option  label="成功量"  value="2" v-if="this.$store.state.isDateState == 1"></el-option>
                        <el-option  label="发送成功率"  value="3" v-if="this.$store.state.isDateState == 1"></el-option>
                    </el-select>
                    <span  style="font-weight:normal;">总发送量：{{aggregate}} 条</span>
                </div> -->
            </div>
            <chart id="DataSChert" :option='option' style="height:320px"></chart>
            
            <!-- 统计列表查询条件（天，月） -->
            <div class="dataScreening-title">
                <div class="dataScreening-chart-title">
                    <span style="line-height: 32px;font-weight: normal;">发送时间：</span>
                    <el-radio-group v-model="getObjects.specificTime" size="small" text-color="#fff" fill="#16a589" @change="handleChangeTimeOptions1">
                        <el-radio-button label="1">统计月</el-radio-button>
                        <el-radio-button label="2" class="threeDay">统计年</el-radio-button>
                    </el-radio-group>
                    <date-plugin v-if="getObjects.specificTime == '1'" class="search-date" :datePluginValueList="datePluginValueList1"  @handledatepluginVal="handledatepluginVals" ></date-plugin>
                    <el-date-picker
                        v-if="getObjects.specificTime == '2'"
                        v-model="year"
                        type="monthrange"
                        :clearable='false'
                        value-format="yyyy-MM"
                        @change="handelMonth"
                        :picker-options="monthPickerOptions" 
                        range-separator="-"
                        start-placeholder="开始月份"
                        end-placeholder="结束月份"
                        placeholder="选择年月">
                    </el-date-picker>
                    <!-- <el-date-picker
                        v-if="getObjects.specificTime == '2'"
                        v-model="getObjects.year"
                        type="year"
                        :clearable='false'
                        value-format="yyyy"
                        placeholder="选择年">
                    </el-date-picker> -->
                    <el-button type="primary" plain style="margin-left: 10px;" @click="download">导出数据</el-button>
                </div>
            </div>
            <div style="padding-bottom:56px;" class="fillet dataScreening-chart-box">
                <table-tem :tableDataObj="tableDataObjs1" @handelOptionButton="handelOptionButton"></table-tem>
                <template>
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange1s" @current-change="handleCurrentChange1s" :current-page="getObjects.currentPage" :page-size="getObjects.pageSize" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObjs1.totalRow"></el-pagination>
                    </el-col>
                </template>
            </div>
            </div>
            <!-- <div style="padding-bottom:56px;" class="fillet dataScreening-chart-box">
                <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton"></table-tem>
                <template v-if="(getObject.specificTime != '1') && (getObject.specificTime != '2')">
                    <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                        <el-pagination class="page_bottom" @size-change="handleSizeChange1" @current-change="handleCurrentChange1" :current-page="getObject.currentPage" :page-size="getObject.pageSize" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.totalRow"></el-pagination>
                    </el-col>
                </template>
            </div> -->
            <!-- 报告的弹出框 -->
            <el-dialog title="任务报告查看" :visible.sync="sendMsgDialogVisible" width="860px" height="70%">
                <template v-if="this.$store.state.isDateState == 1">
                    <div style="display:inline-block;width:46%;text-align:center; border-right:1px solid #f7f7f7;margin-top:-20px;">
                        <PieChart id="pie2" width="360px" height="300px" :basicOption="basicOption1" ></PieChart>
                    </div>
                    <div style="display:inline-block;width:52%;text-align:center;margin-top:-20px;">
                        <PieChart id="pie1" width="420px" height="300px" :basicOption="basicOption2" ></PieChart>
                    </div>
                    <span style="display:block;padding:0 0 10px 0;color:#333;"> 发送明细列表</span>
                    <table-tem :tableDataObj="tableDataObj1" ></table-tem>
                </template>
                <template v-if="this.$store.state.isDateState == 2">
                    <div style="text-align:center;margin-top:-20px;">
                        <PieChart id="pie1"  height="300px" :basicOption="basicOption2" ></PieChart>
                    </div>
                </template>
                <span style="display:block;padding:20px 0 10px 0;color:#333;"> 回执失败代码分析列表</span>
                <table-tem :tableDataObj="tableDataObj2" ></table-tem>
                <div slot="footer" class="dialog-footer">
                    <el-button type="primary" @click="sendMsgDialogVisible = false">知道了</el-button>
                </div>
            </el-dialog>
        </div>
</template>

<script>
import PieChart from '@/components/publicComponents/PieChart' //饼图
import TableTem from '@/components/publicComponents/TableTem'
import Chart from '@/components/publicComponents/Chart'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import moment from 'moment'
export default {
    name: "VideoOverview",
    components: {
        TableTem,
        Chart,
        DatePlugin,
        PieChart
    },
    data () {
        return {
            name: "VideoOverview",
            MsgNotice:'行业通知',
            updateTime:'',
            billingNumber:'',
            restNumSum:'',
            sendAmounts:'',
            successNum:'',
            successRates:'',
            aggregate:'',
            sendMsgDialogVisible: false, //报告弹出框
            value: '1',
            Time10:'',//储存近10天时间
            getObject:{
                specificTime: '1', //选择那一天
                value:'',
                beginTime:'',
                endTime:'', 
                currentPage:1,
                pageSize:10,
                isDownload:2,
                productId:3,
            },
            getObject1:{
                specificTime: '1', //选择那一天
                beginTime:'',
                endTime:'', 
                currentPage:1,
                pageSize:10,
                isDownload:2,
                productId:3,
                flag:''
            },
            //统计日 统计月
            year: [moment().subtract(7, "Months").format("YYYY-MM"), moment().format('YYYY-MM')],
            getObjects:{
                specificTime: '1', //选择那一天
                // year:'',
                beginMonth: moment().subtract(7, "Months").format("YYYY-MM"),
                endMonth: moment().format('YYYY-MM'),
                beginTime:'',
                endTime:'',  
                currentPage:1,
                pageSize:10,
                isDownload:2,
                productId:3,
            },
            getObjects1:{
                specificTime: '1', //选择那一天
                // year:'',
                beginMonth: moment().subtract(7, "Months").format("YYYY-MM"),
                endMonth: moment().format('YYYY-MM'),
                beginTime:'',
                endTime:'',  
                currentPage:1,
                pageSize:10,
                isDownload:2,
                productId:3,
                flag:''
            },
            monthPickerOptions: {
                disabledDate: (time) => {
                    // const date = new Date();
                    // const year = date.getFullYear();
                    // let month = date.getMonth() + 1;
                    // if (month >= 1 && month <= 9) {
                    //     month = '0' + month;
                    // }
                    // const currentdate = year.toString() + month.toString();  // 当前年⽉
                    // const page_year = time.getFullYear();
                    // let page_month = time.getMonth() + 1;
                    // if (page_month >= 1 && page_month <= 9) {
                    //     page_month = '0' + page_month;
                    // }
                    // const pageDate = page_year.toString() + page_month.toString();  //页⾯中的年⽉
                    // let min_year = date.getFullYear();
                    // let min_month = date.getMonth() + 1 - 6; //半年
                    // // let min_month = date.getMonth() + 1 - 12; //一年
                    // if (min_month <= 0) {
                    //     min_year = min_year - 1;
                    //     min_month = 12 + min_month;
                    // }
                    // if (min_month >= 1 && min_month <= 9) {
                    //     min_month = '0' + min_month;
                    // }
                    // const minDate = min_year.toString() + min_month.toString(); // 最⼩年⽉
                    // return currentdate < pageDate || pageDate < minDate;
                    // let Y = (new Date()).getFullYear();
                    // let M = (new Date()).getMonth();
                    // let D = (new Date()).getDate();
                    // let curDate = (new Date()).getTime();
                    // let end = new Date(new Date().getFullYear()-1, 12)//获取去年的最后一个月
                    // // console.log(end.setTime(end.getTime() - 3600 * 1000 * 24),'end.setTime(end.getTime() - 3600 * 1000 * 24)');
                    // let defDate = (new Date(Y,M,D)).getTime() - end.setTime(end.getTime() - 3600 * 1000 * 24);//获取去年最后一个月的最后一天
                    // // let defDate = (new Date(Y,M,D)).getTime() - new Date(2021,11,31).getTime(); // 14 * 24 * 3600 * 1000;因为月份取出来的值小1，所以直接用了new Date(2019,3,11).getTime()
                    // let chooseDate = curDate - defDate;
                    
                    // return time.getTime() > Date.now() || time.getTime() < chooseDate;
                    function add0 (m) { return m < 10 ? '0' + m : m }
                    function setDate () {
                        var time = new Date();
                        var y = time.getFullYear();
                        var m = time.getMonth() + 1;
                        return y + '.' + add0(m) + '.01'
                    }
                    const date = new Date(setDate())// 说一下，为啥要加setDate()，因为在使用的时候会出现初始值选不中的情况，需要把初始值设置成当月的第一天的0点，才能选中，否则不能选中
                    const year = date.getFullYear()
                    return time.getTime() > Date.now() || time.getTime() < date.setFullYear(year - 1)
                },
            },
            datePluginValueList: { //日期选择器
                type:"daterange",
                start:"",
                end:'',
                range:'-',
                clearable:false, //是否开启关闭按钮
                pickerOptions:{
                    onPick: ({ maxDate, minDate }) => {
                        this.pickerMinDate = minDate.getTime();
                        if (maxDate) {
                            this.pickerMinDate = ''
                        }
                    },
                    disabledDate: (time) => {
                        if(this.pickerMinDate&&this.pickerMinDate>Date.now()){
                            return false
                        }
                        if (this.pickerMinDate !=='') {
                            const day30 = 30 * 24 * 3600 * 1000
                            let maxTime = this.pickerMinDate + day30
                            if (maxTime >  Date.now() - 86400000) {
                                maxTime =  Date.now() - 86400000
                            }
                            const minTime = this.pickerMinDate - day30
                            return time.getTime() < minTime || time.getTime() > maxTime
                        }
                        this.getObject.specificTime='1'
                        return time.getTime() > Date.now() - 86400000;
                    }
                },
                datePluginValue: '',
            },
            datePluginValueList1: { //日期选择器
                type:"daterange",
                start:"",
                end:'',
                range:'-',
                clearable:false, //是否开启关闭按钮
                pickerOptions:{
                    onPick: ({ maxDate, minDate }) => {
                        this.pickerMinDate = minDate.getTime();
                        if (maxDate) {
                            this.pickerMinDate = ''
                        }
                    },
                    disabledDate(time) {
                        // let curDate = (new Date()).getTime();
                        // // let three = 365 * 24 * 3600 * 1000;
                        // let three = 180 * 24 * 3600 * 1000;
                        // let threeMonths = curDate - three;
                        // return time.getTime() > Date.now() || time.getTime() < threeMonths;
                    //     let Y = (new Date()).getFullYear();
                    //     let M = (new Date()).getMonth();
                    //     let D = (new Date()).getDate();
                    //     let curDate = (new Date()).getTime();
                    //     let end = new Date(new Date().getFullYear()-1, 12)//获取去年的最后一个月
                    // // console.log(end.setTime(end.getTime() - 3600 * 1000 * 24),'end.setTime(end.getTime() - 3600 * 1000 * 24)');
                    //     let defDate = (new Date(Y,M,D)).getTime() - end.setTime(end.getTime() - 3600 * 1000 * 24);//获取去年最后一个月的最后一天
                    //     // let defDate = (new Date(Y,M,D)).getTime() - new Date(2021,11,31).getTime(); // 14 * 24 * 3600 * 1000;因为月份取出来的值小1，所以直接用了new Date(2019,3,11).getTime()
                    //     let chooseDate = curDate - defDate;
                        
                    //     return time.getTime() > Date.now() || time.getTime() < chooseDate;
                        let t = new Date();
                        let Y = t.getFullYear();
                        let M = t.getMonth();
                        let D = t.getDate();
                        return (time.getTime() < new Date(Y-1,M,D).getTime()||time.getTime() > new Date(Y,M,D).getTime())
                    }
                    // disabledDate: (time) => {
                    //     if(this.pickerMinDate&&this.pickerMinDate>Date.now()){
                    //         return false
                    //     }
                    //     if (this.pickerMinDate !=='') {
                    //         const day30 = 30 * 24 * 3600 * 1000
                    //         let maxTime = this.pickerMinDate + day30
                    //         if (maxTime >  Date.now() - 86400000) {
                    //             maxTime =  Date.now() - 86400000
                    //         }
                    //         const minTime = this.pickerMinDate - day30
                    //         return time.getTime() < minTime || time.getTime() > maxTime
                    //     }
                    //     this.getObjects.specificTime='1'
                    //     return time.getTime() > Date.now() - 86400000;
                    // }
                },
                datePluginValue: [moment().subtract(10, "days").format("YYYY-MM-DD"), moment().format('YYYY-MM-DD')],
            },
            tableDataObj1: { 
                //列表数据
                tableData: [],
                tableLabel:[//列表表头
                    {prop:"sendAmount",showName:'提交号码计费数',fixed:false},
                    {prop:"successAmount",showName:'成功号计费数',fixed:false},
                    {prop:"failNumber",showName:'失败号计费数',width:120,fixed:false},
                    {prop:"waitNumber",showName:'待返回号码计费数',width:120,fixed:false}
                ],
                tableStyle:{ //列表配置项
                    isSelection:false,//是否复选框
                    isExpand:false,//是否是折叠的
                    isDefaultExpand:false, //是否默认打开
                    style: {//表格样式,表格宽度
                        width:"100%"
                    },
                    optionWidth:'120',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                }
            },
            tableDataObj2: { 
                tableData: [],
                tableLabel:[{ //列表表头
                        prop:"failureCodeNoteName",
                        showName:'失败原因',
                        fixed:false
                    },{
                        prop:"codeNoteNum",
                        showName:'数量',
                        fixed:false
                    },{
                        prop:"codeNoteProportion",
                        showName:'占比',
                        fixed:false,
                        formatData: function(val) {
                            if(val != ''){
                                return val+'%';
                            }else{
                                return val;
                            }
                        }
                    }
                ],
                tableStyle:{
                    isSelection:false,//是否复选框
                    height:200,//是否固定表头
                    isExpand:false,//是否是折叠的
                    isDefaultExpand:false, //是否默认打开
                    style: {//表格样式,表格宽度
                        width:"100%"
                    },
                    optionWidth:'120',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                }
            },
            basicOption1:{//发送明细图表
                data:[
                    {value:'',name: '成功'},
                    {value:'',name: '失败'}, 
                    {value:'',name: '待返回'}
                ],
                ledate:['成功','失败','待返回'],
                bgColor: ['#8996E6', '#98D87D','#FFD86E'],
                radius:'62%',
                title:{
                    textStyle:{
                        color:"#999",
                        fontSize:14
                    },
                    text:'发送明细图表',
                    x:'right'
                },

            },
            basicOption2:{//回执失败代码分析图表
                data:[],
                ledate:[],
                bgColor: ['#8996E6', '#49A9EE', '#98D87D','#FFD86E','#F3857C'],
                radius:'62%',
                title: {
                    text:'回执失败代码分析图表',
                    textStyle:{
                        color:"#999",
                        fontSize:14
                    },
                    x:'right'
                },

            },
            option:{
                color: ['#0099FF','#67c23a'],
                grid: {
                    left: '2%',
                    right: '3%',
                    bottom: '1%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    },
                },
                legend: {
                    data:['发送量']
                },
                xAxis: {
                    type: 'category',
                    data: [],
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#EBEBEB',//坐标轴线的颜色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#0099FF',//坐标值得具体的颜色
                        }
                    }
                },
                yAxis:[ {
                    type: 'value',
                    name:'发送量',
                    splitLine: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#999',//坐标轴线的颜色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#0099FF',//坐标值得具体的颜色
                        },
                        formatter: ''
                    },
                },
                 {
                    // type: 'value',
                    // name:'发送成功率',
                    // splitLine: {
                    //     show: false
                    // },
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#999',//坐标轴线的颜色
                        }
                    },
                
                    axisLabel: {
                        textStyle: {
                            color: '#67c23a',//坐标值得具体的颜色
                        },
                        formatter: ''
                    },
                    
                }
                ],

                series: [
                    {
                    name:'',
                    type: "bar",
                    barWidth:50,
                    lineStyle: {
                        normal: {
                            width: 3,
                            shadowColor: 'rgba(0,0,0,0.4)',
                            shadowBlur: 6,
                            shadowOffsetY: 10
                        }
                    },
                    markPoint: {//最大值 最小值
                        data: [
                            {type: 'max', name: '最大值'},
                            {type: 'min', name: '最小值'}
                        ]
                    },
                    markLine: { //平均值
                        data: [
                            {type: 'average', name: '平均值'}
                        ],
                        label:{
                            position:"middle"  //将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
                        }
                    },
                    data:[],
                    // smooth: true //折线折脚
                },
                // {
                //     name:'',
                //     type: "line",
                //     yAxisIndex: 1,
                //     lineStyle: {
                //         normal: {
                //             width: 3,
                //             shadowColor: 'rgba(0,0,0,0.4)',
                //             shadowBlur: 6,
                //             shadowOffsetY: 10
                //         }
                //     },
                //     markPoint: {//最大值 最小值
                //         data: [
                //             {type: 'max', name: '最大值'},
                //             {type: 'min', name: '最小值'}
                //         ]
                //     },
                //     markLine: { //平均值
                //         data: [
                //             {type: 'average', name: '平均值'}
                //         ],
                //         label:{
                //             position:"middle"  //将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
                //         }
                //     },
                //     data:[],
                //     smooth: true //折线折脚
                // }
                ]
            },
            sendAmount:[],
            successAmount:[],
            successRate:[],

            tableDataObj:{ //表格数据
                totalRow:0,
                tableData: [],
                tableLabel:[  //列表表头
                    {prop:"createTime",showName:'统计时间',fixed:false},
                    {prop:"sendAmount",showName:'发送量',fixed:false},
                    // {prop:"successAmount",showName:'成功量',fixed:false},
                    // // {prop:"successAmount",showName:'计费条数',fixed:false},
                    // {prop:"successRate",showName:'发送成功率',formatData:function(val) { return val ? val +' %' :  val;},fixed:false}
                ],
                tableStyle:{
                    isSelection:false,//是否复选框
                    // height:250,//是否固定表头
                    isExpand:false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width:"100%"
                        },
                    optionWidth:'160',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                },
                tableOptions:[
                    {
                        optionName:'任务报告',
                        type:'',
                        size:'mini',
                        optionMethod:'missionReport',
                        icon:'el-icon-success'
                    }
                ]
            },
            //月，日统计表
            tableDataObjs1:{ //表格数据
                totalRow:0,
                loading2:false,
                tableData: [],
                tableLabel:[  //列表表头
                    {prop:"sendTime",showName:'发送时间',fixed:false},
                    {prop:"sendAmount",showName:'发送号码数',fixed:false},
                    {prop:"chargeNum",showName:'发送计费数',fixed:false},
                    {prop:"chargeSuccessNum",showName:'成功计费数',fixed:false},
                    {prop:"chargeFailNum",showName:'失败计费数',fixed:false},
                    {prop:"chargeWaitNum",showName:'待返回计费数',fixed:false},
                    {prop:"successRate",showName:'成功率',
                        formatData:function(val) { return val ? val +' %' :  val;},
                        fixed:false,
                        showColorTag: {
                            color: "red"
                        }
                    },
                    // {prop:"failRate",showName:'失败率',fixed:false,formatData:function(val) { return val ? val +' %' :  val;},},
                    {prop:"waitRate",showName:'待返回率',fixed:false,formatData:function(val) { return val ? val +' %' :  val;},}
                ],
                tableStyle:{
                    isSelection:false,//是否复选框
                    // height:250,//是否固定表头
                    isExpand:false,//是否是折叠的
                    style: {//表格样式,表格宽度
                        width:"100%"
                        },
                    optionWidth:'160',//操作栏宽度
                    border:true,//是否边框
                    stripe:false,//是否有条纹
                },
            }
        }
    },
    created(){
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth()+1;
        var day = date.getDate();
        var nowDate = year + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day);
        var lastDate = new Date(date - 1000 * 60 * 60 * 24 * 10);//最后30天可以更改，意义：是获取多少天前的时间
        var lastY = lastDate.getFullYear();
        var lastM = lastDate.getMonth()+1;
        var lastD = lastDate.getDate();
        var LDate = lastY + "-" + (lastM < 10 ? "0" + lastM : lastM) + "-"+(lastD < 10 ? "0" + lastD : lastD)
        this.getObjects.beginTime=LDate
        this.getObjects.endTime=nowDate
        this.Time10=[LDate,nowDate]

        // this.getCustominfo();
    },
    methods: {
        getCustominfo(){
            this.$api.get(this.API.cpus+'consumerclientinfo/getClientInfo?productId=3',null,res=>{
                if(res.data.isDateState == 1){
                    this.tableDataObjs1.tableLabel.push(
                        {prop:"chargeSuccessNum",showName:'成功计费数',fixed:false},
                            {prop:"chargeFailNum",showName:'失败计费数',fixed:false},
                            {prop:"chargeWaitNum",showName:'待返回计费数',fixed:false},
                            
                            {prop:"successRate",showName:'成功率',
                                formatData:function(val) { return val ? val +' %' :  val;},
                                fixed:false,
                                showColorTag: {
                                    color: "red"
                                }
                            },
                            // {prop:"failRate",showName:'失败率',fixed:false,formatData:function(val) { return val ? val +' %' :  val;},},
                            {prop:"waitRate",showName:'待返回率',fixed:false,formatData:function(val) { return val ? val +' %' :  val;},})

                    this.tableDataObj.tableLabel.push({prop:"successAmount",showName:'成功量',fixed:false},
                        {prop:"successRate",showName:'发送成功率',formatData:function(val) { return val ? val +' %' :  val;},fixed:false})
                }
            })
        },
        getAlldatasMD(){
            Object.assign(this.getObjects1, this.getObjects);
            this.getObjects1.flag = this.getObjects.specificTime;
            this.tableDataObjs1.loading2 = true;
            this.$api.post( this.API.cpus + 'statistics/page',this.getObjects1,res=>{
                console.log(res)
                this.tableDataObjs1.loading2 = false;
                this.tableDataObjs1.tableData = res.data.records;
                this.tableDataObjs1.totalRow = res.data.total;
            })
        },
        getDatas(){
            this.$api.get(this.API.cpus+'consumerdataoverviewday/businessOverview?productId=3',{},res=>{
                this.billingNumber = res.data.billingNumber;
                // this.restNumSum = res.data.restNumSum;
                this.sendAmounts = res.data.sendAmount;
                this.successNum = res.data.successNum;
                this.successRates = res.data.successRate;
                this.updateTime = res.data.updateTime;
            })
            this.$api.get(this.API.recharge+'client/balance/3',{},res=>{
                this.restNumSum = res.data.num;
            })
        },
        handleSizeChange1(size){
            this.getObject.pageSize = size;
            // this.getdataPage();
        },
        handleCurrentChange1(currentPage){
            this.getObject.currentPage = currentPage;
            // this.getdataPage();
        },
        handleSizeChange1s(size){
            this.getObjects.pageSize = size;
            this.getAlldatasMD();
        },
        handleCurrentChange1s(currentPage){
            this.getObjects.currentPage = currentPage;
            this.getAlldatasMD();
        },
        //获取统计图表的数据
        getdataPage(){
            Object.assign(this.getObject1, this.getObject);
            this.getObject1.flag = this.getObject.specificTime;
            this.$api.get( this.API.cpus + 'statistics/sendCharts?productId=3',{},res=>{
                //重置统计图表的数据
                this.sendAmount = [];
                this.successAmount = [];
                this.successRate = [];
                this.option.xAxis.data = []; //横坐标
                
                for(let i = 0; i < res.data.length;i++){
                    this.option.xAxis.data.push(res.data[i].statisticsTime); //横坐标
                    this.sendAmount.push(res.data[i].sendAmount); //发送量
                    this.successAmount.push(res.data[i].successAmount);//成功
                    this.successRate.push(res.data[i].successRate);//成功率
                }
                //获取今天的列表数据
                this.tableDataObj.tableData = res[1];//列表赋值
                this.getObject.value = ''
                this.setOptionItem();
            })
        },
        //设置统计图的展示数据
        setOptionItem(){
            if(this.value == '1'){
                this.option.series[0].data = this.sendAmount;
                this.option.series[0].name = '发送量';
                // this.option.yAxis[0].axisLabel.formatter = '{value}'
                // this.option.tooltip.formatter =' ';
                if(this.$store.state.isDateState == 1){
                    // this.option.series[1].data = this.successRate;
                    // this.option.series[1].name = '发送成功率';
                    // this.option.yAxis[1].axisLabel.formatter = '{value}%'
                    // this.option.tooltip.formatter =' {b}  <br/>发送量 ：{c}  <br/>发送成功率 ：{c1} %';
                }
            }
        },
        handelOptionButton: function(val){
            //任务报告
            if(val.methods=='missionReport'){
                //发送明细（图表）
                this.basicOption1.data[0].value = val.row.successAmount;
                this.basicOption1.data[1].value = val.row.failNumber;
                this.basicOption1.data[2].value = val.row.waitNumber;
                //发送明细（列表）
                this.tableDataObj1.tableData = [val.row];
               
                //获取回执失败代码数据
                this.$api.get(this.API.cpus + 'statistics/statusReport?flag='+this.getObject.specificTime+'&day='+val.row.createTime+'&productId=3',{},res=>{
                    this.basicOption2.ledate = [];
                    this.basicOption2.data = [];
                    if(res){
                        this.tableDataObj2.tableData = res.data;
                        for(let i =0 ; i< res.data.length ; i++){
                            this.basicOption2.ledate.push(res.data[i].failureCodeNoteName);
                            this.basicOption2.data.push({name:res.data[i].failureCodeNoteName,value : res.data[i].codeNoteNum});
                        }
                    }
                })
                this.sendMsgDialogVisible = true
            }
        },
        //时间范围选择
        handledatepluginVal: function(val1,val2){
            if(val1){
                this.getObject.specificTime='5'
                this.getObject.beginTime = val1;
                this.getObject.endTime = val2;
            }else{
                this.getObject.specificTime = '1'
                this.getObject.beginTime = '';
                this.getObject.endTime = '';
            }
        },
        handelMonth(val) {
            if (val) {
                this.getObjects.beginMonth = val[0]
                this.getObjects.endMonth = val[1]
            } else {
                this.getObjects.beginMonth = ''
                this.getObjects.endMonth = ''
            }
        },
        //时间范围选择(月，天)
        handledatepluginVals: function(val1,val2){
            if(val1){
                this.getObjects.specificTime='1'
                this.getObjects.beginTime = val1;
                this.getObjects.endTime = val2;
            }else{
                this.getObjects.specificTime = '2'
                this.getObjects.beginTime = '';
                this.getObjects.endTime = '';
            }
        },
        handleChangeTimeOptions: function(){
            this.datePluginValueList.datePluginValue = ''  
        },
        handleChangeTimeOptions1: function(){
             this.datePluginValueList1.datePluginValue= [moment().subtract(10, "days").format("YYYY-MM-DD"), moment().format('YYYY-MM-DD')] 
        },
        // 导出数据
        download() {
            if(this.tableDataObjs1.tableData.length == 0){
                this.$message({
                    message: '列表无数据，不可导出！',
                    type: 'warning'
                }); 
            }else{
                this.$File.export(this.API.cpus+'statistics/exportData',this.getObjects1,'统计总览数据导出.xls');
            }
        },
    },
    mounted(){
        this.getDatas();
        this.getdataPage();
        // this.getnumList();
        this.getAlldatasMD();
    },
    // activated(){
    //     this.getDatas();
    //     this.getdataPage();
    //     // this.getnumList();
    //     this.getAlldatasMD();
    // },
    watch:{
        //监听选择时间的变化
        getObject:{
            handler(val){
                window.Vue.cancel();
                // this.getdataPage();
            },
            deep:true,
        },
        //监听 统计天，统计月 选择时间的变化
        getObjects:{
            handler(val){
                window.Vue.cancel();
                this.getAlldatasMD();
            },
            deep:true,
        },
        //监听是否选择月
        'getObjects.specificTime'(val){
            if(val == 2){
                this.getObjects.beginMonth = moment().subtract(7, "Months").format("YYYY-MM")
                this.getObjects.endMonth = moment().format('YYYY-MM')
            //    this.getObjects.year = ""+new Date().getFullYear();
               this.getObjects.beginTime = '';
                this.getObjects.endTime = '';
            }else{
                this.getObjects.beginMonth = ''
                this.getObjects.endMonth = ''
                // this.getObjects.year = '';
                this.getObjects.beginTime = this.Time10[0];
                this.getObjects.endTime = this.Time10[1];
            }
        },
        //发送量，成功量的变换
        value:{
            handler(val){
                this.setOptionItem();
            },
            deep:true,
            immediate:true
        }
    }
}
</script>

<style scoped>
@media screen and (min-width: 1200px){
    .businessOverview-header{
        display: flex;
    }
    .statistics-amount{
        width: calc(100% - 410px);
        height:200px;
        text-align: center;
    }
    .total-data{
        font-size:14px;
        margin-top:23px;
    }
    .total-box{
        margin:20px 0;
        height:110px;
    }
    .border-l-r{
        border-left:1px solid #eaeaea;
        border-right:1px solid #eaeaea;
    }
    .border-l-r-1{
        border-left:1px solid #eaeaea;
    }
    .total-data span{
         font-size: 30px;
         color:#333;
     }
    .this-month{
        font-size:14px;
    }
    .product-amount{
        width:400px;
        margin-left:10px;
        height:200px;
    }
    .product-amount-1{
        width:500px;
        margin-left:10px;
        height:150px;
    }
    .vertical-line{
        font-weight: bold;
    }
    .amount-title{
        font-size: 14px;
        margin-top:45px;
    }
    .amount-titles{
        padding-left:16px;
    }
    .amount-content{
        font-size:20px;
        padding:10px 0 0 5px;
        color:#000;
    }
    .amount-contents{
         padding:10px 0 0 18px;
    }
   .dataScreening-chart-box{
       margin:10px 0;
       padding:20px 20px 20px 20px;
   }
   .dataScreening-chart-title{
        display: flex;
   }
   .dataScreening-chart{
       height:360px;
   }
   .dataScreening-title{
       padding-top:40px;
       font-weight: bold;
   }
   .look-at-more{
       color:#16a589;
   }
   .dataScreening-select{
       position: relative;
       margin-top:10px;
   }
}
@media screen and (max-width: 1200px){
    .businessOverview-header{
        /* display: flex; */
    }
    .statistics-amount{
        /* width: calc(100% - 410px); */
        height:200px;
        text-align: center;
    }
    .total-data{
        font-size:14px;
        margin-top:23px;
    }
    .total-box{
        margin:20px 0;
        height:110px;
    }
    .border-l-r{
        border-left:1px solid #eaeaea;
        border-right:1px solid #eaeaea;
    }
    .border-l-r-1{
        border-left:1px solid #eaeaea;
    }
    .total-data span{
         font-size: 30px;
         color:#333;
     }
    .this-month{
        font-size:14px;
    }
    .product-amount{
        width:400px;
        margin-left:10px;
        height:200px;
    }
    .product-amount-1{
        width:500px;
        margin-left:10px;
        height:150px;
    }
    .vertical-line{
        font-weight: bold;
    }
    .amount-title{
        font-size: 14px;
        margin-top:45px;
    }
    .amount-titles{
        padding-left:16px;
    }
    .amount-content{
        font-size:20px;
        padding:10px 0 0 5px;
        color:#000;
    }
    .amount-contents{
         padding:10px 0 0 18px;
    }
   .dataScreening-chart-box{
       margin:10px 0;
       padding:20px 20px 20px 20px;
   }
   .dataScreening-chart-title{
        display: flex;
   }
   .dataScreening-chart{
       height:360px;
   }
   .dataScreening-title{
       padding-top:40px;
       font-weight: bold;
   }
   .look-at-more{
       color:#16a589;
   }
   .dataScreening-select{
       position: relative;
       margin-top:10px;
   }
}

   
</style>
<style>

#DataScreening .threeDay .el-radio-button__inner{
    border-right: 0 ;
}
.dataScreening-chart-box .el-radio-button:last-child .el-radio-button__inner{
    border-radius: 0;
}
.dataScreening-chart-box .el-range-editor.el-input__inner{
    border-radius: 0px 4px 4px 0;
}
@media screen and (max-width: 1200px){
    .el-picker-panel{
        width: 370px;
        height: 350px;
        overflow: auto;
    }
    .el-date-range-picker__content{
        width: 100%;
    }
    .el-pagination{
        white-space: pre-wrap;
    }
}
</style>
