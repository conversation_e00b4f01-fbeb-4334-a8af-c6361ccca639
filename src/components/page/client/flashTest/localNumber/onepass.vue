
<template>
  <div class="bag">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><i class="el-icon-lx-emoji"></i> 应用配置</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div class="OuterFrame fillet OuterFrameList">
      <div class="sensitive-fun" style="margin: 10px 0">
        <!-- <span class="sensitive-list-header" style="height: 35px;line-height: 35px;">APPID列表</span> -->
        <el-button type="primary" plain @click="AddID()">新增APPID</el-button>
      </div>
      <div class="sensitive-table">
        <!-- 表格和分页开始 -->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
        >
          <el-table-column label="APP名称">
            <template slot-scope="scope">{{ scope.row.appName }}</template>
          </el-table-column>
          <el-table-column label="APP ID">
            <template slot-scope="scope">
              <span>{{ scope.row.appid }}</span>
              <i style="color:#409eff;cursor: pointer;margin: 4px;font-size: 14px;" class="el-icon-document-copy" @click="handleCopy(scope.row.appid,$event )"></i>
            </template>
          </el-table-column>
          <el-table-column label="KEY">
            <template slot-scope="scope">
              <div class="icon iconfont">
                <span style="margin-right: 10px">{{ scope.row.keys }}</span>
                <i
                  class="iconStyle el-icon-view"
                  style="color: #16a589; cursor: pointer"
                  @click="ViewKey(scope.row, scope.$index)"
                ></i>
              </div>
            </template>
          </el-table-column>
          <el-table-column label="审核状态" width="120">
            <template slot-scope="scope">
              <span v-if="scope.row.status == 'pass'">审核通过</span>
              <span v-else-if="scope.row.status == 'illegal'">审核不通过</span>
              <span v-else>未审核</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="120">
            <template slot-scope="scope">
              <el-button
                type="text"
                style="margin-left: 0px"
                @click="SYedit(scope.row)"
                ><i class="el-icon-success"></i>&nbsp;编辑</el-button
              >
              <el-button
                type="text"
                style="color: orange; margin-left: 0px"
                @click="SYdelete(scope.row)"
                ><i class="el-icon-error"></i>&nbsp;删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <!--分页-->
        <el-col
          :xs="24"
          :sm="24"
          :md="24"
          :lg="24"
          class="page"
          slot="pagination"
          style="background: #fff; padding: 10px 0; text-align: right"
        >
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="sensitiveConObj.currentPage"
            :page-size="sensitiveConObj.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.total"
          >
          </el-pagination>
        </el-col>
      </div>
    </div>
    <!-- 添加app -->
    <el-dialog :title="title" :visible.sync="AddAPPID" width="50%">
      <div
        style="width: 100%; box-shadow: 2px 2px 5px #ccc; margin-bottom: 20px"
      >
        <div
          style="
            width: 100%;
            height: 40px;
            line-height: 40px;
            background: #f9fbfd;
            border-bottom: 1px solid #e0e4eb;
          "
        >
          <div style="margin-left: 10px">选择产品能力</div>
        </div>
        <div style="width: 100%; height: 50px; line-height: 50px">
          <el-radio
            @change="hendRadio"
            style="margin-left: 10px"
            v-model="AddAPPIDform.type"
            label="onepass"
            >APP本机校验</el-radio
          >
          <el-radio
            @change="hendRadio"
            v-model="AddAPPIDform.type"
            label="onepass_web"
            >h5本机校验</el-radio
          >
        </div>
      </div>
      <div style="width: 100%; box-shadow: 2px 2px 5px #ccc; margin-top: 10px">
        <div
          style="
            width: 100%;
            height: 40px;
            line-height: 40px;
            background: #f9fbfd;
            border-bottom: 1px solid #e0e4eb;
          "
        >
          <div style="margin-left: 10px">填写应用信息</div>
        </div>
        <div style="padding: 10px">
          <el-form
            :model="AddAPPIDform"
            :rules="rules"
            ref="AddAPPIDformRef"
            label-width="120px"
            style="padding: 0px 25px 0px 0px"
          >
            <div v-if="AddAPPIDform.type == 'onepass'">
              <el-form-item label="APP名称" prop="appName">
                <el-input
                  v-model="AddAPPIDform.appName"
                  placeholder="请输入要使用的本机号码校验服务的APP名称"
                ></el-input>
              </el-form-item>
              <p
                style="
                  width: 140px;
                  padding: 0 12px 5px 0;
                  text-align: right;
                  box-sizing: border-box;
                  font-size: 14px;
                "
                class="icon iconfont"
              >
                操作系统
                <el-tooltip
                  class="item"
                  effect="dark"
                  content="请至少提供一个客户端平台信息用以审核"
                  placement="top"
                >
                  <i class="iconStyle el-icon-warning"></i>
                </el-tooltip>
              </p>
              <el-form-item label="iOS APP包名" prop="bundle">
                <el-input
                  v-model="AddAPPIDform.bundle"
                  maxlength="100"
                  placeholder="以字母或下划线开头,数字、字母、下划线或点的组合,小于100个字符"
                ></el-input>
              </el-form-item>
              <el-form-item label="Android APP包名" prop="packageName">
                <el-input
                  v-model="AddAPPIDform.packageName"
                  maxlength="100"
                  placeholder="以字母或下划线开头,数字、字母、下划线或点的组合,小于100个字符"
                ></el-input>
                <el-tooltip placement="top">
                  <div slot="content">
                    1、下载签名获取工具APP(Gen_Signature_Android.apk)并在Android手机上安装<br />2、在手机上安装开发者的应用<br />3、启动签名获取工具APP，然后输入开发者的应用APP包名(如：com.cmic.sso.myapplication)，点击"Get
                    Signature"获取签名按钮，在下面会显示获取到的签名字符串。(注意：包签名区分大小写)<br />4、运营商会对提交的APP信息进行审核，只有审核通过的APP才可正常使用本机号码校验服务
                  </div>
                  <i
                    style="position: absolute; top: 30%; right: -20px"
                    class="iconStyle el-icon-warning"
                  ></i>
                </el-tooltip>
              </el-form-item>
              <el-form-item label="APP包签名" prop="sign">
                <el-input
                  v-model="AddAPPIDform.sign"
                  maxlength="32"
                  placeholder="数字与字母组合,32个字符"
                ></el-input>
              </el-form-item>
            </div>
            <div v-else>
              <el-form-item label="APP名称" prop="appName">
                <el-input
                  v-model="AddAPPIDform.appName"
                  placeholder="请输入要使用的本机号码校验服务的APP名称"
                ></el-input>
              </el-form-item>
              <el-form-item label="应用平台" prop="appType">
                <el-radio v-model="AddAPPIDform.appType" label="1">h5</el-radio>
                <el-radio v-model="AddAPPIDform.appType" label="2"
                  >小程序</el-radio
                >
              </el-form-item>
              <div v-if="AddAPPIDform.appType == '1'">
                <el-form-item label="origin" prop="origin">
                  <el-input
                    type="textarea"
                    v-model="AddAPPIDform.origin"
                    placeholder="例如：https://oneloginh5.gtapp.xyz"
                  ></el-input>
                  <p style="font-size: 12px">
                    请求来源origin指发起请求的业务来源即使用H5本机校验的页面的域名。一般为protocal+host，不包含路径等信息。多个origin请使用英文逗号分割。
                  </p>
                </el-form-item>
                <el-form-item label="Referer" prop="referer">
                  <el-input
                    type="textarea"
                    v-model="AddAPPIDform.referer"
                    placeholder="例如：https://oneloginh5.gtapp.xyz/onepass_new"
                  ></el-input>
                  <p style="font-size: 12px">
                    Referer集成网页地址是指集成了取号js的页面请求头的Referer，若需要在多个页面集成则每个页面都需要进行报备。未报备的页面无法正常进行校验，多个Referer地址使用英文逗号分割。总长度不能超过3000个字符
                  </p>
                </el-form-item>
              </div>
              <div v-else>
                <el-form-item label="小程序ID" prop="wxId">
                  <el-input
                    v-model="AddAPPIDform.wxId"
                    placeholder="请填写您要接入的小程序ID"
                  ></el-input>
                  <p style="font-size: 12px">
                    小程序ID可在您的微信小程序开发后台查看
                  </p>
                </el-form-item>
              </div>
            </div>
          </el-form>
        </div>
      </div>

      <div style="text-align: right; margin: 10px">
        <span slot="footer" class="dialog-footer">
          <el-button type="primary" @click="determine('AddAPPIDformRef')"
            >确定</el-button
          >
          <el-button @click="AddAPPID = false">取 消</el-button>
        </span>
      </div>
    </el-dialog>
  </div>
</template>
<script>
// 引入时间戳转换
import { formatDate } from "@/assets/js/date.js";
import clip from '../../../utils/clipboard'
export default {
  name: "onepass",
  data() {
    return {
      AddAPPID: false,
      title: "添加APPID",
      appid: "",
      //查询条件的值
      // sensitiveCondition: {
      //     type:'',
      //     currentPage:1,
      //     pageSize:10
      // },
      //赋值查询条件的值
      sensitiveConObj: {
        type: "onepass",
        currentPage: 1,
        pageSize: 10,
      },
      //列表数据
      tableDataObj: {
        loading2: false,
        tableData: [],
        total: 0,
      },
      // 新增修改数据
      AddAPPIDform: {
        type: "onepass",
        appName: "",
        bundle: "",
        packageName: "",
        sign: "",
        appType: "1",
        origin: "",
        referer: "",
        wxId: "",
      },
      rules: {
        origin: [
          { required: true, message: "请填写请求来源Orign", trigger: "change" },
        ],
        referer: [
          {
            required: true,
            message: "请填写集成网页地址Referer",
            trigger: "change",
          },
        ],
        wxId: [
          { required: true, message: "请填写微信小程序ID", trigger: "change" },
        ],
      },
    };
  },
  methods: {
    //获取列表数据
    getTableDtate() {
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.cpus + "consumeronelogin/page",
        this.sensitiveConObj,
        (res) => {
          res.data.records.forEach((e, i) => {
            e.keys = e.key.substr(0, 3) + "****" + e.key.substr(-3);
          });
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.total = res.data.total;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    //查询
    Query() {
      Object.assign(this.sensitiveConObj, this.sensitiveCondition); //把查询框的值赋给一个对象
      this.getTableDtate();
    },
    hendRadio() {
      this.AddAPPIDform.appName = "";
      //   console.log(this.AddAPPIDform.type);
      //   this.$refs['AddAPPIDformRef'].resetFields();
    },
    handleCopy(name,event){
        clip(name, event)
      },
    //重置
    // sensitiveReload(formName){
    //     this.$refs[formName].resetFields();
    //     this.sensitiveCondition.beginTime='';
    //     this.sensitiveCondition.endTime='';
    //     this.sensitiveCondition.auditStartTime='';
    //     this.sensitiveCondition.auditEndTime='';
    //     Object.assign(this.sensitiveConObj,this.sensitiveCondition); //把查询框的值赋给一个对象
    // },
    // //获取查询时间的开始时间和结束时间
    // hande1: function (val) {
    //     if(val){
    //         this.sensitiveCondition.beginTime = this.moment(val[0]).format("YYYY-MM-DD")+' 00:00:00';
    //         this.sensitiveCondition.endTime = this.moment(val[1]).format("YYYY-MM-DD")+' 23:59:59';
    //     }else{
    //         this.sensitiveCondition.beginTime = '';
    //         this.sensitiveCondition.endTime = '';
    //     }
    // },
    //分页切换每页所展示数量
    handleSizeChange(size) {
      this.sensitiveConObj.pageSize = size;
    },
    //分页切换页数
    handleCurrentChange: function (currentPage) {
      this.sensitiveConObj.currentPage = currentPage;
    },
    // key展示
    ViewKey(row, index) {
      if (row.keys.length > 10) {
        this.tableDataObj.tableData[index].keys =
          row.key.substr(0, 3) + "****" + row.key.substr(-3);
      } else {
        this.tableDataObj.tableData[index].keys = row.key;
      }
    },
    // 新增
    AddID() {
      this.title = "添加APPID";
      this.appid = "";
      this.AddAPPID = true;
    },
    determine(formName) {
      this.$refs[formName].validate((valid) => {
        if (valid) {
          if (this.title == "添加APPID") {
            this.$confirms.confirmation(
              "post",
              "确定新增?",
              this.API.cpus + "consumeronelogin",
              this.AddAPPIDform,
              (res) => {
                if (res.code == 200) {
                  this.AddAPPID = false;
                  this.getTableDtate();
                }
              }
            );
          } else {
            this.AddAPPIDform.appid = this.appid;
            this.$confirms.confirmation(
              "put",
              "确定编辑?",
              this.API.cpus + "consumeronelogin",
              this.AddAPPIDform,
              (res) => {
                if (res.code == 200) {
                  this.AddAPPID = false;
                  this.getTableDtate();
                }
              }
            );
          }
        }
      });
      //   this.AddAPPIDform.type = "onepass";
    },
    //编辑
    SYedit(val) {
    //   console.log(val, "val");
      this.title = "编辑APPID";
      this.AddAPPID = true;
      this.appid = val.appid;
      if (val.type && val.type == "onepass") {
        this.AddAPPIDform.type = val.type;
        this.AddAPPIDform.appName = val.appName;
        this.AddAPPIDform.bundle = val.bundle;
        this.AddAPPIDform.packageName = val.packageName;
        this.AddAPPIDform.sign = val.sign;
      } else {
        this.AddAPPIDform.type = val.type;
        if (val.appType && val.appType == 1) {
          this.AddAPPIDform.appType = val.appType + "";
          this.AddAPPIDform.appName = val.appName;
          this.AddAPPIDform.origin = val.origin;
          this.AddAPPIDform.referer = val.referer;
        } else {
          // console.log(11);
          this.AddAPPIDform.appType = val.appType + "";
          this.AddAPPIDform.appName = val.appName;
          this.AddAPPIDform.wxId = val.wxId;
        }
      }
    },
    // 删除
    SYdelete(val) {
      this.$confirms.confirmation(
        "delete",
        "此操作将发送检测数据, 是否继续?",
        this.API.cpus + "consumeronelogin/" + val.id,
        {},
        (res) => {
          this.getTableDtate();
        }
      );
    },
  },
  mounted() {
    this.getTableDtate();
  },
  // activated(){
  //     this.getTableDtate()
  // },
  watch: {
    AddAPPID(val) {
      if (val == false) {
        this.$refs["AddAPPIDformRef"].resetFields();
        this.AddAPPIDform.appName = "";
        this.AddAPPIDform.bundle = "";
        this.AddAPPIDform.packageName = "";
        this.AddAPPIDform.sign = "";
        this.AddAPPIDform.type = "onepass";
        this.AddAPPIDform.appType = "1";
      }
    },
    //监听查询框对象的变化
    // sensitiveConObj:{
    //     handler(val){
    //         this.getTableDtate();
    //     },
    //     deep:true,
    //     immediate:true
    // },
  },
};
</script>
<style scoped>
.OuterFrame {
  padding: 20px;
}
.demo-form-inline .el-form-item {
  margin-right: 50px;
}
.sensitive-table {
  padding-bottom: 40px;
}
.sig-type .el-radio + .el-radio {
  margin-left: 0px;
}
.sig-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.sig-type .el-radio-group {
  padding-top: 8px;
}
.tips {
  margin-top: 30px;
}
.tips p {
  margin-left: 29px;
  color: #c5c5c5;
}
.send-mobel-box {
  width: 300px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}
.send-mobel-box img {
  width: 300px;
}
.mms-content-exhibition {
  position: absolute;
  top: 0;
  width: 300px;
  height: 375px;
  margin: 135px 25px 0px 25px;
  overflow: auto;
  overflow-y: auto;
}
</style>
<style>
.el-table--small th {
  background: #f5f5f5;
}
.OuterFrameList .el-carousel__button {
  background-color: #16a589;
}
</style>


