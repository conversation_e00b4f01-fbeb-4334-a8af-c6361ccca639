
<template>
    <div class="bag">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 数据统计</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="OuterFrame fillet OuterFrameList" style="background:#fff">
            <div>
               <el-form :inline="true" :model="sensitiveCondition" class="demo-form-inline" label-width="82px" ref="sensitiveCondition">
                    <el-form-item label="APP名称" label-width="82px" prop="type">
                         <el-select v-model="sensitiveCondition.appid" placeholder="请选择" clearable class="input-w">
                            <el-option v-for="(item,index) in AppAll" :label="item.appName" :value="item.appid" :key="index"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="时间" label-width="82px" prop="type">
                         <el-select v-model="sensitiveCondition.flag" placeholder="请选择" clearable class="input-w">
                            <el-option label="今天" value="1"></el-option>
                            <el-option label="昨天" value="2"></el-option>
                            <el-option label="7天" value="3"></el-option>
                            <el-option label="30天" value="4"></el-option>
                        </el-select>
                    </el-form-item>
                    <el-form-item label="具体时间" label-width="82px"  prop="time1">
                        <el-date-picker class="input-w"
                        v-model="sensitiveCondition.time1"
                        value-format="yyyy-MM-dd"
                        type="daterange"
                        range-separator="-"
                        start-placeholder="开始日期"
                        end-placeholder="结束日期"
                        @change="hande1"
                        >
                        </el-date-picker>
                    </el-form-item> 
                </el-form>
            </div>
            <div class="fillet dataScreening-chart-box">
                <!-- 统计图查询条件 -->
                <!-- <div class="dataScreening-chart-title">
                    <span style="line-height: 32px;font-weight: normal;">发送时间：</span>
                    <span class="spanStyleG">更新时间</span>
                </div> -->
                <chart id="DataSChert" :option='option' style="height:320px"></chart>
            </div>
        </div>
    </div>        
</template>
<script>
// 引入时间戳转换
import {formatDate} from '@/assets/js/date.js' 
import Chart from '@/components/publicComponents/Chart'
export default {
    name:'onepassStatistics',
    components: {
        Chart,
    },
     data(){
        return {
            AddAPPID:false,
            //查询条件的值
            sensitiveCondition: {
                type:"onepass",
                flag:'1',
                appid:'',
                beginTime:'',
                endTime:''
            },
            AppAll:[],
            option:{
                color: ['#0099FF','#d14a61','#67c23a'],
                grid: {
                    left: '2%',
                    right: '3%',
                    bottom: '1%',
                    containLabel: true
                },
                tooltip: {
                    trigger: 'axis',
                    axisPointer: {
                        type: 'cross',
                        crossStyle: {
                            color: '#999'
                        }
                    },
                },
                legend: {
                    data:['总计费量','匹配量','不匹配量']
                },
                xAxis: {
                    type: 'category',
                    data: [],
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#EBEBEB',//坐标轴线的颜色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#0099FF',//坐标值得具体的颜色
                        }
                    }
                },
                yAxis:[ {
                    type: 'value',
                    name:'计费数',
                    splitLine: {
                        show: false
                    },
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#999',//坐标轴线的颜色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#0099FF',//坐标值得具体的颜色
                        },
                        formatter: ''
                    },
                },
                 {
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#999',//坐标轴线的颜色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#67c23a',//坐标值得具体的颜色
                        },
                        formatter: ''
                    },
                    
                },
                {
                    axisLine: {
                        lineStyle: {
                            type: 'solid',
                            color: '#999',//坐标轴线的颜色
                        }
                    },
                    axisLabel: {
                        textStyle: {
                            color: '#d14a61',//坐标值得具体的颜色
                        },
                        formatter: ''
                    },
                    
                }
                ],
                series: [
                    {
                    name:'总计费量',
                    type: "line",
                    barWidth:50,
                    lineStyle: {
                        normal: {
                            width: 3,
                            shadowColor: 'rgba(0,0,0,0.4)',
                            shadowBlur: 6,
                            shadowOffsetY: 10
                        }
                    },
                    markPoint: {//最大值 最小值
                        data: [
                            {type: 'max', name: '最大值'},
                            {type: 'min', name: '最小值'}
                        ]
                    },
                    markLine: { //平均值
                        data: [
                            {type: 'average', name: '平均值'}
                        ],
                        label:{
                            color: '#0099FF',
                            position:"middle"  //将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
                        }
                    },
                    data:[],
                    // smooth: true //折线折脚
                },
                 {
                    name:'匹配量',
                    type: "line",
                    barWidth:50,
                    lineStyle: {
                        normal: {
                            width: 3,
                            shadowColor: 'rgba(0,0,0,0.4)',
                            shadowBlur: 6,
                            shadowOffsetY: 10
                        }
                    },
                    markPoint: {//最大值 最小值
                        data: [
                            {type: 'max', name: '最大值'},
                            {type: 'min', name: '最小值'}
                        ]
                    },
                    markLine: { //平均值
                        data: [
                            {type: 'average', name: '平均值'}
                        ],
                        label:{
                            color: '#67c23a',
                            position:"middle"  //将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
                        }
                    },
                    data:[],
                    // smooth: true //折线折脚
                },
                {
                    name:'不匹配量',
                    type: "line",
                    barWidth:50,
                    lineStyle: {
                        normal: {
                            width: 3,
                            shadowColor: 'rgba(0,0,0,0.4)',
                            shadowBlur: 6,
                            shadowOffsetY: 10
                        }
                    },
                    markPoint: {//最大值 最小值
                        data: [
                            {type: 'max', name: '最大值'},
                            {type: 'min', name: '最小值'}
                        ]
                    },
                    markLine: { //平均值
                        data: [
                            {type: 'average', name: '平均值'}
                        ],
                        label:{
                            color: '#d14a61',
                            position:"middle"  //将警示值放在哪个位置，三个值“start”,"middle","end"  开始  中点 结束
                        }
                    },
                    data:[],
                    // smooth: true //折线折脚
                },
                ]
            },
        }
    },
    methods: {
        //获取列表数据
        getTableDtate(){
            this.$api.post( this.API.cpus + 'consumerflashhourstatistics/list',this.sensitiveCondition,res=>{
                this.option.xAxis.data=[]
                this.option.series[0].data=[]
                this.option.series[1].data=[]
                this.option.series[2].data=[]
                for(let i = 0; i < res.data.length;i++){
                    this.option.xAxis.data.push(res.data[i].statisticsTime); //横坐标
                    this.option.series[0].data.push(res.data[i].chargeNum)//总计费量
                    this.option.series[1].data.push(res.data[i].successNum)//匹配量
                    this.option.series[2].data.push(res.data[i].failNum)//不匹配量
                }
            })
        },
        // //获取查询时间的开始时间和结束时间
        hande1: function (val) {
            if(val){
                this.sensitiveCondition.beginTime = this.moment(val[0]).format("YYYY-MM-DD")+' 00:00:00';
                this.sensitiveCondition.endTime = this.moment(val[1]).format("YYYY-MM-DD")+' 23:59:59';
            }else{
                this.sensitiveCondition.beginTime = '';
                this.sensitiveCondition.endTime = '';
            }
        },
    },
    created(){
        this.$api.get( this.API.cpus + 'consumeronelogin/app/all?type=onepass',{},res=>{
            this.AppAll=res.data
        })
        this.getTableDtate()
    },
    // activated(){
    //     this.$api.get( this.API.cpus + 'consumeronelogin/app/all?type=onepass',{},res=>{
    //         this.AppAll=res.data
    //     })
    // },
    watch:{
        sensitiveCondition:{ 
            handler(val){
                this.getTableDtate()
            },
            deep:true,
            immediate:true
        },
    }
}
</script>
<style scoped>
.OuterFrame {
    padding: 20px;    
}
.demo-form-inline .el-form-item{
    margin-right: 50px;
}
.spanStyleG{
    display: inline-block;
    border: 1px solid;
    padding: 5px 10px;
    background: rgb(22, 165, 137);
    color: #fff;
}
.dataScreening-chart-box{
    margin-top: 40px;
}
</style>
<style>
.el-table--small th{
    background:#f5f5f5;
}
.OuterFrameList .el-carousel__button{
    background-color:#16a589;
}
</style>


