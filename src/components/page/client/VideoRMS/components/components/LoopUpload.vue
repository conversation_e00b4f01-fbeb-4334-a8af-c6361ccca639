<template>
    <div class="LoopDiv">
        <div>
            <!-- <p class="type_title">
                <span>视频介绍</span>
            </p> -->
            <div class="pic_img albumvideo" style="border: 1px solid #16A589;border-radius: 5px;">
                <div  v-loading="loadingFlag " :element-loading-text="titleW" class="picDiv pic_top" @mouseover="mouseOver" @mouseleave="mouseLeave">
                    <img src="@/assets/images/1395fa6f-07d1-45af-85f9-7adfaed8be48.png" v-if="children.showVideoPath||children.showaudioPath||children.imageUrl" @click="FileRemoval" class="imgX" :style="styleShow?'display: block;':'display: none;'" alt="">
                    <!-- 内容展示 -->
                    <div class="videoF" style="padding: 10px 10px 0px;position: absolute;left: 50%;transform: translate(-50%, 0);">
                        <img v-if="children.imageUrl" :src="children.imageUrl" class="avatar video-avatar"  ref="avatar">
                    </div>
                    <div class="videoF" style="padding: 10px 10px 0 10px;position: absolute;">
                        <video v-if="children.showVideoPath"
                            v-bind:src="children.showVideoPath"
                            class="avatar video-avatar"
                            style="width: 300px;"
                            controls="controls">
                            您的浏览器不支持视频播放
                        </video>
                    </div>
                    <div class="videoF" style="padding: 10px 10px 0px;position: absolute;top: 25%;left: 1%;">
                        <audio 
                            v-if="children.showaudioPath"
                            autoplay="autoplay"
                            controls="controls"
                            preload="auto"
                            v-bind:src="children.showaudioPath">
                        </audio>
                    </div>
                    <el-progress v-if="videoFlag == true"
                        type="circle"
                        :format="format"
                    style="margin-top: 7px;position: absolute;top: 50%;left: 50%;transform: translate(-50%, -50%);">
                    <!-- <span>视频压缩中，请稍等...</span> -->
                    </el-progress>
                    <!-- 内容展示 -->
                    <!-- 图片上传 -->
                    <div v-if="divFlag" class="pic_img_box">
                        <el-upload 
                            v-if="!children.imageUrl&&children.showVideoPath ==''&&children.showaudioPath==''" 
                            class="avatar-uploader"
                            :auto-upload="true"
                            :action="this.API.cpus+'v3/file/uploadAndCompress'"
                            v-bind:data="{FoldPath:'上传目录',SecretKey:'安全验证',fileType:'pic'}"
                            :headers="header"
                            :show-file-list="false"
                            :on-success="handleAvatarSuccess"
                            :before-upload="beforeAvatarUpload">
                                <div v-if="!children.imageUrl" class="icon iconfont">
                                    <i v-if="!children.imageUrl" class="iconStyle el-icon-picture"></i>
                                </div>
                                <div v-if="!children.imageUrl">
                                    <span class="iconFont">添加图片</span>
                                </div>
                        </el-upload>
                    </div>
                    <!-- 图片上传 -->
                    <!-- 视频上传 -->
                    <div v-if="divFlag" class="pic_img_box">
                        <el-upload 
                            v-if="!children.imageUrl&&children.showVideoPath =='' &&children.showaudioPath==''"
                            class="avatar-uploader" 
                            :action="this.API.cpus+'v3/file/uploadAndCompress'"
                            :headers ='header'
                            v-bind:data="{FoldPath:'上传目录',SecretKey:'安全验证',fileType:'video'}"
                            v-bind:on-progress="uploadVideoProcess"
                            v-bind:on-success="handleVideoSuccess"
                            v-bind:before-upload="beforeUploadVideo"
                            v-bind:show-file-list="false">
                            <div class="icon iconfont">
                                <i v-if="children.showVideoPath ==''"
                                class="iconStyle icon-icon-test"></i>
                            </div>
                            <div v-if="children.showVideoPath =='' ">
                                <span class="iconFont">添加视频</span>
                            </div>
                        </el-upload>
                    </div>
                    <!-- 视频上传 -->
                    <!-- 音频上传 -->
                    <div v-if="divFlag" class="pic_img_box">
                        <el-upload 
                            v-if="!children.imageUrl&&children.showVideoPath ==''&&children.showaudioPath==''"
                            class="avatar-uploader" 
                            :action="this.API.cpus+'v3/file/uploadAndCompress'"
                            :headers ='header'
                            v-bind:data="{FoldPath:'上传目录',SecretKey:'安全验证',fileType:'audio'}"
                            v-bind:on-success="handleAudioSuccess"
                            v-bind:before-upload="beforeUploadAudio"
                            v-bind:show-file-list="false">
                            <div class="icon iconfont">
                                <i v-if="children.showaudioPath==''"
                                class="iconStyle icon-icon-test"></i>
                            </div>
                            <div v-if="children.showaudioPath==''">
                                <span class="iconFont">添加音频</span>
                            </div>
                        </el-upload>
                    </div>
                    <!-- 音频上传 -->
                    <div style="text-align: center;" v-if="!children.imageUrl&&children.showVideoPath =='' && !videoFlag&&children.showaudioPath==''&&divFlag">
                        <p style="margin: 20px;">点击上传文件</p>
                        <p style="color: #ccc;">支持文件格式：.jpg .png .gif .mp3 .mp4 .3gp</p>
                        <p style="color: #ccc;margin-top:5px">文件大于1.8MB自动压缩</p>
                    </div>
                </div>
                <div class="picDiv pic_bottom">
                    <el-input type="textarea" v-model="children.txt" placeholder="请输入内容" class="textarea"></el-input>
                </div>
            </div>
            <div>
                <div class="icon iconfont" style="text-align: center;margin-top: 10px;">
                    <span class="timeDelet" title="时间" @click="timeShow">
                        <i class="iconClass icon-shijian"></i>
                    </span>
                    <span class="timeDelet" title="删除此帧" @click="deleteLoop">
                        <i class="iconClass icon-shanchu"></i>
                    </span>
                </div>
                <div style="padding: 0 10px;" v-if="timeFlag">
                    <el-slider
                        v-model="children.time"
                        :max="15"
                        :step="1">
                    </el-slider>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { mapState, mapMutations,mapActions } from "vuex";
export default {
    name: "Loopupload",
    props: {children:Object},
    data () {
        return {
            header:{},//token
            videoFlag: false,//是否显示进度条
            videoUploadPercent: "",//进度条的进度，
            isShowUploadVideo: false,//显示上传按钮
            timeFlag:false,
            styleShow:false,
            timer:null,
            loadingFlag:false,
            divFlag:true,
            titleW:""
        }
    },
    components:{

    },
    methods:{
        handleClick(tab, event) {
            console.log(tab, event);
        },
        format(){
            return '视频加载中···'
        },
        // 视频上传功能》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》
        //上传前回调
        beforeUploadVideo(file) {
            // console.log(file,'file');
            // debugger
            // var fileSize = file.size / 1024 < 2048;
            const isLt5M = file.size / 1024 / 1024 < 30;
            if (['video/mp4'].indexOf(file.type) == -1&&['video/3gpp'].indexOf(file.type) == -1) {
            // if (['audio/mid'].indexOf(file.type) == -1||['audio/midi'].indexOf(file.type) == -1) {
                this.$message({
                    type: 'warning',
                    message:"请上传正确的视频格式"
                });
                return false;
            }
            if (!isLt5M) {
                this.$message({
                    type: 'warning',
                    message:"视频大小不能超过30MB"
                });
                return false;
            }
            this.isShowUploadVideo = false;
        },
        //进度条
        uploadVideoProcess(event, file, fileList) {
            // this.videoFlag = true;
            this.loadingFlag =true
            this.divFlag = false
            this.titleW='视频压缩中大致需要30秒至60秒···'
            // console.log(file);
            // this.videoUploadPercent = file.percentage.toFixed(0) * 1;
        },
        settimer(data,file,type){
            // console.log(id);
            // console.log(type,'ll');
            // console.log(data,'data');
            this.$api.get(this.API.cpus+'v3/file/'+data.fileId,{},res=>{
                // console.log(res);
                if(!res.data.fullpath){
                    this.timer = setTimeout(()=>{
                    this.settimer(data,file,type)
                },3000)
                }else{
                    this.loadingFlag = false
                    // console.log(11111111);
                    clearTimeout(this.timer);
                    if(type==1){
                        // console.log('audio');
                        this.children.showaudioPath =this.API.imgU+ res.data.fullpath;
                        this.children.size=res.data.fileSize / 1024 
                        this.children.media=res.res.data.fileName.split('.')[res.data.fileName.split(".").length-1]
                        this.children.mediaGroup=res.data.group
                        this.children.mediaPath=res.data.path
                        // this.children.type='audio'
                    }else{
                        // console.log('video');
                        this.children.showVideoPath = this.API.imgU+ res.data.fullpath;
                        this.children.media=res.data.fileName.split('.')[res.data.fileName.split(".").length-1]
                        this.children.size=res.data.fileSize / 1024 
                        this.children.mediaGroup=res.data.group
                        this.children.mediaPath=res.data.path
                        this.children.type='video'
                    }
                    
                    // console.log(this.children,'this.children');
                }
                // console.log(this.children);
                
                    // if(ress.code == 200){
                    //     this.videoFlag = false;
                    //     this.children.showVideoPath =this.API.imgU+ res.data.fullpath;
                    // }else{
                    //         this.$message({
                    //             type: 'warning',
                    //             message:res.code
                    //         });
                    // }
                })
        },
        //上传成功回调
        handleVideoSuccess(res, file) {
            if(res.code == 200){
                this.isShowUploadVideo = true;
                if(res.data.fullpath){
                // this.videoFlag = false;
                this.loadingFlag = false
                this.children.showVideoPath = this.API.imgU+ res.data.fullpath;
                this.children.media=file.name.split('.')[file.name.split(".").length-1]
                // console.log(file.name.split('.')[file.name.split(".").length-1],'file.name.split');
                this.children.size=res.data.fileSize / 1024 
                this.children.mediaGroup=res.data.group
                this.children.mediaPath=res.data.path
                this.children.type='video'
            }else{
                // console.log(res.);
                this.settimer(res.data,file,2)
                
            }
            }else{
                this.loadingFlag = false
                this.divFlag = true
                this.$message({
                    type: 'warning',
                    message:'上传失败，请重新上传'
                });
            }
            // console.log(res,'res');
            // console.log(file,'file');
            
            
            // this.videoUploadPercent = 0;
            
            
            // 前台上传地址
            // if (file.status == 'success' ) {
            //     this.children.showVideoPath =this.API.imgU+file.response.data.fullpath;
            //     this.children.size=file.size / 1024 
            //     this.children.media=file.name.split('.')[1]
            //     this.children.mediaGroup=res.data.group
            //     this.children.mediaPath=res.data.path
            //     this.children.type='video'
            // } else {
            //     this.$message({
            //         type: 'warning',
            //         message:'上传失败，请重新上传'
            //     });
            // }
            // //后台上传地址
            // if (res.code == 200) {
            //     this.children.showVideoPath =this.API.imgU+ res.data.fullpath;
            //     this.children.size=file.size / 1024 
            //     this.children.media=file.name.split('.')[1]
            //     this.children.mediaGroup=res.data.group
            //     this.children.mediaPath=res.data.path
            // } else {
            //     this.$message({
            //         type: 'warning',
            //         message:res.Message
            //     });
            // }
        },
        // 时间展示
        timeShow(){
            this.timeFlag=!this.timeFlag
        },
        // 视频上传功能《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《


        // 图片上传功能 》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》
        // 上传成功
        handleAvatarSuccess(res, file) {
            // console.log(file,'file');
            // console.log(res);
            this.titleW='图片压缩中大致需要30秒至60秒···'
            this.loadingFlag = true
            this.divFlag = false
            if(res.code ==200){
                this.loadingFlag = false
                this.children.imageUrl = URL.createObjectURL(file.raw);
                this.children.size=res.data.fileSize / 1024 
                this.children.media=file.name.split('.')[file.name.split(".").length-1]
                this.children.mediaGroup=res.data.group
                this.children.mediaPath=res.data.path
                this.children.type='img'
            }else{
                this.loadingFlag = false
                this.divFlag = true
                this.$message({
                    type: 'warning',
                    message:'上传失败，请重新上传'
                });
            }
            
        },
        beforeAvatarUpload(file) {
            // console.log(file,'file');
            const siJPGGIF = file.name.split('.')[file.name.split(".").length-1]
            const isLt5M = file.size / 1024 / 1024 < 30;
            if (siJPGGIF!='jpg'&&siJPGGIF!='gif'&&siJPGGIF!='png') {
                this.$message.warning('上传头像图片只能是 jpg、gif 、png格式!');
                return false;
            }
            if (!isLt5M) {
                this.$message.warning('上传头像图片大小不能超过 30MB!');
                return false;
            }
            
            // return isJPG && isLt2M;
        },
        // 图片上传功能 《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《
        // 音频上传功能》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》》
        //上传前回调
        beforeUploadAudio(file) {
            // var fileSize = file.size / 1024 < 2048;
            const isLt5M = file.size / 1024 / 1024 < 30;
             if (['audio/mpeg'].indexOf(file.type) == -1&&['audio/mp3'].indexOf(file.type) == -1) {
            // if (['audio/mid'].indexOf(file.type) == -1||['audio/midi'].indexOf(file.type) == -1) {
                this.$message({
                    type: 'warning',
                    message:"请上传正确的音频格式"
                });
                return false;
            }
            if (!isLt5M) {
                this.$message({
                    type: 'warning',
                    message:"音频大小不能超过30MB"
                });
                return false;
            }
            this.isShowUploadVideo = false;
        },
        //上传成功回调
        handleAudioSuccess(res, file) {
            this.isShowUploadVideo = true;
            this.titleW='音频压缩中大致需要30秒至60秒···'
            this.loadingFlag = true
            this.divFlag = false
            // this.videoFlag = false;
            this.audioUploadPercent = 0;
            if(res.code == 200){
                if(res.data.fullpath){
                // this.videoFlag = false;
                this.loadingFlag = false
                this.children.showaudioPath =this.API.imgU+ res.data.fullpath;
                this.children.size=res.data.fileSize / 1024 
                this.children.media=res.data.fileName.split('.')[res.data.fileName.split(".").length-1]
                
                this.children.mediaGroup=res.data.group
                this.children.mediaPath=res.data.path
                this.children.type='audio'
            }else{
                // console.log(res.);
                this.settimer(res.data,file,1)
                
            }
            }else{
                this.loadingFlag = false
                this.divFlag = true
                this.$message({
                    type: 'warning',
                    message:'上传失败，请重新上传'
                });
            }
            
            // 前台上传地址
            // if (file.status == 'success' ) {
            //    this.children.showaudioPath =this.API.imgU+file.response.data.fullpath;
            //     this.children.size=file.size / 1024 
            //     this.children.media=file.name.split('.')[1]
            //     this.children.mediaGroup=res.data.group
            //     this.children.mediaPath=res.data.path
            //     this.children.type='audio'
            // } else {
            //     this.$message({
            //         type: 'warning',
            //         message:'上传失败，请重新上传'
            //     });
            // }
            // //后台上传地址
            // if (res.code == 200) {
            //     this.children.showaudioPath =this.API.imgU+ res.data.fullpath;
            //     this.children.size=file.size / 1024 
            //     this.children.media=file.name.split('.')[1]
            //     this.children.mediaGroup=res.data.group
            //     this.children.mediaPath=res.data.path
            // } else {
            //     this.$message({
            //         type: 'warning',
            //         message:res.Message
            //     });
            // }
        },
        // 音频上传功能 《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《《

        // 组件删除
        deleteLoop(){
           this.$emit('childrenChange', '')
        },
        // 鼠标事件
        mouseOver(){
            this.styleShow=true
        },
        mouseLeave(){
            this.styleShow=false
        },
        // 文件移除
        FileRemoval(){
            this.$confirm('确认移除当前文件？', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.children.showVideoPath=''//视频url
                this.children.showaudioPath=''//音频url
                this.children.imageUrl=''//图片url
                this.children.mediaGroup=''
                this.children.mediaPath=''
                this.children.media=''
                this.children.size=0
                this.divFlag = true
            })
        }
    },
    mounted(){
        this.header = {Authorization :"Bearer"  + this.$common.getCookie('ZTGlS_TOKEN')};
    },
}
</script>
<style scoped>
@media screen and (min-width: 1200px){
    .video-avatar{
    height: 200px;
    max-width: 300px;
}
.albumvideo{
    padding: 10px;
    height: 530px;
}
.pic_img_box{
display: inline-block;
margin: 20px;
}
.pic_top{
    position: relative;
    border: 1px dashed #ccc;
    height: 230px;
    width: 100%;
    margin-bottom: 20px;
    text-align: center;
}
.iconStyle{
    font-style: normal;
    font-size: 35px;
    color: darksalmon;
}
.iconClass{
    font-style: normal;
    color: #fff;
    font-size: 25px;
}
.timeDelet{
    margin: 0 20px;
    display: inline-block;
    border: 1px solid #16A589;
    padding: 5px;
    border-radius: 5px;
    background: #16A589;
    cursor: pointer;
}
.LoopDiv{
    width: 100%;;
}
.imgX{
    width: 20px;position: absolute;top: 1px;right: 1px;z-index:100;cursor: pointer;
}
}
@media screen and (max-width: 1200px){
    .video-avatar{
    height: 200px;
    max-width: 300px;
}
.albumvideo{
    width: 200px;
    padding: 10px;
    height: 530px;
}
.pic_img_box{
display: inline-block;
margin: 5px;
}
.pic_top{
    position: relative;
    border: 1px dashed #ccc;
    height: 230px;
    width: 100%;
    margin-bottom: 20px;
    text-align: center;
}
.iconStyle{
    font-style: normal;
    font-size: 35px;
    color: darksalmon;
}
.iconClass{
    font-style: normal;
    color: #fff;
    font-size: 25px;
}
.timeDelet{
    margin: 0 20px;
    display: inline-block;
    border: 1px solid #16A589;
    padding: 5px;
    border-radius: 5px;
    background: #16A589;
    cursor: pointer;
}
.LoopDiv{
    width: 100%;;
}
.imgX{
    width: 20px;position: absolute;top: 1px;right: 1px;z-index:100;cursor: pointer;
}
}


</style>
<style >
.LoopDiv .textarea >textarea{
    height: 270px !important;
    resize: none !important;
    background: white !important;
    border: 1px dashed #CACACA !important;
    border-radius: 5px;
}
/* .el-upload--text{
    height: 70px !important;
} */
</style>