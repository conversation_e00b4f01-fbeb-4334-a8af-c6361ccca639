<template>
    <div>
        <div class="upVideo">
            <div>
                <el-upload
                    :class="{hide: hideUpload}"
                    :action="action"
                    :headers="token"
                    list-type="picture-card"
                    v-bind:data="{FoldPath: '上传目录', SecretKey: '安全验证',}"
                    :file-list="fileList"
                    :limit="1"
                    v-bind:on-progress="uploadVideoProcess"
                    v-bind:before-upload="beforeUploadVideo"
                    :on-remove="handleRemove"
                    :on-success="handleSuccess"
                >
                    <i slot="default" class="el-icon-plus"></i>
                    <div slot="file" slot-scope="{file}">
                        <video style="width: 146px;height: 146px;" :src="path" controls="controls"></video>
                        <span class="el-upload-list__item-actions">
                            <span v-if="!disabled" class="el-upload-list__item-delete" @click="handleRemove">
                                <i class="el-icon-delete"></i>
                            </span>
                        </span>
                    </div>
                </el-upload>
            </div>
            <div v-if="formInline.url">
                <div class="detailsList">
                    <span class="detailsList-title">文件名称:</span>
                    <span class="detailsList-content">
                        {{ formInline.fileName }}
                    </span>
                </div>
                <div class="detailsList">
                    <span class="detailsList-title">原始文件大小:</span>
                    <span class="detailsList-content">
                        <div class="detailsList-text">
                            {{ formInline.size + 'MB' }}
                        </div>
                        <!-- <div style="flex; 1; margin-left: 16px; display: flex; align-items: center;">
                            <el-input-number
                                style="width: 180px; margin-right: 5px;"
                                class="input_number"
                                :precision="2"
                                :step="0.01"
                                v-model="videoInpSize"
                                @change="videoInpSizeChange"
                                placeholder="压缩后视频预期大小"
                            />MB
                        </div> -->
                    </span>
                </div>
                <!-- <div class="detailsList">
                    <span class="detailsList-title">视频宽:</span>
                    <span class="detailsList-content">
                        <div class="detailsList-text">
                            {{ formInline.width }}
                        </div>
                        <el-slider :step="10" style="flex: 1; margin: 0 16px;" :format-tooltip="() => {return editFormInline.width}" v-model="videoWidth" @input="handelChange($event, 'videoWidth')"></el-slider>
                    </span>
                </div>
                <div class="detailsList">
                    <span class="detailsList-title">视频高:</span>
                    <span class="detailsList-content">
                        <div class="detailsList-text">
                            {{ formInline.height }}
                        </div>
                        <el-slider :step="10" style="flex: 1; margin: 0 16px;" :format-tooltip="() => {return editFormInline.height}" v-model="videoHeight" @input="handelChange($event, 'videoHeight')"></el-slider>
                    </span>
                </div>
                <div class="detailsList">
                    <span class="detailsList-title">视频帧数:</span>
                    <span class="detailsList-content">
                        <div class="detailsList-text">
                            {{ formInline.frameRate }}
                        </div>
                        <el-slider :step="10" style="flex: 1; margin: 0 16px;" :format-tooltip="() => {return editFormInline.frameRate}" v-model="videoFrameRate" @input="handelChange($event, 'videoFrameRate')"></el-slider>
                    </span>
                </div> -->
                <div class="detailsList">
                    <span class="detailsList-title">清晰度:</span>
                    <span class="detailsList-content">
                        <div class="detailsList-text">
                            {{ formInline.videoBitRate }}
                        </div>
                        <el-slider :step="1" style="flex: 1; margin: 0 16px;" :format-tooltip="() => {return editFormInline.videoBitRate}" v-model="videoBitRate" @input="handelChange($event, 'videoBitRate')"></el-slider>
                    </span>
                </div>
                <div class="detailsList">
                    <span class="detailsList-title">压缩预估大小:</span>
                    <span class="detailsList-content">
                        {{ formInline.videoChangeSize }} MB
                    </span>
                </div>
                <!-- <div class="detailsList">
                    <span class="detailsList-title">音质:</span>
                    <span class="detailsList-content">
                        <div class="detailsList-text">
                            {{ formInline.audioBitRate }}
                        </div>
                        <el-slider :step="10" style="flex: 1; margin: 0 16px;" :format-tooltip="() => {return editFormInline.audioBitRate}" v-model="audioBitRate" @input="handelChange($event, 'audioBitRate')"></el-slider>
                    </span>
                </div> -->
            </div>
        </div>
        <div class="demarcation"></div>
        <div class="upVideo">
            <div v-if="formInline.url" class="btn_video">
                <p class="tips_video">注：视频短信容量最大不超过1.8M，压缩视频需注意图片和文字大小；</br>如：图片300kb，文字50kb，则视频建议在1.5M内</p>
                <el-button v-if="!pressLoding" type="primary" @click="compressVideo">
                    <i class="iconfont icon-compress-video" style="">压缩</i>
                </el-button>
                <el-button v-else type="primary" :loading="true">
                    <span v-if="showTip">{{ showTip }}</span>
                    <span v-else>压缩中</span>
                </el-button>
                <div style="margin-top: 5px;">
                    <el-button v-if="videoPath"  type="primary" style="width: 83px;">
                        <a style="color: #fff;" @click="downVideo(videoPath)" href="javascript:void(0)" rel="noopener noreferrer">
                            <i class="iconfont icon-download-1-copy" style="">下载</i>
                        </a>
                    </el-button>
                </div>
                
            </div>
            <div v-if="videoPath !=''" class="video_time">
                <div style="width: 500px; margin: 0 auto;">
                    <video style="width: 100%; height: 240px;" :src="videoPath" controls="controls"></video>
                </div>
                <span style="margin: 6px auto 0;" v-if="pressedData.size">压缩后文件大小：{{pressedData.size}}MB</span>
            </div>
        </div>
        <!-- <div class="btns">
            <div style="float: right;">
                <el-button @click="compressVideoDia = false">取 消</el-button>
                <el-button type="primary" @click="compressVideoDia = false">确 定</el-button>
            </div>
        </div> -->
    </div>
</template>

<script>
export default {
    props: {
        compressVideoDia: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            action: this.API.fiveWeb + "/compress/upload",
            // action: this.API.cpus + "v3/file/uploadFile",
            token: {},
            fileList: [],
            videoFlag: false,
            videoUploadPercent: "",
            loadingFlag: false,
            titleW: "",
            path: "",
            disabled: false,
            formInline: {},
            formInlineCopy: {},
            editFormInline: {},
            videoPath:"",
            pressLoding:false,
            videoWidth: 100,
            videoHeight: 100,
            videoFrameRate: 100,
            videoBitRate: 100,
            audioBitRate: 100,
            compressId: "",
            pressTimmer: "",
            showTip: "",
            pressedData: {},
            hideUpload: false,
            // videoInpSize: "",
            videoInpSizeFlag: "",
            videoChangeSize: "",
        }
    },
    methods: {
        // 清除素材地址
        handleRemove() {
            this.fileList = [];
            this.path = ''
            this.formInline =  {}
            this.formInlineCopy =  {}
            this.editFormInline = {}
            this.videoPath = ""
            this.hideUpload = false
            this.videoWidth = 100
            this.videoHeight = 100
            this.videoFrameRate = 100
            this.videoBitRate = 100
            this.audioBitRate = 100
            this.compressId = ""
            this.showTip = ""
            this.pressedData = {}
            // this.videoInpSize = ""
            this.videoChangeSize = ""
            if (this.pressTimmer) {
                window.clearInterval(this.pressTimmer)
                this.pressTimmer = ""
            }
        },

        // 上传成功钩子函数
        handleSuccess(res, fileList) {
            // console.log(res, 'res');
            if (res.code == 200) {
                this.videoFlag = false;
                this.videoUploadPercent = 0;
                this.path = res.data.url
                this.formInline = res.data
                this.formInlineCopy = JSON.parse(JSON.stringify(res.data))
                let obj = JSON.stringify(res.data)
                this.editFormInline = JSON.parse(obj)
                // this.videoInpSize = res.data.size
                this.formInline.size = res.data.size
                this.hideUpload = true
            } else {
                this.$message({
                    type: "error",
                    message: res.msg,
                });
                this.fileList = [];
                this.path = ''
                this.loadingFlag = false;
                this.hideUpload = false
            }
        },
        
        
        uploadVideoProcess(event, file, fileList) {
            this.videoFlag = true;
            this.videoUploadPercent = file.percentage.toFixed(0) * 1;
            this.loadingFlag = true;
            this.hideUpload = true
            // this.titleW = "视频压缩中大致需要30秒至60秒···";
        },

        beforeUploadVideo(file) {
            console.log(file.type, "file.type")
            const isLt5M = file.size / 1024 / 1024 < 50;
            if (
                ["video/mp4"].indexOf(file.type) == -1 &&
                ["video/WEBM"].indexOf(file.type) == -1
            ) {
                this.$message({
                    type: "warning",
                    message: "请上传正确的视频格式",
                });
                return false;
            }
            if (!isLt5M) {
                this.$message({
                    type: "warning",
                    message: "视频大小不能超过20MB",
                });
                return false;
            }
            // this.upVideo = false;

        },
        
        //压缩
        compressVideo() {
            this.pressLoding = true
            this.editFormInline.width = 640
            this.editFormInline.height = 360
            // console.log("压缩", this.editFormInline)
            this.$api.post(
                this.API.fiveWeb + "/compress/asynVideo",
                this.editFormInline,
                (res) => {
                    // console.log("开始压缩", res)
                    if(res.code == 200){
                        if (res.data) {
                            this.compressId = res.data
                            this.getCompressVideoResult()
                        } else {
                            this.$message.error("解压错误，请重试");
                        }
                    }else{
                        this.$message.error(res.msg);
                    }

                    // if(res.code == 200){
                    //     this.pressLoding = false
                    //     this.videoPath = res.data.url
                    //     this.editFormInline.size = res.data.size
                    // }else{
                    //     this.$message.error(res.msg);
                    // }

                    // this.tableDataObj.tableData = res.data.records;
                    // this.tableDataObj.total = res.data.total;
                    // this.tableDataObj.loading2 = false;
                    // this.$refs.plTable.reloadData(res.data.records);
                }
            );
        },

        getCompressVideoResult() {
            // console.log("this.pressTimmer", this.pressTimmer)
            if (!this.pressTimmer) {
                this.pressTimmer = window.setInterval(() => {
                    this.$api.get(
                        this.API.fiveWeb + "/compress/result",
                        {compressId: this.compressId},
                        (res) => {
                            // console.log("压缩结果", res)
                            if (res.code === 200) {
                                window.clearInterval(this.pressTimmer)
                                // console.log("压缩成功", res)
                                this.pressLoding = false
                                this.videoPath = res.data.url
                                this.pressedData = res.data
                                // this.editFormInline.size = res.data.size
                                this.pressTimmer = ""
                            } else if (res.code === 1) {
                                this.showTip = res.msg
                            } else {
                                this.$message.error(res.msg);
                                window.clearInterval(this.pressTimmer)
                                this.pressTimmer = ""
                                this.pressLoding = false
                            }
                        }
                    );
                }, 3000)
            }
            
        },

        //下载
        downVideo(url){
            window.open(`${url}?response-content-type=application%2Foctet-stream`)
        },

        calculatePercentage(originalValue, percentage) {
            return (originalValue * (percentage / 100)).toFixed(0)
        },

        // 计算预期清晰度
        // videoInpSizeChange(e) {
        //     if (e && e > 0) {
        //         if (e > this.editFormInline.size) {
        //             if (!this.videoInpSizeFlag) {
        //                 this.$message({
        //                     message: '压缩预期大小需小于原始文件大小！',
        //                     type: 'warning'
        //                 });
        //                 this.videoInpSizeFlag = true
        //                 setTimeout(() => {
        //                     this.videoInpSizeFlag = false
        //                 }, 2000)
        //             }
        //         } else {
        //             // console.log("原视频大小：", this.editFormInline.size, " 压缩后大小：", e)
        //             // console.log("?", (e / this.editFormInline.size).toFixed(2))
        //             this.videoBitRate = e / this.editFormInline.size.toFixed(2) * 100
        //         }
        //     } else {
        //         this.videoBitRate = 100
        //     }
        //     // console.log("this.videoBitRate", this.videoBitRate)
        // },

        handelChange(e, tag) {
            switch (tag) {
                case 'videoWidth':
                    this.formInline.width = JSON.parse(JSON.stringify(this.formInlineCopy.width))
                    this.editFormInline.width = this.calculatePercentage(this.formInline.width, e)
                    this.formInline.width = this.editFormInline.width
                    break;
                case 'videoHeight':
                    this.formInline.height = JSON.parse(JSON.stringify(this.formInlineCopy.height))
                    this.editFormInline.height = this.calculatePercentage(this.formInline.height, e)
                    this.formInline.height = this.editFormInline.height
                    break;
                case 'videoFrameRate':
                    this.formInline.frameRate = JSON.parse(JSON.stringify(this.formInlineCopy.frameRate))
                    this.editFormInline.frameRate = this.calculatePercentage(this.formInline.frameRate, e)
                    this.formInline.frameRate = this.editFormInline.frameRate
                    break;
                case 'videoBitRate':
                    this.formInline.videoBitRate = JSON.parse(JSON.stringify(this.formInlineCopy.videoBitRate))
                    this.editFormInline.videoBitRate = this.calculatePercentage(this.formInline.videoBitRate, e)
                    this.formInline.videoBitRate = this.editFormInline.videoBitRate
                    this.formInline.videoChangeSize = (this.formInline.size * (e / 100)).toFixed(2)
                    break;
                case 'audioBitRate':
                    this.formInline.audioBitRate = JSON.parse(JSON.stringify(this.formInlineCopy.audioBitRate))
                    this.editFormInline.audioBitRate = this.calculatePercentage(this.formInline.audioBitRate, e)
                    this.formInline.audioBitRate = this.editFormInline.audioBitRate
                    break;
                default:
                    break;
            }
            // console.log("!!!!!!", this.editFormInline)
        }
    },
    mounted() {
        this.token = { Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"), };
    },
    watch: {
        compressVideoDia: {
            handler(newValue) {
                if (!newValue) {
                    this.handleRemove()
                }
            },
            immediate: true
        }
    }
}
</script>

<style lang="less" scoped>
.btns {
    width: 100%;
    height: 36px;
}
.upVideo {
    display: flex;
    position: relative;
}

.detailsList {
    /* height: 42px; */
    line-height: 42px;
    padding-left: 30px;
    font-size: 12px;
    position: relative;
    display: flex;
}

.bs {
    position: absolute;
    top: 0px;
    left: 100px;
    color: red;
    font-size: 14px;
    z-index: 9999;
}

.detailsList-title {
    display: inline-block;
    width: 80px;
}

.detailsList-content {
    display: flex;
    width: 300px;
    color: #848484;
    padding-left: 10px;
    border-bottom: 1px solid #eee;
}
.detailsList-text {
    width: 50px;
}
.btn_video{
    position: absolute;
    // top: 50%;
    left: 50%;
    margin-left: -50px;
    padding-top: 35px;
    .tips_video {
        position: absolute;
        width: 450px;
        top: 0px;
        left: -130px;
        font-size: 12px;
        color: #999;
    }
}
.video_time{
    width: 100%;
    display: flex;
    margin-top: 115px;
    flex-direction: column;
}
.demarcation{
    width: 100%;
    height: 1px;
    margin: 10px 0;
    border-top: 1px dashed #ccc
}

// .hide {
//     display: none;
// }
.hide {
    /deep/ .el-upload--picture-card {
        display: none;
    }
}

</style>

<style lang="less">

.el-input-number__decrease,
.el-input-number__increase {
    display: none;
}

.input_number {
    .el-input-number,
    .el-input__wrapper {
        padding: 0;
    }
    .el-input__inner {
        text-align: left;
        padding-left: 11px;
    }
}
</style>