<template>
  <div class="login_cell_phone">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> {{Vtitle}}</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="Templat-matter">
      <p style="font-weight: bolder">温馨提醒：</p>
      <p>
        模板报备：支持变量模板，变量请使用英文{}进行包裹，例如：尊敬的{user}。网页端发送时变量模板必须使用excel表格提交，表头必须与大括号{}内的变量名保持一致。
      </p>
      <p>
        格式要求：支持.xlsx .xls等格式；手机号码需在A列，变量参数则为 英文字母
        代替
      </p>
      <p>变量支持：最多只支持9个变量。</p>
      <p>模板签名：第一帧的文本开头以【】包住。</p>
    </div>
    <!-- 视频短信标题 -->
    <div class="fillet shortChain-box" style="margin-top: 20px; background: #fff">
      <p style="
          font-size: 20px;
          font-weight: 600;
          padding: 20px;
          border-bottom: 1px solid #eaeaea;
        ">
        视频短信标题
      </p>

      <div style="margin: 0px 50px; padding: 20px 0px">
        <el-input v-model="title" maxlength="70"></el-input>
      </div>
    </div>


    <el-tooltip effect="dark" content="视频压缩" placement="left">
      <div class="videoPress" @click="compressVideoDia = true">
        <!-- <el-button type="primary" @click="compressVideoDia = true">视频压缩</el-button> -->
        <i style="font-size: 30px;" class="iconfont icon-compress-video"></i>
        <!-- 视频压缩 -->
      </div>
    </el-tooltip>



    <el-dialog :visible.sync="compressVideoDia" :close-on-click-modal="false" :close-on-press-escape="false" width="45%"
      title="视频压缩">
      <CompressVideo :compressVideoDia="compressVideoDia" />
      <span slot="footer" class="dialog-footer">
        <el-button @click="compressVideoDia = false">关 闭</el-button>
        <!-- <el-button type="primary" @click="compressVideoDia = false">确 定</el-button> -->
      </span>
    </el-dialog>

    <!-- 视频短信内容 -->
    <div class="fillet shortChain-box" style="margin-top: 20px; background: #fff">
      <div style="margin: 0px 50px; padding: 20px 0px">
        <el-tabs v-model="activeName" type="card">
          <el-tab-pane :label="'视频短信内容' + mmsSize + 'KB/1843KB'" name="MMScontent">
            <div class="LoopUpload_div" v-for="(item, index) in LoopNum" :key="index">
              <div style="height: 30px">
                <span style="font-size: 18px">第 {{ index + 1 }} 帧</span>
                <span :style="Math.ceil(item.size + sizeof(item.txt)) > 2048
                  ? 'color: #ff00009e;'
                  : ''
                  ">({{ Math.ceil(item.size + sizeof(item.txt)) }}KB)</span>
              </div>
              <Loop-upload :children="item" @childrenChange="childrenChange(index)"></Loop-upload>
              <!-- <div class="LoopUpload_check" v-if="index === LoopNum.length - 1">
                <el-checkbox @change="handleQuite" style="margin-left: 10px; color: #409eff; padding-bottom: 5px" v-model="checked"
                  >拒收请回复R</el-checkbox>
              </div> -->
            </div>
            <div class="LoopUpload_div">
              <div style="height: 30px"></div>
              <div class="AddTo" @click="AddToLoop" style="
                  border: 1px dashed #ccc;
                  position: relative;
                  border-radius: 5px;
                  cursor: pointer;
                ">
                <i class="el-icon-plus" style="
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    font-size: 60px;
                    font-weight: 100;
                    color: #eaeaea;
                  "></i>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
    <!-- 预览，审核 -->
    <div v-if="activeName == 'MMScontent'" class="fillet shortChain-box" style="margin-top: 20px">
      <div style="margin: 0px 50px; padding: 20px 0px; text-align: center">
        <el-button style="margin-right: 10px" @click="Preview">预览</el-button>
        <el-button type="primary" style="margin-right: 10px" @click="NextStep()">提交审核</el-button>
      </div>
    </div>
    <!-- 预览手机弹框   -->
    <el-dialog title="预览" :visible.sync="dialogVisible" width="40%">
      <div>
        <div class="send-mobel-box">
          <img src="../../../../../../assets/images/phone.png" alt="" />
          <div class="mms-content-exhibition">
            <el-scrollbar class="sms-content-exhibition">
              <div style="width: 253px">
                <span style="
                    display: inline-block;
                    padding: 5px;
                    border-radius: 5px;
                    background: #e2e2e2;
                    margin-top: 5px;
                  ">{{ this.title }}</span>
              </div>
              <div style="
                  overflow-wrap: break-word;
                  width: 234px;
                  background: #e2e2e2;
                  padding: 10px;
                  border-radius: 5px;
                  margin-top: 5px;
                " v-for="(item, index) in LoopNum" :key="index">
                <img v-if="item.imageUrl" :src="item.imageUrl" style="width: 235px" class="avatar video-avatar"
                  ref="avatar" />
                <video v-if="item.showVideoPath" style="width: 235px" v-bind:src="item.showVideoPath"
                  class="avatar video-avatar" controls="controls"></video>
                <audio v-if="item.showaudioPath" style="width: 235px" autoplay="autoplay" controls="controls"
                  preload="auto" v-bind:src="item.showaudioPath"></audio>
                <div style="white-space: pre-line">
                  {{ item.txt }}
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { mapState, mapMutations, mapActions } from "vuex";
import LoopUpload from "../components/LoopUpload.vue";
import TemplateLibrary from "../components/TemplateLibrary.vue";
import CompressVideo from "../components/compressVideo.vue"
export default {
  name: "videoTemplateCreate",
  data() {
    return {
      activeName: "MMScontent",
      dialogVisible: false, //预览手机
      Vtitle:"创建视频短信模板",
      title: "", //视频短信标题
      LoopNum: [
        {
          showVideoPath: "", //视频url
          showaudioPath: "", //音频url
          imageUrl: "", //图片url
          media: "", //媒体格式
          type: "", //文件类型
          mediaGroup: "", //返回路径
          mediaPath: "", //返回路径
          txt: "", //文本框
          time: 0, //选择时间
          size: 0, //文件大小
        },
      ],
      mmsSize: 0,

      videoId: "", //编辑ID
      compressVideoDia: false, // 视频压缩弹框
      // checked: false, // 拒收请回复R
    };
  },
  components: {
    LoopUpload,
    TemplateLibrary,
    CompressVideo,
  },
  computed: {
    ...mapState({
      //比如'movies/hotMovies
      contentMMS: (state) => state.contentMMS,
    }),
  },
  methods: {
    // 获取项目绝对路径
    ...mapActions([
      //比如'movies/getHotMovies
      "saveMMS",
    ]),
    // 添加帧
    AddToLoop() {
      if (this.LoopNum.length < 10) {
        let flag = true;
        for (let i = 0; i < this.LoopNum.length; i++) {
          if (
            this.LoopNum[i].showVideoPath ||
            this.LoopNum[i].showaudioPath ||
            this.LoopNum[i].imageUrl ||
            this.LoopNum[i].txt
          ) {
            flag = true;
          } else {
            flag = false;
          }
        }
        if (flag) {
          let obj = {
            showVideoPath: "", //视频url
            showaudioPath: "", //音频url
            imageUrl: "", //图片url
            media: "", //媒体格式
            type: "", //文件类型
            mediaGroup: "", //返回路径
            mediaPath: "", //返回路径
            txt: "", //文本框
            time: 0, //选择时间
            size: 0, //大小
          };
          // this.checked = false
          // this.handleQuite(false)
          this.LoopNum.push(obj);
        } else {
          this.$message({
            type: "warning",
            message: "请先编辑前面的内容",
          });
        }
      } else {
        this.$message({
          type: "warning",
          message: "最多添加10帧",
        });
      }
    },
    //    删除帧
    childrenChange(index) {
      this.LoopNum.splice(index, 1);
    },
    //    预览
    Preview() {
      let a = true;
      for (let i = 0; i < this.LoopNum.length; i++) {
        if (
          this.LoopNum[i].showVideoPath ||
          this.LoopNum[i].showaudioPath ||
          this.LoopNum[i].imageUrl ||
          this.LoopNum[i].txt
        ) {
          if (!this.title) {
            this.$message({
              type: "warning",
              message: "请编辑视频短信标题",
            });
            a = false;
            return false;
          }
        } else {
          this.$message({
            type: "warning",
            message: "请先编辑视频短信内容",
          });
          a = false;
          return false;
        }
      }
      if (a) {
        this.dialogVisible = true;
      }
    },
    //提交审核
    NextStep(val) {
      let a = true;
      for (let i = 0; i < this.LoopNum.length; i++) {
        if (
          this.LoopNum[i].showVideoPath ||
          this.LoopNum[i].showaudioPath ||
          this.LoopNum[i].imageUrl ||
          this.LoopNum[i].txt
        ) {
          if (!this.title) {
            this.$message({
              type: "warning",
              message: "请编辑视频短信标题",
            });
            a = false;
            return false;
          }
        } else {
          this.$message({
            type: "warning",
            message: "请先编辑视频短信内容",
          });
          a = false;
          return false;
        }
      }
      if (a) {
        this.$prompt("请输入备注", "确认当前模板提交审核？", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
        })
          .then(({ value }) => {
            this.$api.post(
              this.API.cpus + "v1/consumervideo/custom/create",
              {
                contents: this.LoopNum,
                title: this.title,
                videoId: this.videoId || null,
                remark: value
              },
              (res) => {
                if (res.code == 200) {
                  this.$message({
                    type: "success",
                    message: res.msg,
                  });
                  this.$router.push({ path: "/videoTemplate" });
                } else {
                  this.$message({
                    type: "error",
                    message: res.msg,
                  });
                }
              }
            );
          })
          .catch(() => {
            this.$message({
              type: "info",
              message: "取消输入",
            });
          });
        // this.$confirms.confirmation(
        //   "post",
        //   "确认当前模板提交审核？",
        //   this.API.cpus + "v1/consumervideo/custom/create",
        //   {
        //     contents: this.LoopNum,
        //     title: this.title,
        //     videoId: this.videoId || null,
        //   },
        //   (res) => {
        //     this.$router.push({ path: "/videoTemplate" });
        //   }
        // );
      }
    },
    // 计算文本字节
    sizeof(str, charset) {
      var total = 0,
        charCode,
        i,
        len;
      charset = charset ? charset.toLowerCase() : "";
      if (charset === "utf-16" || charset === "utf16") {
        for (i = 0, len = str.length; i < len; i++) {
          charCode = str.charCodeAt(i);
          if (charCode <= 0xffff) {
            total += 2;
          } else {
            total += 4;
          }
        }
      } else {
        for (i = 0, len = str.length; i < len; i++) {
          charCode = str.charCodeAt(i);
          if (charCode <= 0x007f) {
            total += 1;
          } else if (charCode <= 0x07ff) {
            total += 2;
          } else if (charCode <= 0xffff) {
            total += 3;
          } else {
            total += 4;
          }
        }
      }
      return total / 1024;
    },
    // handleQuite(val) {
    //   if (val) {
    //     this.LoopNum[this.LoopNum.length - 1].txt += "，拒收请回复R"
    //   } else {
    //     var reg = new RegExp("，拒收请回复R");
    //     this.LoopNum.forEach(item => {
    //       item.txt = item.txt.replace(reg, "");
    //     })
    //   }
    // },
  },
  mounted() {
    window.sessionStorage.removeItem("tpeId");
    if (this.$route.query.id) {
      this.Vtitle = "编辑视频短信模板";
      this.$api.get(this.API.cpus + 'v1/consumervideo/getOne?videoId=' + this.$route.query.id, {}, res => {
        if (res.code == 200) {
          (this.videoId = this.$route.query.id),
            (this.LoopNum = res.data.contents);
          this.title = res.data.title;
          for (let i = 0; i < this.LoopNum.length; i++) {
            if (this.LoopNum[i].type == "img") {
              this.LoopNum[i].imageUrl =
                this.API.imgU +
                this.LoopNum[i].mediaGroup +
                "/" +
                this.LoopNum[i].mediaPath;
            } else if (this.LoopNum[i].type == "video") {
              this.LoopNum[i].showVideoPath =
                this.API.imgU +
                this.LoopNum[i].mediaGroup +
                "/" +
                this.LoopNum[i].mediaPath;
            } else if (this.LoopNum[i].type == "audio") {
              this.LoopNum[i].showaudioPath =
                this.API.imgU +
                this.LoopNum[i].mediaGroup +
                "/" +
                this.LoopNum[i].mediaPath;
            }
          }
        }
      })

    }else{
      this.Vtitle = "创建视频短信模板";
    }
  },
  watch: {
    LoopNum: {
      handler: function (newValue, oldValue) {
        let a = 0;
        for (let i = 0; i < this.LoopNum.length; i++) {
          // sizeof(item.textarea)
          a += parseInt(
            Math.ceil(this.LoopNum[i].size + this.sizeof(this.LoopNum[i].txt))
          );
        }
        this.mmsSize = a;
      },
      deep: true,
    },
  },
};
</script>
<style scoped>
.AddTo {
  padding: 10px;
  height: 530px;
}

.Templat-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px 14px;
  border-radius: 5px;
  font-size: 12px;
}

.Templat-matter>p {
  padding: 5px 0;
}

.AddTo:hover {
  background: #ecf5ff70;
}

.LoopUpload_div {
  width: 25%;
  float: left;
  margin-right: 100px;
  height: 700px;
  min-width: 325px;
  position: relative;
}

.LoopUpload_check {
  position: absolute;
  bottom: 73px;
  right: -3px;
}

.send-mobel-box {
  width: 300px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}

.send-mobel-box img {
  width: 300px;
}

.mms-content-exhibition {
  position: absolute;
  top: 0;
  /* width: 300px; */
  height: 375px;
  margin: 135px 25px 0px 25px;
  overflow: auto;
  overflow-y: auto;
}

.el-scrollbar__wrap {
  margin-bottom: 0;
  margin-right: 0;
}

.videoPress {
  position: fixed;
  right: 80px;
  bottom: 50px;
  color: #fff;
  background-color: #409eff;
  width: 80px;
  height: 80px;
  border-radius: 50%;
  border: 1px solid #409eff;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  cursor: pointer;
  z-index: 99;
}
</style>