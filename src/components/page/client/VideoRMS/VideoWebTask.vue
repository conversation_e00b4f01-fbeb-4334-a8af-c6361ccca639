<template>
  <div class="login_cell_phone" style="background: #fff">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> Web任务</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="fillet Statistics-box">
      <div class="OuterFrame fillet" style="height: 100%">
        <div>
          <el-form :inline="true" ref="formInline" :model="formInline" class="demo-form-inline">
            <el-form-item label="发送类型" style="display: inline-block" prop="timingSend" label-width="82px">
              <el-select v-model="formInline.timingSend" placeholder="请选择类型">
                <el-option label="全部" value=""></el-option>
                <el-option label="立即发送" value="0"></el-option>
                <el-option label="定时发送" value="1"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="消息id" label-width="82px" prop="msgid">
              <el-input v-model="formInline.msgid" placeholder="" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="标题" label-width="82px" prop="title">
              <el-input v-model="formInline.title" placeholder="" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="来源" label-width="82px" prop="source">
              <el-input v-model="formInline.source" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="任务名称" label-width="82px" prop="taskName">
              <el-input v-model="formInline.taskName" class="input-w"></el-input>
            </el-form-item>
            <el-form-item label="发送时间" label-width="82px" prop="time">
              <el-date-picker class="input-time" v-model="formInline.time" value-format="yyyy-MM-dd" type="daterange"
                range-separator="-" :clearable="false" @change="getTimeOperating" start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker>
            </el-form-item>

            <el-form-item label="提交时间" label-width="80px" prop="time">
              <!-- <el-date-picker class="input-w" v-model="formInline.time1" value-format="yyyy-MM-dd" type="daterange"
                range-separator="-" :clearable='false' @change="getTimeOperating1" start-placeholder="开始日期"
                end-placeholder="结束日期">
              </el-date-picker> -->
              <el-button type="primary" plain style="" @click="ListSearch">查询</el-button>
              <el-button type="primary" plain style="" @click="Reset('formInline')">重置</el-button>
              <el-button type="primary" @click="exportNums()">导出数据</el-button>
            </el-form-item>
          </el-form>
        </div>
        <!-- <div class="boderbottom" style="margin: 10px 0">

        </div> -->
        <div class="sensitive-fun">
          <!-- <span class="sensitive-list-header">发送任务列表</span><span style="font-size: 12px;font-weight: 500;">（可刷新页面查看最新发送进度）</span> -->
          <el-button type="primary" style="margin-right: 10px" @click="batchDeletion"
            v-if="selectId.length">批量取消</el-button>
        </div>
        <div class="Mail-table" style="padding-bottom: 40px">
          <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)" ref="multipleTable"
            border :data="tableDataObj.tableData" style="width: 100%">
            <el-table-column width="70" label="发送ID">
              <template slot-scope="scope">{{ scope.row.id }}</template>
            </el-table-column>
            <el-table-column width="220" label="消息ID">
              <template slot-scope="scope">
                {{ scope.row.msgid }}
                <i style="color:#409eff;cursor: pointer;margin: 4px;font-size: 14px;" class="el-icon-document-copy"
                  @click="handleCopy(scope.row.msgid, $event)"></i>
              </template>
            </el-table-column>
            <el-table-column width="90" label="任务名称">
              <template slot-scope="scope">
                <Tooltip v-if="scope.row.taskName" :content="scope.row.taskName" className="wrapper-text" effect="light">
                </Tooltip>
                <!-- {{ scope.row.taskName }} -->
              </template>
            </el-table-column>
            <el-table-column label="发送类型">
              <template slot-scope="scope">
                <span v-if="scope.row.timingSend == 0">立即发送</span>
                <span v-else-if="scope.row.timingSend == 1">定时发送</span>
              </template>
            </el-table-column>
            <!-- <el-table-column label="提交时间" >
                            <template slot-scope="scope">
                                <span >{{ scope.row.createTime }}</span>
                            </template>
                        </el-table-column> -->
            <el-table-column label="预览">
              <template slot-scope="scope">
                <span style="cursor: pointer; color: #16a589" @click="View(scope.row)"><i
                    class="el-icon-picture"></i>预览</span>
              </template>
            </el-table-column>
            <el-table-column label="文件名/手机号" width="140">
              <template slot-scope="scope">
                <div v-if="scope.row.filePath">
                  <Tooltip v-if="scope.row.fileOriginalName" :content="scope.row.fileOriginalName"
                    className="wrapper-text" effect="light">
                  </Tooltip>
                  <!-- {{ scope.row.fileOriginalName }} -->
                </div>
                <div :title="scope.row.mobile" style="
                    max-width: 150px;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    display: block;
                  ">
                  <Tooltip v-if="scope.row.mobile" :content="scope.row.mobile" className="wrapper-text" effect="light">
                  </Tooltip>
                  <!-- {{ scope.row.mobile }} -->
                </div>
              </template>
            </el-table-column>
            <el-table-column label="发送时间" width="160">
              <template slot-scope="scope">
                <span>{{ scope.row.sendTime }}</span>
              </template>
            </el-table-column>
            <el-table-column label="提交号码数（总行数）" width="140">
              <template slot-scope="scope">
                <span>{{ scope.row.totalNum }}</span>
              </template>
            </el-table-column>
            <el-table-column label="有效号码（有效行数）" width="140">
              <template slot-scope="scope">
                <span>{{ scope.row.effectiveNum }}</span>
              </template>
            </el-table-column>
            <el-table-column label="无效号码（无效行）" width="140">
              <template slot-scope="scope">
                <span v-if="scope.row.invalidNum > 0 && scope.row.invalidFilePath" style="color: #1890ff; cursor: pointer"
                  @click="download(scope.row, '2')">
                  <el-tooltip class="item" effect="dark" content="点击下载" placement="bottom">
                    <span>{{ scope.row.invalidNum }}</span>
                  </el-tooltip>
                </span>
                <span v-else>{{ scope.row.invalidNum }}</span>
              </template>
            </el-table-column>
            <el-table-column label="发送状态" width="90">
              <template slot-scope="scope">
                <span v-if="scope.row.status == 1">处理中</span>
                <span v-else-if="scope.row.status == 2">已完成</span>
                <span v-else-if="scope.row.status == 3">取消发送</span>
                <span v-else-if="scope.row.status == 0">待处理</span>
                <span style="color: red" v-else-if="scope.row.status == -1">处理异常</span>
              </template>
            </el-table-column>
            <el-table-column label="发送成功数" width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.sendSuccessNum }}</span>
              </template>
            </el-table-column>
            <el-table-column label="成功率" width="70">
              <template slot-scope="scope">
                <span>{{ scope.row.successRate }}</span>
              </template>
            </el-table-column>
            <el-table-column label="待返回数" width="70">
              <template slot-scope="scope">
                <span>{{ scope.row.waiteNum }}</span>
              </template>
            </el-table-column>
            <el-table-column label="失败数" width="70">
              <template slot-scope="scope">
                <span v-if="scope.row.failNum">{{ scope.row.failNum }}</span>
                <span v-else>- -</span>
              </template>
            </el-table-column>
            <el-table-column label="来源" width="90">
              <template slot-scope="scope">
                <span>{{ scope.row.source }}</span>
              </template>
            </el-table-column>
            <!-- <el-table-column label="审核状态" width="90">
                            <template slot-scope="scope">
                                <span v-if="scope.row.auditStatus==1" style="color:#ffc800ed">未审核</span>
                                <span v-if="scope.row.auditStatus==2" style="color:rgb(22, 165, 137)">审核通过</span>
                                <span v-if="scope.row.auditStatus==3" style="color:rgb(245, 108, 108)">审核未通过</span>
                            </template>
                        </el-table-column> -->
            <!-- <el-table-column label="运营商状态" width="230">
                            <template slot-scope="scope">
                                <span v-if="scope.row.ydChannelStatus==0" style="margin: 0 10px;">移动：<i style="background:#ffc800ed" class="YYSstatus"></i></span>
                                <span v-else-if="scope.row.ydChannelStatus==1" style="margin: 0 10px;">移动：<i style="background:rgb(22, 165, 137)" class="YYSstatus"></i></span>
                                <span v-else-if="scope.row.ydChannelStatus==2" style="margin: 0 10px;">移动：<i style="background:rgb(245, 108, 108)" class="YYSstatus"></i></span>
                                <span v-if="scope.row.ltChannelStatus==0" style="margin: 0 10px;">联通：<i style="background:#ffc800ed" class="YYSstatus"></i></span>
                                <span v-else-if="scope.row.ltChannelStatus==1" style="margin: 0 10px;">联通：<i style="background:rgb(22, 165, 137)" class="YYSstatus"></i></span>
                                <span v-else-if="scope.row.ltChannelStatus==2" style="margin: 0 10px;">联通：<i style="background:rgb(245, 108, 108)" class="YYSstatus"></i></span>
                                <span v-if="scope.row.dxChannelStatus==0" style="margin: 0 10px;">电信：<i style="background:#ffc800ed" class="YYSstatus"></i></span>
                                <span v-else-if="scope.row.dxChannelStatus==1" style="margin: 0 10px;">电信：<i style="background:rgb(22, 165, 137)" class="YYSstatus"></i></span>
                                <span v-else-if="scope.row.dxChannelStatus==2" style="margin: 0 10px;">电信：<i style="background:rgb(245, 108, 108)" class="YYSstatus"></i></span>
                            </template>
                        </el-table-column> -->
            <el-table-column label="操作" width="120" fixed="right">
              <template slot-scope="scope">
                <el-button type="text" v-if="scope.row.status != 2 &&
                  scope.row.status != 3 &&
                  scope.row.status != -1 &&
                  scope.row.auditStatus != 2 &&
                  scope.row.auditStatus != 3 &&
                  scope.row.timingSend != 0
                  " style="color: #16a589; margin-left: 0px" @click="edit(scope.row)"><i class="el-icon-edit"></i>
                  编辑</el-button>
                <el-button type="text" v-if="scope.row.status == 0" style="color: red; margin-left: 0px"
                  @click="cancel(scope.row)"><i class="el-icon-error"></i> 取消</el-button>
              </template>
            </el-table-column>
          </el-table>
          <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
            style="background: #fff; padding: 10px 0; text-align: right">
            <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage" :page-size="formInlines.pageSize" :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tablecurrent.total">
            </el-pagination>
          </el-col>
        </div>
      </div>
    </div>
    <!-- 预览手机弹框   -->
    <el-dialog title="预览" :visible.sync="dialogVisible" width="40%">
      <div>
        <div class="send-mobel-box">
          <img src="../../../../assets/images/phone.png" alt="" />
          <div class="mms-content-exhibition">
            <el-scrollbar class="sms-content-exhibition">
              <div style="width: 253px">
                <span style="
                    display: inline-block;
                    padding: 5px;
                    border-radius: 5px;
                    background: #e2e2e2;
                    margin-top: 5px;
                  ">{{ title }}</span>
              </div>
              <div style="
                  overflow-wrap: break-word;
                  width: 234px;
                  background: #e2e2e2;
                  padding: 10px;
                  border-radius: 5px;
                  margin-top: 5px;
                " v-for="(item, index) in viewData" :key="index">
                <img v-if="item.media == 'jpg' ||
                  item.media == 'gif' ||
                  item.media == 'png' ||
                  item.media == 'jpeg'
                  " :src="API.imgU + item.mediaGroup + '/' + item.mediaPath" style="width: 235px"
                  class="avatar video-avatar" ref="avatar" />
                <video v-if="item.type == 'video'" style="width: 235px"
                  v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath" class="avatar video-avatar"
                  controls="controls"></video>
                <audio v-if="item.type == 'audio'" style="width: 235px" autoplay="autoplay" controls="controls"
                  preload="auto" v-bind:src="API.imgU + item.mediaGroup + '/' + item.mediaPath"></audio>
                <div style="white-space: pre-line">
                  {{ item.txt }}
                </div>
              </div>
            </el-scrollbar>
          </div>
        </div>
      </div>
    </el-dialog>
    <!-- 编辑时间   -->
    <el-dialog title="编辑定时时间" :visible.sync="dialogVisibleTime" width="22%">
      <div>
        <date-plugin class="Mail-search-date" :datePluginValueList="datePluginValueList"
          @handledatepluginVal="handledatepluginVal" style="width: 364px"></date-plugin>
      </div>
      <div class="sms-seconnd-steps-btns" style="margin-top: 15px; padding-right: 10px">
        <el-button type="primary" @click="determine()" style="padding: 10px 20px">确 定</el-button>
        <el-button @click="dialogVisibleTime = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import DatePlugin from "@/components/publicComponents/DatePlugin.vue";
import TableTem from "@/components/publicComponents/TableTem";
import Tooltip from "@/components/publicComponents/tooltip";
import clip from '../../utils/clipboard'
import getNoce from "../../../../plugins/getNoce";
export default {
  name: "VideoWebTask",
  components: {
    DatePlugin,
    TableTem,
    Tooltip
  },
  data() {
    return {
      name: "VideoWebTask",
      // 定时时间
      datePluginValueList: {
        //日期参数配置
        type: "datetime",
        pickerOptions: {
          disabledDate(time) {
            return time.getTime() < Date.now() - 8.64e7;
          },
        },
        defaultTime: "", //默认起始时刻
        datePluginValue: "",
      },
      sendTime: "",
      taskSmsId: "",
      dialogVisible: false,
      viewData: [], // 查看内容
      title: "",
      dialogVisibleTime: false,
      //复选框值
      selectId: "",
      // 搜索数据
      formInline: {
        timingSend: "",
        msgid: "",
        title: "",
        source: "",
        taskName: "",
        beginTime: "",
        endTime: "",
        time: [],
        decode:false,
        // flag: 0,
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        timingSend: "",
        msgid: "",
        title: "",
        source: "",
        taskName: "",
        beginTime: "",
        endTime: "",
        time: [],
        decode:false,
        // flag: 0,
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
      },
    };
  },
  methods: {
    //批量取消
    batchDeletion() {
      this.$confirms.confirmation(
        "post",
        "确定取消定时发送？",
        this.API.cpus + "v1/video/cancel",
        { ids: this.selectId },
        (res) => {
          this.InquireList();
        }
      );
    },
    handleCopy(name, event) {
      clip(name, event)
    },
    //列表复选框的值
    handelSelection(val) {
      let selectId = [];
      for (let i = 0; i < val.length; i++) {
        selectId.push(val[i].taskSmsId);
      }
      this.selectId = selectId; //批量操作选中id
    },
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.cpus + "v1/video/consumerWebTask",
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false;
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.tablecurrent.total = res.data.total;
        }
      );
    },
    // 编辑
    edit(val) {
      if (Date.parse(val.sendTime) - Date.now() < 1800000) {
        this.$message({
          message: "定时小于现在30分钟无法编辑",
          type: "warning",
        });
      } else {
        this.taskSmsId = val.id;
        this.dialogVisibleTime = true;
      }
    },
    // 预览
    View(val) {
      this.viewData = val.contents;
      this.title = val.title;
      this.dialogVisible = true;
    },
    // 取消
    cancel(val) {
      this.$confirms.confirmation(
        "post",
        "确定取消定时视频短信？",
        this.API.cpus + "v1/video/cancel",
        { ids: [val.id] },
        (res) => {
          this.InquireList();
        }
      );
    },
    // 下载
    download(val, tag) {
      // 时间过滤
      Date.prototype.format = function (format) {
        var args = {
          "M+": this.getMonth() + 1,
          "d+": this.getDate(),
          "h+": this.getHours(),
          "m+": this.getMinutes(),
          "s+": this.getSeconds(),
          "q+": Math.floor((this.getMonth() + 3) / 3), //quarter
          S: this.getMilliseconds(),
        };
        if (/(y+)/.test(format))
          format = format.replace(
            RegExp.$1,
            (this.getFullYear() + "").substr(4 - RegExp.$1.length)
          );
        for (var i in args) {
          var n = args[i];
          if (new RegExp("(" + i + ")").test(format))
            format = format.replace(
              RegExp.$1,
              RegExp.$1.length == 1 ? n : ("00" + n).substr(("" + n).length)
            );
        }
        return format;
      };
      var that = this;
      filedownload();
      async function filedownload() {
        const nonce = await getNoce.useNonce();
        if (tag == "2") {
          let group = val.invalidFilePath.substring(0, 6);
          let invalidFilePath = val.invalidFilePath.slice(7);
          fetch(
            "/gateway/client-cpus/v3/file/download?fileName=" +
            new Date().getTime() +
            "&group=" +
            group +
            "&path=" +
            invalidFilePath,
            {
              method: "get",
              headers: {
                "Content-Type": "application/json",
                Authorization:
                  "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
                'Once': nonce,
              },
              // body: JSON.stringify({
              //     batchNo: val.row.batchNo,
              // })
            }
          )
            .then((res) => res.blob())
            .then((data) => {
              let blobUrl = window.URL.createObjectURL(data);
              download(blobUrl);
            });
        } else {
          fetch(
            that.API.cpus +
            "v3/file/download?fileName=" +
            val.fileOriginalName +
            "&group=group1&path=" +
            val.filePath.slice(7),
            {
              method: "get",
              headers: {
                "Content-Type": "application/json",
                Authorization:
                  "Bearer " + window.Vue.$common.getCookie("ZTGlS_TOKEN"),
                'Once': nonce,
              },
            }
          )
            .then((res) => res.blob())
            .then((data) => {
              let blobUrl = window.URL.createObjectURL(data);
              download(blobUrl);
            });
        }
      }
      function download(blobUrl) {
        if (tag == "2") {
          var a = document.createElement("a");
          a.style.display = "none";
          a.download =
            "(" +
            new Date().format("yyyy-MM-dd hh:mm:ss") +
            ") " +
            new Date().getTime() +
            ".txt";
          a.href = blobUrl;
          a.click();
        } else {
          var a = document.createElement("a");
          a.style.display = "none";
          a.download =
            "(" +
            new Date().format("yyyy-MM-dd hh:mm:ss") +
            ") " +
            val.fileOriginalName;
          a.href = blobUrl;
          a.click();
        }
      }
    },
    // 查询
    ListSearch() {
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields();
      (this.formInline.time = []), (this.formInline.beginTime = "");
      this.formInline.endTime = "";
      (this.formInline.time1 = []), (this.formInline.startTime = "");
      this.formInline.stopTime = "";
      this.formInline.source = "";
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    exportFn(obj) {
      this.$api.post(this.API.cpus + "statistics/export", obj, (res) => {
        if (res.code == 200) {
          this.exportShow = false
          this.$message({
            type: "success",
            duration: "2000",
            message: "已加入到文件下载中心!",
          });
          this.$router.push('/FileExport');
        } else {
          this.$message({
            type: "error",
            duration: "2000",
            message: res.msg,
          });
        }
      });
    },
    exportNums() {
      if (this.tableDataObj.tableData.length == 0) {
        this.$message({
          message: "列表无数据，不可导出！",
          type: "warning",
        });
        return;
      }
      let data = Object.assign({}, this.formInlines);
      data.productType = 13;
      this.exportFn(data)
    },
    // 发送时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = val[0] + " 00:00:00";
        this.formInline.endTime = val[1] + " 23:59:59";
      } else {
        this.formInline.beginTime = "";
        this.formInline.endTime = "";
      }
    },
    // 提交时间
    getTimeOperating1(val) {
      if (val) {
        this.formInline.startTime = val[0] + " 00:00:00";
        this.formInline.stopTime = val[1] + " 23:59:59";
      } else {
        this.formInline.startTime = "";
        this.formInline.stopTime = "";
      }
    },
    // 定时时间
    handledatepluginVal: function (val1, val2) {
      //日期
      this.sendTime = val1;
    },
    // 确定定时时间
    determine() {
      if (this.sendTime == "") {
        this.$message({
          message: "请选大于当前30分钟的定时时间！",
          type: "warning",
        });
      } else {
        let nowTiem = new Date(this.sendTime).getTime();
        if (nowTiem < Date.now() + 1800000) {
          this.$message({
            message: "定时时间应大于当前时间30分钟，需重新设置！",
            type: "warning",
          });
          this.datePluginValueList.datePluginValue = "";
          this.sendTime = "";
        } else {
          this.$confirms.confirmation(
            "put",
            "确定修改定时？",
            this.API.cpus + "v1/video/consumerWebTask",
            { sendTime: this.sendTime, taskSmsId: this.taskSmsId },
            (res) => {
              this.InquireList();
              this.dialogVisibleTime = false;
            }
          );
        }
      }
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size;
      this.InquireList();
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage;
      this.InquireList();
    },
  },
  // activated(){
  //     this.InquireList()
  // },
  created() {
    if (this.$route.query.source) {
      this.formInline.source = this.$route.query.source;
      this.formInlines.source = this.$route.query.source;
    }
    this.InquireList();
  },
  watch: {
    // 监听搜索/分页数据
    // formInlines:{
    //     handler() {
    //         this.InquireList()
    //     },
    //     deep: true,
    //     immediate: true,
    // },
  },
};
</script>
<style scoped>
.Statistics-box {
  padding: 20px;
}

.addC .el-select {
  width: 100%;
}

.addC .el-cascader {
  width: 100%;
}

.send-mobel-box {
  width: 300px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  left: 28%;
}

.send-mobel-box img {
  width: 300px;
}

.el-scrollbar__wrap {
  margin-bottom: 0px !important;
}

.mms-content-exhibition {
  position: absolute;
  top: 0;
  width: 300px;
  height: 375px;
  margin: 135px 25px 0px 25px;
  overflow: auto;
  overflow-y: auto;
}

.YYSstatus {
  display: inline-block;
  width: 10px;
  height: 10px;
  border-radius: 50%;
  position: relative;
}
</style>
