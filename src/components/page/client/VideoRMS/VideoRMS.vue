<template>
    <div id="MMSCJ" class="bag">
        <!-- <div class="Top_title">
            <span style="display:inline-block;padding-left:6px;">编辑视频短信</span>
        </div> -->
        <div class="fillet shortChain-box" style="padding-bottom: 1px;">
            <p style="font-size: 20px;font-weight: 600;padding: 20px;border-bottom: 1px solid #eaeaea;">使用流程</p>
            <div style="margin: 20px 50px;border: 1px solid #ccc;padding: 20px 50px;border-radius: 20px;">
                <el-steps :active="active">
                    <el-step class="icon iconfont" title="选择已审核模板" icon="el-icon-edit"></el-step>
                    <el-step class="icon iconfont" title="录入手机号" icon="icon-shoujilianxiren"></el-step>
                    <el-step class="icon iconfont" title="发送提交" icon="icon-fasong"></el-step>
                </el-steps>
            </div>
        </div> 
        <!-- 创建模板 -->
        <create @childrenNextStep="childrenNextStep" v-show="active==0" :key="Reorganization"></create>
        <!-- 号码录入 -->
        <EnterTheNumber @childrenPrevious="childrenPrevious" :header='header' v-show="active==1"></EnterTheNumber>
        <!-- 短信审核 -->
        <div v-show="active==2" style="background: rgb(255, 255, 255);margin: 20px 0px;padding-bottom: 50px;">
            <div class="fillet shortChain-box" style="padding-bottom: 1px;">
                <p style="font-size: 20px;font-weight: 600;padding: 20px;border-bottom: 1px solid #eaeaea;">提交发送</p>
            </div>
            <div>
                <div style="text-align: center;">
                    <p style="font-size: 18px;font-weight: 600;margin: 30px;">提交成功</p>
                    <!-- <p>视频短信提交成功，需等待平台审核，请耐心等候</p> -->
                </div>
                <div style="text-align: center;margin: 70px 0;">
                    <span style="font-size: 15px;font-weight: 600;">视频短信标题：</span><span style="color: #a4a4a4;">{{title}}</span>
                    <span style="font-size: 15px;font-weight: 600;margin-left:10%;">视频短信ID：</span><span style="color: #a4a4a4;">{{videoId}}</span>
                    <span style="font-size: 15px;font-weight: 600;margin-left:10%;">创建时间：</span><span style="color: #a4a4a4;">{{time}}</span>
                </div>
                <div v-if="getTepId()!=null" style="margin: 20px 50px;padding: 20px 50px;">
                    <el-steps :active="1">
                        <el-step title="创建视频短信" ></el-step>
                        <el-step title="发送完成" ></el-step>
                    </el-steps>
                </div>
                <div v-else style="margin: 20px 50px;padding: 20px 50px;">
                    <el-steps :active="1">
                        <el-step title="创建视频短信" ></el-step>
                        <el-step title="平台审核" ></el-step>
                        <el-step title="发送完成" ></el-step>
                    </el-steps>
                </div>
            </div>
        </div>
        <!-- 上一步，发送 -->
        <div v-show="active==2" class="fillet shortChain-box" style="margin-top: 20px;">
            <div style="margin: 0px 50px;padding: 20px 0px;text-align: center;">
                <el-button style="margin-right:10px;" @click="WebTask">查看待发</el-button>
                <el-button type="primary" style="margin-right:10px;" @click="addMMS">新建视频短信</el-button>
            </div>
        </div>
        <el-dialog
            title="实名认证"
            :visible.sync="dialogSmrzFlag"
            width="30%"
            center>
            <span>尊敬的客户，根据《中华人民共和国网络安全法》及相关法律的规定，请您尽快完成实名认证。如需帮助，请联系在线售后客服。</span>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="goSmrz">前往实名认证</el-button>
            </span>
        </el-dialog>
    </div>    
</template>

<script>
import create from './components/create.vue'
import EnterTheNumber from './components/EnterTheNumber.vue'
import { mapState, mapMutations,mapActions } from "vuex";
export default {
    name: "VideoRMS",
    components:{
        create,
        EnterTheNumber
    },
    data(){
        return{
            header:{},
            active:0,
            time:'',
            title:'',
            videoId:'',
            Reorganization:'',
            dialogSmrzFlag :false,
        }
    },
    created(){
        this.header = {
            Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
        };
        this.$api.get(
        this.API.cpus + "consumerclientinfo/getClientInfo",
        null,
      (res) => {
        // console.log(res.data);
        if(res.data.certificate == 0){
            this.dialogSmrzFlag = true
        }
        // this.certificate = res.data.certificate;

      }
    );
    },
    // activated(){
    //     this.$api.get(
    //     this.API.cpus + "consumerclientinfo/getClientInfo",
    //     null,
    //   (res) => {
    //     // console.log(res.data);
    //     if(res.data.certificate == 0){
    //         this.dialogSmrzFlag = true
    //     }
    //     // this.certificate = res.data.certificate;

    //   }
    // );
    // },
    methods:{
        goSmrz(){
            this.dialogSmrzFlag = false
            this.$router.push("/authentication")
        },
        childrenNextStep(val){
            this.active=1
        },
        childrenPrevious(val){
            if(val){
                this.time=val.createTime
                this.videoId=val.videoId
                this.title=val.title
                this.active=2
            }else{
                this.active=0
            }
        },
        addMMS(){
            this.Reorganization = new Date().getTime()
            this.active=0
        },
        // 获取项目绝对路径
        ...mapActions([  //比如'movies/getHotMovies
            'saveUrls'
        ]),
        WebTask(){
            // 存储点击的菜单
            let logUrls={
                logUrls:'VideoWebTask'
            } 
            this.saveUrls(logUrls);
            window.sessionStorage.setItem('logUrls','VideoWebTask')
            this.$router.push({ path: '/VideoWebTask'})
        },
        getTepId(){
            return window.sessionStorage.getItem('tpeId')
        }
    },
}
</script>

<style scoped>

</style>
<style>
#MMSCJ .el-step__icon-inner{
    font-size: 25px !important;
    font-weight: 400 !important;
    font-style:normal
}
</style>

