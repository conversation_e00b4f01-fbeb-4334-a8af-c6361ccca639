<template>
  <div class="bag">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 国际短信发送</el-breadcrumb-item>
      </el-breadcrumb>
    </div>
    <div class="fillet shortChain-box" style="background: #fff">
      <div>
        <el-form :model="formData" :rules="formRule" ref="formRef" label-width="95px" style="padding: 20px 8px 0 8px">
          <el-form-item label="类型" prop="type">
            <el-select style="width: 500px" v-model="formData.type" placeholder="请选择">
              <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="内容" prop="content">
            <el-input v-model="formData.content" type="textarea" style="width: 500px; height: 120px"
              placeholder="请输入发送内容" @input="handleInput"></el-input>
          </el-form-item>
          <el-form-item label="发送方式">
            <el-radio-group v-model="formData.sendType" @change="handelSend">
              <el-radio label="0">号码发送</el-radio>
              <el-radio label="1">文件发送</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item v-if="formData.sendType == '1'" label="文件发送">
            <div style="display:flex;position: relative;s">
              <el-upload class="upload-demo" drag :action="this.API.cpus + 'v3/file/upload'" :headers="header"
                :on-remove="handleRemove" :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload" multiple
                :limit="1" :file-list="fileList">
                <i class="el-icon-upload active"></i>
                <div class="el-upload__text">
                  将文件拖到此处，或<em>点击上传</em>
                </div>
                <div class="el-upload__tip" slot="tip">
                  <!-- 格式要求：支持.xlsx .xls格式,文件大小不超过300M,表格<span style="color:rgb(210, 7, 7)">第一列手机号、第二列发送内容；</span> -->
                </div>
              </el-upload>
              <el-button style="height:32px; margin-left: 10px;margin-top: 145px;"
                @click="Variabledownload">模板下载</el-button>
            </div>

            <!-- <div>
                        
                            </div> -->
          </el-form-item>
          <el-form-item v-if="formData.sendType == '0'" label="手机号" prop="mobile">
            <el-input type="textarea" v-model="formData.mobile" style="width: 500px; height: 120px"
              placeholder="例如：86158********"></el-input>
            <span style="margin-left: 10px; font-size: 12px; color: #ccc">例如：86158********</span>
          </el-form-item>
          <el-form-item label="外显" prop="from">
            <el-input v-model="formData.from" style="width: 400px;" placeholder="" class="input-w"></el-input>
          </el-form-item>
          <el-form-item label="" prop="">
            <div style="display: flex">
              <el-button type="primary" @click="send">立即发送</el-button>
            </div>
          </el-form-item>
          <el-form-item label="计费规则" prop="">
              <ul style="list-style: disc; margin-left: 20px">
                <li>
                  纯英文内容<span style="color: rgb(210, 7, 7)">140字内（含140字）</span>按每140字计一条
                </li>
                <li>
                  中文内容<span
                    style="color: rgb(210, 7, 7)">70字内（含70字）</span>按每70字计一条
                </li>
                <li>
                  中英文内容超过<span
                    style="color: rgb(210, 7, 7)">70字（含70字）</span>，按每70字计一条
                </li>
              </ul>
            </el-form-item>
        </el-form>
      </div>
      <div class="send-mobel-box">
        <img style="margin-left: 20px;" src="../../../../assets/images/phone.png" alt="" />
        <!-- <p
          style="
            width: 80px;
            position: absolute;
            padding-left: 76px;
            top: 122px;
            left: 25px;
            font-size: 12px;
            color: #666;
          "
        >
          国际短信
        </p> -->
        <div class="sms-content-exhibition">
          <div style="
              width: 168px;
              word-wrap: break-word;
              word-break: break-all;
              white-space: pre-wrap;
            ">
            {{ formData.content }}
          </div>
        </div>
        <div style="width: 80px; position: absolute; left: 90px; top: 260px">
          <span style="
              width: 17px;
              height: 20px;
              position: absolute;
              left: -50px;
              top: 50px;
              border-radius: 100% 0px 80% 0px;
              background: #e2e2e2;
            "></span>
          <span style="
              width: 17px;
              height: 20px;
              position: absolute;
              left: -58px;
              top: 50px;
              border-radius: 100% 0px 100% 0px;
              background: #fff;
            "></span>
        </div>
        <div style="font-size: 12px;margin-top: 10px;text-align: center;">
          <div>
            当前发送内容<span style="color: red;">{{ formData.content.length }}</span>个字，预计发送条数约为 <span
              style="color: red;">{{ smsCount }}</span> 条短信
          </div>
          <!-- <div style="color: red;">(实际发送时，如有模板变量会影响计费条数，请注意关注)</div> -->
        </div>
      </div>
    </div>
    <el-dialog title="实名认证" :visible.sync="dialogSmrzFlag" width="30%" center>
      <span>尊敬的客户，根据《中华人民共和国网络安全法》及相关法律的规定，请您尽快完成实名认证。如需帮助，请联系在线售后客服。</span>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="goSmrz">前往实名认证</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
export default {
  name: "ImsSend",
  data() {
    var content = (rule, value, callback) => {
      if (!/^\d{4,6}$/.test(value)) {
        callback(new Error("请输入4-6位的验证码"));
      } else {
        callback();
      }
    };
    var mobile = (rule, value, callback) => {
      if (value != "") {
        if (!/^((00){1}[1-9]{1}[0-9]{1,3}|86|\+86)?1[3458]\d{9}$/.test(value)) {
          callback(new Error("请输入正确的手机号"));
        } else {
          callback();
        }
      } else {
        callback(new Error("请输入手机号"));
      }
    };
    return {
      dialogSmrzFlag: false,
      endingName: "",
      fileList: [],
      fileStyle: {
        size: 245678943234,
        style: ["xlsx", "xls", "txt"],
      },
      smsCount: 0,
      tip: "仅支持.xlsx .xls.txt 等格式",
      formData: {
        content: "",
        mobile: "",
        type: "",
        sendType: "0",
        group: "",
        path: "",
        fileName: "",
        from: ""
      },
      // flag: true,
      header: {},
      fileList: [],
      options: [
        {
          value: "YZM",
          label: "验证码",
        },
        {
          value: "YX",
          label: "营销",
        },
      ],
      formRule: {
        content: [{ required: true, message: "请输入内容", trigger: "blur" }],
        type: [{ required: true, message: "请选择短信类型", trigger: "blur" }],
        mobile: [
          {
            required: true,
            message: "请输入手机号",
            trigger: "blur",
          },
        ],
      },
    };
  },
  created() {
    this.header = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
    };
    this.$api.get(
      this.API.cpus + "consumerclientinfo/getClientInfo",
      null,
      (res) => {
        // console.log(res.data);
        if (res.data.certificate == 0) {
          this.dialogSmrzFlag = true;
        }
        // this.certificate = res.data.certificate;
      }
    );
  },
  // activated(){
  //     this.$api.get(
  //     this.API.cpus + "consumerclientinfo/getClientInfo",
  //     null,
  //   (res) => {
  //     // console.log(res.data);
  //     if(res.data.certificate == 0){
  //         this.dialogSmrzFlag = true
  //     }
  //     // this.certificate = res.data.certificate;

  //   }
  // );
  // },
  methods: {
    goSmrz() {
      this.dialogSmrzFlag = false;
      this.$router.push("/authentication");
    },
    send() {
      console.log(this.formData.sendType, 'llthis.formData');
      this.$refs["formRef"].validate((valid, value) => {
        if (valid) {
          this.$confirms.confirmation(
            "post",
            "确认发送短信？",
            this.API.cpus + "v1/consumerIms/send",
            this.formData,
            (res) => {
              if (res.code == 200) {
                // this.$refs["formRef"].resetFields();
                this.$router.push("/ImsSendDetails");
              }
            }
          );
        }
      });
    },
    handleInput(val) {
      const englishPattern = /^[a-zA-Z\s]*$/; // 仅英文字符
      const chinesePattern = /^[\u4e00-\u9fa5\s]*$/; // 仅中文字符
      // 混合文本：仅包含字母、汉字、数字和空格，但可包含特殊字符
      const mixedPattern = /[a-zA-Z\u4e00-\u9fa5\s\d\W]/;
      if (englishPattern.test(val)) {
        this.smsCount = Math.ceil(val.length / 140);
      } else if (chinesePattern.test(val)) {
        this.smsCount = Math.ceil(val.length / 70);
      } else if (mixedPattern.test(val)) {
        this.smsCount = Math.ceil(val.length / 70);
      }
    },
    Variabledownload() {
      this.$File.export(
        this.API.cpus + "v3/consumersms/templateZipDownload",
        {},
        `发送文件模板（变量模板可用）.zip`
      );
    },
    //限制用户上传文件格式和大小
    beforeAvatarUpload(file) {
      let endingCode = file.name;//结尾字符
      this.endingName = endingCode.slice(endingCode.lastIndexOf('.') + 1, endingCode.length);
      let isStyle = false; //文件格式
      const isSize = file.size / 1024 / 1024 < this.fileStyle.size; //文件大小
      console.log(isSize)
      for (let i = 0; i < this.fileStyle.style.length; i++) {
        if (this.endingName === this.fileStyle.style[i]) {
          isStyle = true;
          break;
        }
      }
      //不能重复上传文件
      let fileArr = this.fileList;
      let fileNames = [];
      if (fileArr.length > 0) {
        for (let k = 0; k < fileArr.length; k++) {
          fileNames.push(fileArr[k].name)
        }
      }
      if (fileNames.indexOf(endingCode) !== -1) {
        this.$message.error('不能重复上传文件');
        return false;
      } else if (!isStyle) { //文件格式判断
        this.$message.error(this.tip);
        return false;
      } else {
        //文件大小判断
        if (!isSize) {
          this.$message.error('上传文件大小不能超过' + this.fileStyle.size);
          return false;
        }
      }
    },
    handleAvatarSuccess(res, fileList) {
      if (res.code == 200) {
        // this.flag = false;
        this.formData.group = res.data.group;
        this.formData.path = res.data.path;
        this.formData.fileName = res.data.fileName;
      } else {
        this.$message({
          message: res.msg,
          type: "error",
        });
      }
    },
    handleRemove() {
      // this.flag = true;
      this.formData.group = "";
      this.formData.path = "";
      this.formData.fileName = "";
      this.fileList = []
    },
    handelSend(val) {
      if (val == 0) {
        this.handleRemove()
      } else {
        this.formData.mobile = ''
      }
    },
  },
  mounted() { },
  watch: {},
};
</script>
<style scoped>
.shortChain-box {
  padding: 20px;
}

.shortChain-box {
  width: 100%;
  display: flex;
  justify-content: space-between;
}

.send-mobel-box {
  width: 320px;
  overflow: hidden;
  position: relative;
  margin-bottom: 35px;
  margin-right: 100px;
  /* left: 28%; */
}

.send-mobel-box img {
  width: 255px;
}

.sms-content-exhibition {
  width: 170px;
  height: 40%;
  border-radius: 10px;
  position: absolute;
  padding: 8px 9px;
  background: #e2e2e2;
  top: 100px;
  left: 50px;
  font-size: 12px;
  line-height: 18px;
  color: #000;
  overflow: auto;
}

.sms-seconnd-steps-box {
  padding: 30px 20px 30px 55px;
}

.sms-seconnd-steps-btns {
  text-align: right;
}

.active {
  font-size: 67px !important;
  color: #C0C4CC !important;
  margin: 40px 0 16px !important;
  line-height: 50px !important;
}
</style>
<style>
.el-textarea__inner {
  height: 100%;
}
</style>