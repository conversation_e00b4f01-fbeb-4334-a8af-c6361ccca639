<template>
    <div class="bag">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 白名单申请</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div class="fillet shortChain-box">
            <!-- <div class="shortChain-matter">
                <p style="font-weight:bolder;font-size: 14px;">温馨提醒：</p>
                <p>1、请您先申请短链服务，短链服务是指将长网址转换为短网址，通过短链可将网址缩短，提高用户访问效率。</p>
                <p>2、短链服务开通后，您将获得短链域名，短链域名可用于访问您的长网址。</p>
            </div> -->
            <div class="short-box">
                <div>
                    <el-form :inline="true" :model="formInline" class="demo-form-inline" ref="queryForm">
                        <el-form-item label="域名" prop="domain">
                            <el-input v-model="formInline.domain" placeholder="" class="input-w"></el-input>
                        </el-form-item>
                        <el-form-item label="审核状态" prop="auditStatus">
                            <el-select v-model="formInline.auditStatus" placeholder="请选择">
                                <el-option label="全部" value=""></el-option>
                                <el-option label="审核中" value="1"></el-option>
                                <el-option label="审核通过" value="2"></el-option>
                                <el-option label="审核未通过" value="3"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="创建时间" prop="time">
                            <el-date-picker type="daterange" v-model="formInline.time" range-separator="-"
                                start-placeholder="开始日期" end-placeholder="结束日期" class="input-time"
                                @change="handleTimeChange"></el-date-picker>
                        </el-form-item>
                        <el-form-item>
                            <el-button type="primary" plain style="" @click="Query">查询</el-button>
                            <el-button type="primary" plain style="" @click="Reload('queryForm')">重置</el-button>
                        </el-form-item>
                    </el-form>
                </div>
                <div class="short-btn">
                    <!-- <el-button @click="openShort()" v-if="shortStatus != 2" :disabled="shortStatus == 1 ? true : false"
                        type="primary">开通短链</el-button> -->
                    <el-button :disabled="shortStatus == 1 ? true : false" @click="addWhiteList('add')"
                        type="primary">添加白名单</el-button>
                    <!-- <el-button :disabled="selectId.length == 0" @click="batchDelete" type="danger">批量删除</el-button> -->
                </div>
                <div class="Mail-table Mail-table1">
                    <!-- 表格和分页开始 -->
                    <el-table v-loading="tableDataObj.loading2" element-loading-text="拼命加载中"
                        element-loading-spinner="el-icon-loading" element-loading-background="rgba(0, 0, 0, 0.6)"
                        ref="multipleTable" border stripe :data="tableDataObj.tableData" style="width: 100%">
                        <!-- <el-table-column type="selection" width="46"></el-table-column> -->
                        <el-table-column label="域名">
                            <template slot-scope="scope">
                                <span>{{ scope.row.domain }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="状态">
                            <template slot-scope="scope">
                                <el-tag type="warning" effect="dark" v-if="scope.row.auditStatus == '1'">审核中 </el-tag>
                                <el-tag type="success" effect="dark"
                                    v-else-if="scope.row.auditStatus == '2'">审核通过</el-tag>
                                <el-tag type="danger" effect="dark"
                                    v-else-if="scope.row.auditStatus == '3'">审核未通过</el-tag>
                                <span v-else>-</span>
                                <el-tooltip effect="dark" :content="scope.row.auditReason" placement="top">
                                    <i v-if="scope.row.auditStatus == '3'"
                                        style="color: #F56C6C;font-size: 18px;margin-left: 10px;"
                                        class="el-icon-question"></i>
                                </el-tooltip>
                            </template>
                        </el-table-column>
                        <el-table-column label="备注">
                            <template slot-scope="scope">
                                <span>{{ scope.row.remark }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column prop="createName" label="创建人">
                        </el-table-column>
                        <el-table-column label="创建时间 ">
                            <template slot-scope="scope">
                                <span>{{ scope.row.createTime ? moment(scope.row.createTime).format('YYYY-MM-DDHH: mm:ss') :'' }}</span>
                            </template>
                        </el-table-column>
                        <el-table-column label="操作" width="150">
                            <template slot-scope="scope">
                                <el-button v-if="scope.row.auditStatus == '3'" type="text"
                                    style="color:#409EFF ; margin-left: 0px" @click="editWhiteList(scope.row)"><i
                                        class="el-icon-delete mr-2"></i>
                                    修改</el-button>
                                <el-button v-if="scope.row.auditStatus == '3'" type="text"
                                    style="color: #F56C6C; margin-left:10px" @click="handelDeleta(scope.row)"><i
                                        class="el-icon-delete mr-2"></i>
                                    删除</el-button>
                            </template>
                        </el-table-column>
                    </el-table>
                    <!--分页-->
                    <div style="display: flex;justify-content: space-between;margin-top: 10px;">
                        <div></div>
                        <el-pagination class="page_bottom" @size-change="handleSizeChange"
                            @current-change="handleCurrentChange" :current-page="formInline.currentPage"
                            :page-size="formInline.pageSize" :page-sizes="[10, 20, 50, 100]"
                            layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.total">
                        </el-pagination>
                    </div>
                    <!-- 表格和分页结束 -->
                </div>
            </div>
        </div>
        <el-dialog :show-close="false" title="添加白名单" :visible.sync="dialogVisible" width="800px"
            :before-close="handleClose">
            <el-form :inline="true" :model="ruleForm" :rules="rules" ref="ruleForm" class="demo-ruleForm">
                <div v-if="ruleForm.domainList.length">
                    <template v-for="(row, index) in ruleForm.domainList">
                        <el-form-item :rules="rules.domain" label="域名" :prop="'domainList.' + index + '.domain'">
                            <el-input class="input-t" v-model="row.domain" placeholder=" 请输入域名，例如：www.zthysms.com"
                                @change="(val) => handleDomainChange(val, index)"></el-input>
                        </el-form-item>
                        <el-form-item :rules="rules.remark" label="备注" :prop="'domainList.' + index + '.remark'">
                            <el-input class="input-t" v-model="row.remark" placeholder=" 请输入域名用途或备注信息"></el-input>
                        </el-form-item>
                        <i style="font-size: 24px;color: #F56C6C;cursor: pointer;" class="el-icon-remove-outline"
                            @click="ruleForm.domainList.splice(index, 1)"></i>
                    </template>
                    <div class="add-white" @click="addWhiteListAction">
                        添加白名单
                    </div>
                </div>
                <div v-else class="add-white-list" @click="addWhiteListAction">
                    添加白名单
                </div>
                <!-- <el-form-item label="上传材料" prop="relationFile">
                    <el-upload class="upload-demo" :action="apiUrl" :headers="headers" :limit="1" :on-remove="handleRemove"
                        :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload" :file-list="fileList"
                        list-type="picture">
                        <el-button size="small" type="primary">点击上传</el-button>
                        <div slot="tip" class="el-upload__tip">只能上传jpg/jpeg/png文件，且不超过2M</div>
                    </el-upload>
                </el-form-item> -->
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="handleSubmit('ruleForm')">确 定</el-button>
            </span>
        </el-dialog>
        <el-dialog :show-close="false" title="修改白名单" :visible.sync="dialogEditVisible" width="800px"
            :before-close="handleClose">
            <el-form :inline="true" :model="editForm" :rules="rules" ref="editForm" class="demo-ruleForm">
                <el-form-item label="域名" prop="domain">
                    <el-input class="input-t" v-model="editForm.domain"
                        placeholder=" 请输入域名，例如：www.zthysms.com"></el-input>
                </el-form-item>
                <el-form-item label="备注" prop="remark">
                    <el-input class="input-t" v-model="editForm.remark" placeholder=" 请输入域名用途或备注信息"></el-input>
                </el-form-item>
                <!-- <el-form-item label="上传材料" prop="relationFile">
                    <el-upload class="upload-demo" :action="apiUrl" :headers="headers" :limit="1" :on-remove="handleRemove"
                        :on-success="handleAvatarSuccess" :before-upload="beforeAvatarUpload" :file-list="fileList"
                        list-type="picture">
                        <el-button size="small" type="primary">点击上传</el-button>
                        <div slot="tip" class="el-upload__tip">只能上传jpg/jpeg/png文件，且不超过2M</div>
                    </el-upload>
                </el-form-item> -->
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogEditVisible = false">取 消</el-button>
                <el-button type="primary" @click="handleSubmitEdit('editForm')">确 定</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
import clip from '../../utils/clipboard'
import moment from 'moment'
export default {
    name: "shortApply",
    data() {
        var domain = (rule, value, callback) => {
            if (!value) {
                callback(new Error('域名不能为空'));
            } else {
                var domain = /^(?!-)[A-Za-z0-9-]+([-.]{1}[A-Za-z0-9]+)*.[A-Za-z]{2,6}$/;
                if (domain.test(value)) {
                    callback();
                } else {
                    callback(new Error('域名格式不正确'));
                }
            }
        }
        return {
            shortStatus: 1, //短链状态
            dialogVisible: false,
            dialogEditVisible: false,
            actionStauts: "add",
            ruleForm: {
                domainList: []
                // domainList: "",
                // relationFile: "",
            },
            editForm: {
                id: "",
                domain: "",
                remark: ""
            },
            apiUrl: this.API.cpus + 'v3/file/upload',
            headers: {},
            fileList: [],
            rules: {
                domain: [
                    { required: true, validator: domain, trigger: 'blur' }
                ],
                remark: [
                    { required: true, message: '备注不能为空', trigger: 'blur' }
                ],
                relationFile: [
                    { required: false, message: '请上传审核材料', trigger: 'blur' }
                ]
            },
            // auditReason: "",
            formInline: {
                domain: "",
                auditStatus: "",
                time: [],
                beginTime: "",
                endTime: "",
                currentPage: 1,
                pageSize: 10,
            },
            tableDataObj: {
                //列表数据
                loading2: false, //loading动画
                tableData: [],
                total: 0, //总条数
            },
            selectId: [],
            // {
            //         domain:"",
            //         remark:""
            //     }

        }
    },
    created() {
        this.headers = {
            Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
        };
        this.getShortStatus(); //获取短链状态
        this.getTableData(); //获取列表数据
    },
    methods: {
        //开通短链
        openShort() {
            this.$api.post(this.API.slms + 'shortLink/apply', {}, res => {
                if (res.code == 200) {
                    this.$message.success('短链申请中，请耐心等待审核结果！');
                    this.getShortStatus()
                } else {
                    this.$message.error(res.msg);
                }
            })
        },
        //开通状态
        getShortStatus(val) {
            let data = JSON.parse(localStorage.getItem('userInfo'));
            this.$api.get(this.API.cpus + 'consumerclientshortlink/userShortLinkInfo/' + data.userId, {}, res => {
                if (res.code == 200) {
                    if (res.data) {
                        console.log(1);
                        this.shortStatus = res.data.productOpen;
                    } else {
                        console.log(2);
                        this.shortStatus = 1
                    }

                }
            })
        },
        //查询
        Query() {
            this.getTableData()
        },
        //重置
        Reload(formName) {
            this.$refs[formName].resetFields(); //清空查询表单
            this.formInline.beginTime = "";
            this.formInline.endTime = "";
            this.getTableData()

        },
        //-----复制功能
        handleCopy(name, event) {
            clip(name, event)
        },
        //-----表格数据
        getTableData() {
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.slms + 'shortLink/domainApply/page', this.formInline, res => {
                if (res.code == 200) {
                    this.tableDataObj.tableData = res.data.records;
                    this.tableDataObj.total = res.data.total;
                    this.tableDataObj.loading2 = false;
                }
            })
        },
        //-----翻页操作
        handleSizeChange(size) {
            this.formInline.pageSize = size;
            this.getTableData()

        },
        handleCurrentChange: function (currentPage) {
            this.formInline.currentPage = currentPage;
            this.getTableData()

        },
        handleTimeChange(val) {
            if (val) {
                this.formInline.beginTime = moment(val[0]).format('YYYY-MM-DD HH:mm:ss');
                this.formInline.endTime = moment(val[1]).format('YYYY-MM-DD HH:mm:ss');
            } else {
                this.formInline.beginTime = "";
                this.formInline.endTime = "";
            }
        },
        //全选
        handleSelectionChange(val) {
            let selectId = [];
            for (let i = 0; i < val.length; i++) {
                selectId.push(val[i].id);
            }
            this.selectId = selectId;
        },
        batchDelete() {
            this.$confirms.confirmation(
                "post",
                "确认是否删除白名单？",
                this.API.slms + "shortLink/domain/delete",
                { ids: this.selectId },
                (res) => {
                    if (res.code == 200) {
                        this.getTableData();
                    }

                }
            );
        },
        addWhiteListAction() {
            let obj = {
                domain: "",
                remark: "",
            }
            this.ruleForm.domainList.push(obj)
        },
        handelDeleta(row) {
            this.$confirms.confirmation(
                "post",
                "是否确定删除吗？",
                this.API.slms + "shortLink/domain/delete",
                { ids: [row.id] },
                (res) => {
                    this.getTableData()
                }
            );
        },
        addWhiteList(type, row) {
            this.dialogVisible = true;
        },
        editWhiteList(row) {
            this.dialogEditVisible = true;
            this.editForm.id = row.id;
            this.editForm.domain = row.domain;
            this.editForm.remark = row.remark;
            // this.editForm.relationFile = row.relationFile;
        },
        beforeAvatarUpload(file) {
            const isLt5M = file.size / 1024 / 1024 < 2;
            const fileType = ["jpg", "jpeg", "png"];

            if (!isLt5M) {
                this.$message.error("上传文件大小不能超过 2MB!");
                return false;
            }
            if (!fileType.includes(file.type.split("/")[1])) {
                this.$message.error("上传文件仅支持 jpg、jpeg、png格式!");
                return false;
            }
            return true;
        },
        handleRemove(file, fileList) {
            this.fileList = fileList;
            this.ruleForm.relationFile = "";
        },
        handleAvatarSuccess(res, file, fileList) {
            this.ruleForm.relationFile = res.data.fullpath;
            this.fileList = fileList;
        },
        handleClose() {
            this.ruleForm.domainList = [];
            this.ruleForm.relationFile = "";
            this.ruleForm.remark = "";
            this.editForm.domain = "";
            this.editForm.remark = "";
            // this.editForm.relationFile = "";
            this.fileList = [];
        },
        handleSubmit(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.$api.post(this.API.slms + 'shortLink/domainApply', this.ruleForm, res => {
                        if (res.code == 200) {
                            this.$message.success('添加成功');
                            this.dialogVisible = false;
                            this.getTableData()
                            // this.getShortStatus();
                        } else {
                            this.$message.error(res.msg);
                        }
                    })
                } else {
                    console.log('error submit!!');
                    // this.$message.error('请检查输入项！');
                }
            })
        },
        handleSubmitEdit(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    this.$api.post(this.API.slms + 'shortLink/domain/update', this.editForm, res => {
                        if (res.code == 200) {
                            this.$message.success('修改成功');
                            this.dialogEditVisible = false;
                            this.getTableData()
                            // this.getShortStatus();
                        } else {
                            this.$message.error(res.msg);
                        }
                    })
                } else {
                    console.log('error submit!!');
                    // this.$message.error('请检查输入项！');
                }
            })
        },
        getDomain(url) {
            try {
                // 创建 URL 对象
                const urlObj = new URL(url);
                // 返回主机名（域名）
                return urlObj.hostname;
            } catch (error) {
                // console.error("Invalid URL:", error);
                var domain = /^(?!-)[A-Za-z0-9-]+([-.]{1}[A-Za-z0-9]+)*.[A-Za-z]{2,6}$/;
                if (domain.test(url)) {
                    return url;
                }else{
                    this.$message.error("提取域名中链接必须带有http://或https://！");
                }
                return url;
            }
        },
        handleDomainChange(val, index) {
            this.ruleForm.domainList[index].domain = this.getDomain(val);
            // console.log(this.getDomain(val));

        },
    },
    watch: {
        dialogVisible(val) {
            if (!val) {
                this.$refs.ruleForm.resetFields(); //清空表单
                this.ruleForm.domainList = [];
                // this.ruleForm.relationFile = "";
                // this.ruleForm.id = "";
                // this.fileList = [];

            }
        }
    }
}
</script>

<style scoped>
.shortChain-box {
    padding: 20px;
}

.shortChain-matter {
    border: 1px solid #66CCFF;
    background: #E5F0FF;
    padding: 10px 14px;
    font-size: 12px;
}

.shortChain-matter>p {
    padding: 5px 0px;
}

.short-box {
    margin: 10px 0;
}

.short-btn {

    margin-bottom: 10px;
}

.input-t {
    width: 300px;
}

.add-white-list {
    width: 650px;
    height: 50px;
    line-height: 50px;
    border: 1px dashed #66CCFF;
    text-align: center;
    color: #66CCFF;
    cursor: pointer;
    margin: 0 auto;
}

.add-white {
    width: 640px;
    height: 30px;
    line-height: 30px;
    border: 1px dashed #66CCFF;
    text-align: center;
    color: #66CCFF;
    cursor: pointer;
    margin: 0 auto;
}
</style>