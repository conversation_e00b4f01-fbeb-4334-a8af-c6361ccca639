<template>
  <div class="login_cell_phone bag">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><i class="el-icon-lx-emoji"></i> 短链统计</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div style="padding: 10px">
      <div style="height: 100%">
        <div>
          <el-form
            :inline="true"
            ref="formInline"
            :model="formInline"
            class="demo-form-inline"
          >
            <el-form-item label="短链名称" prop="name">
              <el-input
                v-model="formInline.name"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="短链" label-width="50px" prop="shortCode">
              <el-input
                v-model="formInline.shortCode"
                placeholder=""
                class="input-w"
              ></el-input>
            </el-form-item>
            <el-form-item label="创建时间" label-width="80px" prop="time">
              <el-date-picker
                class="input-time"
                v-model="formInline.time"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="-"
                :clearable="false"
                @change="getTimeOperating"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
              >
              </el-date-picker>
            </el-form-item>
            <el-form-item label-width="80px">
              <el-button type="primary" plain style="" @click="ListSearch"
                >查询</el-button
              >
              <el-button
                type="primary"
                plain
                style=""
                @click="Reset('formInline')"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
        </div>
        <div class="Mail-table" style="padding-bottom: 40px">
          <el-table
            v-loading="tableDataObj.loading2"
            element-loading-text="拼命加载中"
            element-loading-spinner="el-icon-loading"
            element-loading-background="rgba(0, 0, 0, 0.6)"
            ref="multipleTable"
            border
            :data="tableDataObj.tableData"
          >
            <el-table-column label="短链名称">
              <template slot-scope="scope">{{ scope.row.name }}</template>
            </el-table-column>
            <el-table-column label="短链接" width="180px">
              <template slot-scope="scope">{{ scope.row.shortCode }}</template>
            </el-table-column>
            <el-table-column label="长链接" width="550px">
              <template slot-scope="scope">
                <!-- <el-tooltip
                    effect="dark"
                    :content="scope.row.originalUrl"
                    placement="top-start"
                  >
                    <span class="originalUrl">{{
                      scope.row.originalUrl
                    }}</span>
                  </el-tooltip> -->
                <span>{{ scope.row.originalUrl }}</span>
              </template>
            </el-table-column>
            <el-table-column label="打开次数">
              <template slot-scope="scope">
                <span
                  v-if="
                    tableDataObj.tablecurrent.open == 0 &&
                    scope.row.countNum != 0
                  "
                  class="short"
                  @click="checkShort(scope.row)"
                  >{{ scope.row.countNum }}</span
                >
                <span v-else>{{ scope.row.countNum }}</span>
              </template>
            </el-table-column>
            <el-table-column label="可用状态">
              <template slot-scope="scope">
                <span style="color: red" v-if="scope.row.available == 1"
                  >失效</span
                >
                <span style="color: #16a589" v-else>生效</span>
              </template>
            </el-table-column>
            <el-table-column label="有效时间" width="160px">
              <template slot-scope="scope">
                <div v-if="scope.row.expireTime">
                  <div
                    v-if="
                      scope.row.remainingDays > 0 &&
                      scope.row.remainingDays < 7
                    "
                  >
                    <div style="color: #e6a23c">
                      {{
                        moment(scope.row.expireTime).format(
                          "YYYY-MM-DD HH:mm:ss"
                        )
                      }}
                    </div>
                    <div style="font-size: 12px;">
                      即将到期，剩余{{ scope.row.remainingDays }}天
                    </div>
                  </div>
                  <div v-else-if="scope.row.remainingDays <= 0">
                    <div style="color: #f56c6c; cursor: pointer">
                      {{
                        moment(scope.row.expireTime).format(
                          "YYYY-MM-DD HH:mm:ss"
                        )
                      }}
                    </div>
                    <div style="font-size: 12px;">
                     已到期
                    </div>
                  </div>
                  <div v-else>
                    {{
                      moment(scope.row.expireTime).format("YYYY-MM-DD HH:mm:ss")
                    }}
                  </div>
                </div>
              </template>
            </el-table-column>
            <el-table-column label="创建时间" width="160px">
              <template slot-scope="scope">
                <span>{{
                  moment(scope.row.createTime).format("YYYY-MM-DD HH:mm:ss")
                }}</span>
              </template>
            </el-table-column>
            <el-table-column label="备注">
              <template slot-scope="scope">
                <span>{{ scope.row.reason }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作">
              <template slot-scope="scope">
                <el-button type="text" style="color: #F56C6C" @click="deleteShort(scope.row)">删除</el-button>
              </template>
            </el-table-column>
          </el-table>
          <!-- <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton"></table-tem> -->
          <el-col
            :xs="24"
            :sm="24"
            :md="24"
            :lg="24"
            class="page"
            slot="pagination"
            style="background: #fff; padding: 10px 0; text-align: right"
          >
            <el-pagination
              class="page_bottom"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
              :current-page="formInlines.currentPage"
              :page-size="formInlines.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              layout="total, sizes, prev, pager, next, jumper"
              :total="tableDataObj.tablecurrent.total"
            >
            </el-pagination>
          </el-col>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import DatePlugin from "@/components/publicComponents/DatePlugin.vue";
import TableTem from "@/components/publicComponents/TableTem";
export default {
  name: "Shortchainstatistics",
  components: {
    DatePlugin,
    TableTem,
  },
  data() {
    return {
      name: "Shortchainstatistics",
      // 搜索数据
      formInline: {
        shortCode: "",
        name: "",
        time: [],
        beginTime: "",
        endTime: "",
        // beginTime:new Date().toLocaleDateString().split('/').join('-')+' 00:00:00',
        // endTime:new Date().toLocaleDateString().split('/').join('-')+" 23:59:59",
        // time: [new Date().toLocaleDateString().split('/').join('-'),new Date().toLocaleDateString().split('/').join('-')],
        pageSize: 10,
        currentPage: 1,
      },
      // 存储搜索数据
      formInlines: {
        shortCode: "",
        name: "",
        time: [],
        beginTime: "",
        endTime: "",
        // beginTime:new Date().toLocaleDateString().split('/').join('-')+' 00:00:00',
        // endTime:new Date().toLocaleDateString().split('/').join('-')+" 23:59:59",
        // time: [new Date().toLocaleDateString().split('/').join('-'),new Date().toLocaleDateString().split('/').join('-')],
        pageSize: 10,
        currentPage: 1,
      },
      //用户列表数据
      tableDataObj: {
        loading2: false,
        tablecurrent: {
          //分页参数
          total: 0,
          open: 1,
        },
        tableData: [],
        tableLabel: [
          // {
          // prop:"shortLinkTime",
          // showName:'发送时间',
          // width:'140',
          // fixed:false
          // },
          {
            prop: "shortCode",
            showName: "短链接",
            fixed: false,
          },
          {
            prop: "originalUrl",
            showName: "长链接",
            fixed: false,
          },
          // {
          // prop:"generateCount",
          // showName:'发送号码数',
          // fixed:false
          // },{
          // prop:"receiveCount",
          // showName:'成功号码数',
          // fixed:false
          // },
          {
            prop: "countNum",
            showName: "链接打开次数",
            fixed: false,
          },
          {
            prop: "available",
            showName: "长链接",
            fixed: false,
            formatData: function (val) {
              return val == "1" ? "可用" : "不可用";
            },
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "120", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        // tableOptions:[
        //     {
        //         optionName:'查看详情',
        //         type:'',
        //         size:'mini',
        //         optionMethod:'exportNums',
        //         icon:''
        //     }
        // ],
      },
    };
  },
  created() {
    if (this.$route.query.shortCode) {
      this.formInline.shortCode = this.$route.query.shortCode;
      Object.assign(this.formInlines, this.formInline);
    }
    this.InquireList();
  },
  methods: {
    // 发送请求方法
    InquireList() {
      const currentDate = new Date();

      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.slms + "v3/shortLink/report",
        this.formInlines,
        (res) => {
          this.tableDataObj.loading2 = false;
          this.tableDataObj.tableData = res.data.list;
          this.tableDataObj.tableData.forEach((item) => {
            if (item.expireTime) {
              item.expirationDate = new Date(item.expireTime);
              item.remainingTime = item.expirationDate - currentDate;
              item.remainingDays = Math.ceil(
                item.remainingTime / (1000 * 60 * 60 * 24)
              );
            }
          });
          console.log(
            this.tableDataObj.tableData,
            "this.tableDataObj.tableData"
          );

          this.tableDataObj.tablecurrent.total = res.data.total;
          this.tableDataObj.tablecurrent.open = res.data.open;
        }
      );
    },
    handelOptionButton(val) {
      if (val.methods == "exportNums") {
        //查看详情
        this.$router.push({ path: "/detailShort?code=" + val.row.shortCode });
      }
    },
    // 查询
    ListSearch() {
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields();
      this.formInline.beginTime = "";
      this.formInline.endTime = "";
      // (this.formInline.time = [
      //   new Date().toLocaleDateString().split("/").join("-"),
      //   new Date().toLocaleDateString().split("/").join("-"),
      // ]),
      //   (this.formInline.beginTime =
      //     new Date().toLocaleDateString().split("/").join("-") + " 00:00:00");
      // this.formInline.endTime =
      //   new Date().toLocaleDateString().split("/").join("-") + " 23:59:59";
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    // 操作时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.beginTime = this.moment(val[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        this.formInline.endTime = this.moment(val[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      } else {
        this.formInline.beginTime = "";
        this.formInline.endTime = "";
      }
    },
    // 分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size;
      this.InquireList();
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage;
      this.InquireList();
    },
    //查看链接打开次数
    checkShort(row) {
      this.$router.push({
        path: "shortStatistics",
        query: { shortCode: row.originShortCode },
      });
    },
    //删除短链
    deleteShort(row) {
      this.$confirms.confirmation("delete", "确定删除短链吗？", this.API.slms + "v3/shortLink/delete/" + row.id, {}, (res) => {
        if (res.code == 200) {
          // this.$message.success("删除成功");
          this.InquireList();
        }
      });
    },
  },
  // activated(){
  //     this.InquireList()
  // },

  watch: {
    // 监听搜索/分页数据
    // formInlines: {
    //   handler() {
    //     this.InquireList();
    //   },
    //   deep: true,
    //   immediate: true,
    // },
    "$route.query.shortCode"(newValue, oldValue) {
      console.log(newValue, "newValue");
      if (newValue) {
        this.formInline.shortCode = newValue;
        Object.assign(this.formInlines, this.formInline);
        this.InquireList();
      }
    },
  },
};
</script>
<style scoped>
.Statistics-box {
  padding: 20px;
}
.addC .el-select {
  width: 100%;
}
.addC .el-cascader {
  width: 100%;
}
.originalUrl {
  display: inline-block;
  width: 500px;
}
.short {
  cursor: pointer;
  color: #409eff;
}
</style>
