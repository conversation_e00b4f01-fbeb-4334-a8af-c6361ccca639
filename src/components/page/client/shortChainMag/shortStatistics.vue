<template>
  <div class="login_cell_phone bag">
    <div class="Top_title" style="padding: 10px">
      <span
        style="
          display: inline-block;
          padding-right: 10px;
          cursor: pointer;
          color: #16a589;
        "
        @click="goBack()"
        ><i class="el-icon-arrow-left"></i> 返回</span
      >|
      <span>短链用户配置</span>
    </div>
    <div class="short-message-recording-type">
      <el-form
        ref="shortFrom"
        :inline="true"
        :model="shortFrom"
        label-width="86px"
        class="query-frame"
      >
        <el-form-item label="手机号码" prop="mobile">
          <el-input
            v-model="shortFrom.mobile"
            placeholder="请输入手机号"
            class="input-w"
          ></el-input>
        </el-form-item>
        <el-form-item label="打开时间" label-width="80px" prop="time">
          <el-date-picker
            class="input-w"
            v-model="shortFrom.time"
            value-format="yyyy-MM-dd"
            type="daterange"
            range-separator="-"
            :clearable="false"
            @change="getTimeOperating"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
          >
          </el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="querySending()"
            >查 询</el-button
          >
          <el-button type="primary" plain @click="resetSending('shortFrom')"
            >重 置</el-button
          >
          <el-button type="primary" @click="exportNums1()">导出数据</el-button>
          <!-- <el-button type="primary" v-if="specificTime==2" @click="exportNums()">导出数据</el-button> -->
        </el-form-item>
      </el-form>
    </div>
    <div style="padding: 10px">
      <el-table
        v-loading="tableDataObj.loading2"
        element-loading-text="拼命加载中"
        element-loading-spinner="el-icon-loading"
        element-loading-background="rgba(0, 0, 0, 0.6)"
        ref="multipleTable"
        border
        :data="tableDataObj.tableData"
        style="width: 100%"
      >
        <el-table-column label="手机号">
          <template slot-scope="scope">
            <div v-if="scope.row.mobile">
              <span v-if="pIndex == scope.$index">{{
                scope.row.mobile || "-"
              }}</span>
              <span
                v-else
                style="cursor: pointer; color: #16a589"
                @click="phoneClickTable(scope.$index, scope.row)"
                >{{ scope.row.maskMobile || "-" }}</span
              >
            </div>
            <div v-else>未知</div>
          </template>
        </el-table-column>
        <el-table-column label="短链">
          <template slot-scope="scope">{{ scope.row.shortCode }}</template>
        </el-table-column>
        <el-table-column label="UA">
          <template slot-scope="scope">
            <el-tooltip
              class="item"
              effect="dark"
              :content="scope.row.userAgent"
              placement="top"
            >
              <div class="text">
                {{ scope.row.userAgent }}
              </div>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="ip">
          <template slot-scope="scope">{{ scope.row.ip }}</template>
        </el-table-column>
        <el-table-column label="打开时时间">
          <template slot-scope="scope">
            {{ moment(scope.row.clickTime).format("YYYY-MM-DD HH:mm:ss") }}
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination">
        <div></div>
        <div>
          <el-pagination
            class="page_bottom"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :current-page="1"
            :page-size="formData.pageSize"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            :total="tableDataObj.totalRow"
          >
          </el-pagination>
        </div>
      </div>
    </div>
    <el-dialog title="导出数据" :visible.sync="exportShow" width="30%" :before-close="handleClose">
      <el-form :model="ruleForm" ref="ruleForm" label-width="100px" class="demo-ruleForm">
        <el-form-item label="导出类型" prop="decode">
          <el-radio-group v-model="ruleForm.decode">
            <el-radio label="0">掩码</el-radio>
            <el-radio label="1">明码</el-radio>
          </el-radio-group>
        </el-form-item>
        <div style="margin-left: 28px;color: #F56C6C;" v-if="ruleForm.decode == '1'">
          tips: 明码文件将会发送您的{{ emailInfo.email }}邮箱，请注意查收。
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="exportShow = false">取 消</el-button>
        <el-button type="primary" @click="submitExport">{{ ruleForm.decode == '0' ? '下载' : '发送' }}</el-button>
      </span>
    </el-dialog>
    <!-- <el-dialog
            title="手机验证"
            :visible.sync="exportFlag"
            width="40%"
            top="30vh"
        >  
            <DownLoadExport ref="ExportChild" :formData1='formData' productType='11' :isDownload='formData.isDownload' :phoneList="phoneList"/>
        </el-dialog> -->
  </div>
</template>

<script>
import moment from "moment";
import DownLoadExport from "@/components/publicComponents/downLodExport";
export default {
  components: {
    DownLoadExport,
  },
  name:"shortStatistics",
  data() {
    return {
      phoneList: [],
      pIndex: -1,
      exportFlag: false,
      shortFrom: {
        mobile: "",
        time: [],
        beginTime: "",
        endTime: "",
        shortCode: "",
        currentPage: 1,
        pageSize: 10,
      },
      formData: {
        mobile: "",
        time: [],
        beginTime: "",
        endTime: "",
        shortCode: "",
        currentPage: 1,
        pageSize: 10,
      },
      tableDataObj: {
        //表格数据
        loading2: false,
        totalRow: 0,
        tableData: [],
      },
      emailInfo: {
        email: "",
        username: ""
      },
      exportShow: false,
      ruleForm: {
        decode: "0",
      },
    };
  },
  created() {
    this.formData.shortCode = this.$route.query.shortCode;
    this.shortFrom.shortCode = this.$route.query.shortCode;
    this.getList();
  },
  methods: {
    goBack() {
      this.$router.push({ path: "Shortchainstatistics" });
    },
    // 操作时间
    getTimeOperating(val) {
      if (val) {
        this.shortFrom.beginTime = this.moment(val[0]).format(
          "YYYY-MM-DD HH:mm:ss"
        );
        this.shortFrom.endTime = this.moment(val[1]).format(
          "YYYY-MM-DD 23:59:59"
        );
      } else {
        this.shortFrom.beginTime = "";
        this.shortFrom.endTime = "";
      }
    },
    getList() {
      this.pIndex = -1;
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.slms + "shortLinkClick/page",
        this.formData,
        (res) => {
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.tableData.forEach(item=>{
            item.maskMobile = item.mobile
          })
          this.tableDataObj.totalRow = res.data.total;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    querySending() {
      Object.assign(this.formData, this.shortFrom);
      this.getList();
    },
    resetSending(formName) {
      this.$refs[formName].resetFields();
      this.shortFrom.beginTime = "";
      this.shortFrom.endTime = "";
      Object.assign(this.formData, this.shortFrom);
      this.getList();
    },
    //获取分页的每页数量
    handleSizeChange(size) {
      this.formData.pageSize = size;
      this.getList();
    },
    //获取分页的第几页
    handleCurrentChange(currentPage) {
      this.formData.currentPage = currentPage;
      this.getList();
    },
    handleClose() {
      this.exportFlag = false;
    },
    phoneClickTable(index, row) {
      this.pIndex = index;
      this.$api.post(
        this.API.upms + "/generatekey/decryptMobile",
        {
          keyId: row.keyId,
          cipherMobile: row.cipherMobile,
          smsInfoId:row.cipherMobile
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data;
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
        }
      );
    },
    getLoginPhone() {
      this.$api.post(
        this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
        {},
        (res) => {
          if (res.code == 200) {
            this.phoneList = res.data.data;
          }
        }
      );
    },
    exportFn(obj) {
      this.$api.post(this.API.cpus + "statistics/export", obj, (res) => {
        if (res.code == 200) {
          this.exportShow = false
          this.$message({
            type: "success",
            duration: "2000",
            message: "已加入到文件下载中心!",
          });
          this.$router.push('/FileExport');
        } else {
          this.$message({
            type: "error",
            duration: "2000",
            message: res.msg,
          });
        }
      });
    },
    exportNums1() {
      if(this.tableDataObj.tableData.length==0){
        this.$message({
          message: "列表无数据，不可导出！",
          type: "warning",
        });
      }else{
        this.$api.get(this.API.cpus + "statistics/exportDecode/check", {}, (res) => {
          if (res.code == 200) {
            this.emailInfo.email = res.data.email;
            this.emailInfo.username = res.data.username;
            if (res.data.decode) {
              this.exportShow = true
            } else {
              let data = {};
              Object.assign(data, this.formData);
              data.productType = 11;
              this.exportFn(data)
            }
          }
        })
      }
      // this.exportFlag = true
      // this.getLoginPhone()
      // let data = Object.assign({}, this.formData);
      // data.productType = 11;
      //  this.$File.export(this.API.cpus +'statistics/export', data,`彩信发送明显.zip`)
      // this.$api.post(this.API.cpus + "statistics/export", data, (res) => {
      //   if (res.code == 200) {
      //     this.$message({
      //       type: "success",
      //       duration: "2000",
      //       message: "已加入到文件下载中心!",
      //     });
      //   } else {
      //     this.$message({
      //       type: "error",
      //       duration: "2000",
      //       message: res.msg,
      //     });
      //   }
      // });
    },
    submitExport() {
      let data = Object.assign({}, this.formData);
      data.productType = 11;
      data.decode = this.ruleForm.decode == 0 ? false : true;
      this.exportFn(data)
    }
  },
};
</script>

<style scoped>
.short-message-recording-type {
  margin-top: 40px;
}

.pagination {
  display: flex;
  justify-content: space-between;
}

.text {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>