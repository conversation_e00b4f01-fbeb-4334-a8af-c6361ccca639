<template>
  <div class="bag">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><i class="el-icon-lx-emoji"></i> 短链转换</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div class="fillet shortChain-box">
      <div class="shortChain-matter">
        <p style="font-weight: bolder">温馨提醒：</p>
        <p>
          1、短链接，通俗来说就是将长的URL网址，通过程序计算等方式，转换为简短的网址字符串;
        </p>
        <p>2、使用短链接可节省字符数空间，输入更多的短信内容。</p>
        <p>3、链转换服务，可以帮助您快速生成短链，提升营销效果。</p>
      </div>
      <div class="short-box">
        <p class="short-title">短链名称</p>
        <el-input placeholder="请输入链接名称" v-model="name" class="width-l">
        </el-input>
      </div>
      <div class="short-box">
        <p class="short-title">长网址链接</p>
        <el-input
          placeholder="请输入长链接"
          v-model="originalUrl"
          class="width-l"
        >
          <el-button
            slot="append"
            type="primary"
            icon="el-icon-refresh"
            @click="transformation()"
            >转换</el-button
          >
        </el-input>
        <div class="font-sizes font-sizes1">
          <span style="color: red">* </span
          >我们可以帮您把长链接压缩，让您可以输入更多的内容。
        </div>
        <div class="font-sizes">
          <span style="color: red">* </span
          >插入短信内容中时，将在链接前后生成空格符号，以防止出现手机短信客户端不识别链接的情况。
        </div>
      </div>
      <div class="short-box">
        <p class="short-title">短网址链接</p>
        <el-input
          v-model="shortConUrl"
          oncut="return false;"
          class="width-l"
          :disabled="true"
        >
          <el-button
            slot="append"
            type="primary"
            @click="handlePreview()"
            icon="el-icon-share"
            >预览</el-button
          >
          <i class="el-icon-document-copy"></i>
          <el-button
            slot="append"
            type="primary"
            @click="handleCopy(shortConUrl, $event)"
            icon="el-icon-tickets"
            >复制</el-button
          >
        </el-input>
        <!-- <el-button style="margin-left:30px" type="primary" @click="handleCopy(shortConUrl,$event)"  icon="el-icon-copy-documen">复制</el-button> -->
      </div>
    </div>
  </div>
</template>
<script>
import clip from "../../utils/clipboard";
export default {
  name: "shortChainCvs",
  data() {
    return {
      originalUrl: "", //长链接的值
      shortConUrl: "", //短链接的值
      name: "",
    };
  },
  methods: {
    //转换
    transformation() {
      if (this.originalUrl != "") {
        this.$api.post(
          this.API.slms + "v3/shortLink/add",
          { originalUrl: this.originalUrl, name: this.name },
          (res) => {
            if (res.code == 200) {
              this.shortConUrl = res.data.shortLinkUrl;
              this.$api.get(this.API.slms + "v3/shortLink/count", {}, (res) => {
                if (res.code == 200) {
                  if (res.data <= 1) {
                    this.$message({
                      message: "短链接转换成功,短链默认有效期为30天！",
                      type: "success",
                    });
                  } else {
                    this.$message({
                      message: "短链接转换成功！",
                      type: "success",
                    });
                  }
                } else {
                  this.$message({
                    message: res.msg,
                    type: "error",
                  });
                }
              });
            } else if (res.code === 4100001) {
              this.$message({
                type: "error",
                duration: "3000",
                message:
                  "该链接不在白名单内，请到“短链白名单”进行申请、或联系客服处理",
              });
            } else {
              this.originalUrl = "";
              this.$message({
                message: res.msg,
                type: "warning",
              });
            }
          }
        );
      } else {
        this.$message({
          message: "长链接不可为空",
          type: "warning",
        });
      }
    },
    //预览
    handlePreview() {
      if (this.shortConUrl != "") {
        window.open("https://" + this.shortConUrl, "_blank");
      } else {
        this.$message({
          message: "短连接为空，无法预览",
          type: "warning",
        });
      }
    },
    handleCopy(text, event) {
      clip(text, event);
      console.log("clicp");
    },
  },
  watch: {},
};
</script>

<style scoped>
.shortChain-box {
  padding: 20px;
}
.shortChain-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px 14px;
  font-size: 12px;
}
.shortChain-matter > p {
  padding: 5px 0px;
}
.basic-cfg-title {
  font-weight: bold;
  padding-bottom: 32px;
  color: #333;
}
.width-l {
  width: 480px;
}
.font-sizes {
  padding-top: 2px;
  font-size: 12px;
  color: rgb(163, 163, 163);
}
.font-sizes1 {
  margin-top: 10px;
}
.short-box {
  margin-top: 30px;
}
.short-title {
  font-weight: bolder;
  padding-bottom: 5px;
}
</style>