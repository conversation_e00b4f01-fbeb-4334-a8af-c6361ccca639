<template>
  <div class="login_cell_phone bag">
    <div class="Top_title" style="margin: 10px 0px; padding: 10px">
      登录手机号管理
    </div>
    <div class="fillet LoginCellPhone-box" style="padding-bottom: 60px">
      <div class="LoginCellPhone-matter">
        <div>温馨提示</div>
        <div>1.默认登录手机号为您注册账号时的手机号。</div>
        <div>
          2.该登录手机号至多可添加50个，至少一个。当只有一个手机号时不允许删除！
        </div>
      </div>
      <div class="LoginCellPhone-creat">
        <el-button
          v-if="loginInfo.isAdmin == 1"
          type="primary"
          @click="addphone"
          >添加登录手机号</el-button
        >
      </div>
      <div class="LoginCellPhone-search-fun">
        <!-- <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton"></table-tem> -->
        <el-table
          v-loading="tableDataObj.loading2"
          element-loading-text="拼命加载中"
          element-loading-spinner="el-icon-loading"
          element-loading-background="rgba(0, 0, 0, 0.6)"
          ref="multipleTable"
          border
          :stripe="true"
          :data="tableDataObj.tableData"
          style="width: 100%"
        >
          <el-table-column label="用户名">
            <template slot-scope="scope">
              <span>{{ scope.row.consumerName }}</span>
            </template>
          </el-table-column>
          <el-table-column label="手机号码">
            <template slot-scope="scope">
              <div  v-if="loginInfo.isAdmin == 1" class="spanColor">
                <span
                  v-if="scope.$index == mobileIndex"
                  style="cursor: pointer; color: #67c23a"
                  >{{ scope.row.mobile }}</span
                >
                <span
                  v-else
                  style="cursor: pointer; color: #67c23a"
                  @click="handelDecode(scope.row, scope.$index)"
                  >{{ scope.row.maskMobile }}</span
                >
                <!-- {{ scope.row.mobile }} -->
                <el-tooltip effect="dark" content="管理员" placement="top">
                  <i
                    v-if="scope.row.isAdmin == 1"
                    style="color: #409eff; margin-left: 10px"
                    class="iconfont icon-yonghuming"
                  ></i>
                </el-tooltip>
              </div>
              <div v-else>
                <span>{{ scope.row.maskMobile }}</span>
                <el-tooltip effect="dark" content="管理员" placement="top">
                  <i
                    v-if="scope.row.isAdmin == 1"
                    style="color: #409eff; margin-left: 10px"
                    class="iconfont icon-yonghuming"
                  ></i>
                </el-tooltip>
              </div>
            </template>
          </el-table-column>
          <!-- <el-table-column label="是否管理员">
                        <template slot-scope="scope">
                            <span v-if="scope.row.isAdmin == 1">管理员</span>
                            <span v-else>用户</span>
                        </template>
                    </el-table-column> -->
          <el-table-column label="备注">
            <template slot-scope="scope">
              <span v-if="scope.row.remark">{{ scope.row.remark }}</span>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="220">
            <template slot-scope="scope">
              <el-button
                v-if="loginInfo.isAdmin == 1"
                style="color: #e6a23c"
                icon="el-icon-edit"
                type="text"
                @click="editphone(scope.row)"
                >编辑</el-button
              >
              <el-button
                v-if="loginInfo.isAdmin == 1 && scope.row.isAdmin != 1"
                style="color: #f56c6c"
                icon="el-icon-delete"
                type="text"
                @click="deletephone(scope.row)"
                >删除</el-button
              >
              <el-button
                v-if="loginInfo.isAdmin == 1 && scope.row.isAdmin != 1"
                style="color: #409eff"
                icon="el-icon-s-unfold"
                type="text"
                @click="transferAdmin(scope.row)"
                >管理员转让</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </div>
    <!-- 添加手机号 -->
    <el-dialog
      title="添加登录手机号"
      :visible.sync="LcpDialogVisible"
      width="800px"
      class="LoginCellPhoneDialog"
      :close-on-click-modal="false"
      :before-close="handelClose"
    >
      <!-- <el-steps :active="setPhoneSteps" simple style="margin-bottom:26px;">
                <el-step title="获取原手机号验证" icon="el-icon-edit"></el-step>
                <el-step title="新手机号添加" icon="el-icon-upload"></el-step>
            </el-steps>
            <div v-show="setPhoneSteps == 1">
                <el-table
                :data="tableD"
                class="Login-c-p-getPhone"
                border
                style="width: 100%;">
                    <el-table-column
                    align="center"
                    prop="name"
                    label="编号"
                    width="120">
                    </el-table-column>
                    <el-table-column
                    prop="address"
                    align="center"
                    label="手机号">
                    </el-table-column>
                    <el-table-column align="center"  width="80" label="选择" >
                        <template slot-scope="scope" >
                             <el-radio v-if="!flag" @change.native="getCurrentRow(scope.$index)" :label="scope.$index" v-model="radio" class="textRadio" >&nbsp;</el-radio>
                             <el-radio v-else :disabled="true" @change.native="getCurrentRow(scope.$index)" :label="scope.$index" v-model="radio" class="textRadio" >&nbsp;</el-radio>
                        </template>
                    </el-table-column>
                </el-table>
                <el-form :model="setphoneFrom.ruleForm1" :rules="setphoneFrom.rules1" ref="ruleForm1"  class="demo-ruleForm" label-width="120px">
                    <el-form-item label="手机验证码" prop="verCode" style="margin: 40px auto ;">
                        <el-input v-model="setphoneFrom.ruleForm1.verCode" style="display:inline-block;width:180px;"></el-input>
                        <el-button  type="primary" plain style="width:124px;padding:9px 0px;" @click="CountdownCode" v-if="nmb==120">获取验证码</el-button>
                        <el-button  type="primary" plain style="width:124px;padding:9px 0px;" disabled v-else>重新获取({{nmb}})</el-button>
                    </el-form-item>
                    <el-form-item style="">
                        <el-button @click="cancel()"  style="width:100px; padding:9px 0;">取消</el-button>
                        <el-button type="primary" @click="submitForm('ruleForm1')" style="width:100px; padding:9px 0;">下一步</el-button>
                    </el-form-item>
                </el-form> 
            </div>
            <div v-show="setPhoneSteps == 2">
                <el-form :model="setphoneFrom.ruleForm2" :rules="setphoneFrom.rules2" ref="ruleForm2"  class="demo-ruleForm" label-width="130px">
                    <el-form-item label="姓名" prop="username">
                        <el-input v-model="setphoneFrom.ruleForm2.username" style="width:290px;"></el-input>
                    </el-form-item>
                    <el-form-item label="输入手机号" prop="setNewPhone" style="margin-bottom:200px;">
                        <el-input v-model="setphoneFrom.ruleForm2.setNewPhone" style="width:290px;"></el-input>
                    </el-form-item>
                    <el-form-item>
                        <el-button @click="cancel()" style="width:100px; padding:9px 0;">取消</el-button>
                        <el-button type="primary" @click="submitForm('ruleForm2')" style="width:100px; padding:9px 0;">提交</el-button>
                    </el-form-item>
                </el-form>
            </div>        -->
      <el-form
        :inline="true"
        :model="setphoneFrom.ruleForm2"
        :rules="setphoneFrom.rules2"
        ref="ruleForm2"
        class="demo-ruleForm"
      >
        <div v-if="!codeStatus" class="Login-c-p-getPhone">
          验证码将会发送至您的手机号：{{ loginInfo.mobile }}，请注意查收！
        </div>
        <div v-if="!codeStatus">
          <el-form-item
            label-width="120px"
            label="手机验证码"
            prop="verifyCode"
          >
            <el-input
              v-model="setphoneFrom.ruleForm2.verifyCode"
              style="display: inline-block; width: 180px"
            ></el-input>
            <el-button
              type="primary"
              plain
              style="width: 110px; padding: 9px 0px"
              @click="CountdownCode"
              v-if="nmb == 120"
              >获取验证码</el-button
            >
            <el-button
              type="primary"
              plain
              style="width: 110px; padding: 9px 0px"
              disabled
              v-else
              >重新获取({{ nmb }})</el-button
            >
          </el-form-item>
        </div>
        <div v-if="setphoneFrom.ruleForm2.list.length">
          <template v-for="(row, index) in setphoneFrom.ruleForm2.list">
            <el-form-item
              label-width="120px"
              :rules="setphoneFrom.rules2.phone"
              label="登录手机号"
              :prop="'list.' + index + '.phone'"
            >
              <el-input v-model="row.phone" class="input-t"></el-input>
            </el-form-item>
            <el-form-item
              :rules="setphoneFrom.rules2.remark"
              label="备注"
              :prop="'list.' + index + '.remark'"
            >
              <el-input v-model="row.remark" class="input-t"></el-input>
            </el-form-item>
            <i
              style="font-size: 24px; color: #f56c6c; cursor: pointer"
              class="el-icon-remove-outline"
              @click="setphoneFrom.ruleForm2.list.splice(index, 1)"
            ></i>
          </template>
          <div class="add-white" @click="addWhiteListAction">添加手机号</div>
        </div>
        <div v-else class="add-white-list" @click="addWhiteListAction">
          添加手机号
        </div>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button
          @click="LcpDialogVisible = false"
          style="width: 100px; padding: 9px 0"
          >取消</el-button
        >
        <el-button
          type="primary"
          @click="submitForm('ruleForm2')"
          style="width: 100px; padding: 9px 0"
          >提交</el-button
        >
      </span>
    </el-dialog>
    <el-dialog
      :title="title"
      :visible.sync="DeletePhone"
      width="560px"
      class="LoginCellPhoneDialog"
      :close-on-click-modal="false"
      :before-close="handelClose"
    >
      <el-form
        :model="delphone"
        :rules="setphoneFrom.rules2"
        ref="ruleForm3"
        class="demo-ruleForm"
        label-width="130px"
      >
        <div v-if="title != '编辑'" class="Login-c-p-getPhone">
          验证码将会发送至您的手机号：{{ loginInfo.mobile }}，请注意查收！
        </div>
        <el-form-item
          v-if="title != '编辑'"
          label="手机验证码"
          prop="verifyCode"
        >
          <el-input
            v-model="delphone.verifyCode"
            style="display: inline-block; width: 180px"
          ></el-input>
          <el-button
            type="primary"
            plain
            style="width: 110px; padding: 9px 0px"
            @click="CountdownCode"
            v-if="nmb == 120"
            >获取验证码</el-button
          >
          <el-button
            type="primary"
            plain
            style="width: 110px; padding: 9px 0px"
            disabled
            v-else
            >重新获取({{ nmb }})</el-button
          >
        </el-form-item>
        <div v-else>
          <el-form-item label="手机号" prop="mobile">
            <el-input
              disabled
              v-model="delphone.mobile"
              class="input-t"
            ></el-input>
          </el-form-item>
          <el-form-item label="备注" prop="remark">
            <el-input
              v-model="delphone.remark"
              type="textarea"
              class="input-t"
            ></el-input>
          </el-form-item>
        </div>

        <el-form-item>
          <el-button
            @click="DeletePhone = false"
            style="width: 100px; padding: 9px 0"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="delSubmitForm('ruleForm3')"
            style="width: 100px; padding: 9px 0"
            >{{
              title == "删除手机号"
                ? "确认删除"
                : title == "管理员转让"
                ? "确认转让"
                : "确认编辑"
            }}</el-button
          >
        </el-form-item>
      </el-form>
    </el-dialog>
    <ResetNumberVue
      v-if="resetVideo"
      ref="resetNumber"
      :infoData="infoData"
      :visible="resetVideo"
    ></ResetNumberVue>
    <!-- 删除手机号
        <el-dialog
        title="删除手机号"
        :visible.sync="DeletePhone"
        width="560px"
        class="LoginCellPhoneDialog"
        :before-close='handelClose1'
        >
            <el-steps :active="setPhoneSteps" simple style="margin-bottom:26px;">
                <el-step title="获取手机号验证码" icon="el-icon-edit"></el-step>
            </el-steps>
                <el-table
                :data="tableD"
                class="Login-c-p-getPhone"
                border
                style="width: 100%;">
                    <el-table-column align="center"  width="80" label="选择" >
                        <template slot-scope="scope" >
                            <el-radio @change.native="getCurrentRow(scope.$index)" :label="scope.$index" v-model="radio" class="textRadio" >&nbsp;</el-radio>
                        </template>
                    </el-table-column>
                    <el-table-column
                    align="center"
                    prop="name"
                    label="序号"
                    width="120">
                    </el-table-column>
                    <el-table-column
                    prop="address"
                    align="center"
                    label="手机号">
                    </el-table-column>
                </el-table>
                <el-form :model="setphoneFrom.ruleForm1" :rules="setphoneFrom.rules1" ref="ruleForm2"  class="demo-ruleForm" label-width="120px">
                    <el-form-item label="手机验证码" prop="verCode" style="margin: 40px auto ;">
                        <el-input v-model="setphoneFrom.ruleForm1.verCode" style="display:inline-block;width:180px;"></el-input>
                        <el-button  type="primary" plain style="width:124px;padding:9px 0px;" @click="CountdownCode" v-if="nmb==20">获取验证码</el-button>
                        <el-button  type="primary" plain style="width:124px;padding:9px 0px;" disabled v-else>重新获取({{nmb}})</el-button>
                    </el-form-item>
                    <el-form-item style="">
                        <el-button @click="cancel()"  style="width:100px; padding:9px 0;">取消</el-button>
                        <el-button type="primary" @click="submitFormDelete('ruleForm2')" style="width:100px; padding:9px 0;">提交</el-button>
                    </el-form-item>
                </el-form>       
        </el-dialog> -->
  </div>
</template>

<script>
import ResetNumberVue from "@/components/publicComponents/ResetNumber.vue";
import TableTem from "@/components/publicComponents/TableTem";
import bus from "../../../../common/bus";
import common from "../../../../../assets/js/common";
export default {
  name: "LoginCellPhone",
  components: { TableTem, ResetNumberVue },
  data() {
    // 验证IP规则
    var code = (rule, value, callback) => {
      // if (!this.phoneData) {
      //     return callback(new Error("请选中手机号"));
      // } else
      if (value == "") {
        return callback(new Error("请输入验证码"));
      } else {
        callback();
      }
    };
    var phone = (rule, value, callback) => {
      if (!/^([，；,;]*1\d{10}[，；,;]*)*$/.test(value)) {
        return callback(new Error("请输入正确手机号"));
      } else if (value == "") {
        return callback(new Error("请输入手机号"));
      } else {
        callback();
      }
    };
    return {
      nmb: 120,
      timer: null,
      radio: "",
      title: "",
      setPhoneSteps: 1, // 设置手机号的步骤
      loginInfo: {
        isAdmin: null,
        consumerName: "",
        mobile: "",
        remark: "",
        id: "",
      },
      codeStatus: null,
      LcpDialogVisible: false, //弹出框显示隐藏
      DeletePhone: false,
      count: null,
      flag: false,
      // 存储选中手机号
      phoneData: "",
      // 存储原有手机号
      phoneOriginal: [],
      // 存储个人信息
      roleInfo: {},
      delphone: {
        verifyCode: "",
        flag: "2",
        id: "",
        isAdmin: "",
        mobile: "",
        remark: "",
      },
      setphoneFrom: {
        ruleForm1: {
          verCode: "",
        },
        rules1: {
          verCode: [
            { required: true, validator: code, trigger: "blur" },
            { min: 6, max: 6, message: "请输入6位数字验证码" },
          ],
        },
        ruleForm2: {
          verifyCode: "",
          list: [
            {
              phone: "",
              remark: "",
            },
          ],
          // phone: "",
          // remark: "",
        },
        rules2: {
          verifyCode: [
            { required: true, validator: code, trigger: "blur" },
            { min: 6, max: 6, message: "请输入6位数字验证码" },
          ],
          remark: [
            { required: true, message: "请输入备注" },
            {
              min: 2,
              max: 10,
              message: "长度在 2 到 10 个字符",
              trigger: "blur",
            },
          ],
          phone: [
            { required: true, validator: phone, trigger: "blur" },
            // { min: 6, max: 6, message: '请输入6位数字验证码' }
          ],
        },
      },
      tableD: [],
      mobileIndex: null,
      resetVideo: false,
      infoData: {},
      tableDataObj: {
        //列表数据
        // loading2:false,
        tablecurrent: {
          //分页参数
          total: 0,
        },
        tableData: [],
        tableLabel: [
          // {
          //     prop: "Numbering",
          //     showName: "编号",
          //     width: "60",
          //     fixed: false,
          // },
          {
            prop: "consumerName",
            showName: "用户名",
            fixed: false,
          },
          {
            prop: "mobile",
            showName: "手机号码",
            fixed: false,
          },
          {
            prop: "isAdmin",
            showName: "是否管理员",
            fixed: false,
            formatData: function (val) {
              if (val == 1) {
                return (val = "管理员");
              } else {
                return (val = "普通用户");
              }
            },
          },
          {
            prop: "remark",
            showName: "备注",
            fixed: false,
          },
        ],
        tableStyle: {
          isSelection: false, //是否复选框
          // height:250,//是否固定表头
          isExpand: false, //是否是折叠的
          isDefaultExpand: false, //默认打开折叠
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "180", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: "删除",
            type: "",
            size: "mini",
            optionMethod: "dele",
            icon: "el-icon-error",
          },
        ],
      },
      adminform: {
        destId: "",
        sourceId: "",
      },
      phoneId: "",
    };
  },
  methods: {
    getLoginInfo() {
      this.$api.get(
        this.API.cpus + "userLoginAdmin/loginPhoneInfo",
        {},
        (res) => {
          if (res.code == 200) {
            this.loginInfo = res.data;
          }
        }
      );
    },
    // 发送请求方法
    InquireList() {
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.cpus + "consumerclientinfo/loginTelephoneManager/list",
        {},
        (res) => {
          // console.log(res, "res");
          // let resdata = [];
          // let tabledata = [];
          // let phonelength = res.data.data;
          // this.phoneData = phonelength[0].mobile;
          // this.phoneOriginal = [];
          // console.log("------------------");
          // console.log(phonelength);
          // for (var i = 0; i < phonelength.length; i++) {
          //     // 列表数据
          //     let a = {};
          //     a.Numbering = i + 1;
          //     a.username = phonelength[i].consumerName;
          //     a.maskMobile = phonelength[i].maskMobile;
          //     a.phone = phonelength[i].mobile;
          //     resdata[resdata.length] = a;
          //     this.phoneOriginal.push(phonelength[i].mobile);
          //     // 登录手机号列表
          //     let b = {};
          //     b.index = i;
          //     b.name = i + 1;
          //     b.address = phonelength[i].mobile;
          //     tabledata[tabledata.length] = b;
          // }
          // this.tableDataObj.tableData = resdata;
          // this.tableD = tabledata;
          this.tableDataObj.tableData = res.data.data;
          this.tableDataObj.tableData.forEach((item) => {
            item.maskMobile = item.mobile;
          });
          this.tableDataObj.loading2 = false;
          // 存储个人信息
          this.roleInfo = res.data[0];
        }
      );
    },
    // 获取验证码倒计时
    CountdownCode() {
      this.$api.get(
        this.API.cpus +
          "userLoginAdmin/sendVerificationCode?flag=2&phoneId=" +
          this.loginInfo.id,
        {},
        (res) => {
          if (res.code == 200) {
            this.flag = true;
            --this.nmb;
            this.timer = setInterval((res) => {
              --this.nmb;
              if (this.nmb < 1) {
                this.nmb = 120;
                this.flag = false;
                clearInterval(this.timer);
              } else {
                this.flag = true;
              }
            }, 1000);
            this.$message({
              type: "success",
              duration: "2000",
              message: "验证码已发送至手机!",
            });
          } else {
            this.flag = true;
            this.$message({
              type: "warning",
              message: "验证码未失效，需失效后重新获取!",
            });
          }
        }
      );
      // if (this.phoneData) {

      // } else {
      //     this.$message({
      //         message: "请先选中手机号码",
      //         type: "warning",
      //     });
      // }
    },
    handelDecode(val, index) {
      this.mobileIndex = index;
      this.$api.post(
        this.API.upms + "generatekey/decryptMobile",
        {
          keyId: val.keyId,
          smsInfoId: val.smsInfoId,
          cipherMobile: val.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].mobile = res.data;
            // this.$nextTick(() => {
            //     this.$set(this.tableDataObj.tableData[index], "maskMobile", res.data);
            // });
            // console.log(this.tableDataObj.tableData, 'this.tableDataObj.tableData');
          } else if (res.code == 4004002) {
            common.fetchData().then((res) => {
              if (res.code == 200) {
                if (res.data.isAdmin == 1) {
                  this.resetVideo = true;
                  this.infoData = res.data;
                } else {
                  this.$message({
                    message:
                      "您今日解密次数已超限，如需重置解密次数，请联系管理员！",
                    type: "warning",
                  });
                }
              } else {
                this.$message({
                  message: res.msg,
                  type: "error",
                });
              }
            });
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
          // this.tableDataObj.tableData[index].mobile=res.data
        }
      );
    },
    // detailsRow() {
    //     this.SigDialogVisible = true;
    // },
    // showRow(row) {
    //     //赋值给radio
    //     this.radio = this.tableD.indexOf(row);
    // },
    // getCurrentRow(val) {
    //     // console.log(val,'ll');
    //     // this.count = val
    //     // if(this.count == val){
    //     //     this.flag = true
    //     // }else{
    //     //     this.flag = false
    //     // }
    //     this.phoneData = this.tableD[val].address; //赋值手机号
    // },
    // handelOptionButton: function (val) {
    //     if (val.methods == "details") {
    //         this.detailsRow();
    //     }
    // },
    //登录手机号弹出层
    delFun(obj) {
      this.$confirms.confirmation(
        "post",
        "确认删除该手机号",
        this.API.cpus + "userLoginAdmin/deleteLoginPhoneV2",
        obj,
        (res) => {
          if (res.code == 200) {
            this.DeletePhone = false; //关闭弹出框
            this.nmb = 120;
            clearInterval(this.timer);
            this.InquireList();
          }
        }
      );
    },
    getCodeStatus(type, obj) {
      this.$api.get(
        this.API.cpus + "userLoginAdmin/verifiedStatus",
        {},
        (res) => {
          if (res.code == 200) {
            this.codeStatus = res.data;
            if (type == "del") {
              if (res.data === true) {
                let data = {
                  flag: this.delphone.flag,
                  phoneId: obj.id,
                };
                this.delFun(data);
              } else {
                this.delphone.id = obj.id;
                this.delphone.isAdmin = obj.isAdmin;
                this.DeletePhone = true;
              }
            } else if (type == "admin") {
              if (res.data === true) {
                let data = {
                  flag: this.delphone.flag,
                  destId: obj.destId,
                  sourceId: obj.sourceId,
                };
                this.adminFun(data);
              } else {
                this.adminform.destId = obj.destId;
                this.adminform.sourceId = obj.sourceId;
                this.DeletePhone = true;
              }
            }
          }
        }
      );
    },
    addphone() {
      if (this.tableDataObj.tableData.length >= 50) {
        this.$message({
          type: "error",
          duration: "2000",
          message: "用户最多添加50个手机号码",
        });
      } else {
        this.radio = 0;
        this.getCodeStatus();
        this.LcpDialogVisible = true;
      }
    },
    addWhiteListAction() {
      let obj = {
        phone: "",
        remark: "",
      };
      this.setphoneFrom.ruleForm2.list.push(obj);
    },
    // 新增登录手机号
    submitForm(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          let data = {
            flag: 2,
            // phone: this.setphoneFrom.ruleForm2.phone,
            verifyCode: this.setphoneFrom.ruleForm2.verifyCode,
            phoneList: this.setphoneFrom.ruleForm2.list,
            // remark: this.setphoneFrom.ruleForm2.remark,
          };
          this.$api.post(
            this.API.cpus + "userLoginAdmin/addLoginPhoneV2",
            data,
            (res) => {
              if (res.code == 200) {
                this.LcpDialogVisible = false; //关闭弹出框
                // this.cancel();
                this.nmb = 120;
                clearInterval(this.timer);
                this.InquireList(); //刷新列表
              } else {
                this.$message({
                  type: "error",
                  duration: "2000",
                  message: res.msg,
                });
              }
            }
          );
        } else {
          return false;
        }
        // if (val == "ruleForm1") {
        //     if (valid) {
        //         this.$api.get(
        //             this.API.cpus +
        //             "code/checkVerificationCode?code=" +
        //             this.setphoneFrom.ruleForm1.verCode +
        //             "&flag=2",
        //             {},
        //             (res) => {
        //                 if (res.code == 200) {
        //                     clearInterval(this.timer);
        //                     this.nmb = 120;
        //                     this.flag = false;
        //                     this.setPhoneSteps = 2;
        //                 } else {
        //                     this.$message({
        //                         type: "error",
        //                         duration: "2000",
        //                         message: "验证码无效！",
        //                     });
        //                 }
        //             }
        //         );
        //     } else {
        //         console.log("error submit!!");
        //         return false;
        //     }
        // } else {
        //     if (valid) {
        //         let flag = true;
        //         for (var i = 0; i < this.phoneOriginal.length; i++) {
        //             if (
        //                 this.phoneOriginal[i] == this.setphoneFrom.ruleForm2.setNewPhone
        //             ) {
        //                 flag = false;
        //                 break;
        //             }
        //         }
        //         if (flag == true) {
        //             this.$confirms.confirmation(
        //                 "get",
        //                 "确认添加该手机号",
        //                 this.API.cpus +
        //                 "consumerclientinfo/addLoginPhone/" +
        //                 this.setphoneFrom.ruleForm2.setNewPhone,
        //                 {},
        //                 (res) => {
        //                     this.cancel();
        //                 }
        //             );
        //         } else {
        //             this.$message({
        //                 type: "error",
        //                 duration: "2000",
        //                 message: "该手机号已存在,不可重复添加",
        //             });
        //         }
        //     } else {
        //         console.log("error submit!!");
        //         return false;
        //     }
        // }
      });
    },
    delSubmitForm(val) {
      this.$refs[val].validate((valid) => {
        if (valid) {
          if (this.title == "删除手机号") {
            let data = {
              verifyCode: this.delphone.verifyCode,
              flag: this.delphone.flag,
              phoneId: this.delphone.id,
            };
            this.delFun(data);
          } else if (this.title == "管理员转让") {
            let data = {
              verifyCode: this.delphone.verifyCode,
              destId: this.adminform.destId,
              sourceId: this.adminform.sourceId,
              flag: this.delphone.flag,
            };
            this.adminFun(data);
          } else if (this.title == "编辑") {
            let data = {
              phoneId: this.phoneId,
              remark: this.delphone.remark,
            };
            this.$api.post(
              this.API.cpus + "userLoginAdmin/updateLoginPhone",
              data,
              (res) => {
                if (res.code == 200) {
                  this.DeletePhone = false;
                  this.InquireList(); //刷新列表
                } else {
                  this.$message({
                    type: "error",
                    duration: "2000",
                    message: res.msg,
                  });
                }
              }
            );
          }

          // if (this.delphone.isAdmin == 1) {
          //     this.$message({
          //         type: "error",
          //         duration: "2000",
          //         message: "管理员账号不可删除",
          //     });
          // } else {
          //     this.$confirms.confirmation(
          //         "post",
          //         "确认删除该手机号",
          //         this.API.cpus +
          //         "userLoginAdmin/deleteLoginPhoneV2",
          //         data,
          //         (res) => {
          //             if (res.code == 200) {
          //                 this.DeletePhone = false; //关闭弹出框
          //                 this.nmb = 120;
          //                 clearInterval(this.timer);
          //                 this.InquireList();
          //             }
          //         }
          //     );
          // }
        } else {
          return false;
        }
      });
    },
    // cancel() {
    //     this.LcpDialogVisible = false; //关闭弹出框
    //     this.setPhoneSteps = 1; //步进改为1
    //     this.setphoneFrom.ruleForm1.verCode = ""; //验证码置空
    //     this.setphoneFrom.ruleForm2.setNewPhone = ""; //手机号置空
    //     this.InquireList();
    // },
    handelClose() {
      //×号关闭弹窗
      // 点击关闭
      this.LcpDialogVisible = false; //关闭弹出框
      this.DeletePhone = false;
    },
    /**
     * 表格公共的方法--start
     * **/
    // handelOptionButton(val) {
    //     //操作列表的点击
    //     if (val.methods == "dele") {
    //         //点击删除
    //         if (this.tableDataObj.tableData.length <= 1) {
    //             this.$message({
    //                 type: "error",
    //                 duration: "2000",
    //                 message: "最少存在一个手机号",
    //             });
    //         } else {
    //             this.$confirms.confirmation(
    //                 "get",
    //                 "确认删除该手机号",
    //                 this.API.cpus +
    //                 "consumerclientinfo/deleteLoginPhone/" +
    //                 val.row.phone,
    //                 {},
    //                 (res) => {
    //                     this.InquireList();
    //                 }
    //             );
    //         }
    //     }
    // },
    handleSizeChange(size) {
      this.pagesize = size;
    },
    handleCurrentChange: function (currentPage) {
      this.currentPage = currentPage;
    },
    editphone(row) {
      this.title = "编辑";
      this.phoneId = row.id;
      this.delphone.mobile = row.mobile;
      this.delphone.remark = row.remark || "";
      this.DeletePhone = true;
    },
    deletephone(row) {
      this.title = "删除手机号";
      if (row.isAdmin == 1) {
        this.$message({
          type: "error",
          duration: "2000",
          message: "管理员账号不可删除",
        });
      } else {
        let data = {
          id: row.id,
          isAdmin: row.isAdmin,
        };
        this.getCodeStatus("del", data);
      }
    },
    adminFun(data) {
      this.$confirms.confirmation(
        "post",
        "是否将该账号设置为管理员",
        this.API.cpus + "userLoginAdmin/transferAdmin",
        data,
        (res) => {
          if (res.code == 200) {
            this.DeletePhone = false;
            this.getLoginInfo();
            this.InquireList();
          }
        }
      );
    },
    transferAdmin(row) {
      let data = {
        destId: row.id,
        sourceId: "",
      };
      for (let i = 0; i < this.tableDataObj.tableData.length; i++) {
        if (this.tableDataObj.tableData[i].isAdmin == 1) {
          data.sourceId = this.tableDataObj.tableData[i].id;
          break;
        }
      }
      this.title = "管理员转让";
      this.getCodeStatus("admin", data);
    },
    /**
     * 表格公共的方法--end
     * **/
  },
  created() {
    bus.$on("closeVideo", (msg) => {
      this.resetVideo = msg;
    });
    this.getLoginInfo();
    this.InquireList();
  },
  watch: {
    DeletePhone: function (val) {
      if (!val) {
        this.$refs.ruleForm3.resetFields();
      }
    },
    LcpDialogVisible: function (val) {
      if (!val) {
        // this.setphoneFrom.ruleForm1.verCode = ""; //验证码置空
        // this.setphoneFrom.ruleForm2.setNewPhone = ""; //手机号置空
        // this.setPhoneSteps = 1; //步进改为1
        // this.radio = "";
        // this.phoneData = this.phoneOriginal[0];
        // this.$refs.ruleForm1.resetFields();
        this.$refs.ruleForm2.resetFields();
        this.setphoneFrom.ruleForm2.list = [
          {
            phone: "",
            remark: "",
          },
        ];
      }
    },
    resetVideo(val) {
      if (!val) {
        this.mobileIndex = null;
      }
    },
  },
};
</script>

<style scoped>
.LoginCellPhone-box {
  padding: 20px;
}

.LoginCellPhone-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}

.LoginCellPhone-matter > div {
  /* height:26px; */
  line-height: 26px;
}

.LoginCellPhone-set {
  color: #0066cc;
}

.LoginCellPhone-creat {
  margin: 20px 0px;
}

.LoginCellPhone-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}

.LoginCellPhone-list-header {
  display: block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}

.Mail-table {
  padding-bottom: 40px;
}

.LoginCP-type .el-radio + .el-radio {
  margin-left: 0px;
}

.LoginCP-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}

.LoginCP-type .el-radio-group {
  padding-top: 8px;
}

.LoginCP-type-title-tips {
  font-weight: bold;
}

.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}

.Login-c-p-getPhone {
  margin-top: 20px;
  color: #909399;
  font-size: 14px;
  margin-left: 46px;
  margin-bottom: 10px;
}

.add-white-list {
  width: 650px;
  height: 50px;
  line-height: 50px;
  border: 1px dashed #66ccff;
  text-align: center;
  color: #66ccff;
  cursor: pointer;
  margin: 0 auto;
}

.add-white {
  width: 640px;
  height: 30px;
  line-height: 30px;
  border: 1px dashed #66ccff;
  text-align: center;
  color: #66ccff;
  cursor: pointer;
  margin: 0 auto;
}

.input-t {
  width: 250px;
}
</style>
<style>
.LoginCellPhoneDialog .el-steps--simple {
  background: #fff;
  border-bottom: 1px solid #f2f2f2;
  border-radius: 0;
  padding: 13px 7%;
}

.Login-c-p-getPhone .el-radio__label {
  padding-left: 0px;
}

.LoginCellPhoneDialog .el-dialog__body {
  padding: 20px 40px 30px;
}

.login_cell_phone .el-steps--simple > div:nth-child(1) .el-step__title {
  width: 70%;
}
</style>
