<template>
  <div class="showMsgLeft">
    <el-col class="showMsgMenues">
      <el-menu
        class="el-menu-vertical-demo"
        @select="selectMsg"
        :default-active="defaultActive"
      >
        
        <el-menu-item index="1">
          <i class="el-icon-circl circle1"></i>
          <span slot="title" class="navTitle"
            >全部消息({{ showMsgData.count }})</span
          >
          <span class="msgPop">{{ showMsgData.unread }}</span>
        </el-menu-item>
        <el-menu-item index="6">
          <i class="el-icon-circl circle6"></i>
          <span slot="title" class="navTitle"
            >公告通知 ({{ showMsgData.advertisement }})</span
          >
          <span class="msgPop">{{ showMsgData.advertisementUnread }}</span>
        </el-menu-item>
        <el-menu-item index="2">
          <i class="el-icon-circl circle2"></i>
          <span slot="title" class="navTitle"
            >预警通知 ({{ showMsgData.warning }})</span
          >
          <span class="msgPop">{{ showMsgData.warningUnread }}</span>
        </el-menu-item>
        <el-menu-item index="3">
          <i class="el-icon-circl circle3"></i>
          <span slot="title" class="navTitle"
            >系统消息 ({{ showMsgData.system }})</span
          >
          <span class="msgPop">{{ showMsgData.systemUnread }}</span>
        </el-menu-item>
        <el-menu-item index="4">
          <i class="el-icon-circl circle4"></i>
          <span slot="title" class="navTitle"
            >活动福利 ({{ showMsgData.activity }})</span
          >
          <span class="msgPop">{{ showMsgData.activityUnread }}</span>
        </el-menu-item>
        <el-menu-item index="5">
          <i class="el-icon-circl circle5"></i>
          <span slot="title" class="navTitle"
            >审批提醒 ({{ showMsgData.audit }})</span
          >
          <span class="msgPop">{{ showMsgData.auditUnread }}</span>
        </el-menu-item>
      </el-menu>
    </el-col>
  </div>
</template>
<script>
export default {
  name: "showMsgNav",
  components: {
    //    docMenueList
  },
  props: {
    showMsgData: {
      type: Object,
    },
  },
  computed: {
    defaultActive() {
      return this.$route.query.msg ? this.$route.query.msg : "1";
    },
  },
  methods: {
    selectMsg(a, b) {
      this.$router.push({ path: "showMsg", query: { msg: a } });
    },
  },
  data() {
    return {
      // defaultActive:'1',
    };
  },
};
</script>
<style scoped>
.showMsgLeft i.el-icon-circl {
  width: 13px;
  height: 13px;
  border-radius: 50%;
  margin-right: 15px;
}
.circle1 {
  background-color: #333333;
}
.circle2 {
  background-color: #e95d69;
}
.circle3 {
  background-color: #76d1bf;
}
.circle4 {
  background-color: #ffda94;
}
.circle5 {
  background-color: #9dab97;
}
.circle6 {
  background-color: #ffb800;
}
.msgPop {
  padding: 0 8px;
  top: 15px;
  display: block;
  position: absolute;
  right: 40px;
  height: 28px;
  line-height: 28px;
  background: #f2f2f2;
  text-align: center;
  font-size: 12px;
  border-radius: 3px;
  color: #e95d69;
  font-weight: bold;
}
.msgPop:before {
  content: "";
  position: absolute;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 5px 5px 5px 0;
  border-color: transparent #f2f2f2 transparent transparent;
  left: -4px;
  top: 4px;
  top: 12px;
}
/* .msgPop:after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  right: -1px;
  top: -2px;
  background-color: #EA646F;
} */
</style>

<style>
.showMsgLeft .el-menu-item {
  position: relative;
  height: 48px;
  line-height: 48px;
}
.showMsgLeft .el-menu-item:after {
  position: absolute;
  content: "";
  height: 1px;
  background: #e4e4e4;
  width: 90%;
  bottom: 0;
  left: 5%;
}
.showMsgLeft .showMsgMenues .el-menu {
  background: #f7f8fa;
}
.showMsgLeft .el-menu-item.is-active .navTitle {
  color: #333;
  font-weight: bold;
}
.showMsgLeft .el-menu {
  border-right: solid 0px #e6e6e6;
}
.showMsgMenues .el-menu-item:focus,
.showMsgMenues .el-menu-item:hover,
.showMsgMenues .el-submenu__title:hover {
  background: #f7f8fa;
}
</style>