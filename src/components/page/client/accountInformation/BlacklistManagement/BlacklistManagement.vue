<template>
<div class="login_cell_phone bag">
    <div class="Top_title" style="margin:10px 0px;padding:10px;">黑名单管理</div>
    <div class="fillet Black-box" >
        <el-button type="primary" style="margin:10px;" @click="AddBlack()">添加用户黑名单</el-button>
        <el-button type="primary" style="margin:10px;" @click="amountImport = true">批量导入黑名单</el-button>
        <el-button type="primary" style="margin:10px;" @click="batchDeletion" v-if="selectId.length">批量删除黑名单</el-button>
        <el-button type="primary" style="margin:10px;" @click="ExBlack()">导出黑名单</el-button>
        <div class="black-search-fun">
            <span class="black-list-header">黑名单列表</span>
            <!-- 搜索框和日期选择器开始 -->
            <el-input
                placeholder="手机号"
                v-model="InputphoneNamber" style="width:200px;" class="search-box">
                <i slot="suffix" class="el-input__icon el-icon-search"></i>
            </el-input>
            <div class="block">
                <el-date-picker
                v-model="valueData"
                @change="timeChange"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                value-format="yyyy-MM-dd"
                end-placeholder="结束日期">
                </el-date-picker>
            </div>
            <!-- 搜索框和日期选择器结束 -->
        </div>

        <div class="Black-table">
            <!-- 表格和分页开始 -->
            <table-tem :tableDataObj="tableDataObj" @handelOptionButton="handelOptionButton" @handelSelection="handelSelection"></table-tem>
                <!--分页-->
            <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination" style="background:#fff;padding:10px 0;text-align:right;">
                <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page="1" :page-sizes="[10, 20, 50, 100]"  layout="total, sizes, prev, pager, next, jumper" :total="tableDataObj.tablecurrent.total">
                </el-pagination>
            </el-col>
            <!-- 表格和分页结束 -->
        </div>
         <el-dialog
            title="批量导入黑名单"
            :visible.sync="amountImport"
            width="540px"
            top="30vh"
        >  
            <!-- 文件上传 -->
            <!-- <file-upload :action="action" :limit="1"></file-upload> 
            <span slot="footer" class="dialog-footer">
                <el-button @click="amountImport = false">取 消</el-button>
                <el-button type="primary" @click="amountImport = false">确 定</el-button>
            </span> -->
                <div>
                    <el-form label-width="100px" style="padding: 0px 25px 0px 0px;">
                    <el-form-item label="上传号码" prop="upload">
                        <el-upload
                            class="upload-demo"
                            ref="upload"
                            :action="action"
                            :headers="token"
                            :data="{'username':userName,'remark':remark}"
                            :on-preview="handlePreview"
                            :on-remove="handleRemove"
                            :file-list="fileList"
                            :limit="1"
                            :on-success="handleAvatarSuccess"
                            :before-upload="beforeAvatarUpload"
                            :auto-upload="false">
                            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
                        </el-upload>
                        <span style="color:red;font-size:12px;">只支持.txt格式导入</span>
                    </el-form-item>
                    <el-form-item label="备注" >
                        <el-input type="textarea" maxlength="70" placeholder="请输入备注内容，不超过70个字" v-model="remark"></el-input>
                    </el-form-item>
                    </el-form>
                    
                    <!-- <div slot="tip" class="el-upload__tip Remarks">2.建议单次最多上传10万行</div> -->
                </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="amountImport = false">取 消</el-button>
                <el-button type="primary" @click="submitUpload">确 定</el-button>
            </span>
        </el-dialog>
        <!-- 添加黑名单 -->
        <el-dialog
            title="添加黑名单"
            :visible.sync="AddBlacks"
            width="500px"
            top="30vh"
        >  
            <el-form :model="AddBlackform" ref="AddBlackformRef"  label-width="100px" style="padding: 0px 25px 0px 0px;">
                <el-form-item label="手机号码" prop="phone" :rules="filter_rules({required:true,type:'mobile',message:'手机号不能为空'})">
                    <el-input v-model="AddBlackform.phone"></el-input>
                </el-form-item>
                <el-form-item label="备注" prop="remark" >
                    <el-input type="textarea" maxlength="70" placeholder="请输入备注内容，不超过70个字" v-model="AddBlackform.remark"></el-input>
                </el-form-item>
            </el-form>
            <div style="text-align: right;">
                <span slot="footer" class="dialog-footer">
                    <el-button  type="primary" @click="AddBlacklist('AddBlackformRef')">确定</el-button>
                    <el-button @click="AddBlacks = false">取 消</el-button>
                </span>
            </div>
        </el-dialog>
    </div>    
    </div>
</template>

<script>
import TableTem from '@/components/publicComponents/TableTem'
import FileUpload from '@/components/publicComponents/FileUpload'
import DatePlugin from '@/components/publicComponents/DatePlugin'
import { mapState, mapMutations,mapActions } from "vuex";

let _ = require('lodash')
export default {
    name: "BlacklistManagement",
    components: {
        TableTem,
        FileUpload,
        DatePlugin
    },
    data() {
        return {
        name: "BlackList",
        AddBlacks:false,
        // 手机号值
        InputphoneNamber:'',
        // 日期值
        valueData:"",
        // 添加黑名单
        AddBlackform:{
            phone:'',
            remark:''
        },
        //复选框值
        selectId:'',
        pagesize:'10',
        currentPage:'1',
        datePluginValueList: {
            type:"date",
            start:"",
            end:'',
            range:'-',
            defaultTime:"2020-02-03", //默认起始时刻
            datePluginValue: ""
        },
        action:this.API.omcs + "/clientBlacklist/upload",
        dialogVisible: false,
        amountImport:false,
        token:{},
        fileList: [],
        remark:'',
        tableDataObj: {
            loading2:false,
            tablecurrent:{ //分页参数
                total:0,
            },
            tableData: [],
            tableLabel:[{
                prop:"phone",
                showName:'手机号',
                fixed:false
                },{
                prop:"complaintType",
                showName:'号码类别',
                fixed:false,
                formatData:function(val){
                    return val == '1' ? '用户自主加黑' : '上行内容加黑'
                }
                },{
                prop:"createTime",
                showName:'创建时间',
                fixed:false
                },{
                prop:"remark",
                showName:'备注',
                fixed:false
                }],
            tableStyle:{
                isSelection:true,//是否复选框
                // height:250,//是否固定表头
                isExpand:false,//是否是折叠的
                style: {//表格样式,表格宽度
                    width:"100%"
                    },
                optionWidth:'80',//操作栏宽度
                border:true,//是否边框
                stripe:false,//是否有条纹
            },
            tableOptions:[
                {
                    optionName:'删除',
                    type:'',
                    size:'mini',
                    optionMethod:'dele',
                    icon:'el-icon-error',
                    optionButtonColor:'red',//按钮颜色
                }
            ]
        },
      };
    },
    computed:{
        ...mapState({  //比如'movies/hotMovies
            // portraitUrl:state=>state.portraitUrl,
            userName:state=>state.userName
        })
    },
    methods: {
        getphoneBlackList(){
            this.tableDataObj.loading2=true;
            this.$api.post(this.API.omcs+'clientBlacklist/page',{username:this.userName,phone:this.InputphoneNamber,pageSize:this.pagesize,beginTime:this.valueData[0],endTime:this.valueData[1]},res=>{
                this.tableDataObj.tableData=res.records
                this.tableDataObj.loading2=false;
                this.tableDataObj.tablecurrent.total=res.total
            })
        },
        detailsRow: function (index, rows) {
            this.dialogVisible = true
        },
        //列表复选框的值
        handelSelection(val){
            let selectId = [];
            for(let i=0;i<val.length;i++){
                selectId.push(val[i].clientBlacklistId)
            }
            this.selectId = selectId; //批量操作选中id
        },
        //批量删除
        batchDeletion(){
            this.$confirms.confirmation('post','确定删除黑名单',this.API.omcs+'clientBlacklist/batchDelete',{ids:this.selectId},res =>{
                this.getphoneBlackList()
            })
        },
        // 单条删除黑名单
        handelOptionButton: function(val){
            if(val.methods=='dele'){
                this.$confirms.confirmation('get','确定删除黑名单',this.API.omcs+'clientBlacklist/deleteById/'+val.row.clientBlacklistId,{},res =>{
                        this.getphoneBlackList()
                })
            }
        },
        handleSizeChange(size) {
            this.pagesize = size;
            this.tableDataObj.loading2=true;
            this.$api.post(this.API.omcs+'clientBlacklist/page',{username:this.userName,phone:this.InputphoneNamber,beginTime:this.valueData[0],endTime:this.valueData[1],pageSize:size,currentPage:this.currentPage},res=>{
                this.tableDataObj.tableData=res.records
                this.tableDataObj.loading2=false;
                this.tableDataObj.tablecurrent.total=res.total
            })
        },
        handleCurrentChange: function(currentPage){
            this.currentPage = currentPage;
            this.tableDataObj.loading2=true;
            this.$api.post(this.API.omcs+'clientBlacklist/page',{username:this.userName,phone:this.InputphoneNamber,beginTime:this.valueData[0],endTime:this.valueData[1],pageSize:this.pagesize,currentPage:currentPage},res=>{
                this.tableDataObj.tableData=res.records
                this.tableDataObj.loading2=false;
                this.tableDataObj.tablecurrent.total=res.total
            })
        },
        handledatepluginVal: function(val1,val2){
            this.datePluginValue = [val1,val2];
        },
        timeChange:function(val){
            // console.log(val);
            if(val==null){
                this.valueData=['','']
            }
            this.getphoneBlackList()
        },
        // 添加黑名单
        AddBlack(){
            this.AddBlacks=true;
        },
        //导出黑名单
        ExBlack(){
            this.$confirms.confirmation('post','确认导出全部黑名单',this.API.cpus+'v3/export/blacklist',{phone:this.InputphoneNamber,beginTime:this.valueData[0],endTime:this.valueData[1]},res =>{
                
                if(res.code == 200){
                    this.getphoneBlackList()
                    this.$message({
                    type: 'success',
                    duration:'2000',
                    message:res.msg
                });
                this.$router.push({ path: '/FileExport'})
                }else{
                    this.$message({
                    type: 'error',
                    duration:'2000',
                    message:res.msg
                });
                }
            })
        },
        //确定添加黑名单
        AddBlacklist(val){
            this.$refs[val].validate((valid) => {
                if(valid){
                    this.AddBlackform.username=this.userName
                    this.$confirms.confirmation('post','确定添加用户黑名单',this.API.omcs+'clientBlacklist/add',this.AddBlackform,res =>{
                        this.getphoneBlackList()
                        this.AddBlacks=false;
                    })
                }
            })
        },
        // 文件上传
        beforeAvatarUpload(file){
            console.log(file,'file');
            const isJPG1 = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet';
            const isJPG2 = file.type === 'text/plain';
            if (!isJPG1&&!isJPG2) {
                this.$message.error('只能上传 .txt 格式!');
                return false;
            }
            if(file.size > 20 * 1024 *1024){
                this.$message.error('上传文件过大');
                return false
            }
        },
        handleAvatarSuccess(file){
            console.log(file)
            if(file.code=="200"){
                this.$message({
                    type: 'success',
                    duration:'2000',
                    message:'上传成功'
                });
                setTimeout(()=>{
                    this.getphoneBlackList()
                },1000)
            }else{
                this.$message({
                    type: 'error',
                    duration:'2000',
                    message:"上传失败"
                });
                this.getphoneBlackList()
            }
            this.fileList=[]
            this.remark=''
            this.amountImport=false;
        },
        submitUpload() {
            this.$refs.upload.submit();
        },
        handleRemove(file, fileList) {
            console.log(file, fileList);
        },
        handlePreview(file) {
            console.log(file);
        }
    },
    watch:{
        InputphoneNamber(val){
            this.getPhone()
        },
        AddBlacks(val){
            if(val==false){
                this.$refs.AddBlackformRef.resetFields() 
            }           
        },
        amountImport(val){
            if(val==false){
                this.remark=''
                this.$refs.upload.clearFiles();
            }
        }
    },
    created(){
        var date = new Date();
        var year = date.getFullYear();
        var month = date.getMonth()+1;
        var day = date.getDate();
        var nowDate = year + "-" + (month < 10 ? "0" + month : month) + "-" + (day < 10 ? "0" + day : day);
        var lastDate = new Date(date - 1000 * 60 * 60 * 24 * 30);//最后30天可以更改，意义：是获取多少天前的时间
        var lastY = lastDate.getFullYear();
        var lastM = lastDate.getMonth()+1;
        var lastD = lastDate.getDate();
        var LDate = lastY + "-" + (lastM < 10 ? "0" + lastM : lastM) + "-"+(lastD < 10 ? "0" + lastD : lastD)
        this.valueData=[LDate,nowDate]

        this.token = {Authorization :"Bearer"  + this.$common.getCookie('ZTGlS_TOKEN')};
        this.getphoneBlackList()
        this.getPhone= _.debounce(this.getphoneBlackList, 500) 
    }
}
</script>

<style scoped>
.UploadNumber{
    display: inline-block;
    line-height: 30px;
}
.Remarks{
    text-indent: 5em;
    color: red;
}
.block{
    position: absolute;
    top: 10px;
    right: 0;
}
.Black-box{
    padding:20px 20px 60px 20px;
}
.Black-table{
    margin-top:15px;
}
.black-list-header{
    position: absolute;
    font-weight: bold;
    left:0;
    top:20px;
}
.download-tips{
    margin-bottom: 15px;
}
.tem-download{
    margin-right:10px;
}
.notes-cont{
    font-size:12px;
    font-weight: bold;
}
.black-search-fun{
    margin-top:10px;
    position: relative;
    height:40px;
}
.search-box{
    position: absolute;
    top:10px;
    right:360px;
}
.search-date{
    position: absolute;
    top:10px;
    right:0px;
}
</style>
<style>
.notice-box .el-dialog__body{
    padding:30px 50px;
}
.notice-box .el-form-item__label{
    text-align:left;
}
.el-dialog__title{
    font-size:16px;
}
.UploadNumber{
    display: inline-block
}
@media screen and (max-width: 1200px){
    .el-picker-panel{
        width: 370px;
        height: 350px;
        overflow: auto;
    }
    .el-date-range-picker__content{
        width: 100%;
    }
}
</style>
