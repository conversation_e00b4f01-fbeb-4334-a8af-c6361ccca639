<template>
  <div class="login_cell_phone bag">
    <div class="Top_title" style="margin: 10px 0px; padding: 10px">
      通知与预警
    </div>
    <div class="login_cell_phone fillet home-box">
      <el-button type="primary" style="margin-right: 10px" @click="addContacts"
        >添加告警联系人</el-button
      >
      <span style="color: red">最多可添加 5 个指定联系人</span>
      <div class="notice-table">
        <table-tem
          :tableDataObj="tableDataObj"
          @handelOptionButton="handelOptionButton"
        ></table-tem>
      </div>
      <!-- 添加联系人弹出框 -->
      <el-dialog
        :title="dialogTitle"
        :visible.sync="dialogVisible"
        width="520px"
        :close-on-click-modal="false"
      >
        <el-form
          :model="form"
          ref="addContactForm"
          :rules="formRule"
          label-width="150px"
        >
          <el-form-item label="姓名" prop="linkmanName">
            <el-input
              v-model="form.linkmanName"
              autocomplete="off"
              style="width: 290px"
            ></el-input>
          </el-form-item>
          <el-form-item
            label="手机号码"
            prop="linkmanPhone"
            :rules="
              filter_rules({
                required: true,
                type: 'mobile',
                message: '手机号为必填项！',
              })
            "
          >
            <el-input
              v-model="form.linkmanPhone"
              autocomplete="off"
              style="width: 290px"
            ></el-input>
          </el-form-item>
          <!-- <el-form-item label="邮箱"  prop="linkmanEmail">
                        <el-input v-model="form.linkmanEmail" autocomplete="off" style="width:290px;"></el-input>
                    </el-form-item> -->
          <el-form-item
            label="接收预警通知"
            prop="warningRemind"
            v-if="
              this.$store.state.roleId == '14' &&
              this.$store.state.warningRemind == 2
            "
          >
            <el-radio-group v-model="form.warningRemind">
              <el-radio label="2">是</el-radio>
              <el-radio label="1">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="人工审核通知" prop="auditRemind">
            <el-radio-group v-model="form.auditRemind">
              <el-radio label="2">是</el-radio>
              <el-radio label="1">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="余额变更&不足通知" prop="balanceRemind">
            <el-radio-group v-model="form.balanceRemind">
              <el-radio label="2">是</el-radio>
              <el-radio label="1">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <!-- <el-form-item label="适用产品类别选择"  prop="productType">
                         <el-checkbox v-model="checked">短信</el-checkbox>
                    </el-form-item> -->
        </el-form>
        <span slot="footer" class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="addContactOk(dialogStatus)"
            >确 定</el-button
          >
        </span>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import TableTem from "@/components/publicComponents/TableTem";
import { mapState, mapMutations, mapActions } from "vuex";
export default {
  name: "NotificationAlert",
  components: { TableTem },
  data() {
    return {
      value_leixing: "", //发送类型
      value_phone: "", //手机号选择
      balanceState: false,
      tipsVisible: false,
      numjcStatus: "1", //空号检测是否开通
      companyName: "", //公司名称
      lastTime: "", //上次登录时间
      restNumSms: "", //剩余短信条数
      openedBusiness: "", //已开通业务
      username: "", //用户名
      portraitPath: "",
      portraitUrl: "", // 头像
      userBalance: "", //账户余额
      warnDialogVisible: false, //账户余额弹出层
      iphone: "", //当前绑定手机号
      NumberBalances: "", // 余额条数
      reminderBalances: "", //余额提醒条数
      activeName: "first", //昨日、本周、本月数据
      weekTime: "", //本周数据
      monthTime: "", //本月数据
      keyNum: "", //当前传递的日期关键词
      sendNums: "", //发送数量
      successNums: "", //成功数量
      tableRows: {}, //编辑的储存数据
      dialogStatus: 1, //告警联系人弹窗状态----1为新增，2为编辑
      dialogVisible: false,
      checked: true,
      tableDataObj: {
        loading2: false,
        tableData: [],
        tableLabel: [],
        tableStyle: {
          isSelection: false, //是否复选框
          height: 400, //是否固定表头
          isExpand: false, //是否是折叠的
          style: {
            //表格样式,表格宽度
            width: "100%",
          },
          optionWidth: "180", //操作栏宽度
          border: true, //是否边框
          stripe: false, //是否有条纹
        },
        tableOptions: [
          {
            optionName: "编辑",
            type: "",
            size: "mini",
            optionMethod: "editContact",
            icon: "el-icon-edit", //按钮图标
            color: "#16A589", //按钮颜色
          },
          {
            optionName: "删除",
            type: "",
            size: "mini",
            optionMethod: "delContact",
            icon: "el-icon-delete", //按钮图标
            color: "#f56c6c", //按钮颜色
          },
        ],
      },
      form: {
        conLinkmanId: "",
        linkmanName: "",
        linkmanPhone: "",
        // linkmanEmail: '',
        warningRemind: "1",
        auditRemind: "2",
        balanceRemind: "1",
        category: "2",
        productType: "1",
      },
      formRule: {
        linkmanName: [
          { required: true, message: "请输入姓名", trigger: "blur" },
          // {pattern:/^[\u4e00-\u9fa5]{2,4}$/gi,message:'姓名为2-4个中文字符!'}
        ],
        warningRemind: [
          {
            required: true,
            message: "请选择是否接收预警提通知",
            trigger: "change",
          },
        ],
        auditRemind: [
          {
            required: true,
            message: "请选择是否人工审核通知",
            trigger: "change",
          },
        ],
        balanceRemind: [
          {
            required: true,
            message: "请选择是否余额不足通知",
            trigger: "change",
          },
        ],
        productType: [
          {
            required: true,
            message: "请至少选择一个产品类别",
            trigger: "change",
          },
        ],
      },
    };
  },
  watch: {
    checked(newVal, oldVal) {
      if (newVal == true) {
        this.form.productType = "1";
      } else {
        this.form.productType = "";
      }
    },
    dialogVisible(newVal, oldVal) {
      //------监听告警联系人弹窗
      if (newVal == false) {
        this.$refs.addContactForm.resetFields(); //验证置空
        //置空
        this.form.conLinkmanId = "";
        this.form.linkmanName = "";
        this.form.linkmanPhone = "";
        this.form.warningRemind = "1";
        this.form.auditRemind = "2";
        this.form.productType = "1";
        this.checked = true;
      } else {
        this.checked = true;
      }
    },
    warningRemind(val) {
      this.getTableData();
    },
  },
  computed: {
    ...mapState({
      warningRemind: (state) => state.warningRemind,
      roleDesc: (state) => state.roleDesc,
      userId: (state) => state.userId,
    }),
    dialogTitle: function () {
      return this.dialogStatus == 1 ? "新增告警联系人" : "编辑告警联系人";
    },
  },
  created() {
    console.log(this.$store.state.roleId,'this.userId');
    
  },
  methods: {
    // handelClose() {//余额提醒 ×号关闭弹窗
    //      this.warnDialogVisible = false; //关闭弹出框
    //      this.getRemindNums();
    // },
    //点击取消
    // diaCancel(){
    //     this.warnDialogVisible = false;
    //     this.getRemindNums();
    // },
    //点击确定
    // determine(){
    //     const testNum= /^([1-9][0-9]{0,6}|10000000)$/;
    //     if(testNum.test(this.NumberBalances)){
    //         this.$confirms.confirmation('post','确定余额条数不足'+this.NumberBalances+'条时提醒',this.API.cpus + '/consumerClientBalanceNotice/save',{smsNum:this.NumberBalances},res =>{
    //             this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
    //                 this.reminderBalances=res.smsNum
    //             })
    //             this.warnDialogVisible=false;//隐藏弹窗
    //         });
    //     }else{
    //         this.$message({
    //             type: 'error',
    //             duration:'2000',
    //             message:"请填写1-10000000的余额条数"
    //         });
    //     }
    // },
    // getRemindNums(){//获取设置的月提醒条数；
    //     this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
    //         this.NumberBalances=res.smsNum
    //     })
    // },
    addContacts() {
      if (this.tableDataObj.tableData.length >= 5) {
        this.$message({
          message: "最多可添加 5 个指定联系人！",
          type: "warning",
        });
      } else {
        this.dialogStatus = 1;
        this.dialogVisible = true;
      }
    },
    /* --------------- 列表展示 ------------------*/
    getTableData() {
      //获取列表数据
      let balanceState;
      // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
      // if(res){
      //     if(res.smsIsOpen==2){
      //         balanceState=false
      //         this.balanceState=false
      //     }else{
      //         balanceState=true
      //         this.balanceState=true
      //     }
      // }else{
      //     balanceState=false
      //     this.balanceState=false
      // }
      // if (this.$store.state.warningRemind==2) {
      //     if(balanceState==false){
      //         this.tableDataObj.tableLabel=[{
      //             prop:"linkmanName",
      //             showName:'姓名',
      //             width:'140',
      //             fixed:false
      //         },{
      //             prop:"linkmanPhone",
      //             showName:'手机',
      //             fixed:false
      //         },{
      //             prop:"auditRemind",
      //             showName:'审核通知',
      //             fixed:false,
      //             formatData: function(val) { return val == '2' ? 'Y' : 'N' },
      //         },{
      //             prop:"warningRemind",
      //             showName:'预警通知',
      //             fixed:false,
      //             formatData: function(val) { return val == '2' ? 'Y' : 'N' },
      //         }]
      //     }else{
      this.tableDataObj.tableLabel = [
        {
          prop: "linkmanName",
          showName: "姓名",
          width: "140",
          fixed: false,
        },
        {
          prop: "linkmanPhone",
          showName: "手机",
          fixed: false,
        },
        {
          prop: "auditRemind",
          showName: "审核通知",
          fixed: false,
          formatData: function (val) {
            return val == "2" ? "Y" : "N";
          },
        },
        {
          prop: "balanceRemind",
          showName: "余额变更&不足通知",
          fixed: false,
          formatData: function (val) {
            return val == "2" ? "Y" : "N";
          },
        },
        // {
        //     prop:"warningRemind",
        //     showName:'预警通知',
        //     fixed:false,
        //     formatData: function(val) { return val == '2' ? 'Y' : 'N' },
        // }
      ];
      // }
      // } else {
      //     if(balanceState==false){
      // this.tableDataObj.tableLabel=[{
      //     prop:"linkmanName",
      //     showName:'姓名',
      //     width:'140',
      //     fixed:false
      // },{
      //     prop:"linkmanPhone",
      //     showName:'手机',
      //     fixed:false
      // },{
      //     prop:"auditRemind",
      //     showName:'审核通知',
      //     fixed:false,
      //     formatData: function(val) { return val == '2' ? 'Y' : 'N' },
      // }]
      // }else{
      // this.tableDataObj.tableLabel=[{
      //     prop:"linkmanName",
      //     showName:'姓名',
      //     width:'140',
      //     fixed:false
      // },{
      //     prop:"linkmanPhone",
      //     showName:'手机',
      //     fixed:false
      // },{
      //     prop:"auditRemind",
      //     showName:'审核通知',
      //     fixed:false,
      //     formatData: function(val) { return val == '2' ? 'Y' : 'N' },
      // },{
      //     prop:"balanceRemind",
      //     showName:'余额不足通知',
      //     fixed:false,
      //     formatData: function(val) { return val == '2' ? 'Y' : 'N' },
      // }]
      // }
      // }
      this.tableDataObj.loading2 = true;
      this.$api.get(this.API.cpus + "consumerclientlinkman/page", {}, (res) => {
        this.tableDataObj.tableData = res.data;
        this.tableDataObj.loading2 = false;
      });
      // })
    },
    deleRow: function (index, rows) {
      this.$confirm("此操作将永久删除该文件, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      })
        .then(() => {
          this.tableData.splice(index, 1);
          this.$message({
            type: "primary",
            message: "删除成功!",
          });
        })
        .catch(() => {
          this.$message({
            type: "info",
            message: "已取消删除",
          });
        });
    },
    addContactOk: function (val) {
      //新增告警联系人--弹窗
      this.$refs.addContactForm.validate((valid) => {
        if (valid) {
          if (val == 1) {
            //新增
            this.form.conLinkmanId = ""; //id清空
            // if (this.form.linkmanPhone == '' && this.form.linkmanEmail == '') {
            if (this.form.linkmanPhone == "") {
              this.$message({
                message: "手机号或邮箱必须填一个",
                type: "warning",
              });
            } else {
              this.$api.get(
                this.API.cpus +
                  "consumerclientlinkman/validatePhoneExist/" +
                  this.form.linkmanPhone,
                {},
                (res) => {
                  if (res.code == 200) {
                    this.$confirms.confirmation(
                      "post",
                      "确定新增告警联系人",
                      this.API.cpus + "consumerclientlinkman/add",
                      this.form,
                      (res) => {
                        this.getTableData();
                        this.dialogVisible = false; //关闭弹窗
                      }
                    );
                  } else {
                    this.$message({
                      message: "手机号已重复，重新输入！",
                      type: "warning",
                    });
                  }
                }
              );
              //   if (this.form.linkmanPhone != "") {

              //   } else {
              //     this.$confirms.confirmation(
              //       "post",
              //       "确定新增告警联系人",
              //       this.API.cpus + "consumerclientlinkman/add",
              //       this.form,
              //       (res) => {
              //         this.getTableData();
              //         this.dialogVisible = false; //关闭弹窗
              //       }
              //     );
              //   }
            }
          } else if (val == 0) {
            if (this.form.linkmanPhone == "") {
              this.$message({
                message: "手机号或邮箱必须填一个",
                type: "warning",
              });
            } else {
              this.$api.get(
                this.API.cpus +
                  "consumerclientlinkman/validatePhoneExist/" +
                  this.form.linkmanPhone +
                  "?conLinkmanId=" +
                  this.form.conLinkmanId,
                {},
                (res) => {
                  if (res.code == 200) {
                    this.$confirms.confirmation(
                      "put",
                      "确定编辑告警联系人？",
                      this.API.cpus + "consumerclientlinkman/update",
                      this.form,
                      (res) => {
                        this.getTableData();
                        this.dialogVisible = false; //关闭弹窗
                      }
                    );
                  } else {
                    this.$message({
                      message: "手机号已重复，重新输入！",
                      type: "warning",
                    });
                  }
                }
              );
            }
            //编辑
            // if (
            //   this.tableRows.linkmanPhone == this.form.linkmanPhone &&
            //   this.tableRows.linkmanPhone != ""
            // ) {
            //   this.$confirms.confirmation(
            //     "put",
            //     "确定编辑告警联系人？",
            //     this.API.cpus + "consumerclientlinkman/update",
            //     this.form,
            //     (res) => {
            //       this.getTableData();
            //       this.dialogVisible = false; //关闭弹窗
            //     }
            //   );
            // } else {
            //   // if (this.form.linkmanPhone == '' && this.form.linkmanEmail == '') {
            // if (this.form.linkmanPhone != "") {
            // } else {
            //   this.$confirms.confirmation(
            //     "put",
            //     "确定编辑告警联系人？",
            //     this.API.cpus + "consumerclientlinkman/update",
            //     this.form,
            //     (res) => {
            //       this.getTableData();
            //       this.dialogVisible = false; //关闭弹窗
            //     }
            //   );
            // }

            // }
          }
        } else {
          return false;
        }
      });
    },
    handelOptionButton: function (val) {
      if (val.methods == "editContact") {
        //--------------编辑联系人
        this.form.linkmanName = val.row.linkmanName;
        // this.form.linkmanPhone=val.row.linkmanPhone;
        // this.form.linkmanEmail=val.row.linkmanEmail;
        this.form.auditRemind = val.row.auditRemind + "";
        this.form.balanceRemind = val.row.balanceRemind + "";
        this.form.warningRemind = val.row.warningRemind;
        this.form.productType = val.row.productType;
        this.form.conLinkmanId = val.row.conLinkmanId;
        this.tableRows = val.row;
        this.dialogStatus = 0; //打开编辑告警联系人弹窗
        this.dialogVisible = true; //打开弹窗
        this.$api.get(
          this.API.cpus + "consumerclientlinkman/" + val.row.conLinkmanId,
          {},
          (res) => {
            if (res.code == 200) {
              this.form.linkmanPhone = res.data.linkmanPhone;
            } else {
              this.$message({
                message: res.msg,
                type: "error",
              });
            }
          }
        );
      }
      if (val.methods == "delContact") {
        //------------删除联系人
        let conLinkmanId = val.row.conLinkmanId;
        this.$confirms.confirmation(
          "delete",
          "确定删除告警联系人？",
          this.API.cpus + "consumerclientlinkman/" + conLinkmanId,
          {},
          (res) => {
            this.getTableData();
          }
        );
      }
    },
    // 获取项目绝对路径
    ...mapActions([
      //比如'movies/getHotMovies
      "saveUrl",
    ]),
    logUrl(val) {
      let logUrl = {
        logUrl: val,
      };
      this.saveUrl(logUrl);
      window.sessionStorage.setItem("logUrl", val);
    },
  },
  mounted() {
    this.getTableData();
  },
};
</script>

<style scoped>
.LoginCellPhone-box {
  padding: 20px;
}
.LoginCellPhone-matter {
  border: 1px solid #66ccff;
  background: #e5f0ff;
  padding: 10px;
  font-size: 12px;
}
.LoginCellPhone-matter > div {
  height: 26px;
  line-height: 26px;
}
.LoginCellPhone-set {
  color: #0066cc;
}
.LoginCellPhone-creat {
  margin: 20px 0px;
}
.LoginCellPhone-contact {
  color: #0066ff;
  font-size: 12px;
  cursor: pointer;
}
.LoginCellPhone-list-header {
  display: block;
  padding: 30px 0 14px 0;
  font-weight: bold;
}
.Mail-table {
  padding-bottom: 40px;
}
.LoginCP-type .el-radio + .el-radio {
  margin-left: 0px;
}
.LoginCP-type .el-radio {
  display: block;
  white-space: normal;
  padding-bottom: 16px;
}
.LoginCP-type .el-radio-group {
  padding-top: 8px;
}
.LoginCP-type-title-tips {
  font-weight: bold;
}
.audit-criteria {
  display: inline-block;
  padding-left: 80px;
  margin-bottom: 22px;
  font-size: 12px;
  color: #049679;
  cursor: pointer;
}
.div_GG {
  height: 35px;
  line-height: 35px;
  position: sticky;
}
.span-radius {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #16a589;
  display: inline-block;
  margin-right: 5px;
  position: absolute;
  top: 14px;
}
.span_GG {
  display: inline-block;
  white-space: nowrap;
  margin-left: 15px;
  overflow: hidden;
  width: 370px;
  text-overflow: ellipsis;
}
#app .el-tabs {
  height: 213px !important;
}
#app .home-bottom-purple {
  height: 253px;
}
.home-box {
  margin-bottom: 10px;
  padding: 20px;
}
.home-user-info {
  margin-bottom: 10px;
}
.home-bottom-purple {
  padding: 20px;
  background: #fff;
  box-sizing: border-box;
}
.home-user-info-item {
  padding: 30px 0;
  text-align: center;
}
.home-title {
  font-weight: bold;
  padding-bottom: 16px;
}
.home-product-box {
  width: calc(33.33333333333% - 12px);
  padding: 24px 20px;
  margin-right: 12px;
  display: inline-block;
  border: 1px solid #eee;
  box-sizing: border-box;
  margin-bottom: 12px;
}
.home-product-box3,
.home-product-box6 {
  margin-right: 0px;
}
.home-product-box:hover {
  border: 1px solid #16a589;
}
.home-product-img {
  display: inline-block;
  width: 88px;
}
.home-product-img4 {
  width: 83px;
}
.home-product-img5 {
  width: 83px;
}
.home-product-img img {
  width: 100%;
  position: relative;
  top: 8px;
}
.home-product-img4 img {
  top: 2px;
}
.home-product-img5 img {
  top: 2px;
}
.home-product-img6 img {
  top: 6px;
}
.home-product-text {
  width: calc(100% - 93px);
  display: inline-block;
  padding-left: 12px;
  box-sizing: border-box;
}
.home-product-title {
  font-weight: bold;
  padding-bottom: 8px;
}
.home-product-aic {
  height: 46px;
}
/* 用户头像 */
.home-avatar {
  width: 82px;
  height: 82px;
  background: #fff;
  border-radius: 50%;
  overflow: hidden;
  /* margin: 26px 0 10px 26px ; */
}
/* 昨日、本周、本月数据 */
.getXdatas {
  display: flex;
  padding-top: 20px;
  padding-bottom: 22px;
}
.getXdatas li {
  width: 50%;
  text-align: center;
}
.getXdatas-send,
.getXdatas-success {
  padding-top: 20px;
}

@media screen and (max-width: 1628px) {
  .home-product-aic {
    height: 66px;
  }
  .home-product-img1 img {
    top: 1px;
  }
  .home-product-img2 img {
    top: -4px;
  }
  .home-product-img3 img {
    top: 4px;
  }
  .home-product-img4 img {
    top: -4px;
  }
  .home-product-img5 img {
    top: -4px;
  }
  .home-product-img6 img {
    top: 4px;
  }
}

@media screen and (max-width: 1354px) {
  .home-product-box {
    width: calc(50% - 12px);
    padding: 24px 20px;
    margin-right: 12px;
    display: inline-block;
    border: 1px solid #eee;
    box-sizing: border-box;
    margin-bottom: 12px;
  }
  .home-product-box2,
  .home-product-box4,
  .home-product-box6 {
    margin-right: 0px;
  }
}
.home-user-xd .el-col {
  padding: 20px 4%;
}
.home-user-title {
  height: 40px;
  line-height: 40px;
  border: 1px solid #fff;
  border-radius: 20px;
  text-align: center;
  border: 1px solid rgba(22, 165, 137, 1);
}
.notice-table {
  margin-top: 16px;
}
.home-user-info-div {
  display: flex;
}
.home-user-box1 {
  width: 30%;
}
.home-user-box2 {
  display: flex;
  width: 70%;
  margin-left: 12px;
}
.home-bottom-purple1 {
  display: block;
}
.home-bottom-purple1-div {
  margin-left: 12px;
  width: 50%;
}
.home-bottom-purple2-div {
  width: 50%;
}
@media screen and (max-width: 1599px) {
  .home-user-info-div {
    display: block;
  }
  .home-user-box1 {
    width: 100%;
    margin-bottom: 12px;
  }
  .home-user-box2 {
    display: flex;
    width: 100%;
    margin-left: 0px;
  }
}
</style>
<style>
.home-bottom-purple .el-dialog__title {
  font-weight: bold;
  color: #333 !important;
}
.LoginCellPhoneDialog .el-steps--simple {
  background: #fff;
  border-bottom: 1px solid #f2f2f2;
  border-radius: 0;
  padding: 13px 7%;
}
.Login-c-p-getPhone .el-radio__label {
  padding-left: 0px;
}
.LoginCellPhoneDialog .el-dialog__body {
  padding: 20px 40px 30px;
}
.login_cell_phone .el-steps--simple > div:nth-child(1) .el-step__title {
  width: 70%;
}
</style>
