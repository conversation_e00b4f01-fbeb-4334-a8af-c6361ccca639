<template>
  <div class="bag" style="padding: 10px">
    <div class="crumbs">
      <el-breadcrumb separator="/">
        <el-breadcrumb-item
          ><i class="el-icon-lx-emoji"></i> 登录日志</el-breadcrumb-item
        >
      </el-breadcrumb>
    </div>
    <div style="padding: 0 10px">
      <div>
        <el-form
          label-width="80px"
          :inline="true"
          ref="formInline"
          :model="formInline"
          class="demo-form-inline"
        >
          <el-form-item label="登录IP" prop="ip">
            <el-input
              v-model="formInline.ip"
              placeholder=""
              class="input-w"
            ></el-input>
          </el-form-item>
          <el-form-item label="登录状态" prop="isSucced">
            <el-select
              v-model="formInline.isSucced"
              placeholder="不限"
              class="input-w"
            >
              <el-option label="不限" value=""></el-option>
              <el-option label="登录成功" value="1"></el-option>
              <el-option label="登录失败" value="2"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="操作时间" prop="time">
            <el-date-picker
              class="input-time"
              v-model="formInline.time"
              value-format="yyyy-MM-dd"
              type="daterange"
              range-separator="-"
              @change="getTimeOperating"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" plain @click="ListSearch">查询</el-button>
            <el-button type="primary" plain @click="Reset('formInline')"
              >重置</el-button
            >
          </el-form-item>
        </el-form>
      </div>
      <!-- <div class="boderbottom">
                
            </div> -->
      <!-- 表格和分页开始 -->
      <div style="margin: 10px 0">
        <el-table
          v-loading="tableDataObj.loading2"
          ref="multipleTable"
          border
          :data="tableDataObj.tableData"
        >
          <el-table-column label="登录手机号">
            <template slot-scope="scope">
              <div v-if="scope.row.loginMobile">
                <span v-if="pIndex == scope.$index">{{
                  scope.row.loginMobile
                }}</span>
                <span
                  v-else
                  style="cursor: pointer; color: #16a589"
                  @click="phoneClickTable(scope.$index, scope.row)"
                  >{{ scope.row.maskMobile }}</span
                >
              </div>
              <div v-else>-</div>
            </template>
          </el-table-column>
          <!-- <el-table-column
                    label="真实姓名">
                    <template slot-scope="scope">{{ scope.row.realName }}</template>
                    </el-table-column> -->
          <el-table-column label="日志名称">
            <template slot-scope="scope">{{
              scope.row.logName || "-"
            }}</template>
          </el-table-column>
          <el-table-column label="登录IP">
            <template slot-scope="scope">{{ scope.row.ip || "-" }}</template>
          </el-table-column>
          <el-table-column label="IP区域">
            <template slot-scope="scope">{{
              scope.row.ipArea || "-"
            }}</template>
          </el-table-column>
          <el-table-column label="登录状态">
            <template slot-scope="scope">
              <span v-if="scope.row.isSucced == 1">成功</span>
              <span v-else style="color: red">失败</span>
            </template>
          </el-table-column>
          <el-table-column label="操作时间">
            <template slot-scope="scope">{{
              scope.row.createTime || "-"
            }}</template>
          </el-table-column>
          <el-table-column label="详情描述" width="300">
            <template slot-scope="scope"
              >{{ scope.row.logName }}:{{ scope.row.remark
              }}{{ scope.row.isSucced == 1 ? "成功" : "失败" }}</template
            >
          </el-table-column>
        </el-table>
      </div>
      <div style="display: flex; justify-content: space-between">
        <div></div>
        <el-pagination
          class="page_bottom"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :current-page="formInlines.currentPage"
          :page-size="formInlines.pageSize"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          :total="totalSize"
        >
        </el-pagination>
      </div>
    </div>

    <!--分页-->
    <!-- <el-col :xs="24" :sm="24" :md="24" :lg="24" class="page" slot="pagination"
            style="background:#fff;padding:10px 0;text-align:right;">
            
        </el-col> -->
    <!-- 表格和分页结束 -->
  </div>
</template>
<script>
import DatePlugin from "@/components/publicComponents/DatePlugin.vue";
import TableTem from "@/components/publicComponents/TableTem";
import moment from "moment";
export default {
  name: "LoginLog",
  components: {
    DatePlugin,
    TableTem,
  },
  data() {
    return {
      pIndex: -1,
      formInline: {
        userName: "",
        ip: "",
        isSucced: "",
        begTime: moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
        endTime: moment(Date.now()).format("YYYY-MM-DD 23:59:59"),
        time: [
          moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
          moment(Date.now()).format("YYYY-MM-DD 23:59:59"),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      formInlines: {
        userName: "",
        ip: "",
        isSucced: "",
        begTime: moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
        endTime: moment(Date.now()).format("YYYY-MM-DD 23:59:59"),
        time: [
          moment().startOf("day").format("YYYY-MM-DD 00:00:00"),
          moment(Date.now()).format("YYYY-MM-DD 23:59:59"),
        ],
        pageSize: 10,
        currentPage: 1,
      },
      formAdd: {
        name: "",
        state: "",
        menu: "",
        description: "",
      },
      datePluginValueList: {
        type: "date",
        start: "",
        end: "",
        range: "-",
        defaultTime: "", //默认起始时刻
        datePluginValue: "",
      },
      totalSize: 0, //总条数
      tableDataObj: {
        //列表数据
        loading2: false, //loading动画
        tableData: [],
      },
    };
  },
  methods: {
    // 发送请求方法
    InquireList() {
      this.pIndex = -1;
      this.tableDataObj.loading2 = true;
      this.$api.post(
        this.API.cpus + "consumerclient/loginLog/page",
        this.formInlines,
        (res) => {
          this.tableDataObj.tableData = res.data.records;
          this.tableDataObj.tableData.forEach((item) => {
            item.maskMobile = item.loginMobile;
          });
          this.totalSize = res.data.total;
          this.tableDataObj.loading2 = false;
        }
      );
    },
    phoneClickTable(index, row) {
      this.pIndex = index;
      this.$api.post(
        this.API.upms + "/generatekey/decryptMobile",
        {
          keyId: row.keyId,
          cipherMobile: row.cipherMobile,
          smsInfoId: row.cipherMobile,
        },
        (res) => {
          if (res.code == 200) {
            this.tableDataObj.tableData[index].loginMobile = res.data;
          } else {
            this.$message({
              message: res.msg,
              type: "warning",
            });
          }
        }
      );
    },
    // 操作时间
    getTimeOperating(val) {
      if (val) {
        this.formInline.begTime = val[0] + " 00:00:00";
        this.formInline.endTime = val[1] + " 23:59:59";
      } else {
        this.formInline.begTime = "";
        this.formInline.endTime = "";
      }
    },
    // 搜索
    ListSearch() {
      Object.assign(this.formInlines, this.formInline);
      this.InquireList();
    },
    // 重置
    Reset(formName) {
      this.$refs[formName].resetFields();
      this.formInline.begTime = moment()
        .startOf("day")
        .format("YYYY-MM-DD 00:00:00");
      this.formInline.endTime = moment(Date.now()).format(
        "YYYY-MM-DD 23:59:59"
      );
      Object.assign(this.formInlines, this.formInline);
    },
    //分页
    handleSizeChange(size) {
      this.formInlines.pageSize = size;
    },
    handleCurrentChange: function (currentPage) {
      this.formInlines.currentPage = currentPage;
    },
  },
  activated() {
    this.InquireList();
  },
  watch: {
    // 监听搜索/分页数据
    formInlines: {
      handler() {
        this.InquireList();
      },
      deep: true,
      immediate: true,
    },
  },
};
</script>
<style scoped>
.OuterFrame {
  padding: 20px;
}

.addC .el-select {
  width: 100%;
}

.addC .el-cascader {
  width: 100%;
}
</style>