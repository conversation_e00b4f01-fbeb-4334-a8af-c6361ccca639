<template>
    <div class="bag" style="padding:10px">
        <div class="crumbs">
            <el-breadcrumb separator="/">
                <el-breadcrumb-item><i class="el-icon-lx-emoji"></i> 平台操作日志</el-breadcrumb-item>
            </el-breadcrumb>
        </div>
        <div style="padding: 0 10px;">
            <div>
                <el-form label-width="80px" :inline="true" ref="formInline" :model="formInline" class="demo-form-inline">
                    <el-form-item label="标题" prop="ip">
                        <el-input v-model="formInline.title" placeholder="" class="input-w"></el-input>
                    </el-form-item>
                    <!-- <el-form-item label="登录状态" prop="isSucced">
                        <el-select v-model="formInline.isSucced" placeholder="不限" class="input-w">
                            <el-option label="不限" value=""></el-option>
                            <el-option label="登录成功" value="1"></el-option>
                            <el-option label="登录失败" value="2"></el-option>
                        </el-select>
                    </el-form-item> -->
                    <el-form-item label="操作时间" prop="time">
                        <el-date-picker class="input-time" v-model="formInline.time" value-format="yyyy-MM-dd" type="daterange"
                            range-separator="-" @change="getTimeOperating" start-placeholder="开始日期" end-placeholder="结束日期">
                        </el-date-picker>
                    </el-form-item>
                    <el-form-item>
                        <el-button type="primary" plain style="" @click="ListSearch">查询</el-button>
                        <el-button type="primary" plain style="" @click="Reset('formInline')">重置</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <!-- <div class="boderbottom">
               
            </div> -->
            <div style="margin: 10px 0;">
                <!-- 表格和分页开始 -->
                <el-table v-loading="tableDataObj.loading2" ref="multipleTable" border :data="tableDataObj.tableData">
                    <el-table-column label="操作手机号">
                        <template slot-scope="scope">{{ scope.row.loginMobile || '-' }}</template>
                    </el-table-column>
                    <el-table-column label="标题">
                        <template slot-scope="scope">{{ scope.row.title || '-' }}</template>
                    </el-table-column>

                    <el-table-column label="IP">
                        <template slot-scope="scope">{{ scope.row.remoteAddr || '-' }}</template>
                    </el-table-column>
                    <el-table-column label="地址">
                        <template slot-scope="scope">{{ scope.row.requestUri || '-' }}</template>
                    </el-table-column>
                    <el-table-column label="请求方式">
                        <template slot-scope="scope">{{ scope.row.method || '-' }}</template>
                    </el-table-column>
                    <el-table-column label="操作时间">
                        <template slot-scope="scope">{{ scope.row.createTime || '-' }}</template>
                    </el-table-column>
                    <el-table-column label="参数" width="300">
                        <template slot-scope="scope">
                            <el-tooltip class="item" effect="light" placement="top">
                                <div class="tooltip" slot="content">{{ scope.row.params }}</div>
                                <span class="span">{{ scope.row.params || '-' }}</span>
                            </el-tooltip>
                        </template>
                    </el-table-column>
                    <!-- <el-table-column
                    label="登录状态">
                    <template slot-scope="scope">
                        <span v-if="scope.row.isSucced==1">成功</span>
                        <span v-else  style="color:red">失败</span>
                    </template>
                    </el-table-column> -->

                    <!-- <el-table-column
                    label="详情描述"
                    width="300">
                    <template slot-scope="scope" >{{ scope.row.logName }}:{{scope.row.remark}}{{scope.row.isSucced==1?'成功':'失败'}}</template>
                    </el-table-column> -->
                </el-table>
                <!--分页-->
                <div style="display: flex;justify-content: space-between;">
                    <div></div>
                    <el-pagination class="page_bottom" @size-change="handleSizeChange" @current-change="handleCurrentChange"
                        :current-page="formInlines.currentPage" :page-size="formInlines.pageSize"
                        :page-sizes="[10, 20, 50, 100]" layout="total, sizes, prev, pager, next, jumper" :total="totalSize">
                    </el-pagination>
                </div>
                <!-- 表格和分页结束 -->
            </div>
        </div>
    </div>
</template>
<script>
import DatePlugin from '@/components/publicComponents/DatePlugin.vue'
import TableTem from '@/components/publicComponents/TableTem'
import moment from 'moment'
export default {
    name: 'OperationLog',
    components: {
        DatePlugin,
        TableTem
    },
    data() {
        return {
            formInline: {
                userName: '',
                title: '',
                isSucced: '',
                begTime: moment().startOf('day').format('YYYY-MM-DD 00:00:00'),
                endTime: moment(Date.now()).format('YYYY-MM-DD 23:59:59'),
                time: [moment().startOf('day').format('YYYY-MM-DD 00:00:00'), moment(Date.now()).format('YYYY-MM-DD 23:59:59')],
                pageSize: 10,
                currentPage: 1,
            },
            formInlines: {
                userName: '',
                title: '',
                isSucced: '',
                begTime: moment().startOf('day').format('YYYY-MM-DD 00:00:00'),
                endTime: moment(Date.now()).format('YYYY-MM-DD 23:59:59'),
                time: [moment().startOf('day').format('YYYY-MM-DD 00:00:00'), moment(Date.now()).format('YYYY-MM-DD 23:59:59')],
                pageSize: 10,
                currentPage: 1,
            },
            formAdd: {
                name: '',
                state: '',
                menu: '',
                description: ''
            },
            datePluginValueList: {
                type: "date",
                start: "",
                end: '',
                range: '-',
                defaultTime: "", //默认起始时刻
                datePluginValue: ""
            },
            totalSize: 0,//总条数
            tableDataObj: { //列表数据
                loading2: false,//loading动画
                tableData: [],
            },
        }
    },
    methods: {
        // 发送请求方法
        InquireList() {
            this.tableDataObj.loading2 = true;
            this.$api.post(this.API.cpus + 'consumerclient/sysLog/page', this.formInlines, res => {
                this.tableDataObj.tableData = res.data.records;
                this.totalSize = res.data.total;
                this.tableDataObj.loading2 = false;
            })
        },
        // 操作时间
        getTimeOperating(val) {
            if (val) {
                this.formInline.begTime = val[0] + ' 00:00:00'
                this.formInline.endTime = val[1] + ' 23:59:59'
            } else {
                this.formInline.begTime = ""
                this.formInline.endTime = ""
            }
        },
        // 搜索
        ListSearch() {
            Object.assign(this.formInlines, this.formInline);
            this.InquireList();
        },
        // 重置
        Reset(formName) {
            this.$refs[formName].resetFields();
            this.formInline.begTime = moment().startOf('day').format('YYYY-MM-DD 00:00:00');
            this.formInline.endTime = moment(Date.now()).format('YYYY-MM-DD 23:59:59');
            Object.assign(this.formInlines, this.formInline)
        },
        //分页
        handleSizeChange(size) {
            this.formInlines.pageSize = size;
        },
        handleCurrentChange: function (currentPage) {
            this.formInlines.currentPage = currentPage;
        }
    },
    activated() {
        this.InquireList();
    },
    watch: {
        // 监听搜索/分页数据
        formInlines: {
            handler() {
                this.InquireList();
            },
            deep: true,
            immediate: true,
        },
    }
}
</script>
<style scoped>
.OuterFrame {
    padding: 20px;
}

.addC .el-select {
    width: 100%;
}

.addC .el-cascader {
    width: 100%;
}

.span {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.tooltip {
    width: 300px;
}
</style>