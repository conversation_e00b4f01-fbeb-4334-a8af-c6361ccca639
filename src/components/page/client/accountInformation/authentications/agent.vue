<template>
    <div class="lege bag">
        <el-steps :active="active" finish-status="success">
            <el-step title="企业认证"></el-step>
            <el-step title="代理人认证"></el-step>
            <el-step title="查看合同"></el-step>
            <el-step title="完成"></el-step>
        </el-steps>
        <div v-if="active==1">
            
        <div class="lege_cent">
            <div class="lege_tit">
                企业实名信息
            </div>
            <div class="lege_upload">
                <div style="display:flex" @click.capture="fileType = 'fd_L'">
                    <el-upload
                    :action=action
                    :headers="token"
                    :limit="1"
                    list-type="picture-card"
                    :on-preview="handlePictureCardPreview"
                    :on-remove="handleRemove"
                    :on-success="handlewqsSuccess"
                    :file-list="PhotoLege">
                    <i class="el-icon-plus"></i>
                    </el-upload>
                    <div class="lege_upload_r">
                        <div>提示：</div>
                        <div>1.证件照应清晰可见容易识别，且边框完整</div>
                        <div>2.必须真实拍摄，不能使用复印件</div>
                        <div>3.大小不超过10M</div>
                        <div>4.仅支持.png、.jpg、.jpeg</div>
                    </div>
                </div>
                <div class="lege_upload_b">请上传营业执照照片</div>
            </div>
        </div>
        <div>
            <div class="from_tit">请上传照片，系统将自动读取，您可更正读取内容</div>
            <div>
                            <el-form v-loading="loading2 " :inline="true" :rules="formRule" ref="formRefs" :model="formInline" class="demo-form-inline">
                                <el-form-item label="企业名称" label-width="165px" prop="compName">
                                    <el-input  v-model="formInline.compName" placeholder="" class="input-w"></el-input>
                                </el-form-item>
                                <el-form-item label="企业资质编号类型" label-width="165px" prop="qualificationType">
                                    <el-select v-model="formInline.qualificationType" placeholder="请选择">
                                        <el-option
                                        label="企业营业执照统一社会信用代码"
                                        value="1">
                                        </el-option>
                                        <el-option
                                        label="企业营业执照注册号"
                                        value="2">
                                        </el-option>
                                        <el-option
                                        label="个体工商户营业执照统一社会信用代码 "
                                        value="3">
                                        </el-option>
                                        <el-option
                                        label="个体工商户营业执照注册号"
                                        value="4">
                                        </el-option>
                                    </el-select>
                                </el-form-item>
                                <el-form-item label="企业资质编号" label-width="165px" prop="number">
                                    <el-input v-model="formInline.number" placeholder="" class="input-w"></el-input>
                                </el-form-item>
                                <el-form-item label="企业法人姓名" label-width="165px" prop="corporateName">
                                    <el-input  v-model="formInline.corporateName" placeholder="" class="input-w"></el-input>
                                </el-form-item>
                                <el-form-item label="企业法人身份证号码" label-width="165px" prop="corporateIdCard">
                                    <el-input v-model="formInline.corporateIdCard" placeholder="" class="input-w"></el-input>
                                </el-form-item>
                            </el-form>
                        </div>
        </div>
        </div>
        <div v-else-if="active==2">
            <div class="lege_tit">
                代理人实名信息
            </div>
            <div>
                            <el-form  :inline="true" :rules="formRule" ref="formRefs" :model="formInline" class="demo-form-inline">
                                <el-form-item label="代理人身份证正面" label-width="160px" prop="idCardFront">
                                    <div class="lege_upload">
                                        <div style="display:flex" @click.capture="fileType = 'fd_0'">
                                            <el-upload
                                            :action=action
                                            :headers="token"
                                            :limit="1"
                                            list-type="picture-card"
                                            :on-preview="handlePictureCardPreview"
                                            :on-remove="handleRemove"
                                            :on-success="handlewqsSuccess"
                                             :file-list="PhotoFront">
                                            <i class="el-icon-plus"></i>
                                            </el-upload>
                                        </div>
                                    </div>
                                </el-form-item>
                                <el-form-item label="认证人身份证反面" label-width="160px" prop="idCardBack">
                                    <div class="lege_upload">
                                        <div style="display:flex" @click.capture="fileType = 'fd_1'">
                                            <el-upload
                                            :action=action
                                            :headers="token"
                                            :limit="1"
                                            list-type="picture-card"
                                            :on-preview="handlePictureCardPreview"
                                            :on-remove="handleRemove"
                                            :on-success="handlewqsSuccess"
                                             :file-list="PhotoReverse">
                                            <i class="el-icon-plus"></i>
                                            </el-upload>
                                        </div>
                                    </div>
                                </el-form-item>
                                <el-form-item label="委托书" label-width="160px" prop="authorization">
                                    <div class="lege_upload">
                                        <div style="display:flex" @click.capture="fileType = 'fd_S'">
                                            <el-upload
                                            :action=action
                                            :headers="token"
                                            :limit="1"
                                            list-type="picture-card"
                                            :on-preview="handlePictureCardPreview"
                                            :on-remove="handleRemove"
                                            :on-success="handlewqsSuccess"
                                             :file-list="authorizations">
                                            <i class="el-icon-plus"></i>
                                            </el-upload>
                                            <div class="lege_upload_r">
                                                <div>提示：</div>
                                                <div>1.证件照应清晰可见容易识别，且边框完整</div>
                                                <div>2.必须真实拍摄，不能使用复印件</div>
                                                <div>3.大小不超过10M</div>
                                                <div>4.仅支持.png、.jpg、.jpeg</div>
                                                <div><a style="color:blue" href="https://doc.zthysms.com/Public/Uploads/2021-04-27/608774d492ce7.docx">模板下载</a></div>
                                            </div>
                                        </div>
                                    </div>
                                </el-form-item>
                                <div v-loading="loading2 ">
                                    <el-form-item label="代理人姓名" label-width="160px" prop="name">
                                        <el-input v-model="formInline.name" placeholder="" class="input-w"></el-input>
                                    </el-form-item>
                                    <el-form-item label="代理人身份证号码" label-width="160px" prop="idCard">
                                        <el-input v-model="formInline.idCard" placeholder="" class="input-w"></el-input>
                                    </el-form-item>
                                    <el-form-item label="代理人手机号" label-width="160px" prop="phone">
                                        <el-input v-model="formInline.phone" placeholder="" class="input-w"></el-input>
                                    </el-form-item>
                                </div>
                                
                            </el-form>
                        </div>
        </div>
         <div v-else-if="active==3">
                <div class="word_g">
                    <Word/>
                    <div>
                        <el-checkbox v-model="checked">我已阅读并同意</el-checkbox>
                    </div>
                    <div class="word_btn">
                         <el-button v-if="active!=1 && active!=2 && active!=4"  type="primary" style="margin-top: 12px;width:200px" @click="send">提交</el-button>
                    </div>
                   
                </div>
        </div>
        <div v-else class="IMG">
                <div class="imd_m">
                    <img class="img_wc" src="../../../../../assets/images/RZXX.png" alt="">
                </div>
                <div class="imd_t">
                    认证信息已提交
                </div>
        </div>
        <div class="btn_F">
           <div class="btn_lz">
               <el-button v-if="active!=1 && active!=4" style="margin-top: 12px;width:100px" @click="nexts">上一步</el-button>
           </div>
           <div class="btn_Z">
                <el-button v-if="active!=4 && active!=3"  type="primary" style="margin-top: 12px;width:100px" @click="next">下一步</el-button>
           </div>
           <div class="btn_SZ">
               <el-button v-if="active==4"  type="primary"  class="btnc" style="margin-top: 12px;width:150px" @click="success">完成</el-button>
           </div>
       </div>
        <el-dialog :visible.sync="dialogVisible">
                    <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
    </div>
</template>

<script>
  import Word from './word'
export default {
    components: {Word},
    name:"BusinessAgent",
    data() {
        var phone = (rule, value, callback) => {
        if (value) {
          if (!/^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/.test(value)) {
            return callback(new Error("请输入正确的手机号"));
          } else {
            callback();
          }
        } else {
          return callback(new Error("请输入个人手机号"));
        }
      };
      return {
        active: 1,
        loading2:false,
        checked:false,
        dialogImageUrl: '',
        action: this.API.cpus + "v3/file/upload",
        fileType:"",
        PhotoFront: [],
        PhotoReverse: [],
        PhotoLege:[],
        authorizations:[],
        dialogVisible: false,
        formInline:{
            authorization:"",//委托授权书
            businessLicense:"", //营业执照
            compName:"", //公司名称
            corporateIdCard:"", //企业法人身份证号
            corporateName:"", //企业法人姓名或个体工商户
            idCard:"", //身份证
            idCardBack:"", //身份证反面照
            idCardFront:"", //身份证正面照
            name:"", //个人姓名
            number:"", //证件编号
            phone:"", //个人手机号
            qualificationType:"", //证件类型
            type:''
            
        },
        formRule:{
            businessLicense:[
                { required: true, message: '请上传营业执照', trigger: 'change' },
            ],
            authorization:[
                { required: true, message: '请上传委托授权书', trigger: 'change' },
            ],
            idCardBack:[
                { required: true, message: '请上传身份证反面', trigger: 'change' },
            ],
            idCardFront:[
                { required: true, message: '请上传身份证正面', trigger: 'change' },
            ],
            compName:[
                { required: true, message: '请输入公司名', trigger: 'change' },
            ],
            corporateIdCard:[
                { required: true, message: '请输入企业法人身份证号', trigger: 'change' },
            ],
            corporateName:[
                { required: true, message: '请输入企业法人姓名或个体工商户', trigger: 'change' },
            ],
            idCard:[
                { required: true, message: '请输入身份证号码', trigger: 'change' },
            ],
            name:[
                { required: true, message: '请输入个人姓名', trigger: 'change' },
            ],
            number:[
                { required: true, message: '请输入证件编号', trigger: 'change' },
            ],
            phone:[
                { required: true, validator: phone, trigger: 'change' },
            ],
            qualificationType:[
                { required: true, message: '请选择证件类型', trigger: 'change' },
            ],
        }
        // success:"success",
        // error :"error "
        
      };
    },
    methods: {
      next() {
           this.$refs["formRefs"].validate((valid, value) => {
            if(valid){
                 if (this.active++ > 3) {
                    this.active = 4
                }
            }
           })
        
      },
      nexts() {
          this.active--
      },
      success(){
          this.$router.push(
            {path:'/authentication'}
        )
      },
    handlewqsSuccess(res, file, fileList){
        let _this = this;
      if (_this.fileType == "fd_0") {
        _this.PhotoFront = fileList;
         _this.PhotoFront[0].url = this.API.imgU+res.data.fullpath
         this.loading2 = true
         this.formInline.idCardFront = res.data.fullpath
          this.$api.get(this.API.cpus + 'consumerCertificate/idCardInfo',{filePath:res.data.fullpath},ress=>{
            if(ress.code ==200){
                this.formInline.name = ress.data.personName
                this.formInline.idCard = ress.data.personIdCard
                this.loading2 = false
              }else{
                  this.$message({
                                type: "error",
                                duration: "2000",
                                message: ress.msg
                            });
              }
          })
      } else if (_this.fileType == "fd_1") {
        _this.PhotoReverse = fileList;
        _this.PhotoReverse[0].url = this.API.imgU+res.data.fullpath
        this.formInline.idCardBack = res.data.fullpath
      }else if(_this.fileType == "fd_L"){
          _this.PhotoLege = fileList;
          this.loading2 = true
         _this.PhotoLege[0].url = this.API.imgU+res.data.fullpath
         this.formInline.businessLicense = res.data.fullpath
           this.$api.get(this.API.cpus + 'consumerCertificate/entLicenseInfo',{filePath:res.data.fullpath},ress=>{
                if(ress.code ==200){
                    this.formInline.corporateName = ress.data.corporateName
                    this.formInline.compName = ress.data.entName
                    this.formInline.number = ress.data.entQualificationNum
                    // this.formInline.qualificationType = ress.data.entQualificationType+''
                    this.loading2 = false
               }else{
                   this.$message({
                                type: "error",
                                duration: "2000",
                                message: ress.msg
                            });
               }
           })
      }else if(_this.fileType == "fd_S"){
          _this.authorizations = fileList;
          _this.authorizations[0].url = this.API.imgU+res.data.fullpath
          this.formInline.authorization = res.data.fullpath
      }
    },
    handleRemove(file, fileList) {
            let _this = this;
      if (_this.fileType == "fd_0") {
        _this.PhotoFront = [];
        this.loading2 = false
        
      } else if (_this.fileType == "fd_1") {
        _this.PhotoReverse = [];
      
      }else if(_this.fileType == "fd_L"){
          _this.PhotoLege = [];
          this.loading2 = false
        
      }else if(_this.fileType == "fd_S"){
          _this.authorizations = [];
      }
      },
      handlePictureCardPreview(file) {
        this.dialogImageUrl = file.url;
        this.dialogVisible = true;
      },
      send(){
          if(this.checked){
              this.$api.post(this.API.cpus + 'consumerCertificate',this.formInline,res=>{
                        if(res.code == 200){
                            this.$message({
                            type: "success",
                            duration: "2000",
                            message: "提交成功!",
                            });
                            this.active = 4
                        }else{
                            this.$message({
                                type: "error",
                                duration: "2000",
                                message: res.msg
                            });
                        }
                })
          }else{
              this.$message({
                                type: "error",
                                duration: "2000",
                                message: "请勾选我已阅读"
                            });
          }
          
        //    this.$refs["formRefs"].validate((valid, value) => {
        //     if(valid){
                
        //     }
        //    })
      }
    },
    created(){
        this.formInline.type = this.$route.query.type;
        this.token = {
        Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
        };

    }

}
</script>

<style scoped>
@media screen and (min-width: 1200px){
    .lege{
    background: #fff;
    height: 100%;
    padding: 20px;
}
.lege_cent{
    width: 100%;
}
.lege_tit{
    width: 100%;
    height: 50px;
    line-height: 50px;
    padding-left: 20px;
    font-weight: 800;
    font-size: 16px;
    color: #000;
    border-bottom: 1px solid #eee;
}
.lege_upload{
    width: 100%;
    padding: 20px;
}
.lege_upload_r{
    margin-left: 25px;
    line-height: 25px;
    color: #c6c6c6;
    font-size: 12px;
}
.lege_upload_b{
    margin-top:30px;
    margin-left: 10px;
    width: 95%;
    height: 50px;
    border-bottom: 1px dashed  #ccc;
}
.demo-form-inline .el-form-item{
    margin-right: 50px;
}
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
.avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .word_g{
      height: 650px;
      margin-left: 400px;
      overflow: auto;
  }
  .word_btn{
      margin-left: 250px;
  }
  .btn_F{
      position: relative;
  }
  .btn_Z{
      position: absolute;
      right: 150px;
  }
  .btn_lz{
      position: absolute;
      left: 50px;
  }
  .btn_SZ{
      position: absolute;
      right:10%;
      /* margin-right: 100px; */
      margin-top: 500px;
  }
  .IMG{
      position: relative;
  }
  .imd_m{
      position: absolute;
      left:50%;
      margin-left: -150px;
      margin-top: 100px;
  }
  .imd_t{
      position: absolute;
      left:50%;
      margin-left: -140px;
      font-size: 32px;
      font-weight: 800;
      color: #000;
      margin-top: 350px;
  }
  .from_tit{
      /* font-size: 12px;
      margin-left:10px ; */
      margin: 10px 25px;
  }
}
@media screen and (max-width: 1200px){
    .lege{
    background: #fff;
    /* height: 100%; */
    padding: 20px;
}
.lege_cent{
    width: 100%;
}
.lege_tit{
    width: 100%;
    height: 50px;
    line-height: 50px;
    padding-left: 20px;
    font-weight: 800;
    font-size: 16px;
    color: #000;
    border-bottom: 1px solid #eee;
}
.lege_upload{
    width: 100%;
    padding: 20px;
}
.lege_upload_r{
    margin-left: 25px;
    line-height: 15px;
    color: #c6c6c6;
    font-size: 12px;
}
.lege_upload_b{
    margin-top:30px;
    margin-left: 10px;
    width: 95%;
    height: 50px;
    border-bottom: 1px dashed  #ccc;
}
.demo-form-inline .el-form-item{
    margin-right: 50px;
}
.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
  }
.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
  }
.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 178px;
    height: 178px;
    line-height: 178px;
    text-align: center;
  }
.avatar {
    width: 178px;
    height: 178px;
    display: block;
  }
  .word_g{
      height: 650px;
      /* margin-left: 400px; */
      overflow: auto;
  }
  .word_btn{
      margin-left: 250px;
  }
  /* .btn_F{
      position: relative;
  }
  .btn_Z{
      position: absolute;
      right: 150px;
  }
  .btn_lz{
      position: absolute;
      left: 50px;
  }
  .btn_SZ{
      position: absolute;
      right:10%;
      margin-top: 500px;
  } */
  .IMG{
      /* position: relative; */
  }
  .imd_m{
      /* position: absolute;
      left:50%;
      margin-left: -150px; */
      /* margin-top: 100px; */
      margin-left: 120px;
  }
  .imd_t{
      margin-left: 100px;
      /* position: absolute;
      left:50%;
      margin-left: -140px;
      font-size: 32px;
      font-weight: 800;
      color: #000;
      margin-top: 350px; */
  }
  .img_wc{
      width: 50px;
      height: 50px;
  }
  .btnc{
      margin-left: 70px;
  }
  .from-t{
      margin: 10px;
  }
  .from_tit{
      font-size: 12px;
      margin-left:10px ;
  }
}
</style>
<style>
    @media screen and (max-width: 1200px){
    .el-upload--picture-card {
        width: 100px;
        height: 100px;
        line-height: 100px
    }
    .el-step__title {
        font-size: 12px;
        line-height: 38px;
    }
    .el-form--inline .el-form-item__label {
        float: none;
        display: inline;
    }
}
</style>