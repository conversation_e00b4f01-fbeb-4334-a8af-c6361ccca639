<template>
    <div class="lege bag">
        <el-steps :active="active" finish-status="success">
            <el-step title="企业认证"></el-step>
            <el-step title="个体工商户认证"></el-step>
            <el-step v-if="roleId == '22'" title="签署合同"></el-step>
            <el-step title="完成"></el-step>
        </el-steps>
        <div v-if="active == 1">

            <div class="lege_cent">
                <div class="lege_tit">
                    企业实名信息
                </div>
            </div>
            <div style="display: flex;align-items: center;margin:20px 10px">
                <div class="lege_upload">
                    <div>
                        <div style="display:flex" @click.capture="fileType = 'fd_L'">
                            <el-upload :action="action" :headers="token" :limit="1" list-type="picture-card"
                                :before-upload="beforeAvatarUpload" :on-preview="handlePictureCardPreview"
                                :on-remove="handleRemove" :on-success="handlewqsSuccess" :file-list="PhotoLege">
                                <i class="el-icon-plus"></i>
                            </el-upload>
                            <div class="lege_upload_r">
                                <div>提示：</div>
                                <div>1.证件照应清晰可见容易识别，且边框完整</div>
                                <div>2.必须真实拍摄，不能使用复印件</div>
                                <div>3.大小不超过5M</div>
                                <div>4.仅支持.jpg</div>
                            </div>
                        </div>
                        <div class="lege_upload_b">请上传营业执照照片</div>
                    </div>
                </div>
                <div style="margin-left: 20px;margin-right: 20px;border-left: 1px dashed #E6E6E6;flex: 1;">
                    <div class="from_tit">请上传营业执照照片，系统将自动读取，您可更正读取内容</div>
                    <div>
                        <el-form v-loading="loading2" :rules="formRule" ref="formRefs" :model="formInline"
                            class="demo-form-inline" key="one_1">
                            <el-form-item label="企业名称" label-width="165px" prop="compName">
                                <el-input v-model="formInline.compName" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="企业资质编号类型" label-width="165px" prop="qualificationType">
                                <el-select style="width: 100%" v-model="formInline.qualificationType" placeholder="请选择">
                                    <el-option label="企业营业执照统一社会信用代码" value="1">
                                    </el-option>
                                    <!-- <el-option label="企业营业执照注册号" value="2">
                                </el-option>
                                <el-option label="个体工商户营业执照统一社会信用代码 " value="3">
                                </el-option>
                                <el-option label="个体工商户营业执照注册号" value="4"> -->
                                    <!-- </el-option> -->
                                </el-select>
                            </el-form-item>
                            <el-form-item label="企业资质编号" label-width="165px" prop="number">
                                <el-input v-model="formInline.number" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="企业法人姓名" label-width="165px" prop="corporateName">
                                <el-input v-model="formInline.corporateName" placeholder=""></el-input>
                            </el-form-item>
                            <!-- <el-form-item label="企业法人身份证号码" label-width="165px" prop="corporateIdCard">
                            <el-input v-model="formInline.corporateIdCard" placeholder="" class="input-w"></el-input>
                        </el-form-item> -->
                        </el-form>
                    </div>
                </div>
            </div>

        </div>
        <div v-else-if="active == 2">
            <div class="lege_tit">
                个体工商户实名信息
            </div>
            <div>
                <el-form :rules="formRule" ref="formRefs" :model="formInline" class="demo-form-inline" key="two_2">
                    <div style="display: flex;margin:20px 10px">
                        <div>
                            <el-form-item label="身份证正面" label-width="120px" prop="idCardFront">
                                <div class="lege_upload">
                                    <div style="display:flex" @click.capture="fileType = 'fd_0'">
                                        <el-upload :action="action" :headers="token" :limit="1" list-type="picture-card"
                                            :before-upload="beforeAvatarUpload" :on-preview="handlePictureCardPreview"
                                            :on-remove="handleRemove" :on-success="handlewqsSuccess"
                                            :file-list="PhotoFront">
                                            <i class="el-icon-plus"></i>
                                            <div slot="tip" class="el-upload__tip">
                                                <div>证件照应清晰可见容易识别，且边框完整;</div>
                                                <div>必须真实拍摄，不能使用复印件;大小不超过5M;仅支持.jpg</div>
                                            </div>
                                        </el-upload>
                                    </div>
                                </div>
                            </el-form-item>
                            <el-form-item label="身份证反面" label-width="120px" prop="idCardBack">
                                <div class="lege_upload">
                                    <div style="display:flex" @click.capture="fileType = 'fd_1'">
                                        <el-upload :action="action" :headers="token" :limit="1" list-type="picture-card"
                                            :before-upload="beforeAvatarUpload" :on-preview="handlePictureCardPreview"
                                            :on-remove="handleRemove" :on-success="handlewqsSuccess"
                                            :file-list="PhotoReverse">
                                            <i class="el-icon-plus"></i>
                                        </el-upload>
                                        <!-- <div class="lege_upload_r">
                                    <div>提示：</div>
                                    <div>1.证件照应清晰可见容易识别，且边框完整</div>
                                    <div>2.必须真实拍摄，不能使用复印件</div>
                                    <div>3.大小不超过10M</div>
                                    <div>4.仅支持.png、.jpg、.jpeg</div>
                                </div> -->
                                    </div>
                                </div>
                            </el-form-item>
                            <el-form-item label="办公照片" label-width="120px" prop="officePhoto">
                                <div class="lege_upload">
                                    <div style="display:flex" @click.capture="fileType = 'fd_R'">
                                        <!-- :data="{ clarity: true }" -->
                                        <el-upload :action="action" :headers="token" :limit="1" list-type="picture-card"
                                            :before-upload="beforeAvatarUpload" :on-preview="handlePictureCardPreview"
                                            :on-remove="handleRemove" :on-success="handlewqsSuccess"
                                            :file-list="officeFile">
                                            <i class="el-icon-plus"></i>
                                            <div slot="tip" class="el-upload__tip">
                                                办公照片大小不超过5M;仅支持.jpg
                                            </div>
                                        </el-upload>
                                    </div>
                                </div>
                            </el-form-item>
                        </div>
                        <div style="margin-left: 20px;margin-right: 20px;border-left: 1px dashed #E6E6E6;flex: 1;"
                            v-loading="loading2">
                            <el-form-item label="姓名" label-width="120px" prop="name">
                                <el-input v-model="formInline.name" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="身份证号码" label-width="120px" prop="idCard">
                                <el-input v-model="formInline.idCard" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="手机号" label-width="120px" prop="phone">
                                <el-input v-model="formInline.phone" placeholder=""></el-input>
                            </el-form-item>
                            <el-form-item label="验证码" label-width="120px" prop="captcha">
                                <div style="display: flex;">
                                    <el-input v-model="formInline.captcha" placeholder=""></el-input>
                                    <el-button v-if="nmb == 60" type="primary"
                                        @click="sendCode(formInline.phone)">获取验证码</el-button>
                                    <el-button v-else disabled type="primary">重新获取{{ nmb }}</el-button>
                                </div>
                            </el-form-item>
                            <div style="float: right;margin-right: 45px;">
                                <el-button v-if="active == 2" style="margin-top: 12px;width:100px"
                                    @click="nexts">上一步</el-button>
                                <el-button v-if="active == 2" type="primary" style="margin-top: 12px;width:100px"
                                    @click="nextTow">{{ roleId == '22' ? '下一步' : '提交' }}</el-button>
                            </div>
                        </div>
                    </div>

                </el-form>
            </div>
        </div>
        <div v-else-if="active == 3">
            <div class="word_g">
                <Word />
                <div>
                    <el-checkbox v-model="checked">我已阅读并同意</el-checkbox>
                </div>
                <div class="word_btn">
                    <el-button v-if="active != 1 && active != 2 && active != 4" type="primary"
                        style="margin-top: 12px;width:200px" @click="send">提交</el-button>
                </div>

            </div>
        </div>
        <div v-else class="IMG">
            <div class="imd_m">
                <img class="img_wc" src="../../../../../assets/images/RZXX.png" alt="">
            </div>
            <div class="imd_t">
                认证信息已提交
            </div>
        </div>
        <div class="btn_F">
            <div class="btn_lz">
                <el-button v-if="active == 3" style="margin-top: 12px;width:100px" @click="nexts">上一步</el-button>
            </div>
            <div class="btn_Z">
                <el-button v-if="active == 1" type="primary" style="margin-top: 12px;width:100px"
                    @click="next">下一步</el-button>
            </div>
            <!-- <div class="btn_Z">
                <el-button v-if="active == 2" type="primary" style="margin-top: 12px;width:100px"
                    @click="nextTow">下一步</el-button>
            </div> -->
            <div class="btn_SZ">
                <el-button v-if="active == 4" class="btnc" type="primary" style="margin-top: 12px;width:150px"
                    @click="success">完成</el-button>
            </div>
        </div>
        <el-dialog :visible.sync="dialogVisible">
            <img width="100%" :src="dialogImageUrl" alt="">
        </el-dialog>
    </div>
</template>

<script>
import Word from './word'
export default {
    components: { Word },
    name:"Individual",
    data() {
        var phone = (rule, value, callback) => {
            if (value) {
                if (!/^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/.test(value)) {
                    return callback(new Error("请输入正确的手机号"));
                } else {
                    callback();
                }
            } else {
                return callback(new Error("请输入个人手机号"));
            }
        };
        var verify = (rule, value, callback) => {
            if (value) {
                callback();
            } else {
                return callback(new Error(rule.text || "请输入个人手机号"));
            }
        };
        return {
            active: 1,
            roleId: "",
            loading2: false,
            checked: false,
            dialogImageUrl: '',
            action: this.API.cpus + "v3/file/upload",
            fileType: "",
            PhotoFront: [],
            PhotoReverse: [],
            PhotoLege: [],
            authorizations: [],
            officeFile: [],
            dialogVisible: false,
            nmb: 60,
            formInline: {
                authorization: "",//委托授权书
                businessLicense: "", //营业执照
                officePhoto: "", //办公照片
                compName: "", //公司名称
                corporateIdCard: "", //企业法人身份证号
                corporateName: "", //企业法人姓名或个体工商户
                idCard: "", //身份证
                idCardBack: "", //身份证反面照
                idCardFront: "", //身份证正面照
                name: "", //个人姓名
                number: "", //证件编号
                phone: "", //个人手机号
                qualificationType: "1", //证件类型
                type: '',
                // serialNo: "",//流水号
                captcha: ""//验证码

            },
            formRule: {
                businessLicense: [
                    {
                        required: true,
                        validator: verify,
                        text: '请上传营业执照',
                        trigger: 'change'
                    },
                ],
                authorization: [
                    {
                        required: true,
                        validator: verify,
                        text: '请上传委托授权书',
                        trigger: 'change'
                    },
                ],
                idCardBack: [
                    {
                        required: true,
                        validator: verify,
                        text: '请上传身份证反面',
                        trigger: 'change'
                    },
                ],
                idCardFront: [
                    {
                        required: true,
                        validator: verify,
                        text: '请上传身份证正面',
                        trigger: 'change'
                    },
                ],
                officePhoto: [
                    {
                        required: true,
                        validator: verify,
                        text: '请上传办公照片',
                        trigger: 'blur'
                    },
                ],
                compName: [
                    {
                        required: true,
                        validator: verify,
                        text: '请输入公司名',
                        trigger: 'blur'
                    },
                ],
                corporateIdCard: [
                    {
                        required: true,
                        validator: verify,
                        text: '请输入企业法人身份证号',
                        trigger: 'blur'
                    },
                ],
                corporateName: [
                    {
                        required: true,
                        validator: verify,
                        text: '请输入企业法人姓名或个体工商户',
                        trigger: 'blur'
                    },
                ],
                idCard: [
                    {
                        required: true,
                        validator: verify,
                        text: '请输入身份证号码',
                        trigger: 'blur'
                    },
                ],
                name: [
                    {
                        required: true,
                        validator: verify,
                        text: '请输入个人姓名',
                        trigger: 'blur'
                    },
                ],
                number: [
                    {
                        required: true,
                        validator: verify,
                        text: '请输入证件编号',
                        trigger: 'blur'
                    },
                ],
                phone: [
                    {
                        required: true,
                        validator: phone,
                        trigger: 'blur'
                    },
                ],
                captcha: [
                    {
                        required: true,
                        validator: verify,
                        text: '验证码不能为空',
                        trigger: 'blur'
                    },
                ],
                qualificationType: [
                    {
                        required: true,
                        validator: verify,
                        text: '请选择证件类型',
                        trigger: 'change'
                    },
                ],
            }
            // success:"success",
            // error :"error "

        };
    },
    methods: {
        next() {

            this.$refs["formRefs"].validate((valid, value) => {
                if (valid) {
                    this.active++
                }
            })

        },
        nextTow() {
            let data = {
                captcha: this.formInline.captcha
            }
            this.$api.post(this.API.cpus + 'consumerCertificate', data, res => {
                if (res.code == 200) {
                    this.$message({
                        type: "success",
                        duration: "2000",
                        message: "提交成功!",
                    });
                    if (this.roleId == '22') {
                        this.active++
                    } else {
                        this.active = 4
                    }
                } else {
                    this.$message({
                        type: "error",
                        duration: "2000",
                        message: res.msg
                    });
                }
            })
        },
        nexts() {
            this.active--
        },
        success() {
            this.$router.push(
                { path: '/authentication' }
            )
        },
         beforeAvatarUpload(file) {
            // const istype = file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/jpg'
            const siJPGGIF = file.name.split(".")[1]
            const isLt5M = file.size / 1024 / 1024 < 5; //单位MB
            // const isSize = await new Promise(function (resolve, reject) {
            //     let width = 980
            //     let height = 1180
            //     let _URL = window.URL || window.webkitURL
            //     let image = new Image()

            //     image.onload = function () {
            //         const valid =
            //             image.width <= width &&
            //             image.height <= height &&
            //             image.width / image.height == 1
            //         valid ? resolve() : reject()
            //     }
            //     image.src = _URL.createObjectURL(file)
            // }).then(
            //     () => {
            //         return file
            //     },
            //     () => {
            //         this.$message.error(
            //             '最大分辨率不超过980*1180'
            //         )
            //         return Promise.reject(false)
            //     }
            // )
            if (siJPGGIF !== "jpg") {
                this.$message.error('上传图片只能是 jpg格式!')
                return false
            }
            if (!isLt5M) {
                this.$message.error(
                    '上传图片大小不能超过 10MB!'
                )
                return false
            }
            // return istype && isLt5M
        },
        handlewqsSuccess(res, file, fileList) {
            let _this = this;
            if (res.code == 200) {
                if (_this.fileType == "fd_0") {
                    // _this.PhotoFront = fileList;
                    // _this.PhotoFront[0].url = this.API.imgU + res.data.fullpath
                    this.loading2 = true
                    this.formInline.idCardFront = res.data.fullpath
                    this.$api.get(this.API.cpus + 'consumerCertificate/idCardInfo', { filePath: res.data.fullpath }, ress => {
                        if (ress.code == 200) {
                            this.formInline.name = ress.data.personName
                            this.formInline.idCard = ress.data.personIdCard
                            this.loading2 = false
                        } else {
                            this.$message({
                                type: "error",
                                duration: "2000",
                                message: ress.msg
                            });
                            this.loading2 = false
                        }
                    })
                } else if (_this.fileType == "fd_1") {
                    // _this.PhotoReverse = fileList;
                    // _this.PhotoReverse[0].url = this.API.imgU + res.data.fullpath
                    this.formInline.idCardBack = res.data.fullpath
                } else if (_this.fileType == "fd_L") {
                    // _this.PhotoLege = fileList;
                    this.loading2 = true
                    // _this.PhotoLege[0].url = this.API.imgU + res.data.fullpath
                    this.formInline.businessLicense = res.data.fullpath
                    this.$api.get(this.API.cpus + 'consumerCertificate/entLicenseInfo', { filePath: res.data.fullpath }, ress => {
                        if (ress.code == 200) {
                            this.formInline.corporateName = ress.data.corporateName
                            this.formInline.compName = ress.data.entName
                            this.formInline.number = ress.data.entQualificationNum
                            // this.formInline.qualificationType = ress.data.entQualificationType+''
                            this.loading2 = false
                        } else {
                            this.$message({
                                type: "error",
                                duration: "2000",
                                message: ress.msg
                            });
                            this.loading2 = false
                        }
                    })
                } else if (_this.fileType == "fd_S") {
                    // _this.authorizations = fileList;
                    // _this.authorizations[0].url = this.API.imgU + res.data.fullpath
                    this.formInline.authorization = res.data.fullpath
                } else if (_this.fileType == "fd_R") {
                    // _this.officeFile = fileList;
                    // _this.officeFile[0].url = this.API.imgU + res.data.fullpath
                    this.formInline.officePhoto = res.data.fullpath
                }
            } else {
                this.$message({
                    type: "error",
                    duration: "2000",
                    message: res.msg
                });
                this.loading2 = false
            }

        },
        handleRemove(file, fileList) {
            let _this = this;
            if (_this.fileType == "fd_0") {
                _this.PhotoFront = [];
                this.formInline.idCardFront = ''
                this.loading2 = false

            } else if (_this.fileType == "fd_1") {
                _this.PhotoReverse = [];
                this.formInline.idCardBack = ''

            } else if (_this.fileType == "fd_L") {
                _this.PhotoLege = [];
                this.formInline.businessLicense = ''
                this.loading2 = false

            } else if (_this.fileType == "fd_S") {
                _this.authorizations = [];
            } else if (_this.fileType == "fd_R") {
                _this.officeFile = [];
                this.formInline.officePhoto = ''
            }
        },
        handlePictureCardPreview(file) {
            this.dialogImageUrl = file.url;
            this.dialogVisible = true;
        },
        sendCode(val) {
            if (val && /^1[3|4|5|6|7|8|9][0-9]\d{8}$/.test(val)) {
                // let data = {
                //     compName: this.formInline.compName,
                //     corporateIdCard: this.formInline.corporateIdCard,
                //     corporateName: this.formInline.corporateName,
                //     number: this.formInline.number,
                //     phone: this.formInline.phone,
                // }
                this.formInline.corporateIdCard = this.formInline.idCard
                // this.formInline.corporateName = this.formInline.name
                this.$api.post(this.API.cpus + 'consumerCertificate/verify', this.formInline, res => {
                    this.$message({
                        type: "success",
                        duration: "2000",
                        message: res.msg
                    });
                    // this.formInline.serialNo = res.data.serialNo
                    if (res.code == 200) {
                        --this.nmb;
                        const timer = setInterval((res) => {
                            --this.nmb;
                            if (this.nmb < 1) {
                                this.nmb = 60;
                                clearInterval(timer);
                            }
                        }, 1000);
                    } else {
                        this.$message({
                            type: "error",
                            duration: "2000",
                            message: res.msg
                        });
                    }
                })
            } else {
                this.$message({
                    message: "请输入正确用手机号",
                    type: "warning",
                });
            }
        },
        send() {
            //   console.log(111);
            if (this.checked) {
                this.$api.post(this.API.cpus + 'consumerCertificate/signContract', {}, res => {
                    if (res.code == 200) {
                        this.$message({
                            type: "success",
                            duration: "2000",
                            message: res.msg,
                        });
                        this.active = 4
                    } else {
                        this.$message({
                            type: "error",
                            duration: "2000",
                            message: res.msg
                        });
                    }
                })
            } else {
                this.$message({
                    type: "error",
                    duration: "2000",
                    message: "请勾选我已阅读"
                });
            }

            //    this.$refs["formRefs"].validate((valid, value) => {
            //     if(valid){

            //     }
            //    })
        }
    },
    created() {
        let data = JSON.parse(localStorage.getItem("userInfo"))
        this.roleId = data.roleId
        this.formInline.type = this.$route.query.type;
        if (this.$route.query.status == '1') {
            this.active = 3
        }
        this.token = {
            Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
        };

    }

}
</script>

<style scoped>
@media screen and (min-width: 1200px) {
    .lege {
        background: #fff;
        min-height: 100%;
        padding: 20px;
    }

    .lege_cent {
        width: 100%;
    }

    .lege_tit {
        width: 100%;
        height: 50px;
        line-height: 50px;
        padding-left: 20px;
        font-weight: 800;
        font-size: 16px;
        color: #000;
        /* border-bottom: 1px solid #eee; */
    }

    .lege_upload {
        /* width: 100%; */
        padding: 20px;
    }

    .lege_upload_r {
        margin-left: 25px;
        line-height: 25px;
        color: #c6c6c6;
        font-size: 12px;
    }

    .lege_upload_b {
        margin-top: 30px;
        margin-left: 10px;
        width: 95%;
        height: 50px;
        /* border-bottom: 1px dashed #ccc; */
    }

    .demo-form-inline .el-form-item {
        margin-right: 50px;
    }

    .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .avatar-uploader .el-upload:hover {
        border-color: #409EFF;
    }

    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 178px;
        height: 178px;
        line-height: 178px;
        text-align: center;
    }

    .avatar {
        width: 178px;
        height: 178px;
        display: block;
    }

    .word_g {
        height: 650px;
        margin-left: 400px;
        overflow: auto;
    }

    .word_btn {
        margin-left: 250px;
    }

    .btn_F {
        position: relative;
    }

    .btn_Z {
        position: absolute;
        right: 150px;
    }

    .btn_lz {
        position: absolute;
        left: 50px;
    }

    .btn_SZ {
        position: absolute;
        right: 10%;
        /* margin-right: 100px; */
        margin-top: 500px;
    }

    .IMG {
        position: relative;
    }

    .imd_m {
        position: absolute;
        left: 50%;
        margin-left: -150px;
        margin-top: 100px;
    }

    .imd_t {
        position: absolute;
        left: 50%;
        margin-left: -140px;
        font-size: 32px;
        font-weight: 800;
        color: #000;
        margin-top: 350px;
    }

    .from_tit {
        /* font-size: 12px;
      margin-left:10px ; */
        margin: 10px 25px;
    }
}

@media screen and (max-width: 1200px) {
    .lege {
        background: #fff;
        min-height: 100%;
        /* height: 100%; */
        padding: 20px;
    }

    .lege_cent {
        width: 100%;
    }

    .lege_tit {
        width: 100%;
        height: 50px;
        line-height: 50px;
        padding-left: 20px;
        font-weight: 800;
        font-size: 16px;
        color: #000;
        /* border-bottom: 1px solid #eee; */
    }

    .lege_upload {
        /* width: 100%; */
        padding: 20px;
    }

    .lege_upload_r {
        margin-left: 25px;
        line-height: 15px;
        color: #c6c6c6;
        font-size: 12px;
    }

    .lege_upload_b {
        margin-top: 30px;
        margin-left: 10px;
        width: 95%;
        height: 50px;
        /* border-bottom: 1px dashed #ccc; */
    }

    .demo-form-inline .el-form-item {
        margin-right: 50px;
    }

    .avatar-uploader .el-upload {
        border: 1px dashed #d9d9d9;
        border-radius: 6px;
        cursor: pointer;
        position: relative;
        overflow: hidden;
    }

    .avatar-uploader .el-upload:hover {
        border-color: #409EFF;
    }

    .avatar-uploader-icon {
        font-size: 28px;
        color: #8c939d;
        width: 178px;
        height: 178px;
        line-height: 178px;
        text-align: center;
    }

    .avatar {
        width: 178px;
        height: 178px;
        display: block;
    }

    .word_g {
        height: 650px;
        /* margin-left: 400px; */
        overflow: auto;
    }

    .word_btn {
        margin-left: 250px;
    }

    /* .btn_F{
      position: relative;
  }
  .btn_Z{
      position: absolute;
      right: 150px;
  }
  .btn_lz{
      position: absolute;
      left: 50px;
  }
  .btn_SZ{
      position: absolute;
      right:10%;
      margin-top: 500px;
  } */
    .IMG {
        /* position: relative; */
    }

    .imd_m {
        /* position: absolute;
      left:50%;
      margin-left: -150px; */
        /* margin-top: 100px; */
        margin-left: 120px;
    }

    .imd_t {
        margin-left: 100px;
        /* position: absolute;
      left:50%;
      margin-left: -140px;
      font-size: 32px;
      font-weight: 800;
      color: #000;
      margin-top: 350px; */
    }

    .img_wc {
        width: 50px;
        height: 50px;
    }

    .btnc {
        margin-left: 70px;
    }

    .from-t {
        margin: 10px;
    }

    .from_tit {
        font-size: 12px;
        margin-left: 10px;
    }
}
</style>
<style>
@media screen and (max-width: 1200px) {
    .el-upload--picture-card {
        width: 100px;
        height: 100px;
        line-height: 100px
    }

    .el-step__title {
        font-size: 12px;
        line-height: 38px;
    }

    .el-form--inline .el-form-item__label {
        float: none;
        display: inline;
    }
}
</style>