<template>
    <div class="bag">
        <div class="Top_title" style="margin:10px 0px;padding:10px;">个人信息设置</div>
        <div class="fillet PersonalInformation-main">
            <header>基本信息</header>
            <div class="basicInfo">
                <div class="basicInfo-right">
                    <div class="basicInfo-line">
                        <span class="basicInfo-title">用户名：</span>
                        <span class="user-name">{{ consumerName }}</span>
                    </div>
                    <div class="basicInfo-line">
                        <span class="basicInfo-title">公司名称：</span>
                        <span class="comp-name">{{ compName }}</span>
                    </div>
                    <div class="basicInfo-line">
                        <span class="basicInfo-title">创建时间：</span>
                        <span class="create-time">{{ createTime }}</span>
                    </div>
                    <!-- <div class="basicInfo-line">
                        <span class="basicInfo-title">登录密码：</span>
                        <span class="create-time">******</span>
                        <span class="changePassword" style="margin-left: 15px;" @click="addphone">修改登录密码</span>
                    </div> -->
                    <div v-if="roleId != '12'" class="basicInfo-line">
                        <span class="basicInfo-title">接口密码：</span>
                        <span class="create-time" v-if="ispwd == false" style="display:inline-block;">******</span>
                        <span class="create-time" v-if="ispwd == true" style="display:inline-block;">{{ password }}</span>
                        <!-- <span class="changePassword" :disabled="loginInfo.isAdmin == 1 ? false : true"
                            style="margin-left: 15px;" @click="modifyPsd('pwd')">修改接口密码</span> -->
                        <el-button :disabled="loginInfo.isAdmin == 1 ? false : true"
                            style="margin-left: 15px;font-size: 14px;" type="text"
                            @click="modifyPsd('pwd')">修改接口密码</el-button>
                        <el-tooltip class="item" effect="dark" content="修改接口密码请联系管理员！" placement="top">
                            <i v-if="loginInfo.isAdmin != 1" style="color: #409eff;" class="el-icon-question"></i>
                        </el-tooltip>
                        <div class="basic-cfg-tips">接口密码是用来校验短信发送请求合法性的密码，与用户名对应，需要业务方高度保密，切勿把密码存储在客户端。</div>
                    </div>
                    <div class="basicInfo-line" v-if="roleId != '12' && cipherMode == 2">
                        <span class="basicInfo-title">加密盐：</span>
                        <span class="create-time">******</span>
                        <!-- <span class="changePassword" style="margin-left: 15px;" @click="editSalt('salt')">修改加密盐</span> -->
                        <el-button :disabled="loginInfo.isAdmin == 1 ? false : true"
                            style="margin-left: 15px;font-size: 14px;" type="text"
                            @click="editSalt('salt')">修改加密盐</el-button>
                        <el-tooltip class="item" effect="dark" content="修改加密盐请联系管理员！" placement="top">
                            <i v-if="loginInfo.isAdmin != 1" style="color: #409eff;" class="el-icon-question"></i>
                        </el-tooltip>
                    </div>
                </div>
            </div>
        </div>
        <!-- <div v-if="isIndividualization==1" class="fillet PersonalInformation-main">
            <header>个性化设置</header>
            <div class="basicInfo">
                <el-button type="primary" @click="LogoSetting">个性化查看</el-button>
            </div>
        </div> -->
        <!-- 修改密码 -->
        <!-- <el-dialog
            title="修改密码"
            :visible.sync="changePassword"
            :close-on-click-modal=false
            width="420px"
            >
            <el-form :inline="true" :model="formPassword" :rules="formRules" ref="formRule" class="demo-form-inline IPForm">
                <el-form-item label="原密码" label-width="80px" prop="oldPassword">
                    <el-input type="password" v-model="formPassword.oldPassword" placeholder="请输入原密码进行身份验证" class="input-w"></el-input>
                </el-form-item>
                <el-form-item label="新密码" label-width="80px" prop="newPassword">
                    <el-input type="password" v-model="formPassword.newPassword" placeholder="包含字母大小写和数字的8-16位的密码" class="input-w"></el-input>
                </el-form-item>
                <el-form-item label="确认密码" label-width="80px" prop="passWord">
                    <el-input type="password" v-model="formPassword.passWord" placeholder="请确认新密码" class="input-w"></el-input>
                </el-form-item>
            </el-form>
            <span slot="footer" class="dialog-footer">
                <el-button @click="changePassword = false">取 消</el-button>
                <el-button type="primary" @click="changePwd('formRule')">确 定</el-button>
            </span>
        </el-dialog> -->
        <!-- 个性化设置 -->
        <el-dialog title="个性化设置" :visible.sync="Personalization" :close-on-click-modal=false width="500px">
            <el-form :inline="true" :model="formLogo" :rules="formLogoRules" ref="formLogos"
                class="demo-form-inline IPForm">
                <el-form-item label="域名" label-width="100px" prop="domainName">
                    <el-input type="text" v-model="formLogo.domainName" disabled placeholder=" 请输入域名"
                        class="input-w"></el-input>
                </el-form-item>
                <el-form-item label="登录提示语" label-width="100px" prop="reminder">
                    <el-input type="text" v-model="formLogo.reminder" disabled placeholder=" 请输入登录页面提示语"
                        class="input-w"></el-input>
                </el-form-item>
                <el-form-item label="短信签名" label-width="100px" prop="signature">
                    <el-input type="text" v-model="formLogo.signature" disabled placeholder=" 请输入接收短信登录验证码的签名"
                        class="input-w"></el-input>
                </el-form-item>
                <el-form-item label="登录title" label-width="100px" prop="title">
                    <el-input type="text" v-model="formLogo.title" disabled placeholder=" 请输入登录后title"
                        class="input-w"></el-input>
                </el-form-item>
                <el-form-item label="备案信息" label-width="100px" prop="recordInformation">
                    <el-input type="text" v-model="formLogo.recordInformation" disabled placeholder=" 请输入备案信息"
                        class="input-w"></el-input>
                </el-form-item>
                <el-form-item label="企业icon" label-width="100px" prop="">
                    <el-upload class="upload-demo" :action="actionHttp1" :headers="token" :limit="1"
                        :on-preview="handlePreview" :on-remove="handleRemove" :file-list="fileList"
                        :on-success="handleAvatarSuccess1" :before-upload="beforeAvatarUpload1" :disabled="true"
                        list-type="picture">
                        <!-- <el-button size="small" type="primary">点击上传</el-button>
                        <div slot="tip" class="el-upload__tip">只能上传.ico格式</div> -->
                    </el-upload>
                </el-form-item>
                <el-form-item label="企业logo" label-width="100px" prop="">
                    <el-upload class="upload-demo" :action="actionHttp1" :headers="token" :limit="1"
                        :on-preview="handlePreview1" :on-remove="handleRemove1" :file-list="fileList1"
                        :on-success="handleAvatarSuccess2" :before-upload="beforeAvatarUpload2" :disabled="true"
                        list-type="picture">
                        <!-- <el-button size="small" type="primary">点击上传</el-button>
                        <div slot="tip" class="el-upload__tip">png格式照片，大小不超过1M</div> -->
                    </el-upload>
                </el-form-item>
            </el-form>
        </el-dialog>

        <!-- 接口信息 -->
        <!-- <div class="fillet bas-block">
            <div class="basic-cfg-title">接口信息</div>
            <div class="basic-cfg-cot">
                <span class="basic-cfg-tit">用户名</span>
                <span>{{userName}}</span>
                <div class="basic-cfg-tips">用户名是短信应用的唯一标识，调用短信API接口时需要提供该参数</div>
            </div>
            <div class="basic-cfg-cot">
                <span class="basic-cfg-tit">接口密码 <i class="el-icon-warning"></i></span>
                <span class="create-time" v-if="ispwd == false" style="display:inline-block;">******</span>
                <span class="create-time" v-if="ispwd == true" style="display:inline-block;">{{password}}</span>
                <span class="appkeyShow" @click="ispeds()">显示</span>
                <span class="appkeyShow" @click="modifyPsd()">修改</span> 
                <div class="basic-cfg-tips">接口密码是用来校验短信发送请求合法性的密码，与用户名对应，需要业务方高度保密，切勿把密码存储在客户端。</div>
            </div>
            <div class="basic-cfg-cot" style="margin-bottom:0px;">
                <span class="basic-cfg-tit">创建时间</span>
                <span>{{createLocalDateTime}}</span>
            </div>
        </div> -->
        <div v-if="roleId != '12'">
            <div class="fillet bas-block" style="height:230px;">
                <div style="display: inline-block;width: 600px;float: left;">
                    <div class="basic-cfg-title">短信余额提醒</div>
                    <el-button type="primary" class="set-balance-tips" @click="handleRequencyEnable"
                        v-if="requencyEnable == false">启用</el-button>
                    <div v-if="requencyEnable == true">
                        <transition name="fade">
                            <div class="tips-box" v-show="sendfrequency">
                                <div style="padding-bottom:15px;">当前账号余额条数 在不足 <span class="bac-color-red">
                                        {{ reminderBalances }} </span> 条时提醒</div>
                                <span style="color:red;font-size:12px;"> 注：请去添加告警联系人，以便正常接收余额不足通知！ </span>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="set-balance-tips" @click="SetBalance">设置</el-button>
                                    <el-button type="primary" class="set-balance-tips"
                                        @click="shutDownSetBalance">关闭设置</el-button>
                                </el-row>
                            </div>
                        </transition>
                        <transition name="fade">
                            <div class="tips-box" v-show="!sendfrequency">
                                <div style="padding-bottom:15px;">当前账号余额条数 在不足 <el-input style="width:180px;"
                                        v-model="NumberBalances"></el-input> 条时提醒</div>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="sure-balance-tips" @click="determine">确定</el-button>
                                    <el-button type="primary" plain class="cancel-balance-tips"
                                        @click="sendfrequency = true">取消</el-button>
                                </el-row>
                            </div>
                        </transition>
                    </div>
                </div>
                <div style="display: inline-block;width: 600px;float: left;">
                    <div class="basic-cfg-title">彩信余额提醒</div>
                    <el-button type="primary" class="set-balance-tips" @click="handleRequencyEnable1"
                        v-if="requencyEnable1 == false">启用</el-button>
                    <div v-if="requencyEnable1 == true">
                        <transition name="fade">
                            <div class="tips-box" v-show="sendfrequency1">
                                <div style="padding-bottom:15px;">当前账号余额条数 在不足 <span class="bac-color-red">
                                        {{ reminderBalances1 }} </span> 条时提醒</div>
                                <span style="color:red;font-size:12px;"> 注：请去添加告警联系人，以便正常接收余额不足通知！ </span>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="set-balance-tips" @click="SetBalance1">设置</el-button>
                                    <el-button type="primary" class="set-balance-tips"
                                        @click="shutDownSetBalance1">关闭设置</el-button>
                                </el-row>
                            </div>
                        </transition>
                        <transition name="fade">
                            <div class="tips-box" v-show="!sendfrequency1">
                                <div style="padding-bottom:15px;">当前账号余额条数 在不足 <el-input style="width:180px;"
                                        v-model="NumberBalances1"></el-input> 条时提醒</div>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="sure-balance-tips" @click="determine1">确定</el-button>
                                    <el-button type="primary" plain class="cancel-balance-tips"
                                        @click="sendfrequency1 = true">取消</el-button>
                                </el-row>
                            </div>
                        </transition>
                    </div>
                </div>

            </div>
            <div class="fillet bas-block" style="height:230px;">
                <div style="display: inline-block;width: 600px;float: left;">
                    <div class="basic-cfg-title">视频短信余额提醒</div>
                    <el-button type="primary" class="set-balance-tips" @click="handleRequencyEnable3"
                        v-if="requencyEnable3 == false">启用</el-button>
                    <div v-if="requencyEnable3 == true">
                        <transition name="fade">
                            <div class="tips-box" v-show="sendfrequency3">
                                <div style="padding-bottom:15px;">当前账号余额条数 在不足 <span class="bac-color-red">
                                        {{ reminderBalances3 }} </span> 条时提醒</div>
                                <span style="color:red;font-size:12px;"> 注：请去添加告警联系人，以便正常接收余额不足通知！ </span>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="set-balance-tips" @click="SetBalance3">设置</el-button>
                                    <el-button type="primary" class="set-balance-tips"
                                        @click="shutDownSetBalance3">关闭设置</el-button>
                                </el-row>
                            </div>
                        </transition>
                        <transition name="fade">
                            <div class="tips-box" v-show="!sendfrequency3">
                                <div style="padding-bottom:15px;">当前账号余额条数 在不足 <el-input style="width:180px;"
                                        v-model="NumberBalances3"></el-input> 条时提醒</div>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="sure-balance-tips" @click="determine3">确定</el-button>
                                    <el-button type="primary" plain class="cancel-balance-tips"
                                        @click="sendfrequency3 = true">取消</el-button>
                                </el-row>
                            </div>
                        </transition>
                    </div>
                </div>
                <div style="display: inline-block;width: 600px;float: left;">
                    <div class="basic-cfg-title">国际短信余额提醒</div>
                    <el-button type="primary" class="set-balance-tips" @click="handleRequencyEnable4"
                        v-if="requencyEnable4 == false">启用</el-button>
                    <div v-if="requencyEnable4 == true">
                        <transition name="fade">
                            <div class="tips-box" v-show="sendfrequency4">
                                <div style="padding-bottom:15px;">当前账号余额条数 在不足 <span class="bac-color-red">
                                        {{ reminderBalances4 }} </span> 元时提醒</div>
                                <span style="color:red;font-size:12px;"> 注：请去添加告警联系人，以便正常接收余额不足通知！ </span>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="set-balance-tips" @click="SetBalance4">设置</el-button>
                                    <el-button type="primary" class="set-balance-tips"
                                        @click="shutDownSetBalance4">关闭设置</el-button>
                                </el-row>
                            </div>
                        </transition>
                        <transition name="fade">
                            <div class="tips-box" v-show="!sendfrequency4">
                                <div style="padding-bottom:15px;">当前账号余额条数 在不足 <el-input style="width:180px;"
                                        v-model="NumberBalances4"></el-input> 元时提醒</div>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="sure-balance-tips" @click="determine4">确定</el-button>
                                    <el-button type="primary" plain class="cancel-balance-tips"
                                        @click="sendfrequency4 = true">取消</el-button>
                                </el-row>
                            </div>
                        </transition>
                    </div>
                </div>

            </div>
            <div class="fillet bas-block" style="height:230px;">
                <div style="display: inline-block;width: 600px;float: left;">
                    <div class="basic-cfg-title">闪验余额提醒</div>
                    <el-button type="primary" class="set-balance-tips" @click="handleRequencyEnable5"
                        v-if="requencyEnable5 == false">启用</el-button>
                    <div v-if="requencyEnable5 == true">
                        <transition name="fade">
                            <div class="tips-box" v-show="sendfrequency5">
                                <div style="padding-bottom:15px;">当前账号余额条数 在不足 <span class="bac-color-red">
                                        {{ reminderBalances5 }} </span> 条时提醒</div>
                                <span style="color:red;font-size:12px;"> 注：请去添加告警联系人，以便正常接收余额不足通知！ </span>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="set-balance-tips" @click="SetBalance5">设置</el-button>
                                    <el-button type="primary" class="set-balance-tips"
                                        @click="shutDownSetBalance5">关闭设置</el-button>
                                </el-row>
                            </div>
                        </transition>
                        <transition name="fade">
                            <div class="tips-box" v-show="!sendfrequency5">
                                <div style="padding-bottom:15px;">当前账号余额条数 在不足 <el-input style="width:180px;"
                                        v-model="NumberBalances5"></el-input> 条时提醒</div>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="sure-balance-tips" @click="determine5">确定</el-button>
                                    <el-button type="primary" plain class="cancel-balance-tips"
                                        @click="sendfrequency5 = true">取消</el-button>
                                </el-row>
                            </div>
                        </transition>
                    </div>
                </div>
                <div style="display: inline-block;width: 600px;float: left;">
                    <div class="basic-cfg-title">语音验证码余额提醒</div>
                    <el-button type="primary" class="set-balance-tips" @click="handleRequencyEnable6"
                        v-if="requencyEnable6 == false">启用</el-button>
                    <div v-if="requencyEnable6 == true">
                        <transition name="fade">
                            <div class="tips-box" v-show="sendfrequency6">
                                <div style="padding-bottom:15px;">当前账号余额条数 在不足 <span class="bac-color-red">
                                        {{ reminderBalances6 }} </span> 条时提醒</div>
                                <span style="color:red;font-size:12px;"> 注：请去添加告警联系人，以便正常接收余额不足通知！ </span>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="set-balance-tips" @click="SetBalance6">设置</el-button>
                                    <el-button type="primary" class="set-balance-tips"
                                        @click="shutDownSetBalance6">关闭设置</el-button>
                                </el-row>
                            </div>
                        </transition>
                        <transition name="fade">
                            <div class="tips-box" v-show="!sendfrequency6">
                                <div style="padding-bottom:15px;">当前账号余额条数 在不足 <el-input style="width:180px;"
                                        v-model="NumberBalances6"></el-input> 条时提醒</div>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="sure-balance-tips" @click="determine6">确定</el-button>
                                    <el-button type="primary" plain class="cancel-balance-tips"
                                        @click="sendfrequency6 = true">取消</el-button>
                                </el-row>
                            </div>
                        </transition>
                    </div>
                </div>

            </div>
            <div class="fillet bas-block" style="height:230px;">
                <div style="display: inline-block;width: 600px;float: left;">
                    <div class="basic-cfg-title">语音通知余额提醒</div>
                    <el-button type="primary" class="set-balance-tips" @click="handleRequencyEnable7"
                        v-if="requencyEnable7 == false">启用</el-button>
                    <div v-if="requencyEnable7 == true">
                        <transition name="fade">
                            <div class="tips-box" v-show="sendfrequency7">
                                <div style="padding-bottom:15px;">当前账号余额条数 在不足 <span class="bac-color-red">
                                        {{ reminderBalances7 }} </span> 条时提醒</div>
                                <span style="color:red;font-size:12px;"> 注：请去添加告警联系人，以便正常接收余额不足通知！ </span>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="set-balance-tips" @click="SetBalance7">设置</el-button>
                                    <el-button type="primary" class="set-balance-tips"
                                        @click="shutDownSetBalance7">关闭设置</el-button>
                                </el-row>
                            </div>
                        </transition>
                        <transition name="fade">
                            <div class="tips-box" v-show="!sendfrequency7">
                                <div style="padding-bottom:15px;">当前账号余额条数 在不足 <el-input style="width:180px;"
                                        v-model="NumberBalances7"></el-input> 条时提醒</div>
                                <div class="basic-help" style="margin:10px 0 20px 0;"></div>
                                <el-row>
                                    <el-button type="primary" class="sure-balance-tips" @click="determine7">确定</el-button>
                                    <el-button type="primary" plain class="cancel-balance-tips"
                                        @click="sendfrequency7 = true">取消</el-button>
                                </el-row>
                            </div>
                        </transition>
                    </div>
                </div>
            </div>
        </div>
        <el-dialog title="接口密码修改" :visible.sync="dialogFormVisible" width="520px" :close-on-click-modal="false"
            :before-close="beforec" class="dialogBasics">
            <!-- <el-steps :active="active" simple>
                <el-step title="获取手机验证码" icon="el-icon-edit"></el-step>
                <el-step title="接口密码修改" icon="el-icon-upload"></el-step>
            </el-steps>
            <div v-show="active == 1">
                <el-table :data="dialogTable" border style="width: 100%;margin-top:10px;" class="passWord_table">
                    <el-table-column align="center" type="index" label="序号" width="60"></el-table-column>
                    <el-table-column align="center" prop="phone" label="手机号"></el-table-column>
                    <el-table-column label="选择" align="center" width="60">
                        <template slot-scope="scope">
                            <el-radio :label="scope.$index" v-model="templateRadio"
                                @change.native="getTemplateRow(scope.$index, scope.row)">&nbsp;</el-radio>
                        </template>
                    </el-table-column>
                </el-table>
                <el-form :model="updateFormDialog.formData" ref="updateFormDialog" label-width="146px"
                    style="margin-top:16px;">
                    <el-form-item label="手机验证码" prop="code"
                        :rules="filter_rules({ required: true, type: 'code', message: '验证码必填！' })">
                        <el-input v-model="updateFormDialog.formData.code" style="width:180px;"></el-input>
                        <el-button type="primary" plain style="width:124px;padding:9px 0px;" @click.prevent="send"
                            v-if="nmb == 120">获取验证码</el-button>
                        <el-button type="primary" plain style="width:124px;padding:9px 0px;" disabled
                            v-else>重新获取({{ nmb }})</el-button>
                    </el-form-item>
                    <el-form-item style="margin-top: 26px;">
                        <el-button class="footer-center-button" @click="dialogFormVisible = false">取 消</el-button>
                        <el-button class="footer-center-button" type="primary"
                            @click="updateFormDialog_ok('updateFormDialog', 'pwd')">确 认</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div v-show="active == 2">
                <div class="passWord-main">
                    <el-form label-position="right" ref="passWordForm" :rules="formRules"
                        :model="passWordForm.formData"  label-width="100px">
                        <el-form-item label="接口密码" prop="password">
                            <el-input show-password v-model="passWordForm.formData.password" placeholder="密码由数字、大小写字母组成，密码长度8-16位"></el-input>
                        </el-form-item>
                        <div style="margin-top: 30px;">
                            <el-form-item label="确认密码" prop="confirmPwd">
                            <el-input show-password
                                v-model="passWordForm.formData.confirmPwd" placeholder="密码由数字、大小写字母组成，密码长度8-16位"></el-input>
                        </el-form-item>
                        </div>
                    </el-form>
                </div>
                <div class="passWord-footer">
                    <el-button @click="dialogFormVisible = false">取消</el-button>
                    <el-button @click="updateFormOk('passWordForm')" type="primary">确定</el-button>
                </div>
            </div> -->
            <el-form label-position="right" ref="passWordForm" :rules="formRules" :model="passWordForm.formData"
                label-width="100px">
                <div class="Login-c-p-getPhone">
                    验证码将会发送至管理员绑定的手机号：{{ loginInfo.mobile }}，请注意查收！
                </div>
                <el-form-item label="手机验证码" prop="verifyCode"
                    :rules="filter_rules({ required: true, type: 'code', message: '验证码必填！' })">
                    <el-input v-model="passWordForm.formData.verifyCode" style="width:250px;"></el-input>
                    <el-button type="primary" plain style="width:124px;padding:9px 0px;" @click.prevent="send"
                        v-if="nmb == 120">获取验证码</el-button>
                    <el-button type="primary" plain style="width:124px;padding:9px 0px;" disabled v-else>重新获取({{ nmb
                    }})</el-button>
                </el-form-item>
                <el-form-item label="接口密码" prop="password">
                    <el-input show-password v-model="passWordForm.formData.password"
                        placeholder="密码由数字、大小写字母组成，密码长度8-16位"></el-input>
                </el-form-item>
                <div style="margin-top: 30px;">
                    <el-form-item label="确认密码" prop="confirmPwd">
                        <el-input show-password v-model="passWordForm.formData.confirmPwd"
                            placeholder="密码由数字、大小写字母组成，密码长度8-16位"></el-input>
                    </el-form-item>
                </div>
            </el-form>
            <div class="passWord-footer">
                <el-button @click="dialogFormVisible = false">取消</el-button>
                <el-button @click="updateFormOk('passWordForm')" type="primary">确定</el-button>
            </div>
        </el-dialog>
        <!-- 修改加密盐 -->
        <el-dialog title="修改加密盐" :visible.sync="dialogSaltVisible" width="520px" :close-on-click-modal="false"
            :before-close="beforec" class="dialogBasics">
            <!-- <el-steps :active="activeSalt" simple>
                <el-step title="获取手机验证码" icon="el-icon-edit"></el-step>
                <el-step title="修改加密盐" icon="el-icon-upload"></el-step>
            </el-steps> -->
            <!-- 表格 -->
            <!-- <div v-show="activeSalt == 1">
                <el-table :data="dialogTable" border style="width: 100%;margin-top:10px;" class="passWord_table">
                    <el-table-column align="center" type="index" label="序号" width="60"></el-table-column>
                    <el-table-column align="center" prop="phone" label="手机号"></el-table-column>
                    <el-table-column label="选择" align="center" width="60">
                        <template slot-scope="scope">
                            <el-radio :label="scope.$index" v-model="templateRadio"
                                @change.native="getTemplateRow(scope.$index, scope.row)">&nbsp;</el-radio>
                        </template>
                    </el-table-column>
                </el-table>
                <el-form :model="updateFormDialog.formData" ref="updateFormDialog" label-width="146px"
                    style="margin-top:16px;">
                    <el-form-item label="手机验证码" prop="code"
                        :rules="filter_rules({ required: true, type: 'code', message: '验证码必填！' })">
                        <el-input v-model="updateFormDialog.formData.code" style="width:180px;"></el-input>
                        <el-button type="primary" plain style="width:124px;padding:9px 0px;" @click.prevent="send"
                            v-if="nmb == 120">获取验证码</el-button>
                        <el-button type="primary" plain style="width:124px;padding:9px 0px;" disabled v-else>重新获取({{ nmb
                        }})</el-button>
                    </el-form-item>
                    <el-form-item style="margin-top: 26px;">
                        <el-button class="footer-center-button" @click="dialogSaltVisible = false">取 消</el-button>
                        <el-button class="footer-center-button" type="primary"
                            @click="updateFormDialog_ok('updateFormDialog', 'salt')">下一步</el-button>
                    </el-form-item>
                </el-form>
            </div>
            <div v-show="activeSalt == 2">
                <div class="passWord-main">
                    
                </div>
                <div class="passWord-footer">
                    <el-button @click="dialogSaltVisible = false">取消</el-button>
                    <el-button @click="updateSalt('saltForm')" type="primary">确定</el-button>
                </div>
            </div> -->
            <el-form label-position="right" ref="saltForm" :rules="saltForm.formRule" :model="saltForm.formData"
                label-width="100px">
                <div class="Login-c-p-getPhone">
                    验证码将会发送至管理员绑定的手机号：{{ loginInfo.mobile }}，请注意查收！
                </div>
                <el-form-item label="手机验证码" prop="verifyCode"
                    :rules="filter_rules({ required: true, type: 'code', message: '验证码必填！' })">
                    <el-input v-model="saltForm.formData.verifyCode" style="width:250px;"></el-input>
                    <el-button type="primary" plain style="width:124px;padding:9px 0px;" @click.prevent="send"
                        v-if="nmb == 120">获取验证码</el-button>
                    <el-button type="primary" plain style="width:124px;padding:9px 0px;" disabled v-else>重新获取({{ nmb
                    }})</el-button>
                </el-form-item>
                <el-form-item label="加密盐" prop="salt">
                    <el-input v-model="saltForm.formData.salt"></el-input>
                </el-form-item>
            </el-form>
            <div class="passWord-footer">
                <el-button @click="dialogSaltVisible = false">取消</el-button>
                <el-button @click="updateSalt('saltForm')" type="primary">确定</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { mapState, mapMutations, mapActions } from "vuex";
export default {
    name: 'PersonalInformation',
    data() {
        // 验证IP规则
        var code = (rule, value, callback) => {
            // if (!this.phoneData) {
            //     return callback(new Error('请选中手机号'));
            // } else 
            if (value == "") {
                return callback(new Error('请输入验证码'));
            } else {
                callback();
            }
        };
        var oldPassword = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('原密码不能为空'));
            } else {
                this.$api.get(this.API.cpus + 'consumerclientinfo/validatePassword/' + this.formPassword.oldPassword, {}, res => {
                    if (res.code == 200) {
                        callback();
                    } else {
                        callback(new Error('与原密码不相符'));
                    }
                })
            }
        };
        var validatePass = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('新密码不能为空'));
            } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$/g.test(value)) {
                callback(new Error('请输入正确的密码格式！密码由数字、大小写字母组成，密码长度8-16位，且不能包含中文'));
            } else {
                if (this.formPassword.passWord !== '') {
                    this.$refs.formRule.validateField('passWord');
                }
                callback();
            }
        };
        var validatePass2 = (rule, value, callback) => {
            if (value === '') {
                callback(new Error('密码不能为空'));
            } else if (!/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$/g.test(value)) {
                callback(new Error('请输入正确的密码格式！密码由数字、大小写字母组成，密码长度8-16位，且不能包含中文'));
            } else if (value !== this.passWordForm.formData.password) {
                callback(new Error('两次输入密码不一致!'));
            } else {
                callback();
            }
        };
        return {
            SendSettings: {
                overrunCode: "",
                overrunIndustry: "",
                overrunMarket: ""
            },
            SendSettingsArry: ['1', '2', '3', '4', '5', '6'],
            SendSettingsArrys: ['1', '2', '3', '4', '5', '6', "7", "8", "9", "10"],
            sendfrequency: true,
            sendfrequency1: true,
            sendfrequency3: true,
            sendfrequency4: true,
            sendfrequency5: true,
            sendfrequency6: true,
            sendfrequency7: true,
            requencyEnable: false,
            requencyEnable1: false,
            requencyEnable3: false,
            requencyEnable4: false,
            requencyEnable5: false,
            requencyEnable6: false,
            requencyEnable7: false,
            ispwd: false,
            active: 1,
            activeSalt: 1,
            cipherMode: "",//加密方式
            LcpDialogVisible: false,//手机验证弹出框显示隐藏
            setPhoneSteps: 1, // 设置手机号的步骤
            saltForm: {
                formData: {
                    flag: "1",
                    verifyCode: "",
                    salt: "",
                },
                formRule: {
                    salt: [
                        { required: true, message: '请输入加密盐', trigger: 'blur' },
                    ],
                    verifyCode: [
                        { required: true, validator: code, trigger: 'blur' }
                    ]
                }
            },
            passWordForm: {//修改密码的表格--下面表格
                formData: {
                    verifyCode: "",
                    password: '',
                    confirmPwd:"",
                    flag: '1',
                    // product:[]
                },
                // formRule:{
                //     product: [
                //         { type: 'array', required: true, message: '请至少选择一个适用产品', trigger: 'change' }
                //     ]
                // }
            },
            // 余额条数
            NumberBalances: '',
            NumberBalances1: '',
            NumberBalances3: '',
            NumberBalances4: '',
            NumberBalances5: '',
            NumberBalances6: '',
            NumberBalances7: '',
            //余额提醒条数
            reminderBalances: '',
            reminderBalances1: '',
            reminderBalances3: '',
            reminderBalances4: '',
            reminderBalances5: '',
            reminderBalances6: '',
            reminderBalances7: '',
            updateFormDialog: {//修改接口密码--弹窗
                formData: {
                    code: ""//验证码
                },
            },
            dialogFormVisible: false,//弹窗显示状态
            dialogSaltVisible: false,
            //弹窗里的表格
            dialogTable: [],
            templateRadio: 0,
            templateSelection: '',
            nmb: 120,
            // ---------------------
            imageUrl: '',
            actionHttp: '',
            isIndividualization: '',
            token: {},
            passWordFlag: false,
            formPassword: {
                // oldPassword:"",
                newPassword: '',
                passWord: '',
                confirmPwd: "",//确认密码
            },
            formLogo: {
                userName: this.$store.state.userName,
                domainName: "",
                reminder: "",
                signature: "",
                title: "",
                recordInformation: "",
                icon: "",
                logo: ""
            },
            changePassword: false,
            Personalization: false,
            formRules: {
                oldPassword: [
                    // { required: true, message: '原密码不能为空', trigger: 'blur' },
                    { required: true, validator: oldPassword, trigger: 'blur' },
                    // ^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$
                    // {pattern:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[^\u4E00-\u9FA5\uF900-\uFA2D\u0020]{8,16}$/gi,message:'密码必须包含数字以及大小写字母8-16位'}
                ],
                newPassword: [
                    { required: true, validator: validatePass, trigger: 'blur' }
                ],
                password: [
                    { required: true, validator: validatePass2, trigger: 'blur' }
                ],
                confirmPwd: [
                    { required: true, validator: validatePass2, trigger: 'blur' }
                ],
                verifyCode: [
                    { required: true, validator: code, trigger: 'blur' }
                ]
            },
            formLogoRules: {
                domainName: [
                    { required: true, message: '请输入域名', trigger: 'blur' },
                    { min: 1, max: 50, message: '长度在 1 到 50 个字符', trigger: 'blur' }
                ],
                reminder: [
                    { required: true, message: '请输入提示语', trigger: 'blur' },
                    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
                ],
                signature: [
                    { required: true, message: '请输入签名', trigger: 'blur' },
                    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
                ],
                title: [
                    { required: true, message: '请输入登录title', trigger: 'blur' },
                    { min: 1, max: 20, message: '长度在 1 到 20 个字符', trigger: 'blur' }
                ],
                recordInformation: [
                    { required: true, message: '请输入备案信息', trigger: 'blur' },
                    { min: 1, max: 100, message: '长度在 1 到 100 个字符', trigger: 'blur' }
                ]
            },
            fileList: [],
            fileList1: [],

            nmb: 120,
            radio: "",
            tableD: [],
            setphoneFrom: {
                ruleForm1: {
                    verCode: '',
                },
                rules1: {
                    verCode: [
                        { required: true, validator: code, trigger: 'blur' },
                        { min: 6, max: 6, message: '请输入6位数字验证码' }
                    ]
                },
                ruleForm2: {
                    setNewPhone: '',
                },
            },
            timer: null,
            loginInfo: {
                isAdmin: null,
                consumerName: "",
                mobile: "",
                remark: "",
                id: ""
            },
        }
    },
    computed: {
        ...mapState({  //比如'movies/hotMovies
            compName: state => state.compName,
            consumerName: state => state.userName,
            createTime: state => state.createLocalDateTime,
            userID: state => state.userId,
            roleId: state => state.roleId,
        })
    },
    methods: {
        //点击修改密码按钮
        modifyPsd(type) {
            this.getPhoneArr(type);
        },
        editSalt(type) {
            this.getPhoneArr(type)

        },
        getLoginInfo() {
            this.$api.get(
                this.API.cpus + "userLoginAdmin/loginPhoneInfo",
                {},
                (res) => {
                    if (res.code == 200) {
                        this.loginInfo = res.data;
                    }
                }
            );
        },
        getLoginInfo() {
            this.$api.get(
                this.API.cpus + "userLoginAdmin/loginPhoneInfo",
                {},
                (res) => {
                    if (res.code == 200) {
                        this.loginInfo = res.data;
                    }
                }
            );
        },
        //获取弹出框手机号
        getPhoneArr(type) {
            this.dialogTable = [];
            this.$api.post(this.API.cpus + 'consumerclientinfo/loginTelephoneManager/list', {}, res => {
                let phonelength = res.data.data;
                this.templateSelection = phonelength[0].mobile; //把第一个手机号设置为默认选中

                for (var i = 0; i < phonelength.length; i++) {
                    this.dialogTable.push({ phone: phonelength[i].mobile })
                }
                if (type == 'pwd') {
                    this.dialogFormVisible = true;//显示弹窗
                } else {
                    this.dialogSaltVisible = true
                }

            })
        },
        //获取验证码
        send() {
            this.$api.get(this.API.cpus + "userLoginAdmin/sendVerificationCode?flag=1&phoneId=" + this.loginInfo.id, {}, res => {
                if (res.code == 200) {
                    this.$message({
                        type: 'success',
                        duration: '2000',
                        message: '验证码已发送至手机!'
                    });
                    --this.nmb;
                    this.timer = setInterval(res => {
                        --this.nmb;
                        if (this.nmb < 1) {
                            this.nmb = 120;
                            clearInterval(this.timer);
                        };
                    }, 1000);
                } else {
                    this.$message({
                        type: 'warning',
                        message: '验证码未失效，请失效后再次申请!'
                    });
                }

            })
            // if (this.templateSelection) {

            // } else {
            //     this.$message({
            //         message: '选择发送验证码的手机号码！',
            //         type: 'warning'
            //     });
            // }
        },
        ispeds() {
            this.ispwd = !this.ispwd;
        },
        //获取选中手机号码
        getTemplateRow(index, row) {
            this.templateSelection = row.phone;
        },
        beforec() {
            this.dialogFormVisible = false;//隐藏弹窗
            this.dialogSaltVisible = false
        },
        updateFormDialog_ok(formName, type) { //弹框第一步确认
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    //提交验证码
                    this.$api.get(this.API.cpus + "code/checkVerificationCode?code=" + this.updateFormDialog.formData.code + '&flag=1', {}, res => {
                        if (res.code == 200) {
                            this.nmb = 120;
                            clearInterval(this.timer);
                            if (type == 'pwd') {
                                this.active = 2;
                            } else {
                                this.activeSalt = 2
                            }

                        } else {
                            this.$message.error('验证码无效!');
                        }
                    })
                } else {
                    return false;
                }
            });
        },
        handelpwd(val) {
            if (this.passWordForm.formData.confirmPwd) {
                // console.log(this.passWordForm.formData.confirmPwd, 'll');
                if (val === this.passWordForm.formData.confirmPwd) {
                    this.passWordFlag = false
                } else {
                    this.passWordFlag = true
                }
            }
        },
        handelconfirm(val) {
            if (val === this.passWordForm.formData.password) {
                this.passWordFlag = false
            } else {
                this.passWordFlag = true
            }
        },
        updateFormOk(formName) {//弹框第二步确认
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        verifyCode: this.passWordForm.formData.verifyCode,
                        password: this.passWordForm.formData.password,
                        flag: this.passWordForm.formData.flag,
                    }
                    if (this.passWordForm.formData.password === this.passWordForm.formData.confirmPwd) {
                        this.$confirms.confirmation('put', '此操作将修改接口密码数据, 是否继续？', this.API.cpus + 'consumerclientinfo/updPasswordV2', data, res => {
                            if(res.code==200){
                                this.dialogFormVisible = false;//隐藏弹窗
                                this.nmb = 120;
                                clearInterval(this.timer);
                            }
                        });
                    } else {
                        this.$message({
                            message: '请确认两次密码是否一致！',
                            type: 'warning'
                        });
                    }

                } else {
                    return false;
                }
            });
        },
        //修改加密盐
        updateSalt(formName) {
            this.$refs[formName].validate((valid) => {
                if (valid) {
                    let data = {
                        flag: this.saltForm.formData.flag,
                        verifyCode: this.saltForm.formData.verifyCode,
                        salt: this.saltForm.formData.salt,
                    }
                    this.$confirms.confirmation('put', '此操作将修改加密盐, 是否继续？', this.API.cpus + 'consumerclientinfo/saltV2', data, res => {
                        if(res.code==200){
                            this.dialogSaltVisible = false;//隐藏弹窗
                            this.nmb = 120;
                            clearInterval(this.timer);
                        }
                        
                    });

                } else {
                    return false;
                }
            });
        },
        // 点击启用
        handleRequencyEnable() {
            this.requencyEnable = true;
            this.sendfrequency = false;
        },
        handleRequencyEnable1() {
            this.requencyEnable1 = true;
            this.sendfrequency1 = false;
        },
        handleRequencyEnable3() {
            this.requencyEnable3 = true;
            this.sendfrequency3 = false;
        },
        handleRequencyEnable4() {
            this.requencyEnable4 = true;
            this.sendfrequency4 = false;
        },
        handleRequencyEnable5() {
            this.requencyEnable5 = true;
            this.sendfrequency5 = false;
        },
        handleRequencyEnable6() {
            this.requencyEnable6 = true;
            this.sendfrequency6 = false;
        },
        handleRequencyEnable7() {
            this.requencyEnable7 = true;
            this.sendfrequency7 = false;
        },
        //设置
        SetBalance() {
            this.sendfrequency = !this.sendfrequency;
        },
        SetBalance1() {
            this.sendfrequency1 = !this.sendfrequency1;
        },
        SetBalance3() {
            this.sendfrequency3 = !this.sendfrequency3;
        },
        SetBalance4() {
            this.sendfrequency4 = !this.sendfrequency4;
        },
        SetBalance5() {
            this.sendfrequency5 = !this.sendfrequency5;
        },
        SetBalance6() {
            this.sendfrequency6 = !this.sendfrequency6;
        },
        SetBalance7() {
            this.sendfrequency7 = !this.sendfrequency7;
        },
        //点击确定
        determine() {
            const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
            if (testNum.test(this.NumberBalances)) {
                this.$confirms.confirmation('post', '确定余额条数不足' + this.NumberBalances + '条时提醒', this.API.recharge + 'client/balance/notice/open', { num: this.NumberBalances, productId: '1', username: this.consumerName }, res => {
                    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
                    this.reminderBalances = res.data.num
                    // })
                    this.sendfrequency = !this.sendfrequency;
                });
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请填写1-10000000的余额条数"
                });
            }
        },
        determine1() {
            const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
            if (testNum.test(this.NumberBalances1)) {
                this.$confirms.confirmation('post', '确定余额条数不足' + this.NumberBalances1 + '条时提醒', this.API.recharge + 'client/balance/notice/open', { num: this.NumberBalances1, productId: '2', username: this.consumerName }, res => {
                    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
                    this.reminderBalances1 = res.data.num
                    // })
                    this.sendfrequency1 = !this.sendfrequency1;
                });
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请填写1-10000000的余额条数"
                });
            }
        },
        determine3() {
            const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
            if (testNum.test(this.NumberBalances3)) {
                this.$confirms.confirmation('post', '确定余额条数不足' + this.NumberBalances3 + '条时提醒', this.API.recharge + 'client/balance/notice/open', { num: this.NumberBalances3, productId: '3', username: this.consumerName }, res => {
                    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
                    this.reminderBalances3 = res.data.num
                    // })
                    this.sendfrequency3 = !this.sendfrequency3;
                });
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请填写1-10000000的余额条数"
                });
            }
        },
        determine4() {
            const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
            if (testNum.test(this.NumberBalances4)) {
                this.$confirms.confirmation('post', '确定余额条数不足' + this.NumberBalances4 + '条时提醒', this.API.recharge + 'client/balance/notice/open', { num: this.NumberBalances4, productId: '4', username: this.consumerName }, res => {
                    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
                    this.reminderBalances4 = res.data.num
                    // })
                    this.sendfrequency4 = !this.sendfrequency4;
                });
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请填写1-10000000的余额条数"
                });
            }
        },
        determine5() {
            const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
            if (testNum.test(this.NumberBalances5)) {
                this.$confirms.confirmation('post', '确定余额条数不足' + this.NumberBalances5 + '条时提醒', this.API.recharge + 'client/balance/notice/open', { num: this.NumberBalances5, productId: '5', username: this.consumerName }, res => {
                    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
                    this.reminderBalances5 = res.data.num
                    // })
                    this.sendfrequency5 = !this.sendfrequency5;
                });
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请填写1-10000000的余额条数"
                });
            }
        },
        determine6() {
            const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
            if (testNum.test(this.NumberBalances6)) {
                this.$confirms.confirmation('post', '确定余额条数不足' + this.NumberBalances6 + '条时提醒', this.API.recharge + 'client/balance/notice/open', { num: this.NumberBalances6, productId: '6', username: this.consumerName }, res => {
                    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
                    this.reminderBalances6 = res.data.num
                    // })
                    this.sendfrequency6 = !this.sendfrequency6;
                });
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请填写1-10000000的余额条数"
                });
            }
        },
        determine7() {
            const testNum = /^([1-9][0-9]{0,6}|10000000)$/;
            if (testNum.test(this.NumberBalances7)) {
                this.$confirms.confirmation('post', '确定余额条数不足' + this.NumberBalances7 + '条时提醒', this.API.recharge + 'client/balance/notice/open', { num: this.NumberBalances7, productId: '7', username: this.consumerName }, res => {
                    // this.$api.get(this.API.cpus+'consumerClientBalanceNotice/smsNum',{},res=>{
                    this.reminderBalances7 = res.data.num
                    // })
                    this.sendfrequency7 = !this.sendfrequency7;
                });
            } else {
                this.$message({
                    type: 'error',
                    duration: '2000',
                    message: "请填写1-10000000的余额条数"
                });
            }
        },
        shutDownSetBalance() {
            this.$confirms.confirmation('post', '确认关闭余额提醒设置', this.API.recharge + 'client/balance/notice/close', { productId: '1', username: this.consumerName }, res => {
                this.requencyEnable = !this.requencyEnable;
            });
        },
        shutDownSetBalance1() {
            this.$confirms.confirmation('post', '确认关闭余额提醒设置', this.API.recharge + 'client/balance/notice/close', { productId: '2', username: this.consumerName }, res => {
                this.requencyEnable1 = !this.requencyEnable1;
            });
        },
        shutDownSetBalance3() {
            this.$confirms.confirmation('post', '确认关闭余额提醒设置', this.API.recharge + 'client/balance/notice/close', { productId: '3', username: this.consumerName }, res => {
                this.requencyEnable3 = !this.requencyEnable3;
            });
        },
        shutDownSetBalance4() {
            this.$confirms.confirmation('post', '确认关闭余额提醒设置', this.API.recharge + 'client/balance/notice/close', { productId: '4', username: this.consumerName }, res => {
                this.requencyEnable4 = !this.requencyEnable4;
            });
        },
        shutDownSetBalance5() {
            this.$confirms.confirmation('post', '确认关闭余额提醒设置', this.API.recharge + 'client/balance/notice/close', { productId: '5', username: this.consumerName }, res => {
                this.requencyEnable5 = !this.requencyEnable5;
            });
        },
        shutDownSetBalance6() {
            this.$confirms.confirmation('post', '确认关闭余额提醒设置', this.API.recharge + 'client/balance/notice/close', { productId: '6', username: this.consumerName }, res => {
                this.requencyEnable6 = !this.requencyEnable6;
            });
        },
        shutDownSetBalance7() {
            this.$confirms.confirmation('post', '确认关闭余额提醒设置', this.API.recharge + 'client/balance/notice/close', { productId: '7', username: this.consumerName }, res => {
                this.requencyEnable7 = !this.requencyEnable7;
            });
        },
        // =======================================
        // 图片上传
        handleRemove(file, fileList) {
            this.formLogo.icon = ""
        },
        handlePreview(file) {
            // console.log(file);
        },
        handleRemove1(file, fileList) {
            this.formLogo.logo = ""
        },
        handlePreview1(file) {
            // console.log(file);
        },
        // 打上传成功
        handleAvatarSuccess(res, file) {
            this.imageUrl = URL.createObjectURL(file.raw);
            this.getImgUrl();
        },
        handleAvatarSuccess1(res, file) {
            this.formLogo.icon = res.data
        },
        handleAvatarSuccess2(res, file) {
            this.formLogo.logo = res.data
        },
        // 获取项目绝对路径
        ...mapActions([  //比如'movies/getHotMovies
            'saveImg',
        ]),
        beforeAvatarUpload(file) {
            console.log(file)
            const isJPG = file.type === 'image/jpg';
            const isjpeg = file.type === 'image/jpeg';
            const isJPG1 = file.type === 'image/png';
            const isJPG2 = file.type === 'image/gif';
            // const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isJPG && !isJPG1 && !isJPG2 && !isjpeg) {
                this.$message.error('上传头像图片只能是 jpg、png、gif 格式!');
                return false;
            }
            // if (!isLt2M) {
            //     this.$message.error('上传头像图片大小不能超过 2MB!');
            // }

            // return isJPG && isLt2M;
        },
        beforeAvatarUpload1(file) {
            console.log(file)
            const isico = file.type === 'image/x-icon';
            // const isLt2M = file.size / 1024 / 1024 < 2;
            if (!isico) {
                this.$message.error('上传头像图片只能是 ico 格式!');
                return false;
            }
            // if (!isLt2M) {
            //     this.$message.error('上传头像图片大小不能超过 2MB!');
            // }

            // return isJPG && isLt2M;
        },
        beforeAvatarUpload2(file) {
            console.log(file)
            const isJPG1 = file.type === 'image/png';
            const isLt2M = file.size / 1024 / 1024 < 1;
            if (!isJPG1) {
                this.$message.error('上传头像图片只能是 png 格式!');
                return false;
            }
            if (!isLt2M) {
                this.$message.error('上传头像图片大小不能超过 1MB!');
            }

            // return isJPG && isLt2M;
        },
        //获取img
        getImgUrl() {
            this.$api.get(this.API.cpus + 'consumerclientinfo/getClientInfo', null, res => {
                // this.imageUrl = this.API.imgU + res.data.portraitGroup+'/'+res.data.portraitPath; 
                this.imageUrl = 'https://' + res.data.portraitUrl;
                this.isIndividualization = res.data.isIndividualization
                if (res.data.cipherMode) {
                    this.cipherMode = res.data.cipherMode
                }
                //存在vuex里
                if (res.data.portraitUrl) {
                    let SET_PORTRAITURL = 'https://' + res.data.portraitUrl;
                    let param = {
                        portraitUrl: SET_PORTRAITURL
                    }
                    this.saveImg(param);
                }
            })
        },
        avatarClick() {
            this.$refs.avatar.click()
        },
        //确定修改密码
        changePwd(val) {
            this.$refs[val].validate((valid) => {
                if (valid) {
                    let param = {};
                    // param.oldPassword=this.formPassword.oldPassword;
                    param.newPassword = this.formPassword.newPassword;
                    this.$confirms.confirmation('get', '确定修改密码？', this.API.cpus + 'consumerclientinfo/updateLoginPassword', param, res => {
                        this.changePassword = false; //关闭弹出框
                    });
                } else {
                    return false;
                }
            });
        },
        LogoSetting() {
            this.$api.post(this.API.cpus + 'personalizationSettings/getSttingsByUserName', { userName: this.$store.state.userName }, res => {
                if (res.data != null) {
                    this.formLogo = res.data
                    this.fileList = [{ name: '', url: res.data.icon }]
                    this.fileList1 = [{ name: '', url: res.data.logo }]
                }
                this.Personalization = true;
            })
        },
        //登录手机号弹出层
        addphone() {
            this.$api.post(this.API.cpus + 'consumerclientinfo/loginTelephoneManager/list', {}, res => {
                let resdata = []
                let tabledata = []
                let phonelength = res.data.data;
                this.phoneData = phonelength[0].mobile
                this.phoneOriginal = [];
                for (var i = 0; i < phonelength.length; i++) {
                    // 列表数据
                    let a = {};
                    a.Numbering = i + 1;
                    a.username = phonelength[0].consumerName;
                    a.phone = phonelength[i].mobile;
                    resdata[resdata.length] = a;
                    this.phoneOriginal.push(phonelength[i].mobile);
                    // 登录手机号列表
                    let b = {};
                    b.index = i;
                    b.name = i + 1;
                    b.address = phonelength[i].mobile;
                    tabledata[tabledata.length] = b
                }
                this.tableD = tabledata
            })
            // if(this.tableDataObj.tableData.length>=5){
            //     this.$message({
            //         type: 'error',
            //         duration:'2000',
            //         message:"最多添加5个手机号码"
            //     });
            // }else{
            this.radio = 0
            this.LcpDialogVisible = true
            // }
        },
        handelClose() {//×号关闭弹窗
            // 点击关闭
            this.LcpDialogVisible = false; //关闭弹出框
        },
        cancel() {
            this.LcpDialogVisible = false; //关闭弹出框
            this.setPhoneSteps = 1; //步进改为1
            this.setphoneFrom.ruleForm1.verCode = ''; //验证码置空
            this.setphoneFrom.ruleForm2.setNewPhone = ''; //手机号置空
        },
        // 新增登录手机号
        submitForm(val) {
            this.$refs[val].validate((valid) => {
                if (val == "ruleForm1") {
                    if (valid) {
                        this.$api.get(this.API.cpus + "code/checkVerificationCode?code=" + this.setphoneFrom.ruleForm1.verCode + '&flag=2', {}, res => {
                            if (res.code == 200) {
                                this.setPhoneSteps = 2;
                            } else {
                                this.$message({
                                    type: 'error',
                                    duration: '2000',
                                    message: "验证码无效！"
                                });
                            }
                        })
                    } else {
                        console.log('error submit!!');
                        return false;
                    }
                } else {
                    this.$refs[val].validate((valid) => {
                        if (valid) {
                            let param = {};
                            // param.oldPassword=this.formPassword.oldPassword;
                            param.newPassword = this.formPassword.newPassword;
                            this.$confirms.confirmation('get', '确定修改密码？', this.API.cpus + 'consumerclientinfo/updateLoginPassword', param, res => {
                                this.LcpDialogVisible = false; //关闭弹出框
                            });
                        } else {
                            return false;
                        }
                    });
                }

            });
        },
        // 获取验证码倒计时
        CountdownCode() {
            if (this.phoneData) {
                --this.nmb;
                const timer = setInterval(res => {
                    --this.nmb;
                    if (this.nmb < 1) {
                        this.nmb = 120;
                        clearInterval(timer);
                    };
                }, 1000);
                this.$api.get(this.API.cpus + "code/sendVerificationCode?phone=" + this.phoneData + '&flag=2', {}, res => {
                    if (res.data.code == 200) {
                        this.$message({
                            type: 'success',
                            duration: '2000',
                            message: '验证码已发送至手机!'
                        });
                    } else {
                        this.$message({
                            type: 'warning',
                            message: '验证码未失效，需失效后重新获取!'
                        });
                    }

                })
            } else {
                this.$message({
                    message: '请先选中手机号码',
                    type: 'warning'
                });
            }
        },
        showRow(row) {
            //赋值给radio
            this.radio = this.tableD.indexOf(row);
        },
        getCurrentRow(val) {
            this.phoneData = this.tableD[val].address //赋值手机号
        },
    },
    watch: {
        dialogFormVisible(val) {
            if (!val) {
                // this.$refs.updateFormDialog.resetFields(); //清空表单
                this.$refs.passWordForm.resetFields(); //清空表单
                // this.active = 1;
                // this.templateRadio = 0
            }
        },
        dialogSaltVisible(val) {
            if (!val) {
                this.$refs.saltForm.resetFields(); //清空表单
            }
        },
        // ===============================
        changePassword(newVal, oldVal) {
            if (newVal == false) {
                this.$refs.formRule.resetFields();
            }
        },
        Personalization(newVal, oldVal) {
            if (newVal == false) {
                this.$refs.formLogos.resetFields();
                this.fileList = []
                this.fileList1 = []
            }
        },
        LcpDialogVisible: function (val) {
            if (val == false) {
                this.setphoneFrom.ruleForm1.verCode = ''; //验证码置空
                this.setphoneFrom.ruleForm2.setNewPhone = ''; //手机号置空
                this.setPhoneSteps = 1; //步进改为1
                this.radio = "";
                this.phoneData = this.phoneOriginal[0]
                this.$refs.ruleForm1.resetFields()
                this.$refs.ruleForm2.resetFields()
            }
        }
    },
    created() {
        this.getLoginInfo()
        this.$api.get(this.API.recharge + 'client/balance/notice/info', { username: this.consumerName, productId: '1' }, res => {
            if (res) {
                if (res.data.open == 0) {
                    this.requencyEnable = false
                } else {
                    this.requencyEnable = true
                }
                this.reminderBalances = res.data.num
            } else {
                this.requencyEnable = false
            }
        })
        this.$api.get(this.API.recharge + 'client/balance/notice/info', { username: this.consumerName, productId: '2' }, res => {
            if (res) {
                if (res.data.open == 0) {
                    this.requencyEnable1 = false
                } else {
                    this.requencyEnable1 = true
                }
                this.reminderBalances1 = res.data.num
            } else {
                this.requencyEnable1 = false
            }
        })
        this.$api.get(this.API.recharge + 'client/balance/notice/info', { username: this.consumerName, productId: '3' }, res => {
            if (res) {
                if (res.data.open == 0) {
                    this.requencyEnable3 = false
                } else {
                    this.requencyEnable3 = true
                }
                this.reminderBalances3 = res.data.num
            } else {
                this.requencyEnable1 = false
            }
        })
        this.$api.get(this.API.recharge + 'client/balance/notice/info', { username: this.consumerName, productId: '4' }, res => {
            if (res) {
                if (res.data.open == 0) {
                    this.requencyEnable4 = false
                } else {
                    this.requencyEnable4 = true
                }
                this.reminderBalances4 = res.data.num
            } else {
                this.requencyEnable4 = false
            }
        })
        this.$api.get(this.API.recharge + 'client/balance/notice/info', { username: this.consumerName, productId: '5' }, res => {
            if (res) {
                if (res.data.open == 0) {
                    this.requencyEnable5 = false
                } else {
                    this.requencyEnable5 = true
                }
                this.reminderBalances5 = res.data.num
            } else {
                this.requencyEnable5 = false
            }
        })
        this.$api.get(this.API.recharge + 'client/balance/notice/info', { username: this.consumerName, productId: '6' }, res => {
            if (res) {
                if (res.data.open == 0) {
                    this.requencyEnable6 = false
                } else {
                    this.requencyEnable6 = true
                }
                this.reminderBalances6 = res.data.num
            } else {
                this.requencyEnable6 = false
            }
        })
        this.$api.get(this.API.recharge + 'client/balance/notice/info', { username: this.consumerName, productId: '7' }, res => {
            if (res) {
                if (res.data.open == 0) {
                    this.requencyEnable7 = false
                } else {
                    this.requencyEnable7 = true
                }
                this.reminderBalances7 = res.data.num
            } else {
                this.requencyEnable1 = false
            }
        })
        // =====================================
        this.token = { Authorization: "Bearer" + this.$common.getCookie('ZTGlS_TOKEN') };
        this.actionHttp = this.API.cpus + "consumerclientinfo/updatePortrait";
        this.actionHttp1 = this.API.cpus + "consumerclientinfo/uploadImage"
        this.getImgUrl();
        this.$api.get(this.API.cpus + 'consumerclientsms/overrun', {}, res => {
            this.SendSettings.overrunCode = res.data.overrunCode
            this.SendSettings.overrunIndustry = res.data.overrunIndustry
            this.SendSettings.overrunMarket = res.data.overrunMarket
        })
    }
}
</script>
<style scoped>
.appkeyShow {
    display: inline-block;
    padding-left: 10px;
    color: #16a589;
    cursor: pointer;
}

.bas-block {
    margin-bottom: 10px;
    padding: 30px;
    position: relative;
    height: 220px;
}

.basic-cfg-title {
    font-weight: bold;
    padding-bottom: 32px;
    color: #333;
}

.basic-cfg-tit {
    width: 92px;
    display: inline-block;
}

.basic-cfg-cot {
    margin-bottom: 26px;
}

.basic-cfg-tips {
    padding-left: 96px;
    padding-top: 4px;
    font-size: 12px;
    color: #999;
}

.appkeyShow {
    color: #16a589;
    cursor: pointer;
}

.dialogTable {
    margin: 0 0 30px 0;
}

.passWord-footer {
    padding-top: 20px;
    text-align: right;
}

.footer-center-button {
    width: 100px;
    padding: 9px 0px;
}

.passWord-main {
    padding: 35px 24px 71px 0px;
}

.tips-box {
    position: absolute;
    background: #fff;
    padding: 20px;
    border: 1px solid #e6e6e6;
}

.fade-enter-active {
    animation: show-in 1s;
    transition: all 1s;
}

.fade-leave-active {
    animation: show-out 1s;
    transition: all 1s;
}

.fade-enter,
.fade-leave-to {
    opacity: 0;
}

.basic-help {
    margin: 30px 0 20px 0;
    padding-bottom: 10px;
    width: 500px;
    border-bottom: 1px solid #e6e6e6;
}

@keyframes show-in {
    0% {
        transform: rotateX(0deg);
    }

    100% {
        transform: rotateX(360deg);
    }
}

@keyframes show-out {
    0% {
        transform: rotateX(360deg);
    }

    100% {
        transform: rotateX(0deg);
    }
}

.bac-color-red {
    color: red;
}

/* ---------------------------------- */
header {
    font-weight: bold;
    color: #333;
}

.PersonalInformation-main {
    margin-bottom: 10px;
}

.PersonalInformation-main,
.PersonalPass-main {
    width: 100%;
    padding: 20px;
    box-sizing: border-box;
}

.ChangeAvatar {
    padding-right: 20px;
    text-align: center;
}

.ChangeAvatar,
.information {
    display: inline-block
}

.avatarClick {
    cursor: pointer;
    margin-top: 12px;
    display: inline-block;
    width: 100%;
}

.avatarClick:hover {
    color: rgb(22, 160, 133);
}

.basicInfo {
    margin-top: 26px;
    clear: both;
    overflow: hidden;
}

.basicInfo-left {
    float: left;
}

.headerPg {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 1px solid #ccc;
    overflow: hidden;
}

.headerPg img {
    width: 100%;
    height: 100%;
}

.basicInfo-right {
    float: left;
    padding-top: 23px;
    padding-left: 40px;
}

.basicInfo-line {
    margin-bottom: 14px;
}

.basicInfo-title {
    display: inline-block;
    width: 90px;
    text-align: right;
    color: #333;
}

.avatar-uploader {
    border: 1px solid;
    border-radius: 50%;
    overflow: hidden;
    width: 120px;
    height: 120px;
    border-color: #55555561;
}

.avatar-uploader .el-upload {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.avatar-uploader .el-upload:hover {
    border-color: #409EFF;
}

.avatar-uploader-icon {
    font-size: 28px;
    color: #8c939d;
    width: 120px;
    height: 120px;
    line-height: 120px;
    text-align: center;
}

.avatar {
    width: 120px;
    height: 120px;
    display: block;
}

.changePassword {
    color: rgb(22, 160, 133);
    cursor: pointer;
}

.changePassword:hover {
    color: red;
}

.Login-c-p-getPhone {
    margin-top: 20px;
    color: #909399;
    font-size: 14px;
    margin-left: 16px;
    margin-bottom: 10px;
}
</style>



