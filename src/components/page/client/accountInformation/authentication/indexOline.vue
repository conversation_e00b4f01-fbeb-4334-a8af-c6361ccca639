<template>
  <div class="smrz bag">
    <div class="smrz-h">
      <div style="font-size: 20px;
  font-weight: 800;
  color: #000;">
        实名认证
      </div>
      <div class="smrz-x">
        根据《中华人民共和国网络安全法》及相关法律的规定，网络运营者为用户办理网络接入、域名注册服务，办理固定电话、移动电话等入网手续，或者为用户提供信息发布、即时通讯等服务，在与用户签订协议或者确认提供服务时，应当要求用户提供真实身份信息。用户不提供真实身份信息的，网络运营者不得为其提供相关服务。
        <p>
          在您使用助通服务前，需要进行实名认证。
        </p>
        <p>认证时间：周一至周六9:00--22:00（晚22点后提交会在次日9点后审核认证），认证时长20分钟内；周日提交认证则会延期至周一工作日。</p>
        <p>紧急联系电话：15601838771。</p>
      </div>
    </div>
    <div class="smrz-mian">
      <div class="smrz-m-h">
      </div>
      <div class="smrz-mean">
        <div class="mean-h">
          <span>认证信息</span>
        </div>
        <div v-if="smrzObj">
          <div v-if="smrzObj.certificateInfo">
            <div class="mean-item">
              <span>状态</span>
              <span style="color:red"
                v-if="smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.status == 0">待认证</span>
              <span style="color:red"
                v-else-if="smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.status == 1">合同待签署</span>
              <span style="color:red"
                v-else-if="smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.status == 2">认证不通过</span>
              <span style="color:red"
                v-else-if="smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.status == 3">认证通过</span>
              <span style="color:red"
                v-else-if="smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.status == 4">合同签署中</span>
              <span style="color:red" v-else>未认证</span>
            </div>
            <div v-if="(roleId == '14' || roleId == '12') && smrzObj.certificateInfo.status == 3" class="mean-item">
              <span>完善信息状态</span>
              <span v-if="showflag === true" style="color: #F56C6C;">未完善</span>
              <span v-else-if="showflag === false" style="color: #67C23A;">已完善</span>
            </div>
            <div class="mean-item">
              <span>认证类型</span>
              <span v-if="smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.type == 1">企业法定代表人</span>
              <!-- <span v-else-if="smrzObj&&smrzObj.certificateInfo&&smrzObj.certificateInfo.type ==2">企业代理人</span> -->
              <span v-else-if="smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.type == 3">个体工商户经营者</span>
            </div>
            <div class="mean-item">
              <span>认证人姓名</span><span>{{ smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.name }}</span>
            </div>
            <div class="mean-item">
              <span>公司名称</span><span>{{ smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.compName }}</span>
            </div>
            <div class="mean-item" v-if="smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.downloadUrl">
              <span>查看合同</span>
              <span style="color:blue;cursor: pointer;" @click="download(smrzObj.certificateInfo.downloadUrl)">点击下载</span>
              <!-- <a style="color:blue" :href="smrzObj&&smrzObj.certificateInfo&&smrzObj.certificateInfo.downloadUrl">点击下载</a> -->
            </div>
            <!-- <div class="mean-item" v-if="smrzObj&&smrzObj.certificateInfo&&smrzObj.certificateInfo.status == 4"><span>驳回原因</span><span>{{smrzObj&&smrzObj.certificateInfo&&smrzObj.certificateInfo.checkReason}}</span></div> -->
            <div class="mean-item" v-if="smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.status == 2">
              <span>驳回原因</span>
              <span v-if="smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.checkReason">{{
                smrzObj.certificateInfo.checkReason }}</span>
              <span v-else>{{ smrzObj.certificateInfo.auditReason }}</span>
            </div>
            <div v-if="(roleId == '14' || roleId == '12') && smrzObj.certificateInfo.status == 3" style="margin: 15px;">
              <el-button v-if="showflag" type="primary" @click="$router.push('completeAuthenInfo')">资料完善</el-button>
            </div>
          </div>
          <div v-else>
            <div class="mean-item">
              <span>状态</span>
              <span style="color:red" v-if="certiFicate == '0'">未认证</span>
              <span style="color:red" v-else-if="certiFicate == '2'">已认证</span>
            </div>
            <div class="mean-item"><span>公司名称</span><span>{{ CompName }}</span></div>
            <div v-if="(roleId == '14' || roleId == '12') && certiFicate == 2" class="mean-item">
              <span>完善信息状态</span>
              <span v-if="showflag === true" style="color: #F56C6C;">未完善</span>
              <span v-else-if="showflag === false" style="color: #67C23A;">已完善</span>
            </div>
            <div v-if="(roleId == '14' || roleId == '12') && certiFicate == 2" style="margin: 15px;">
              <el-button v-if="showflag" type="primary" @click="$router.push('completeAuthenInfo')">资料完善</el-button>
            </div>
          </div>
          <!-- <div class="mean-item"><span>证照编号</span><span>{{smrzObj&&smrzObj.certificateInfo&&smrzObj.certificateInfo.number}}</span></div> -->
          <!-- <div style="margin-left: 30px;margin-top:10px"><div>证照和授权件</div>
                <div >
                  <img v-if="smrzObj&&smrzObj.certificateInfo&&smrzObj.certificateInfo.authorization" :src="API.imgU+smrzObj.certificateInfo.authorization" alt="" srcset="" width="200" height="200">
                  <img v-if="smrzObj&&smrzObj.certificateInfo&&smrzObj.certificateInfo.businessLicense" :src="API.imgU+smrzObj.certificateInfo.businessLicense" alt="" srcset="" width="200" height="200">
                </div>
              </div> -->
        </div>
        <!-- certiFicate：0待认证；1已认证 -->
        <!-- smrzObj.certificate true可认证；false不可认证 -->
        <div class="btn" v-if="smrzObj.certificate">
          <el-button v-if="smrzObj.certificateInfo && smrzObj.certificateInfo.status == 1" type="primary"
            @click="handSmrz('2')">签署合同</el-button>
          <el-button v-else-if="certiFicate != '2'" type="primary" @click="handSmrz('1')">开始认证</el-button>
        </div>

        <!-- <div class="btn" v-if="smrzObj.certificate&&smrzObj && smrzObj.certificateInfo && smrzObj.certificateInfo.status == 1"
          @click="handSmrz('2')"></div> -->
      </div>

    </div>
    <!-- <el-dialog
  title="认证"
  :visible.sync="smrzFlag"
  width="50%"
   @close="closeFlags">
  <smrz :mockParent="closeFlag" :SMRZObj="smrzObj&&smrzObj"/>
</el-dialog> -->
    <el-dialog :visible.sync="smrzFlag" width="60%" :modal-append-to-body='false' @close="closeFlags">
      <div class="lega_authentication">
        <div class="lega">
          <div class="lega_title">我是企业法定代表人</div>
          <div class="lega_content">
            <div>认证前需要准备：</div>
            <div>本人身份证正反面</div>
            <div>营业执照原件的高清照片</div>
            <div>请确认您是企业法定代表人</div>
          </div>
          <div class="lega_btn">
            <el-button style="width:100px" type="primary" @click="lega()">去认证</el-button>
          </div>
        </div>
        <!-- <div class="lega">
          <div class="lega_title">我是企业代理人</div>
          <div class="lega_content">
            <div>认证前需要准备：</div>
            <div>本人身份证正反面</div>
            <div>营业执照原件的高清照片</div>
            <div>内有<a style="color:blue" href="https://doc.zthysms.com/Public/Uploads/2021-04-27/608774d492ce7.docx">授权书模板</a>，请下载并加盖公章</div>
          </div>
          <div class="lega_btn">
            <el-button style="width:100px" type="primary"  @click="agent()">去认证</el-button>
          </div>
      </div> -->
        <div class="lega">
          <div class="lega_title">我是个体工商户经营者</div>
          <div class="lega_content">
            <div>认证前需要准备：</div>
            <div>本人身份证正反面</div>
            <div>营业执照原件的高清照片</div>
            <div>请确认您是个体工商户的经营者</div>
          </div>
          <div class="lega_btn">
            <el-button style="width:100px" type="primary" @click="Individual()">去认证</el-button>
          </div>
        </div>
      </div>
    </el-dialog>

  </div>
</template>
<script>
import smrz from './smrz'
import axios from 'axios'
import getNoce from '../../../../../plugins/getNoce';
export default {
  name: "authentication",
  components: {
    smrz
  },
  data() {
    var idCard = (rule, value, callback) => {
      // 1 "验证通过!", 0 //校验不通过 // id为身份证号码
      var format = /^(([1][1-5])|([2][1-3])|([3][1-7])|([4][1-6])|([5][0-4])|([6][1-5])|([7][1])|([8][1-2]))\d{4}(([1][9]\d{2})|([2]\d{3}))(([0][1-9])|([1][0-2]))(([0][1-9])|([1-2][0-9])|([3][0-1]))\d{3}[0-9xX]$/;
      //号码规则校验
      if (value) {
        if (!format.test(value)) {
          return callback(new Error("身份证号码不合规"));
        } else {
          //区位码校验
          //出生年月日校验  前正则限制起始年份为1900;
          var year = value.substr(6, 4), //身份证年
            month = value.substr(10, 2), //身份证月
            date = value.substr(12, 2), //身份证日
            time = Date.parse(month + "-" + date + "-" + year), //身份证日期时间戳date
            now_time = Date.parse(new Date()), //当前时间戳
            dates = new Date(year, month, 0).getDate(); //身份证当月天数
          if (time > now_time || date > dates) {
            return callback(new Error("出生日期不合规"));
          }
          //校验码判断
          var c = new Array(
            7,
            9,
            10,
            5,
            8,
            4,
            2,
            1,
            6,
            3,
            7,
            9,
            10,
            5,
            8,
            4,
            2
          ); //系数
          var b = new Array(
            "1",
            "0",
            "X",
            "9",
            "8",
            "7",
            "6",
            "5",
            "4",
            "3",
            "2"
          ); //校验码对照表
          var id_array = value.split("");
          var sum = 0;
          for (var k = 0; k < 17; k++) {
            sum += parseInt(id_array[k]) * parseInt(c[k]);
          }
          if (id_array[17].toUpperCase() != b[sum % 11].toUpperCase()) {
            return callback(new Error("身份证校验码不合规"));
          }
          callback();
        }
      } else {
        return callback(new Error("请输入身份证号码"));
      }

    };
    var phone = (rule, value, callback) => {
      if (value) {
        if (!/^(13[0-9]|14[01456879]|15[0-35-9]|16[2567]|17[0-8]|18[0-9]|19[0-35-9])\d{8}$/.test(value)) {
          return callback(new Error("请输入正确的手机号"));
        } else {
          callback();
        }
      } else {
        return callback(new Error("请输入被授权人手机号"));
      }
    };
    return {
      hostname: window.location.hostname,
      nameTile: ["partner.zthysms.com", "partner.yameidu.com"],
      hostflag: true,
      ClickTrue: true,
      smrzFlag: false,
      showflag: false,
      roleId: "",
      nmb: 120,
      btnflag: true,
      statusType: "1",
      certiFicate: "",
      CompName: "",
      action: this.API.cpus + "v3/file/upload",
      token: {},
      smrzObj: {},
      fileList: [],
      formData: {
        compName: "",
        idCard: "",
        image: "",
        name: "",
        number: "",
        phone: "",
        validCode: "",
      },
      registerData: {
        appKey: "",
        phone: "",
        scene: "",
        sessionId: "",
        sig: "",
        token: "",
      },
      formRule: {
        compName: [
          {
            required: true,
            message:
              "请输入公司/机构/政府企事业单位名称，并与您提交的资质保持一致",
            trigger: "blur",
          },
        ],
        idCard: [{ required: true, validator: idCard, trigger: "blur" }],
        phone: [{ required: true, validator: phone, trigger: "blur" }],
        number: [
          {
            required: true,
            message: "请输入证照编号，并与您提交的资质保持一致",
            trigger: "blur",
          },
        ],
        name: [
          { required: true, message: "请输入被授权人的姓名", trigger: "blur" },
        ],
        validCode: [
          { required: true, message: "请填写手机验证码", trigger: "blur" },
          { min: 6, max: 6, message: "短信验证码需6位字符", trigger: "blur" },
        ],
      },
    };
  },
  created() {
    let data = JSON.parse(localStorage.getItem("userInfo"))
    this.roleId = data.roleId
    this.token = {
      Authorization: "Bearer" + this.$common.getCookie("ZTGlS_TOKEN"),
    };
    this.nameTile.forEach((item) => {
      // console.log(item);
      if (this.hostname == item) {
        // console.log(1);
        this.hostflag = false;
      }
    });
    this.init(this.roleId)
  },
  // activated(){
  //   this.init()
  // },
  methods: {
    // handleRemove(file, fileList) {
    //   console.log(file, fileList);
    // },
    // handlePreview(file) {
    //   console.log(file);
    // },
    // handleSuccess(res, file, fileList) {

    //  this.formData.image += res.data.fullpath+','
    //  console.log(this.formData.image);

    // },
    // show(val){
    //   // console.log(1111,'llll');
    //   console.log(val,'zzzz');
    //   this.smrzObj = val
    //   // console.log(this.smrzObj ,'kkk');
    // },
    lega() {
      if (this.statusType == '1') {
        this.$router.push(
          {
            path: '/LegalPerson',
            query: {
              type: 1
            }
          }
        )
        this.smrzFlag = false
      } else {
        this.$router.push(
          {
            path: '/LegalPerson',
            query: {
              type: 1,
              status: 1
            }
          }
        )
        this.smrzFlag = false
      }

    },
    agent() {
      if (this.statusType == '1') {
        this.$router.push(
          {
            path: '/BusinessAgent',
            query: {
              type: 2
            }
          }
        )
        this.smrzFlag = false
      } else {
        this.$router.push(
          {
            path: '/BusinessAgent',
            query: {
              type: 2,
              status: 1
            }
          }
        )
        this.smrzFlag = false
      }

    },
    Individual() {
      this.$router.push(
        {
          path: '/Individual',
          query: {
            type: 3
          }
        }
      )
      this.smrzFlag = false
    },
    handSmrz(tag) {
      this.smrzFlag = true
      this.statusType = tag
      // this.initDrag()
      // window.nc.show();
    },
    closeFlags() {
      // window.nc.reset();
      this.smrzFlag = false
    },
    //合同下载
    async download(url) {
      let name = new Date().getTime();
      const nonce = await getNoce.useNonce();
      axios({
        method: "get",
        url:
          this.API.cpus +
          "v3/file/downloadFile?fileName=" +
          name +
          "&group=oss" +
          "&path=" +
          url + '&bucketName=contract',
        data: {},
        headers: {
          "Content-Type": "application/json",
          Authorization:
            "Bearer " + window.Vue.$common.getCookie("ZTADMIN_TOKEN"),
          'Once': nonce,
        },
        responseType: 'blob',
      })
        .then(function (res) {
          const blob = new Blob([res.data], { type: 'application/octet-stream;charset=utf-8' })
          let link = document.createElement("a");
          let href = window.URL.createObjectURL(blob); //下载链接
          link.href = href;
          link.text = "下载";
          link.download = name + ".PDF"; //下载后文件名
          document.body.appendChild(link);
          link.click(); //点击下载
          document.body.removeChild(link); //下载完成移除元素
          window.URL.revokeObjectURL(href);
          that.$message({
            message: '下载成功',
            type: "success",
          });
        })
        .catch(err => {
          console.log(err, 'err');
          that.$message({
            message: '下载失败',
            type: "error",
          });
        })
    },
    // closeFlag(){


    //   this.$api.get(this.API.cpus + 'consumerCertificate/info',{},res=>{
    //     console.log(res,'res');
    //     this.smrzObj = res.data
    //         })

    //   this.btnflag = false
    //   this.smrzFlag = false
    // },
    init(roleId) {
      this.$api.get(this.API.cpus + 'consumerCertificate/info', {}, res => {
        if (res.code == 200) {
          this.smrzObj = res.data
          this.$api.get(
            this.API.cpus + "consumerclientinfo/getClientInfo",
            null,
            (res) => {
              // console.log(res, 'res');
              this.certiFicate = res.data.certificate;
              this.CompName = res.data.compName;
              // this.certificate = res.data.certificate;
            }
          );
        }
      })
      if (roleId == '14' || roleId == '12') {
        this.$api.get(this.API.cpus + "consumerclientinfo/checkRealStatus", {},    // 接口地址 {},
          (res) => {
            if (res.code === 200) {
              this.showflag = res.data
            }
          })
      }
    }

  },

  mounted() {
    // this.initDrag();
  },
  computed: {
    // smrzObj(){ 
    //   return this.smrzObj.certificateInfo.status  || ''
    // }
  },

  watch: {
    smrzObj(val, newval) {

      // console.log(val,'val');
      // console.log(newval,'new');
      // this.smrzObj = val
    }
  },
};
</script>
<style scoped>
@media screen and (min-width: 1200px) {
  .lega_authentication {
    display: flex;
    justify-content: space-around;
  }

  .lega {
    width: 410px;
    height: 180px;
    background: #fff;
    border-radius: 5px;
    box-shadow: 0px 0px 5px 1px #eee;
    padding: 20px;
    position: relative;
    margin: 10px;
  }

  .lega_title {
    font-weight: 800;
    font-size: 18px;
    color: #000;
  }

  .lega_content {
    /* height: 100px; */
    margin-top: 15px;
    line-height: 23px;
  }

  .lega_btn {
    position: absolute;
    bottom: 20px;
    left: 50%;
    margin-left: -60px;
  }

  .smrz {
    width: 100%;
    background: #fff;
  }

  .smrz-h {
    width: 100%;
    padding: 25px;
  }

  .smrz-mian {
    width: 100%;

  }

  .smrn-h-t {
    margin-top: 10px;
    width: 970px;
    height: 30px;
    border-bottom: 1px solid #ccc;
  }

  .smrz-m-h {
    width: 400px;
    margin-left: 20px;
  }

  .smrz-p {
    margin-left: 15px;
    font-weight: 800;
  }

  .smrz-x {
    background: #fff2d1;
    color: #906e12;
    border-radius: 10px;
    padding: 15px;
    line-height: 25px;
    width: 600px;
    margin-top: 15px;
  }

  .smrz-mean {
    width: 500px;
    margin-left: 15px;
  }

  .mean-h {
    width: 100%;
    height: 50px;
    line-height: 50px;

  }

  .mean-h span {
    margin-left: 20px;
    color: #1e7cfc;
  }

  .mean-item {
    width: 80%;
    height: 30px;
    margin-left: 30px;
    border-bottom: 1px solid #ccc;
    line-height: 33px;
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
  }

  .btn {
    margin: 20px;

  }
}

@media screen and (max-width: 1200px) {

  /* .lega_authentication{
  display: flex;
  justify-content: space-around;
} */
  .lega {
    width: 100%;
    height: 170px;
    background: #fff;
    border-radius: 5px;
    box-shadow: 0px 0px 5px 1px #eee;
    padding: 5px;
    margin: 10px 0;
    position: relative;
  }

  .lega_title {
    font-weight: 800;
    font-size: 14px;
    color: #000;
  }

  .lega_content {
    /* height: 100px; */
    margin-top: 15px;
    line-height: 23px;
  }

  .lega_btn {
    position: absolute;
    bottom: 10px;
    left: 50%;
    margin-left: -60px;
    /* margin-top: 10px; */
    /* margin-left: 50%; */
  }

  .smrz {
    width: 100%;
    background: #fff;
  }

  .smrz-h {
    width: 90%;
    padding: 0 10px;
  }

  .smrz-mian {
    width: 100%;

  }

  .smrz-m-h {
    width: 100%;
    margin-left: 10px;
  }

  .smrz-p {
    margin-left: 15px;
    font-weight: 800;
  }

  .smrz-x {
    background: #fff2d1;
    color: #906e12;
    border-radius: 10px;
    padding: 10px;
    line-height: 25px;
    width: 100%;
    margin-top: 15px;
    font-size: 12px;
  }

  .smrz-mean {
    width: 100%;
    margin-left: 10px;
  }

  .mean-h {
    width: 100%;
    height: 50px;
    line-height: 50px;

  }

  .mean-h span {
    margin-left: 20px;
    color: #1e7cfc;
  }

  .mean-item {
    width: 80%;
    height: 30px;
    margin-left: 30px;
    border-bottom: 1px solid #ccc;
    line-height: 33px;
    display: flex;
    justify-content: space-between;
    margin-top: 5px;
  }

  .btn {
    margin: 20px;

  }
}

/* .btn button {
  width: 80px;
  height: 30px;
  line-height: 30px;
  text-align: center;
  border-radius: 5px;
  color: #1e7cfc;
  cursor: pointer;
  outline: none;
} */
</style>