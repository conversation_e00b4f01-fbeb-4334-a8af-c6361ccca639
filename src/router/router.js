import Vue from 'vue'
import Router from 'vue-router'
Vue.use(Router)
// 解决报错
// const originalPush = Router.prototype.push
// const originalReplace = Router.prototype.replace
// // push
// Router.prototype.push = function push (location, onResolve, onReject) {
//   if (onResolve || onReject) return originalPush.call(this, location, onResolve, onReject)
//   return originalPush.call(this, location).catch(err => err)
// }
// // replace
// Router.prototype.replace = function push (location, onResolve, onReject) {
//   if (onResolve || onReject) return originalReplace.call(this, location, onResolve, onReject)
//   return originalReplace.call(this, location).catch(err => err)
// }

export default new Router({
  mode: 'hash',
  routes: [
    {
      path: '/',
      redirect: '/home',
      meta:{
        requireAuth:true
      },
    },
    {
      path: '/',
      component: ()=> import('../components/common/Home.vue'),
      // component: resolve => require(['../components/common/Home.vue'], resolve),
      meta: { title: '自述文件' },
      children: [
        {
          path: '/home',
          component: ()=> import('../components/page/home/<USER>'),
          name: 'home',
          // component: resolve => require(['../components/page/gls/Dashboard.vue'], resolve),
          meta: { keepAlive: false, title: '系统首页' }
        },
        {
          path: '/account',
          component: resolve => require(['../components/page/gls/accountInformation/PersonalInformation/account.vue'], resolve),
          meta: { keepAlive: false, title: '账户信息' },
          name: 'account',
        },
        {
          path: '/UserRecord',
          component: resolve => require(['../components/page/gls/SMS/statisticalAnalysis/UserRecord.vue'], resolve),
          meta: { keepAlive: false, title: '短信统计分析' },
          name: 'UserRecord',
        },
        {
          path: '/MonthRecord',
          component: resolve => require(['../components/page/gls/SMS/statisticalAnalysis/MonthRecord.vue'], resolve),
          meta: { keepAlive: false, title: '短信月发送统计' },
          name: 'MonthRecord',
        },
        {
          path: '/subSendDetails',
          component: resolve => require(['../components/page/gls/SMS/statisticalAnalysis/subSendDetails.vue'], resolve),
          meta: { keepAlive: false, title: '子用户短信发送明细' },
          name: 'subSendDetails',
        },
        {
          path: '/subSmsRecord',
          component: resolve => require(['../components/page/gls/SMS/statisticalAnalysis/subSmsRecord.vue'], resolve),
          meta: { keepAlive: false, title: '子用户短信回复明细' },
          name: 'subSmsRecord',
        },
        {
          path: '/MMSuserRecord',
          component: resolve => require(['../components/page/gls/MMS/MMSuserRecord.vue'], resolve),
          meta: { keepAlive: false, title: '彩信统计分析' },
          name: 'MMSuserRecord',
        },
        {
          path: '/MMSmonthRecord',
          component: resolve => require(['../components/page/gls/MMS/MMSmonthRecord.vue'], resolve),
          meta: { keepAlive: false, title: '彩信月统计分析' },
          name: 'MMSmonthRecord',
        },
        {
          path: '/subMMSDetails',
          component: resolve => require(['../components/page/gls/MMS/subMMSDetails.vue'], resolve),
          meta: { keepAlive: false, title: '子用户彩信发送明细' },
          name: 'subMMSDetails',
        },
        {
          path: '/videoUserRecord',
          component: resolve => require(['../components/page/gls/VideoRMS/videoUserRecord.vue'], resolve),
          meta: { keepAlive: false, title: '视频短信统计分析' },
          name: 'videoUserRecord',
        },
        {
          path: '/videoMonthRecord',
          component: resolve => require(['../components/page/gls/VideoRMS/videoMonthRecord.vue'], resolve),
          meta: { keepAlive: false, title: '视频短信月统计分析' },
          name: 'videoMonthRecord',
        },
        {
          path: '/subVideoDetails',
          component: resolve => require(['../components/page/gls/VideoRMS/subVideoDetails.vue'], resolve),
          meta: { keepAlive: false, title: '子用户视频短信发送明细' },
          name: 'subVideoDetails',
        },
        {
          path: '/ImsUserRecord',
          component: resolve => require(['../components/page/gls/IMS/ImsUserRecord.vue'], resolve),
          meta: { keepAlive: false, title: '国际短信发送统计' },
          name: 'ImsUserRecord',
        },
        {
          path: '/ImsMonthRecord',
          component: resolve => require(['../components/page/gls/IMS/ImsMonthRecord.vue'], resolve),
          meta: { keepAlive: false, title: '国际短信月发送统计' },
          name: 'ImsMonthRecord',
        },
        {
          path: '/subImsDetails',
          component: resolve => require(['../components/page/gls/IMS/subImsDetails.vue'], resolve),
          meta: { keepAlive: false, title: '子用户国际短信发送明细' },
          name: 'subImsDetails',
        },
        {
          path: '/ImsPirce',
          component: resolve => require(['../components/page/gls/IMS/ImsPirce.vue'], resolve),
          meta: { keepAlive: false, title: '国际价格表' },
          name: 'ImsPirce',
        },
        {
          path: '/UserManagement',
          component: resolve => require(['../components/page/gls/UserManagement/UserManagement.vue'], resolve),
          meta: { keepAlive: false, title: '用户管理' },
          name: 'UserManagement',
        },
        {
          path: '/subSignatureManagement',
          component: resolve => require(['../components/page/gls/SMS/signature/subSignatureManagement.vue'], resolve),
          meta: { keepAlive: false, title: '子用户签名管理' },
          name: 'subSignatureManagement',
        },
        {
          path: '/settingPhone',
          component: resolve => require(['../components/page/gls/UserManagement/settingPhone.vue'], resolve),
          meta: { keepAlive: false, title: '子用户登录手机号管理' },
          name: 'settingPhone',
        },
        {
          path: '/UserEditing',
          component: resolve => require(['../components/page/gls/UserManagement/UserEditing.vue'], resolve),
          meta: { keepAlive: false, title: '用户新增编辑' },
          name: 'UserEditing',
        },
        {
          path: '/UserDetails',
          component: resolve => require(['../components/page/gls/UserManagement/UserDetails.vue'], resolve),
          meta: { keepAlive: false, title: '用户详情' },
          name: 'UserDetails',
        },
        {
          path: '/userSignature',
          component: resolve => require(['../components/page/gls/UserManagement/userSignature.vue'], resolve),
          meta: { keepAlive: false, title: '创建子用户签名' },
          name: 'userSignature',
        },
        {
          path: '/BalanceManagement',
          component: resolve => require(['../components/page/gls/CostCenter/BalanceManagement.vue'], resolve),
          meta: { keepAlive: false, title: '余额管理' },
          name: 'BalanceManagement',
        },
        {
          path: '/GlsRecord',
          component: resolve => require(['../components/page/gls/CostCenter/GlsRecord.vue'], resolve),
          meta: { keepAlive: false, title: '用户充值记录' },
          name: 'GlsRecord',
        },
        {
          path: '/MindGlsRecord',
          component: resolve => require(['../components/page/gls/CostCenter/MindGlsRecord.vue'], resolve),
          meta: { keepAlive: false, title: '我的充值' },
          name: 'MindGlsRecord',
        },
        {
          path: '/sendDetails',
          component: resolve => require(['../components/page/client/SMS/sendMessages/sendDetails.vue'], resolve),
          meta: { keepAlive: true, title: '发送短信' },
          name: 'sendDetails',
        },
        {
          path: '/PersonalizedDelivery',
          component: resolve => require(['../components/page/client/SMS/PersonalizedDelivery/PersonalizedDelivery.vue'], resolve),
          meta: { keepAlive: false, title: '个性化发送' },
          name: 'PersonalizedDelivery',
        },
        {
          path: '/SendTask',
          component: resolve => require(['../components/page/client/SMS/SendTask/SendTask.vue'], resolve),
          meta: { keepAlive: false, title: '发送任务管理' },
          name: 'SendTask',
        },
        {
          path: '/PersonalizedSendEditing',
          component: resolve => require(['../components/page/client/SMS/SendTask/PersonalizedSendEditing.vue'], resolve),
          meta: { keepAlive: false, title: '个性化编辑' },
          name: 'PersonalizedSendEditing',
        },
        {
          path: '/SendSMSEdit',
          component: resolve => require(['../components/page/client/SMS/SendTask/SendSMSEdit.vue'], resolve),
          meta: { keepAlive: false, title: '发送短信编辑' },
          name: 'SendSMSEdit',
        },
        {
          path: '/TemplateManagement',
          component: resolve => require(['../components/page/client/SMS/SMSContentConfiguration/components/TemplateManagement.vue'], resolve),
          meta: { keepAlive: false, title: '模板管理' },
          name: 'TemplateManagement',
        },
        {
          path: '/CreateTemplate',
          component: resolve => require(['../components/page/client/SMS/SMSContentConfiguration/components/CreateTemplate.vue'], resolve),
          meta: { keepAlive: false, title: '添加模板' },
          name: 'CreateTemplate',
        },
        {
          path: '/NewCreateTemplate',
          component: resolve => require(['../components/page/client/SMS/SMSContentConfiguration/components/CreateTemplate1.vue'], resolve),
          meta: { keepAlive: false, title: '添加模板' },
          name: 'NewCreateTemplate',
        },
        {
          path: '/SignatureManagement',
          component: resolve => require(['../components/page/client/SMS/SMSContentConfiguration/components/SignatureManagement.vue'], resolve),
          meta: { keepAlive: false, title: '签名管理' },
          name: 'SignatureManagement',
        },
        {
          path: '/smsMutual',
          component: resolve => require(['../components/page/client/SMS/smsMutual/mutual.vue'], resolve),
          meta: { keepAlive: false, title: '短信自动回复' },
          name: 'smsMutual',
        },
        {
          path: '/CreateSign',
          component: resolve => require(['../components/page/client/SMS/SMSContentConfiguration/components/CreateSign.vue'], resolve),
          meta: { keepAlive: true, title: '添加签名' },
          name: 'CreateSign',
        },
        {
          path: '/contentExample',
          component: resolve => require(['../components/page/client/SMS/SMSContentConfiguration/components/contentExample.vue'], resolve),
          meta: { keepAlive: true, title: '短信示例管理' },
          name: 'contentExample',
        },
        {
          path: '/timing',
          component: resolve => require(['../components/page/client/SMS/timing/timing.vue'], resolve),
          meta: { keepAlive: false, title: '定时管理' },
          name: 'timing',
        },
        {
          path: '/statisticalAnalysis',
          component: resolve => require(['../components/page/client/SMS/statisticalAnalysis/statisticalAnalysis.vue'], resolve),
          meta: { keepAlive: false, title: '统计分析' },
          name: 'statisticalAnalysis',
        },
        {
          path: '/webSend',
          component: resolve => require(['../components/page/client/SMS/statisticalAnalysis/webSend.vue'], resolve),
          meta: { keepAlive: false, title: 'web发送记录' },
          name: 'webSend',
        },
        {
          path: '/SmsRecord',
          component: resolve => require(['../components/page/client/SMS/statisticalAnalysis/components/SmsRecord.vue'], resolve),
          meta: { keepAlive: false, title: '回复记录' },
          name: 'SmsRecord',
        },
        {
          path: '/ShortMessageRecording2',
          component: resolve => require(['../components/page/client/SMS/statisticalAnalysis/ShortMessageRecording2.vue'], resolve),
          meta: { keepAlive: false, title: '发送详情' },
          name: 'ShortMessageRecording2',
        },
        {
          path: '/smsPhoneSearch',
          component: resolve => require(['../components/page/client/SMS/statisticalAnalysis/smsPhoneSearch.vue'], resolve),
          meta: { keepAlive: false, title: '号码状态查询' },
          name: 'smsPhoneSearch',
        },
        {
          path: '/FileExport',
          component: resolve => require(['../components/page/client/FileExport/FileExport.vue'], resolve),
          meta: { keepAlive: false, title: '文件下载中心' },
          name: 'FileExport',
        },
        {
          path: '/MMSsend',
          component: resolve => require(['../components/page/client/MMS/MMSsend.vue'], resolve),
          meta: { keepAlive: false, title: '创建彩信' },
          name: 'MMSsend',
        },
        {
          path: '/MMStemplate',
          component: resolve => require(['../components/page/client/MMS/MMStemplate.vue'], resolve),
          meta: { keepAlive: false, title: '彩信草稿管理' },
          name: 'MMStemplate',
        },
        {
          path: '/MMSdraft',
          component: resolve => require(['../components/page/client/MMS/MMSdraft.vue'], resolve),
          meta: { keepAlive: false, title: '彩信模板管理' },
          name: 'MMSdraft',
        },
        {
          path: '/MMSWebTask',
          component: resolve => require(['../components/page/client/MMS/MMSWebTask.vue'], resolve),
          meta: { keepAlive: false, title: 'web发送任务' },
          name: 'MMSWebTask',
        },
        {
          path: '/MMSinterfaceSend',
          component: resolve => require(['../components/page/client/MMS/MMSinterfaceSend.vue'], resolve),
          meta: { keepAlive: false, title: '彩信接口发送任务' },
          name: 'MMSinterfaceSend',
        },
        {
          path: '/templateCreate',
          component: resolve => require(['../components/page/client/MMS/components/templateCreate/templateCreate.vue'], resolve),
          meta: { keepAlive: false, title: '彩信模板' },
          name: 'templateCreate',
        },
        {
          path: '/MMSoverview',
          component: resolve => require(['../components/page/client/MMSstatistics/MMSoverview.vue'], resolve),
          meta: { keepAlive: false, title: '彩信统计' },
          name: 'MMSoverview',
        },
        {
          path: '/MMSSendDetails',
          component: resolve => require(['../components/page/client/MMSstatistics/MMSSendDetails.vue'], resolve),
          meta: { keepAlive: false, title: '彩信发送详情' },
          name: 'MMSSendDetails',
        },
        {
          path: '/MMSReplyRecord',
          component: resolve => require(['../components/page/client/MMSstatistics/MMSReplyRecord.vue'], resolve),
          meta: { keepAlive: false, title: '彩信回复记录' },
          name: 'MMSReplyRecord',
        },
        {
          path: '/VideoRMS',
          component: resolve => require(['../components/page/client/VideoRMS/VideoRMS.vue'], resolve),
          meta: { keepAlive: false, title: '视频短信' },
          name: 'VideoRMS',
        },
        {
          path: '/videoTemplate',
          component: resolve => require(['../components/page/client/VideoRMS/videoTemplate.vue'], resolve),
          meta: { keepAlive: false, title: '视频短信模板管理' },
          name: 'videoTemplate',
        },
        {
          path: '/VideoWebTask',
          component: resolve => require(['../components/page/client/VideoRMS/VideoWebTask.vue'], resolve),
          meta: { keepAlive: false, title: '视频短信Web任务' },
          name: 'VideoWebTask',
        },
        {
          path: '/VideoInterfaceSend',
          component: resolve => require(['../components/page/client/VideoRMS/VideoInterfaceSend.vue'], resolve),
          meta: { keepAlive: false, title: '视频短信接口任务' },
          name: 'VideoInterfaceSend',
        },
        {
          path: '/videoTemplateCreate',
          component: resolve => require(['../components/page/client/VideoRMS/components/videoTemplateCreate/videoTemplateCreate.vue'], resolve),
          meta: { keepAlive: false, title: '视频短信模板' },
          name: 'videoTemplateCreate',
        },
        {
          path: '/VideoOverview',
          component: resolve => require(['../components/page/client/Videostatistics/VideoOverview.vue'], resolve),
          meta: { keepAlive: false, title: '视频短信统计' },
          name: 'VideoOverview',
        },
        {
          path: '/VideoSendDetails',
          component: resolve => require(['../components/page/client/Videostatistics/VideoSendDetails.vue'], resolve),
          meta: { keepAlive: false, title: '视频短信发送明细' },
          name: 'VideoSendDetails',
        },
        {
          path: '/VideoReplyRecord',
          component: resolve => require(['../components/page/client/Videostatistics/VideoReplyRecord.vue'], resolve),
          meta: { keepAlive: false, title: '视频短信回复记录' },
          name: 'VideoReplyRecord',
        },
        {
          path: '/ImsSend',
          component: resolve => require(['../components/page/client/IMS/ImsSend.vue'], resolve),
          meta: { keepAlive: false, title: '国际短信发送' },
          name: 'ImsSend',
        },
        {
          path: '/ImsSendTask',
          component: resolve => require(['../components/page/client/IMS/ImsSendTask.vue'], resolve),
          meta: { keepAlive: false, title: '国际短信接口任务' },
          name: 'ImsSendTask',
        },
        {
          path: '/InternationalPrice',
          component: resolve => require(['../components/page/client/IMS/InternationalPrice.vue'], resolve),
          meta: { keepAlive: false, title: '国际短信价格表' },
          name: 'InternationalPrice',
        },
        {
          path: '/ImsOverview',
          component: resolve => require(['../components/page/client/IMS/ImsOverview.vue'], resolve),
          meta: { keepAlive: false, title: '国际短信统计' },
          name: 'ImsOverview',
        },
        {
          path: '/ImsSendDetails',
          component: resolve => require(['../components/page/client/IMS/ImsSendDetails.vue'], resolve),
          meta: { keepAlive: false, title: '国际短信发送明细' },
          name: 'ImsSendDetails',
        },
        {
          path: '/ImsReplyRecord',
          component: resolve => require(['../components/page/client/IMS/ImsReplyRecord.vue'], resolve),
          meta: { keepAlive: false, title: '国际短信回复记录' },
          name: 'ImsReplyRecord',
        },
        {
          path: '/onelogin',
          component: resolve => require(['../components/page/client/flashTest/localNumber/onelogin.vue'], resolve),
          meta: { keepAlive: false, title: '应用配置' },
          name: 'onelogin',
        },
        {
          path: '/oneloginStatistics',
          component: resolve => require(['../components/page/client/flashTest/localNumber/oneloginStatistics.vue'], resolve),
          meta: { keepAlive: false, title: '数据统计' },
          name: 'oneloginStatistics',
        },
        {
          path: '/onepass',
          component: resolve => require(['../components/page/client/flashTest/localNumber/onepass.vue'], resolve),
          meta: { keepAlive: false, title: '校验应用配置' },
          name: 'onepass',
        },
        {
          path: '/onepassStatistics',
          component: resolve => require(['../components/page/client/flashTest/localNumber/onepassStatistics.vue'], resolve),
          meta: { keepAlive: false, title: '校验数据统计' }
        },
        {
          path: '/totalStatistics',
          component: resolve => require(['../components/page/client/flashTest/totalStatistics.vue'], resolve),
          meta: { keepAlive: false, title: '总计费统计' },
          name: 'totalStatistics',
        },
        {
          path: '/voiceCode',
          component: resolve => require(['../components/page/client/voice/voiceSend/voiceCode.vue'], resolve),
          meta: { keepAlive: false, title: '语音验证码' },
          name: 'voiceCode',
        },
        {
          path: '/voiceNotice',
          component: resolve => require(['../components/page/client/voice/voiceSend/voiceNotice.vue'], resolve),
          meta: { keepAlive: false, title: '语音群通知' },
          name: 'voiceNotice',
        },
        {
          path: '/voicePersonalise',
          component: resolve => require(['../components/page/client/voice/voiceSend/voicePersonalise.vue'], resolve),
          meta: { keepAlive: false, title: '个性化通知' },
          name: 'voicePersonalise',
        },
        {
          path: '/VoiceWebTask',
          component: resolve => require(['../components/page/client/voice/voiceSend/voiceWebTask.vue'], resolve),
          meta: { keepAlive: false, title: '语音web任务' },
          name: 'VoiceWebTask',
        },
        {
          path: '/VoiceInterfaceSend',
          component: resolve => require(['../components/page/client/voice/voiceSend/voiceInterfaceSend.vue'], resolve),
          meta: { keepAlive: false, title: '语音接口任务' },
          name: 'VoiceInterfaceSend',
        },
        {
          path: '/voiceSendDetails',
          component: resolve => require(['../components/page/client/voice/statistics/voiceSendDetails.vue'], resolve),
          meta: { keepAlive: false, title: '语音发送明细' },
          name: 'voiceSendDetails',
        },
        {
          path: '/voiceSpeech',
          component: resolve => require(['../components/page/client/voice/speechOverview/speechOverview.vue'], resolve),
          meta: { keepAlive: false, title: '语音短信统计' },
          name: 'voiceSpeech',
        },
        // {
        //   path: '/voiceSpeech',
        //   component: resolve => require(['../components/page/client/voice/speechOverview/speechOverview.vue'], resolve),
        //   meta: { keepAlive: false, title: '语音短信统计' }
        // },
        {
          path: '/RechargeRecord',
          component: resolve => require(['../components/page/client/CostCenter/RechargeRecord.vue'], resolve),
          meta: { keepAlive: false, title: '充值记录' },
          name: 'RechargeRecord',
        },
        {
          path: '/shortApply',
          component: resolve => require(['../components/page/client/shortChainMag/shortApply.vue'], resolve),
          meta: { keepAlive: false, title: '白名单申请' },
          name: 'shortApply',
        },
        {
          path: '/shortChainCvs',
          component: resolve => require(['../components/page/client/shortChainMag/shortChainCvs.vue'], resolve),
          meta: { keepAlive: false, title: '短链转换' },
          name: 'shortChainCvs',
        },
        {
          path: '/Shortchainstatistics',
          component: resolve => require(['../components/page/client/shortChainMag/Shortchainstatistics.vue'], resolve),
          meta: { keepAlive: false, title: '短链统计' },
          name: 'Shortchainstatistics',
        },
        {
          path: '/shortStatistics',
          component: resolve => require(['../components/page/client/shortChainMag/shortStatistics.vue'], resolve),
          meta: { keepAlive: false, title: '短链统计' },
          name: 'shortStatistics',
        },
        {
          path: '/helpCenterHome',
          component: resolve => require(['../components/page/client/helpCenter/helpCenterHome.vue'], resolve),
          meta: { keepAlive: false, title: '帮助中心' },
          name: 'helpCenterHome',
        },
        {
          path: '/showMsg',
          component: resolve => require(['../components/page/client/accountInformation/showMsg/showMsg.vue'], resolve),
          meta: { keepAlive: false, title: '消息展示' },
          name: 'showMsg',
        },
        {
          path: '/LoginCellPhone',
          component: resolve => require(['../components/page/client/accountInformation/LoginCellPhone/LoginCellPhone.vue'], resolve),
          meta: { keepAlive: false, title: '手机号管理' },
          name: 'LoginCellPhone',
        },
        {
          path: '/sendWhiteList',
          component: resolve => require(['../components/page/client/accountInformation/whiteList/whiteList.vue'], resolve),
          meta: { keepAlive: false, title: '发送IP白名单' },
          name: 'sendWhiteList',
        },
        {
          path: '/PersonalInformation',
          component: resolve => require(['../components/page/client/accountInformation/PersonalInformation/PersonalInformation.vue'], resolve),
          meta: { keepAlive: false, title: '个人信息设置' },
          name: 'PersonalInformation',
        },
        {
          path: '/NotificationAlert',
          component: resolve => require(['../components/page/client/accountInformation/NotificationAlert/NotificationAlert.vue'], resolve),
          meta: { keepAlive: false, title: '通知与预警' },
          name: 'NotificationAlert',
        },
        // {
        //   path: '/NotificationAlert',
        //   component: resolve => require(['../components/page/client/accountInformation/NotificationAlert/NotificationAlert.vue'], resolve),
        //   meta: { keepAlive: false, title: '通知与预警' }
        // },
        {
          path: '/BlacklistManagement',
          component: resolve => require(['../components/page/client/accountInformation/BlacklistManagement/BlacklistManagement.vue'], resolve),
          meta: { keepAlive: false, title: '黑名单管理' },
          name: 'BlacklistManagement',
        },
        {
          path: '/authentication',
          component: resolve => require(['../components/page/client/accountInformation/authentication/indexOline.vue'], resolve),
          meta: { keepAlive: false, title: '实名认证' },
          name: 'authentication',
        },
        // {
        //   path: '/completeInfo',
        //   component: resolve => require(['../components/page/client/completeInfo/index.vue'], resolve),
        //   meta: { keepAlive: false, title: '实名信息完善' }
        // },
        {
          path: '/completeAuthenInfo',
          component: resolve => require(['../components/page/client/completeInfo/completeAuthenInfo.vue'], resolve),
          meta: { keepAlive: false, title: '资料填写' },
          name: 'completeAuthenInfo',
        },
        {
          path: '/LegalPerson',
          component: resolve => require(['../components/page/client/accountInformation/authentications/lege.vue'], resolve),
          meta: { keepAlive: false, title: '法人认证' },
          name: 'LegalPerson',
        },
        {
          path: '/BusinessAgent',
          component: resolve => require(['../components/page/client/accountInformation/authentications/agent.vue'], resolve),
          meta: { keepAlive: false, title: '代理人认证' },
          name: 'BusinessAgent',
        },
        {
          path: '/Individual',
          component: resolve => require(['../components/page/client/accountInformation/authentications/individual.vue'], resolve),
          meta: { keepAlive: false, title: '个体工商户认证' },
          name: 'Individual',
        },
        {
          path: '/guide',
          component: resolve => require(['../components/page/home/<USER>'], resolve),
          meta: { keepAlive: false, title: '短信操作快速上手' },
          name: 'guide',
        },
        {
          path: '/ChatBotManage',
          component: resolve => require(['../components/page/5g/chatbot/chatbotList.vue'], resolve),
          meta: { keepAlive: false, title: 'ChatBot管理' },
          name: 'ChatBotManage',
        },
        {
          path: '/5gIndex',
          component: resolve => require(['../components/page/5g/5Gauthentication/index.vue'], resolve),
          meta: { keepAlive: false, title: '5g注册' },
          name: '5gIndex',
        },
        {
          path: '/chatBot',
          component: resolve => require(['../components/page/5g/chatbot/chatBot.vue'], resolve),
          meta: { keepAlive: false, title: '新增ChatBot' },
          name: 'chatBot',
        },
        {
          path: '/LoginLog',
          component: resolve => require(['../components/page/client/accountInformation/loginLog/loginLog.vue'], resolve),
          meta: { keepAlive: false, title: '登录日志' },
          name: 'LoginLog',
        },
        {
          path: '/OperationLog',
          component: resolve => require(['../components/page/client/accountInformation/OperationLog/OperationLog.vue'], resolve),
          meta: { keepAlive: false, title: '操作日志' },
          name: 'OperationLog',
        },
        // {
        //   path: '/table',
        //   component: resolve => require(['../components/page/BaseTable.vue'], resolve),
        //   meta: { title: '基础表格' }
        // },
        // {
        //   path: '/tabs',
        //   component: resolve => require(['../components/page/Tabs.vue'], resolve),
        //   meta: { title: 'tab选项卡' }
        // },
        // {
        //   path: '/form',
        //   component: resolve => require(['../components/page/BaseForm.vue'], resolve),
        //   meta: { title: '基本表单' }
        // },
        // {
        //   // 富文本编辑器组件
        //   path: '/editor',
        //   component: resolve => require(['../components/page/VueEditor.vue'], resolve),
        //   meta: { title: '富文本编辑器' }
        // },
        // {
        //   // markdown组件
        //   path: '/markdown',
        //   component: resolve => require(['../components/page/Markdown.vue'], resolve),
        //   meta: { title: 'markdown编辑器' }
        // },
        // {
        //   // 图片上传组件
        //   path: '/upload',
        //   component: resolve => require(['../components/page/Upload.vue'], resolve),
        //   meta: { title: '文件上传' }
        // },
        // {
        //   // vue-schart组件
        //   path: '/charts',
        //   component: resolve => require(['../components/page/BaseCharts.vue'], resolve),
        //   meta: { title: 'schart图表' }
        // },
        // {
        //   // 拖拽列表组件
        //   path: '/drag',
        //   component: resolve => require(['../components/page/DragList.vue'], resolve),
        //   meta: { title: '拖拽列表' }
        // },
        // {
        //   // 权限页面
        //   path: '/permission',
        //   component: resolve => require(['../components/page/Permission.vue'], resolve),
        //   meta: { title: '权限测试', }
        // },
        {
          path: '/404',
          component: resolve => require(['../components/page/404.vue'], resolve),
          meta: { title: '404' }
        },
        {
          path: '/403',
          component: resolve => require(['../components/page/403.vue'], resolve),
          meta: { title: '403' }
        }
      ]
    },
    {
      path: '/h5Index',
      component: ()=> import('../components/page/h5/home.vue'),
      // redirect: '/zdHome',
      // component: resolve => require(['../components/page/gls/Dashboard.vue'], resolve),
      meta: { title: '首页' },
      children: [
        {
          path: '/mcHome',
          component: resolve => require(['../components/page/h5/home/<USER>'], resolve),
          meta: { title: '首页' }
        },
        {
          path: '/glsHome',
          component: resolve => require(['../components/page/h5/home/<USER>'], resolve),
          meta: { title: '首页' }
        },
        {
          path: '/userInfo',
          component: resolve => require(['../components/page/h5/user/user.vue'], resolve),
          meta: { title: '个人中心' }
        }
      ]
    },
    {
      path: '/glsLoginPhone',
      component: resolve => require(['../components/page/h5/user/components/loginPhone.vue'], resolve),
      meta: { title: '登录手机号管理' }
    },
    {
      path: '/newPhone' ,
      component: resolve => require(['../components/page/h5/user/components/newPhone.vue'], resolve),
      meta: { title: '添加登录手机号' }
    },
    {
      path: '/revamppwd' ,
      component: resolve => require(['../components/page/h5/user/components/revamppwd.vue'], resolve),
      meta: { title: '修改接口密码' }
    },
    {
      path: '/revampCipher' ,
      component: resolve => require(['../components/page/h5/user/components/revampCipher.vue'], resolve),
      meta: { title: '修改接秘钥' }
    },
    {
      path: '/revampSalt' ,
      component: resolve => require(['../components/page/h5/user/components/revampSalt.vue'], resolve),
      meta: { title: '修改加密盐' }
    },
    {
      path: '/userlist' ,
      component: resolve => require(['../components/page/h5/UserManagement/userlist.vue'], resolve),
      meta: { title: '用户列表' }
    },
    {
      path: '/createUser' ,
      component: resolve => require(['../components/page/h5/UserManagement/components/createUser.vue'], resolve),
      meta: { title: '创建用户' }
    },
    {
      path: '/editUserPwd' ,
      component: resolve => require(['../components/page/h5/UserManagement/components/editUserPwd.vue'], resolve),
      meta: { title: '修改密码' }
    },
    {
      path: '/addPhone' ,
      component: resolve => require(['../components/page/h5/UserManagement/components/addPhone.vue'], resolve),
      meta: { title: '添加子用户手机号' }
    },
    {
      path: '/balanceManagment' ,
      component: resolve => require(['../components/page/h5/expenseManagement/balanceManagment.vue'], resolve),
      meta: { title: '余额管理' }
    },
    {
      path: '/recharge' ,
      component: resolve => require(['../components/page/h5/expenseManagement/recharge.vue'], resolve),
      meta: { title: '充值' }
    },
    {
      path: '/deductMoney' ,
      component: resolve => require(['../components/page/h5/expenseManagement/deductMoney.vue'], resolve),
      meta: { title: '扣款' }
    },
    {
      path: '/record' ,
      component: resolve => require(['../components/page/h5/expenseManagement/record.vue'], resolve),
      meta: { title: '充值记录' },
      redirect: '/record/glRecord',
      children:[
        {
          path: '/record/glRecord' ,
          component: resolve => require(['../components/page/h5/expenseManagement/components/glRecord.vue'], resolve),
          meta: { title: '用户充值记录' }
        },
        {
          path: '/record/MindGlsRecord' ,
          component: resolve => require(['../components/page/h5/expenseManagement/components/mindGlsRecord.vue'], resolve),
          meta: { title: '我的充值' }
        },

      ]
    },
    {
      path: '/glsSmsStatistics' ,
      component: resolve => require(['../components/page/h5/statistics/gls/smsStatistics/index.vue'], resolve),
      meta: { title: '短信明细' }
    },
    {
      path: '/glsUserRecord' ,
      component: resolve => require(['../components/page/h5/statistics/gls/smsStatistics/components/userRecord.vue'], resolve),
      meta: { title: '用户发送统计' }
    },
    {
      path: '/glsMonthRecord' ,
      component: resolve => require(['../components/page/h5/statistics/gls/smsStatistics/components/monthRecord.vue'], resolve),
      meta: { title: '月度发送统计' }
    },
    {
      path: '/glsSubUserRecord' ,
      component: resolve => require(['../components/page/h5/statistics/gls/smsStatistics/components/subUserRecord.vue'], resolve),
      meta: { title: '子用户发送明细' }
    },
    {
      path: '/glsSubUserReplyRecord' ,
      component: resolve => require(['../components/page/h5/statistics/gls/smsStatistics/components/subUserReplyRecord.vue'], resolve),
      meta: { title: '子用户回复明细' }
    },
    
    {
      path: '/mcSmsStatistics' ,
      component: resolve => require(['../components/page/h5/statistics/zd/smsStatistics/index.vue'], resolve),
      meta: { title: '短信明细' }
    },
    {
      path: '/mcSmsSendRecord' ,
      component: resolve => require(['../components/page/h5/statistics/zd/smsStatistics/components/mcSmsSendRecord.vue'], resolve),
      meta: { title: '发送详情' }
    },
    {
      path: '/mcSmsReplyRecord' ,
      component: resolve => require(['../components/page/h5/statistics/zd/smsStatistics/components/mcSmsReplyRecord.vue'], resolve),
      meta: { title: '回复记录' }
    },
    {
      path: '/mcBalanceReminder' ,
      component: resolve => require(['../components/page/h5/statistics/zd/actionOperate/balanceReminder.vue'], resolve),
      meta: { title: '账户设置' }
    },
    {
      path: '/mcNotificationWarning' ,
      component: resolve => require(['../components/page/h5/statistics/zd/actionOperate/notificationWarning.vue'], resolve),
      meta: { title: '通知预警设置' }
    },
    {
      path: '/mcWarningContact' ,
      component: resolve => require(['../components/page/h5/statistics/zd/actionOperate/warningContact.vue'], resolve),
      meta: { title: '预警联系人设置' }
    },
    {
      path: '/resetNumberH5' ,
      component: resolve => require(['../components/page/h5/components/resetNumberH5.vue'], resolve),
      meta: { title: '重置解密次数' }
    },
    {
      path: '/mcVideoStatistics' ,
      component: resolve => require(['../components/page/h5/statistics/zd/videoStatistics/index.vue'], resolve),
      meta: { title: '视频短信明细' }
    },
    {
      path: '/mcVideoSendRecord' ,
      component: resolve => require(['../components/page/h5/statistics/zd/videoStatistics/components/mcVideoSendRecord.vue'], resolve),
      meta: { title: '发送详情' }
    },
    {
      path: '/mcVideoReplyRecord' ,
      component: resolve => require(['../components/page/h5/statistics/zd/videoStatistics/components/mcVideoReplyRecord.vue'], resolve),
      meta: { title: '回复记录' }
    },
    {
      path: '/mcImsStatistics' ,
      component: resolve => require(['../components/page/h5/statistics/zd/imsStatistics/index.vue'], resolve),
      meta: { title: '视频短信明细' }
    },
    {
      path: '/mcImsSendRecord' ,
      component: resolve => require(['../components/page/h5/statistics/zd/imsStatistics/components/mcImsSendRecord.vue'], resolve),
      meta: { title: '发送详情' }
    },
    {
      path: '/mcImsReplyRecord' ,
      component: resolve => require(['../components/page/h5/statistics/zd/imsStatistics/components/mcImsReplyRecord.vue'], resolve),
      meta: { title: '回复记录' }
    },
    {
      path: '/mcVoiceCodeSendRecord' ,
      component: resolve => require(['../components/page/h5/statistics/zd/voiceStatistics/components/mcVoiceCodeSendRecord.vue'], resolve),
      meta: { title: '发送详情' }
    },
    {
      path: '/mcVoiceNoticeSendRecord' ,
      component: resolve => require(['../components/page/h5/statistics/zd/voiceStatistics/components/mcVoiceNoticeSendRecord.vue'], resolve),
      meta: { title: '发送详情' }
    },
    // {
    //   path: '/quickActions/multimediaMessage' ,
    //   component: resolve => require(['../components/page/h5/quickActions/multimediaMessage/index.vue'], resolve),
    //   meta: { title: '彩信剩余' }
    // },
    // {
    //   path: '/quickActions/videoMessage' ,
    //   component: resolve => require(['../components/page/h5/quickActions/videoMessage/index.vue'], resolve),
    //   meta: { title: '视频短信剩余' }
    // },
    // {
    //   path: '/quickActions/internationalMessage' ,
    //   component: resolve => require(['../components/page/h5/quickActions/internationalMessage/index.vue'], resolve),
    //   meta: { title: '国际短信剩余' }
    // },
    // {
    //   path: '/quickActions/flashTest' ,
    //   component: resolve => require(['../components/page/h5/quickActions/flashTest/index.vue'], resolve),
    //   meta: { title: '闪验剩余' }
    // },
    // {
    //   path: '/quickActions/voiceVerificationCode' ,
    //   component: resolve => require(['../components/page/h5/quickActions/voiceVerificationCode/index.vue'], resolve),
    //   meta: { title: '语音验证码剩余' }
    // },
    // {
    //   path: '/quickActions/voiceNotification' ,
    //   component: resolve => require(['../components/page/h5/quickActions/voiceNotification/index.vue'], resolve),
    //   meta: { title: '语音通知剩余' }
    // },
    // {
    //   path: '/quickActions/memberManagement' ,
    //   component: resolve => require(['../components/page/h5/quickActions/memberManagement/index.vue'], resolve),
    //   meta: { title: '会员管理' }
    // },
    // {
    //   path: '/quickActions/fiveGMessage' ,
    //   component: resolve => require(['../components/page/h5/quickActions/fiveGMessage/index.vue'], resolve),
    //   meta: { title: '5G消息剩余' }
    // },
    // {
    //   path: '/quickActions/yuexin' ,
    //   component: resolve => require(['../components/page/h5/quickActions/yuexin/index.vue'], resolve),
    //   meta: { title: '阅信剩余' }
    // },
    {
      path: '/register',
      component: resolve => require(['../components/page/register/register.vue'], resolve)
    },
    {
      path: '/protocol',
      // component: () => import ('../components/page/client/register/register.vue')
      component: resolve => require(['../components/page/register/contract.vue'], resolve)
    },
    {
      path: '/login',
      component: resolve => require(['../components/page/Login.vue'], resolve)
    },
    // {
    //   path: '/form',
    //   component: resolve => require(['../components/page/h5.vue'], resolve)
    // },
    // {
    //   path: '/questionnaire',
    //   component: resolve => require(['../components/page/h5_investigate.vue'], resolve)
    // },
    // {
    //   path: '/enroll',
    //   component: resolve => require(['../components/page/h5_message.vue'], resolve)
    // },
    {
      path: '*',
      redirect: '/404'
    }
    // {
    //   path: '/about',
    //   name: 'about',
    //   // route level code-splitting
    //   // this generates a separate chunk (about.[hash].js) for this route
    //   // which is lazy-loaded when the route is visited.
    //   component: () => import(/* webpackChunkName: "about" */ '@/pages/About.vue')
    // }
  ]
})
